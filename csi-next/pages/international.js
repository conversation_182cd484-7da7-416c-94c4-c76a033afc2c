import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useReducer,
  useCallback,
} from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import Link from "next/link";
import Modal from "react-bootstrap/Modal";
import moment from "moment";
import "moment/locale/th";

import { Autoplay, Pagination, EffectFade, Navigation } from "swiper";
import { Swiper, SwiperSlide } from "swiper/react";
import { Timeline, Tween, ScrollTrigger } from "react-gsap";

// SEO//
import { getSeo } from "/pages/api/seo";
import { getCourseFilter } from "/pages/api/courseFilter";
import { getCourseData } from "/pages/api/courseData";
import { getHomeBanner } from "/pages/api/homeBanner";
import { getIntroWord } from "/pages/api/introWord";
import Seo from "/components/seo";
// SEO//

//components
import HeroBanner from "/components/HeroBanner/HeroBanner";
import CardCourse from "/components/CardCourse/CardCourse";
import PaginationView from "/components/PaginationView/PaginationView";
import FormSearch from "/components/FormSearch/FormSearch";
import TitleSectionCourse from "/components/TitleSectionCourse/TitleSectionCourse";
import GroupSearchFilter from "/components/GroupSearchFilter/GroupSearchFilter";
import HomeIntro from "/components/HomeIntro/HomeIntro";

//components

import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";

// Serverside & Api fetching
import nookies from "nookies";
import { parseCookies, setCookie, destroyCookie } from "nookies";
import { getUser } from "/pages/api/user";
import AppContext from "/libs/contexts/AppContext";
import { URLSearchParams } from "url";
import { useSession, getSession } from "next-auth/react";
// Serverside & Api fetching

export async function getServerSideProps(context) {
  const cookies = nookies.get(context);
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + "_token"] || "";
  const lang = context.locale || "th";
  const seoData = await getSeo(utoken, "course_filter", lang);
  const homeBannerData = await getHomeBanner('3',utoken, lang);
  const introWord = await getIntroWord('3' ,utoken, lang);

  // Auth
  const user = await getUser(utoken);
  if (!user) {
    // return {
    //   redirect: { destination: "/" },
    // };
  }
  // Auth

  const filter_all = await getCourseFilter('2',utoken,lang);

  return {
    props: {
      utoken,
      seoData,
      user,
      lang,
      homeBannerData,
      introWord,
      filter_all
    },
  };
}

export default function SearchPage({ utoken, seoData, user,filter_all,lang,homeBannerData,introWord }) {
  const appContext = useContext(AppContext);
  const [introWordData, setIntroWordData] = useState(introWord);
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [filterData, setFilterData] = useState(filter_all);
  const [tag, setTag] = useState([]);
  const [reloadCourse, setReloadCourse] = useState(false);
  const [firstLoad, setFirstLoad] = useState(false);
  const [firstGet, setFirstGet] = useState(false);

  const [filter, setFilter] = useState([]);
  const [pageList, setPageList] = useState([]);
  const [sort, setSort] = useState(null);
  const [keyword, setKeyword] = useState(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(24);
  const [courseData, setCourseData] = useState(null);

  const [filterSelectShow, setFilterSelectShow] = useState(false);
  const [filterSearchShow, setFilterSearchShow] = useState(false);

  const [reloadUrl, setReloadUrl] = useState(true);
  const [reloadCard, setReloadCard] = useState(true);
  const [reloadFilter, setReloadFilter] = useState(false);
  useEffect(() => {
    if (reloadUrl) {
      setReloadUrl(false);
      if (!appContext.isNull(appContext.jGet("keyword"))) {
        setKeyword(appContext.jGet("keyword"));
      }
      if (!appContext.isNull(appContext.jGet("categories"))) {
        selectCateFirst("categories", appContext.jGet("categories"));
      }
      if (!appContext.isNull(appContext.jGet("level"))) {
        selectCateFirst("level", appContext.jGet("level"));
      }
      if (!appContext.isNull(appContext.jGet("job_function"))) {
        selectCateFirst("job_function", appContext.jGet("job_function"));
      }
      if (!appContext.isNull(appContext.jGet("speaker"))) {
        selectCateFirst("speaker", appContext.jGet("speaker"));
      }
      if (!appContext.isNull(appContext.jGet("is_certificate"))) {
        selectCateFirst("is_certificate", appContext.jGet("is_certificate"));
      }
      if (!appContext.isNull(appContext.jGet("duration"))) {
        selectCateFirst("duration", appContext.jGet("duration"));
      }
      setPage(1);
      setReloadCourse(true);
    }
  }, [reloadUrl]);
  function selectCateFirst(_key, _id) {
    const id_replace = _id.replaceAll(' ','');
    const id_arr = id_replace.split(',');
    for (var k = 0; k < id_arr.length; k++) {
      id_arr[k] = parseInt(id_arr[k]);
    }
    if (filter.length == 0) {
      const obj = {
        category_key: _key,
        cate: id_arr,
      };
      filter.push(obj);
    } else {
      var check_key = 0;
      for (var i = 0; i < filter.length; i++) {
        if (filter[i].category_key != _key) {
          check_key++;
        }
      }
      if (check_key == filter.length) {
        const obj = {
          category_key: _key,
          cate: id_arr,
        };
        filter.push(obj);
      } else {
        for (var i = 0; i < filter.length; i++) {
          if (filter[i].category_key == _key) {
            for (var m = 0; m < id_arr.length; m++) {
              const index = filter[i].cate.indexOf(id_arr[m]);
              if (index <= -1) {
                filter[i].cate.push(id_arr[m]);
              }
            }
          }
        }
      }
    }
  }

  useEffect(() => {
    if (reloadCourse) {
      if(!firstGet){
        setFirstGet(true);
      }else{
        window.scrollTo({
          top: document.getElementById("top_search_offset").offsetTop,
          behavior: 'smooth',
        })
      }
      setReloadCard(false);
      setReloadCourse(false);
      setFirstLoad(false);
      const fetchData = async () => {
        const jsonData = await getCourseData('2',JSON.stringify(filter),sort,keyword,limit,page,utoken, lang, null);
        if (jsonData["status"] == "success") {
          setTimeout(() => {
            setFirstLoad(true);
          }, "100");
          setTag(jsonData["tag_data"]);
          setCourseData(jsonData);
  
          var pages = [];
          for(var i=0;i<jsonData['all_page'];i++){
            pages.push((i+1));
          }
          setPageList(pages);
          setReloadFilter(true);
          setTimeout(() => {
            setReloadCard(true);
          }, "0");
        }
        return jsonData;
      };
      const jsonData = fetchData().catch(console.error);
    }
  }, [reloadCourse]);
  function prevPage() {
    if(page>1){
      var int_page = parseInt(page)-1;
      setPage(int_page);
      setReloadCourse(true);
    }
  }
  function nextPage() {
    if(page<pageList.length){
      var int_page = parseInt(page)+1;
      setPage(int_page);
      setReloadCourse(true);
    }
  }
  function selectPage(_val) {
    var page = parseInt(_val);
    setPage(page);
    setReloadCourse(true);
  }
  function selectCate(_key, _id) {
    if (!appContext.isNull(appContext.jGet(_key))) {
      for (var i = 0; i < filter.length; i++) {
        if (filter[i].category_key == _key) {
          const index = filter[i].cate.indexOf(_id);
          if (index <= -1) {
            window.history.pushState("", "", window.location.href.replace(_key+'=',_key+'='+_id+','));
          } else {
            const old_arr = appContext.jGet(_key);
            const split_arr = appContext.jGet(_key).split(',');
            const new_arr = [];
            for (var z = 0; z < split_arr.length; z++) {
              if(split_arr[z]!=_id){
                new_arr.push(split_arr[z]);
              }
            }
            if(new_arr.length>0){
              var new_text = '';
              for (var z = 0; z < new_arr.length; z++) {
                if(new_text!=''){
                  new_text+=',';
                }
                new_text+=new_arr[z].toString();
              }
              window.history.pushState("", "", window.location.href.replace(_key+'='+old_arr,_key+'='+new_text));
            }else{
              if (window.location.href.indexOf("?"+_key) > -1 ){
                window.history.pushState("", "", window.location.href.replace('?'+_key+'='+old_arr,''));
              }else{
                window.history.pushState("", "", window.location.href.replace('&'+_key+'='+old_arr,''));
              }
              if (window.location.href.indexOf("course&") > -1 ){
                window.history.pushState("", "", window.location.href.replace('course&','course?'));
              }
            }
          }
        }
      }
    }else{
      if (window.location.href.indexOf("course?") > -1 ){
        window.history.pushState("", "", window.location.href+"&"+_key+'='+_id);
      }else{
        window.history.pushState("", "", window.location.href+"?"+_key+'='+_id);
      }
    }

    if (filter.length == 0) {
      const obj = {
        category_key: _key,
        cate: [_id],
      };
      filter.push(obj);
    } else {
      var check_key = 0;
      for (var i = 0; i < filter.length; i++) {
        if (filter[i].category_key != _key) {
          check_key++;
        }
      }
      if (check_key == filter.length) {
        const obj = {
          category_key: _key,
          cate: [_id],
        };
        filter.push(obj);
      } else {
        for (var i = 0; i < filter.length; i++) {
          if (filter[i].category_key == _key) {
            const index = filter[i].cate.indexOf(_id);
            if (index <= -1) {
              filter[i].cate.push(_id);
            } else {
              filter[i].cate.splice(index, 1);
            }
          }
        }
      }
    }
    setPage(1);
    setReloadCourse(true);
  }
  function selectSort(_value) {
    setSort(_value[0].value);
    setPage(1);
    setReloadCourse(true);
  }
  function selectCateFilter(_value) {
    var filter_arr = [];
    var obj = {
      category_key: 'categories',
      cate: [_value[0].value],
    };
    filter_arr.push(obj);
    setFilter(filter_arr);
    setPage(1);
    setReloadCourse(true);
  }
  function keywordSort(_value) {
    setKeyword(_value);
    setPage(1);
    setReloadCourse(true);
  }

  const OpenNavSearch = (e, props) => {
    setFilterSearchShow(e);
    // console.log("filterSearchShow : " + filterSearchShow);
  };
  const OpenNavSelect = (e, props) => {
    setFilterSelectShow(e);
    // console.log("filterSelectShow : " + filterSelectShow);
  };

  function resetFilter(){
    setFilter([]);
    setSort('');
    setKeyword('');
    var search_box = document.getElementById('search_box');
    search_box.value = '';
    setPage(1);
    setReloadCourse(true);
  }

  function clearFilter(){
    setFilter([]);
    setKeyword('');
    var search_box = document.getElementById('search_box');
    search_box.value = '';

    // search_box
    setPage(1);
    setReloadCourse(true);
  }

  return (
    <>
      <Seo seoData={seoData}></Seo>
      <div className={`searc_page`}>
        <div className={`headline-course-page ${ filterSelectShow ? "FilterSelectShow" : ""} ${ (introWordData && introWordData.status && introWordData.status=='success') || (homeBannerData && homeBannerData.heroBanner && homeBannerData.heroBanner.length>0) ? "review-page" : ""}`}>
          <div className="container custom-container">
            <h4 className="text-white">
              Course Buffet
            </h4>
          </div>
        </div>
      </div>

      {/* {introWordData && introWordData.status && introWordData.status=='success' ?(
        <HomeIntro data={introWordData.data}></HomeIntro>
      ):null}
      <div id="top_search_offset" className={`searc_page ${ filterSelectShow ? "FilterSelectShow" : ""} ${ introWordData && introWordData.status && introWordData.status=='success' ? "have_intro_word" : ""}`}>
        <div className="container custom-container">
          <div className="inner_search_page">
            <div className="search_page_result search-box">
              <div className="inner_page_result">
                <div className="section sec-form-Search">
                  <FormSearch cateList={filterData.cate_list} OpenNavSelect={OpenNavSelect} OpenNavSearch={OpenNavSearch} FilterSelect={filterSelectShow} FilterSearch={filterSearchShow} keywordSort={keywordSort} resetFilter={resetFilter} selectSort={selectSort} selectCateFilter={selectCateFilter} default={keyword}></FormSearch>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="container custom-container">
          <div className="row row-course-view">
          {reloadCard && courseData &&
          courseData["course_data"] &&
          courseData["course_data"].length > 0
            ? courseData["course_data"].map((val, key) =>
                val["data"].length > 0 ? (
                  val.data.map((val_data, key_data) =>
                    <div key={key_data} className="col-12 col-md-6 col-lg-4 col-course-view">
                      <CardCourse
                        cardType={'course'}
                        isFav={val_data.is_fav}
                        allowed={val_data.allowed}
                        order_status={val_data.order_status}
                        is_promotion={val_data.is_promotion}
                        pro_period={val_data.pro_period}
                        pro_countdown={val_data.pro_countdown}
                        pro_price={val_data.pro_price}
                        price={val_data.price}
                        courseId={val_data.id}
                        prefix={'course'}
                        textTag={val_data.category}
                        textTttle={appContext.truncate(val_data.title_th,40)}
                        textDetail={appContext.truncate(val_data.subtitle_th,60)}
                        video={true}
                        ep={val_data.lesson}
                        isTime={val_data.is_time}
                        timeSet={val_data.time_set}
                        isDoc={val_data.is_doc}
                        docText={val_data.doc_text}
                        durationTime={val_data.duration_time}
                        hours={Math.round(val_data.duration/3600)}
                        daysLeft={val_data.days_left}
                        points={val_data.receive_point}
                        src={val_data.image_th}
                        slug={val_data.slug}
                        themeColor={'dark'}
                      ></CardCourse>
                    </div>
                  )
                ) : null
              )
            : 
            firstLoad?(
              <div className="block-not-found">
                <div className="inner-not-found">
                  <i className="icon-ic-not-found-1"></i>
                  <p>
                    ขออภัยค่ะ<br></br>ไม่พบข้อมูลที่คุณต้องการ
                  </p>
                </div>
              </div>
            ):(
              <div className="block-not-found">
                <div className="inner-not-found">
                  <i className="icon-ic-not-found-1"></i>
                  <p>
                    กำลังค้นหาข้อมูล
                  </p>
                </div>
              </div>
            )
            }
          </div>
          <div className="row row-course-pagination">
            {pageList.length>1?(
                <PaginationView data={pageList} page={page}
                prev={prevPage} next={nextPage} select={selectPage}
                ></PaginationView>
            ):null}
          </div>
        </div>
      </div> */}
    </>
  );
}
