export async function getCourseData(_international,_filter,_sort,_keyword,_limit,_page,_token, lang = "th", _trailer_media = null) {
  if (process.env.NEXT_PUBLIC_API_CONNECT == "true") {
    var _headers;
    if (_token) {
      _headers = {
        "Accept-Language": lang,
        Authorization: "Bearer " + _token,
        "Content-Type": "application/json",
      };
    } else {
      _headers = {
        "Accept-Language": lang,
        "Content-Type": "application/json",
      };
    }

    const formData = new URLSearchParams();
    formData.append("utoken", _token);
    formData.append("filter", _filter);
    formData.append("sort", _sort);
    formData.append("keyword", _keyword);
    formData.append("limit", _limit);
    formData.append("page", _page);
    formData.append("international", _international);

    // Add trailer_media parameter for filtering specific course types
    if (_trailer_media !== null && _trailer_media !== undefined && _trailer_media !== '') {
      formData.append("trailer_media", _trailer_media);
    }

    const response = await fetch(process.env.NEXT_PUBLIC_API + "/core/getCourseFilter", {
      body: formData,
      // headers: _headers,
      method: "POST",
    });

    const jsonData = await response.json();
    return jsonData;
  } else {
    return {
      status: "success",
      filter_data: [
        {
            "category_key": "level",
            "category_name": "ระดับเนื้อหา",
            "cate": [
                {
                    "id": 1,
                    "title_th": "Beginner"
                },
                {
                    "id": 4,
                    "title_th": "Intermediate"
                }
            ]
        },
        {
            "category_key": "categories",
            "category_name": "ประเภทเนื้อหา",
            "cate": [
                {
                    "id": 29,
                    "title_th": "หมอขอเล่า"
                },
                {
                    "id": 22,
                    "title_th": "Basic Science"
                },
                {
                    "id": 21,
                    "title_th": "Clinical Science"
                },
                {
                    "id": 23,
                    "title_th": "แพทยศาสตรศึกษา"
                },
                {
                    "id": 15,
                    "title_th": "ทักษะทางการแพทย์"
                },
                {
                    "id": 20,
                    "title_th": "วิจัยและเทคโนโลยีทางการแพทย์"
                },
                {
                    "id": 11,
                    "title_th": "ติดจอ ฬ จุฬา"
                }
            ]
        },
        {
            "category_key": "department",
            "category_name": "ภาควิชา",
            "cate": [
                {
                    "id": 55,
                    "title_th": "ภาควิชาสูติศาสตร์-นรีเวชวิทยา"
                },
                {
                    "id": 53,
                    "title_th": "ภาควิชาเวชศาสตร์ฟื้นฟู"
                },
                {
                    "id": 56,
                    "title_th": "หน่วยวางแผนครอบครัวและอนามัยเจริญพันธุ์"
                },
                {
                    "id": 52,
                    "title_th": "ภาควิชาออร์โธปิดิกส์"
                },
                {
                    "id": 58,
                    "title_th": "ภาควิชาเวชศาสตร์ครอบครัว"
                },
                {
                    "id": 57,
                    "title_th": "ภาควิชาจักษุวิทยา"
                },
                {
                    "id": 59,
                    "title_th": "โรงพยาบาลจุฬาลงกรณ์ สภากาชาดไทย"
                },
                {
                    "id": 60,
                    "title_th": "คลินิกทันตกรรมผู้สูงอายุและการดูแลผู้ป่วยพิเศษ"
                },
                {
                    "id": 63,
                    "title_th": "ฝ่ายบริหาร"
                },
                {
                    "id": 64,
                    "title_th": "กุมารเวชศาสตร์"
                },
                {
                    "id": 65,
                    "title_th": "จักษุวิทยา"
                },
                {
                    "id": 66,
                    "title_th": "ศูนย์วิจัยโรคเอดส์สภากาชาดไทย"
                },
                {
                    "id": 67,
                    "title_th": "อายุรศาสตร์"
                },
                {
                    "id": 68,
                    "title_th": "ศูนย์ความเป็นเลิศทางการแพทย์ด้านการดูแลผู้สูงอายุ"
                },
                {
                    "id": 69,
                    "title_th": "จิตเวชศาสตร์"
                },
                {
                    "id": 70,
                    "title_th": "รังสีวิทยา"
                },
                {
                    "id": 71,
                    "title_th": "โสต ศอ นาสิกวิทยา"
                },
                {
                    "id": 72,
                    "title_th": "สูติศาสตร์-นรีเวชวิทยา"
                },
                {
                    "id": 73,
                    "title_th": "เวชศาสตร์ฟื้นฟู"
                },
                {
                    "id": 74,
                    "title_th": "เวชศาสตร์ชันสูตร"
                },
                {
                    "id": 75,
                    "title_th": "ธนาคารเลือด"
                },
                {
                    "id": 76,
                    "title_th": "ชีวเคมี"
                },
                {
                    "id": 77,
                    "title_th": "ศูนย์ความเป็นเลิศทางการแพทย์โรคมะเร็งครบวงจร"
                },
                {
                    "id": 78,
                    "title_th": "ศัลยศาสตร์"
                },
                {
                    "id": 79,
                    "title_th": "เภสัชวิทยา"
                },
                {
                    "id": 81,
                    "title_th": "ออร์โธปิดิกส์"
                },
                {
                    "id": 82,
                    "title_th": "ศัลยแพทย์ประจำศูนย์ความเป็นเลิศทางการแพทย์ด้านการปลูกถ่ายอวัยวะ"
                },
                {
                    "id": 83,
                    "title_th": "นาสิกวิทยาและภูมิแพ้"
                },
                {
                    "id": 84,
                    "title_th": "หน่วยศัลยศาสตร์ลำไส้ใหญ่และทวารหนัก"
                },
                {
                    "id": 85,
                    "title_th": "นิทราเวช"
                },
                {
                    "id": 86,
                    "title_th": "โรคหลอดเลือดสมอง"
                },
                {
                    "id": 88,
                    "title_th": "จุลชีววิทยา"
                },
                {
                    "id": 89,
                    "title_th": "ศูนย์ความเป็นเลิศทางการแพทย์ด้านโรคหลอดเลือดสมองแบบครบวงจร โรงพยาบาลจุฬาลงกรณ์ สภากาชาดไทย"
                },
                {
                    "id": 93,
                    "title_th": "เวชศาสตร์ฉุกเฉิน"
                },
                {
                    "id": 97,
                    "title_th": "คณะแพทยศาสตร์ จุฬาลงกรณ์มหาวิทยาลัย"
                },
                {
                    "id": 98,
                    "title_th": "เวชศาสตร์ครอบครัว"
                },
                {
                    "id": 100,
                    "title_th": "หัวหน้าฝ่ายสูติศาสตร์ - นรีเวชวิทยา"
                },
                {
                    "id": 101,
                    "title_th": "อายุรศาสตร์์"
                },
                {
                    "id": 103,
                    "title_th": "สูติศาสตร์ - นรีเวชวิทยา"
                },
                {
                    "id": 104,
                    "title_th": "การแพทย์ด้านโรคตับ"
                },
                {
                    "id": 105,
                    "title_th": "สูติ - นรีเวชวิทยา"
                },
                {
                    "id": 106,
                    "title_th": "แพทย์รังสีรักษาและมะเร็งวิทยา"
                },
                {
                    "id": 22,
                    "title_th": "ภาควิชาโสต ศอ นาสิกวิทยา"
                },
                {
                    "id": 20,
                    "title_th": "ฝ่ายสูติศาสตร์-นรีเวชวิทยา"
                },
                {
                    "id": 17,
                    "title_th": "ฝ่ายจักษุวิทยา"
                },
                {
                    "id": 15,
                    "title_th": "หน่วยศัลยกรรมกระดูกสันหลัง ฝ่ายออร์โธปิดิกส์"
                },
                {
                    "id": 16,
                    "title_th": "ฝ่ายเวชศาสตร์ฟื้นฟู"
                },
                {
                    "id": 23,
                    "title_th": "ศูนย์โรคหัวใจ"
                },
                {
                    "id": 11,
                    "title_th": "สาขาวิชาต่อมไร้ท่อและเมตะบอลิสม"
                },
                {
                    "id": 13,
                    "title_th": "ฝ่ายอายุรศาสตร์"
                },
                {
                    "id": 10,
                    "title_th": "ฝ่ายศัลยศาสตร์"
                },
                {
                    "id": 7,
                    "title_th": "ฝ่ายกุมารเวชศาสตร์"
                },
                {
                    "id": 5,
                    "title_th": "ภาควิชาเภสัชวิทยา"
                },
                {
                    "id": 6,
                    "title_th": "ฝ่ายจิตเวชศาสตร์"
                },
                {
                    "id": 3,
                    "title_th": "ศูนย์เชี่ยวชาญเฉพาะทางด้านไวรัสวิทยาคลินิก"
                },
                {
                    "id": 12,
                    "title_th": "ฝ่ายโสต ศอ นาสิกวิทยา"
                },
                {
                    "id": 26,
                    "title_th": "ภาควิชาศัลยศาสตร์"
                },
                {
                    "id": 46,
                    "title_th": "ภาควิชาอายุรศาสตร์"
                },
                {
                    "id": 44,
                    "title_th": "ภาควิชาสรีรวิทยา"
                },
                {
                    "id": 41,
                    "title_th": "ภาควิชาเวชศาสตร์ชันสูตร"
                },
                {
                    "id": 39,
                    "title_th": "ศูนย์วิจัยวัคซีน"
                },
                {
                    "id": 25,
                    "title_th": "ศูนย์ความเป็นเลิศทางการแพทย์โรคพาร์กินสันและกลุ่มโรคความเคลื่อนไหวผิดปกติ"
                },
                {
                    "id": 35,
                    "title_th": "คลินิกจิตเวชเด็กและวัยรุ่น ฝ่ายจิตเวชศาสตร์"
                },
                {
                    "id": 37,
                    "title_th": "ฝ่ายพยาธิวิทยา"
                },
                {
                    "id": 33,
                    "title_th": "ฝ่ายปรสิตวิทยา"
                },
                {
                    "id": 34,
                    "title_th": "คลินิกกระดูกและข้อ"
                },
                {
                    "id": 31,
                    "title_th": "ภาควิชาปรสิตวิทยา"
                },
                {
                    "id": 32,
                    "title_th": "คลินิกทันตกรรมผู้สูงอายุ"
                },
                {
                    "id": 1,
                    "title_th": "ศูนย์โรคอุบัติใหม่ด้านคลินิก"
                },
                {
                    "id": 36,
                    "title_th": "ฝ่ายวิสัญญีวิทยา"
                }
            ]
        },
        {
            "category_key": "is_certificate",
            "category_name": "มี Certificate",
            "cate": [
                {
                    "id": "1",
                    "title_th": "มี"
                }
            ]
        },
        {
            "category_key": "duration",
            "category_name": "ความยาว",
            "cate": [
                {
                    "id": "1",
                    "title_th": "< 30 นาที"
                },
                {
                    "id": "2",
                    "title_th": "< 1 ชั่วโมง"
                },
                {
                    "id": "3",
                    "title_th": "< 2 ชั่วโมง"
                },
                {
                    "id": "4",
                    "title_th": "2 ชั่วโมงขึ้นไป"
                }
            ]
        }
      ],
    };
  }
}
