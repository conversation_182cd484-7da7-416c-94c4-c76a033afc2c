import { useState, useEffect } from 'react'
import { getCourseData } from '@/pages/api/courseData'
import { courseRepository } from '../repositories/courseRepository'

const useCourseSearch = ({ utoken, lang, userSubscriptions = [], subscriptionMains = [] }) => {
  const [courseData, setCourseData] = useState(null)
  const [tag, setTag] = useState([])
  const [pageList, setPageList] = useState([])
  const [firstLoad, setFirstLoad] = useState(false)
  const [reloadCard, setReloadCard] = useState(true)
  const [loading, setLoading] = useState(false)

  const fetchCourseData = async (filter, sort, keyword, limit, page) => {
    try {
      setLoading(true)
      setReloadCard(false)
      setFirstLoad(false)

      const jsonData = await getCourseData('1', JSON.stringify(filter), sort, keyword, limit, page, utoken, lang, null)

      if (jsonData?.status === 'success') {
        const enhancedData = courseRepository.enhanceCourseListData(jsonData, userSubscriptions, subscriptionMains)

        setCourseData(enhancedData)
        setTag(jsonData.tag_data || [])

        const pages = []
        for (let i = 0; i < jsonData.all_page; i++) {
          pages.push(i + 1)
        }
        setPageList(pages)

        setTimeout(() => {
          setFirstLoad(true)
          setReloadCard(true)
        }, 100)
      }
    } catch (error) {
      console.error('Error fetching course data:', error)
    } finally {
      setLoading(false)
    }
  }

  return {
    courseData,
    tag,
    pageList,
    firstLoad,
    setFirstLoad,
    reloadCard,
    setReloadCard,
    loading,
    fetchCourseData,
  }
}

export default useCourseSearch
