import nookies from 'nookies'
import { getSeo } from '/pages/api/seo'
import { getHomeBanner } from '/pages/api/homeBanner'
import { getHomeSearch } from '/pages/api/homeSearch'
import { getHomeContent } from '/pages/api/homeContent'
import { getSettingCenter } from '/pages/api/settingCenter'
import { getIntroWord } from '/pages/api/introWord'
import { getUserStat } from '/pages/api/userStat'
import { getUser } from '/pages/api/user'
import { getCourseData } from '/pages/api/courseData'
import { getUserDashboard } from '/pages/api/userDashboard'
// Additional imports for course detail page
import { getCourseDetail } from '/pages/api/courseDetail'
import { getCourseVdo } from '/pages/api/courseVdo'
import { getCourseComment } from '/pages/api/courseComment'
import { getCourseRelate } from '/pages/api/courseRelate'
import { addCourseView } from '/pages/api/courseView'

const COURSE_API_CONFIG = [{ name: 'companyvisit', filter: '[]', sort: 'latest', trailer_media: '4' }]

const buildCoreApiCalls = (utoken, lang) => [
  getSeo(utoken, 'home', lang),
  getHomeBanner('5', utoken, lang),
  getHomeSearch(utoken, lang),
  getHomeContent(utoken, lang),
  getSettingCenter('home_vdo', utoken, lang),
  getIntroWord('1', utoken, lang),
  getUserStat(utoken, lang),
  getUser(utoken),
]

const buildCourseApiCalls = (utoken, lang) =>
  COURSE_API_CONFIG.map((config) => getCourseData('1', config.filter, config.sort, '', 20, 1, utoken, lang, config.trailer_media))

const buildDashboardApiCall = (utoken, lang) =>
  utoken ? getUserDashboard('other', utoken, lang) : Promise.resolve(null)

const mapCourseData = (courseResults) => {
  const courseData = {}
  COURSE_API_CONFIG.forEach((config, index) => {
    courseData[`${config.name}CourseData`] = courseResults[index]
  })
  return courseData
}

const buildSuccessResponse = (results, utoken, lang) => {
  const [
    seoData,
    homeBannerData,
    homeSearchData,
    homeContentData,
    settingCenter,
    introWord,
    userStat,
    user,
    ...otherResults
  ] = results

  const courseResults = otherResults.slice(0, COURSE_API_CONFIG.length)
  const userDashboardData = otherResults[COURSE_API_CONFIG.length]
  const courseData = mapCourseData(courseResults)

  return {
    props: {
      utoken,
      seoData,
      user,
      homeBannerData,
      homeSearchData,
      homeContentData,
      settingCenter,
      introWord,
      userStat,
      lang,
      companyvisitData: courseData.companyvisitCourseData,
      userDashboardData,
    },
  }
}

const buildErrorResponse = (utoken, lang, error) => {
  if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
    console.error('Error fetching Company Visit data:', error)
  }

  return {
    props: {
      error: 'Failed to load page data',
      utoken: utoken || '',
      lang,
      seoData: null,
      user: null,
      homeBannerData: null,
      homeSearchData: null,
      homeContentData: null,
      settingCenter: null,
      introWord: null,
      userStat: null,

      companyvisitData: null,
      userDashboardData: null,
    },
  }
}

export async function fetchCompanyVisitData(context) {
  const cookies = nookies.get(context)
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + '_token'] || ''
  const lang = context.locale || 'th'

  try {
    const coreApiCalls = buildCoreApiCalls(utoken, lang)
    const courseApiCalls = buildCourseApiCalls(utoken, lang)
    const dashboardApiCall = buildDashboardApiCall(utoken, lang)

    const results = await Promise.all([...coreApiCalls, ...courseApiCalls, dashboardApiCall])

    return buildSuccessResponse(results, utoken, lang)
  } catch (error) {
    return buildErrorResponse(utoken, lang, error)
  }
}

// New function to handle SSR for company-visit detail pages
const buildDetailApiCalls = (utoken, lang, courseId) => [
  getSeo(utoken, `company-visit/${courseId}`, lang),
  getCourseDetail(courseId, utoken, lang),
  getCourseVdo(courseId, utoken, lang),
  getCourseComment(courseId, utoken, lang),
  getCourseRelate(courseId, utoken, lang),
  getUserStat(utoken, lang),
  getUser(utoken),
]

const buildDetailSuccessResponse = (results, utoken, lang) => {
  const [seoData, courseDetailData, courseVdoData, courseCommentData, courseRelateData, userStat, user] = results

  return {
    props: {
      utoken,
      seoData,
      user,
      courseDetailData,
      courseVdoData,
      courseCommentData,
      courseRelateData,
      userStat,
      lang,
    },
  }
}

const buildDetailErrorResponse = (utoken, lang, error) => {
  if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
    console.error('Error fetching Company Visit Detail data:', error)
  }

  return {
    props: {
      error: 'Failed to load page data',
      utoken: utoken || '',
      lang,
      seoData: null,
      user: null,
      courseDetailData: null,
      courseVdoData: null,
      courseCommentData: null,
      courseRelateData: null,
    },
  }
}

export async function fetchCompanyVisitDetailPageData(context) {
  const cookies = nookies.get(context)
  const utoken = cookies[process.env.NEXT_PUBLIC_APP + '_token'] || ''
  const lang = context.locale || 'th'
  const params = context.query
  const seoData = await getSeo(utoken, 'company-visit/' + params.id, lang)

  try {
    const detail = await getCourseDetail(params.id, utoken, lang)

    if (!detail || detail == null || (detail['status'] && detail['status'] == 'false')) {
      return {
        redirect: { destination: '/company-visit' },
      }
    }

    const vdo = await getCourseVdo(detail.data.id, utoken, lang)
    const comment = await getCourseComment('desc', 5, 1, detail.data.id, utoken, lang)
    const relate = await getCourseRelate(detail.data.id, utoken, lang)
    const view = await addCourseView(detail.data.id, utoken, lang)
    const user = await getUser(utoken)

    return {
      props: {
        utoken,
        seoData,
        user,
        detail,
        vdo,
        comment,
        lang,
        relate,
      },
    }
  } catch (error) {
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      console.error('Error fetching Company Visit Detail data:', error)
    }

    return {
      props: {
        error: 'Failed to load course detail data',
        utoken: utoken || '',
        lang,
        seoData: null,
        user: null,
        detail: null,
        vdo: null,
        comment: null,
        relate: null,
      },
    }
  }
}
