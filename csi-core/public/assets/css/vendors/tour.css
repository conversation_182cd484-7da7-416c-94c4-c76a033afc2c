.introjs-overlay{position:absolute;-webkit-box-sizing:content-box;box-sizing:content-box;z-index:999999;background-color:#000;opacity:0;filter:alpha(opacity=50);-webkit-transition:all 0.3s ease-out;transition:all 0.3s ease-out}.introjs-fixParent{z-index:auto !important;opacity:1.0 !important;-webkit-transform:none !important;transform:none !important}.introjs-showElement{z-index:9999999 !important}tr.introjs-showElement>td,tr.introjs-showElement>th{z-index:9999999 !important}.introjs-disableInteraction{z-index:99999999 !important;position:absolute;background-color:white;opacity:0;filter:alpha(opacity=0)}.introjs-relativePosition{position:relative}tr.introjs-showElement>td,tr.introjs-showElement>th{position:relative}.introjs-helper<PERSON>ayer{-webkit-box-sizing:content-box;box-sizing:content-box;position:absolute;z-index:9999998;background-color:#FFF;background-color:rgba(255,255,255,0.9);border:1px solid rgba(0,0,0,0.5);border-radius:4px;-webkit-box-shadow:0 2px 15px rgba(0,0,0,0.4);box-shadow:0 2px 15px rgba(0,0,0,0.4);-webkit-transition:all 0.3s ease-out;transition:all 0.3s ease-out}.introjs-tooltipReferenceLayer{-webkit-box-sizing:content-box;box-sizing:content-box;position:absolute;visibility:hidden;z-index:100000000;background-color:transparent;-webkit-transition:all 0.3s ease-out;transition:all 0.3s ease-out}.introjs-helperLayer *{-webkit-box-sizing:content-box;box-sizing:content-box}.introjs-helperLayer *:before,.introjs-helperLayer *:after{-webkit-box-sizing:content-box;box-sizing:content-box}.introjs-helperNumberLayer{-webkit-box-sizing:content-box;box-sizing:content-box;position:absolute;visibility:visible;top:-16px;left:-16px;z-index:9999999999 !important;padding:2px;font-size:13px;font-weight:bold;color:white;text-align:center;text-shadow:1px 1px 1px rgba(0,0,0,0.3);background:-webkit-gradient(linear, left top, left bottom, from(#4466f2), to(#4466f2));background:linear-gradient(to bottom, #4466f2 0%, #4466f2 100%);width:20px;height:20px;line-height:20px;border:3px solid white;border-radius:50%;-webkit-box-shadow:0 2px 5px rgba(0,0,0,0.4);box-shadow:0 2px 5px rgba(0,0,0,0.4)}.introjs-arrow{border:15px solid white;content:'';position:absolute}.introjs-arrow.top{top:-30px;border-top-color:transparent;border-right-color:transparent;border-bottom-color:#4466f2;border-left-color:transparent}.introjs-arrow.top-right{top:-10px;right:10px;border-top-color:transparent;border-right-color:transparent;border-bottom-color:#4466f2;border-left-color:transparent}.introjs-arrow.top-middle{top:-10px;left:50%;margin-left:-5px;border-top-color:transparent;border-right-color:transparent;border-bottom-color:#4466f2;border-left-color:transparent}.introjs-arrow.right{right:-30px;top:10px;border-top-color:transparent;border-right-color:transparent;border-bottom-color:transparent;border-left-color:#4466f2}.introjs-arrow.right-bottom{bottom:10px;right:-30px;border-top-color:transparent;border-right-color:transparent;border-bottom-color:transparent;border-left-color:#4466f2}.introjs-arrow.bottom{bottom:-30px;border-top-color:#4466f2;border-right-color:transparent;border-bottom-color:transparent;border-left-color:transparent}.introjs-arrow.left{left:-30px;top:10px;border-top-color:transparent;border-right-color:#4466f2;border-bottom-color:transparent;border-left-color:transparent}.introjs-arrow.left-bottom{left:-30px;bottom:10px;border-top-color:transparent;border-right-color:#4466f2;border-bottom-color:transparent;border-left-color:transparent}.introjs-tooltip{-webkit-box-sizing:content-box;box-sizing:content-box;position:absolute;visibility:visible;padding:15px;background-color:#4466f2;min-width:250px;max-width:300px;border-radius:3px;-webkit-box-shadow:0 1px 10px rgba(0,0,0,0.4);box-shadow:0 1px 10px rgba(0,0,0,0.4);-webkit-transition:opacity 0.1s ease-out;transition:opacity 0.1s ease-out;color:white;border:6px solid #4466f2}.introjs-tooltipbuttons{text-align:right;white-space:nowrap}.introjs-button{-webkit-box-sizing:content-box;box-sizing:content-box;position:relative;overflow:visible;display:inline-block;padding:0.3em 0.8em;border:1px solid #d4d4d4;margin:0;text-decoration:none;text-shadow:1px 1px 0 #fff;font-size:11px;color:#333 !important;white-space:nowrap;cursor:pointer;outline:none;background-color:#fff;background-image:-webkit-gradient(linear, left top, left bottom, from(#fff), to(#fff));background-image:linear-gradient(#fff, #fff);border-radius:0.2em;zoom:1;*display:inline;margin-top:10px}.introjs-button:hover{border-color:#bcbcbc;text-decoration:none;-webkit-box-shadow:0px 1px 1px #e3e3e3;box-shadow:0px 1px 1px #e3e3e3}.introjs-button:focus,.introjs-button:active{background-image:-webkit-gradient(linear, left top, left bottom, from(#ececec), to(#f4f4f4));background-image:linear-gradient(#ececec, #f4f4f4)}.introjs-button::-moz-focus-inner{padding:0;border:0}.introjs-skipbutton{-webkit-box-sizing:content-box;box-sizing:content-box;margin-right:5px;color:#4466f2}.introjs-prevbutton{border-radius:0.2em 0 0 0.2em;border-right:none}.introjs-prevbutton.introjs-fullbutton{border:1px solid #d4d4d4;border-radius:0.2em}.introjs-nextbutton{border-radius:0 0.2em 0.2em 0}.introjs-nextbutton.introjs-fullbutton{border-radius:0.2em}.introjs-disabled{color:#4466f2;border-color:#d4d4d4;-webkit-box-shadow:none;box-shadow:none;cursor:default;background-image:none;text-decoration:none;background:#d4d4d4;opacity:0.5}.introjs-disabled:hover,.introjs-disabled:focus{color:#4466f2;border-color:#d4d4d4;-webkit-box-shadow:none;box-shadow:none;cursor:default;background-image:none;text-decoration:none}.introjs-hidden{display:none}.introjs-bullets{text-align:center}.introjs-bullets ul{-webkit-box-sizing:content-box;box-sizing:content-box;clear:both;margin:15px auto 0;padding:0;display:inline-block !important}.introjs-bullets ul li{-webkit-box-sizing:content-box;box-sizing:content-box;list-style:none;float:left;margin:0 2px}.introjs-bullets ul li a{-webkit-box-sizing:content-box;box-sizing:content-box;display:block;width:6px;height:6px;background:#ccc;border-radius:10px;-moz-border-radius:10px;-webkit-border-radius:10px;text-decoration:none;cursor:pointer}.introjs-bullets ul li a:hover{background:#999}.introjs-bullets ul li a.active{background:#fff}.introjs-progress{-webkit-box-sizing:content-box;box-sizing:content-box;overflow:hidden;height:10px;margin:10px 0 5px 0;border-radius:4px;background-color:#ecf0f1}.introjs-progressbar{-webkit-box-sizing:content-box;box-sizing:content-box;float:left;width:0%;height:100%;font-size:10px;line-height:10px;text-align:center;background-color:#08c}.introjsFloatingElement{position:absolute;height:0;width:0;left:50%;top:50%}.introjs-fixedTooltip{position:fixed}.introjs-hint{-webkit-box-sizing:content-box;box-sizing:content-box;position:absolute;background:transparent;width:20px;height:15px;cursor:pointer}.introjs-hint:focus{border:0;outline:0}.introjs-hidehint{display:none}.introjs-fixedhint{position:fixed}.introjs-hint:hover>.introjs-hint-pulse{border:5px solid rgba(60,60,60,0.57)}.introjs-hint-pulse{-webkit-box-sizing:content-box;box-sizing:content-box;width:10px;height:10px;border:5px solid rgba(60,60,60,0.27);border-radius:30px;background-color:rgba(136,136,136,0.24);z-index:10;position:absolute;-webkit-transition:all 0.2s ease-out;transition:all 0.2s ease-out}.introjs-hint-no-anim .introjs-hint-dot{-webkit-animation:none;animation:none}.introjs-hint-dot{-webkit-box-sizing:content-box;box-sizing:content-box;border:10px solid rgba(146,146,146,0.36);background:transparent;border-radius:60px;height:50px;width:50px;-webkit-animation:introjspulse 3s ease-out;animation:introjspulse 3s ease-out;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;position:absolute;top:-25px;left:-25px;z-index:1;opacity:0}@-webkit-keyframes introjspulse{0%{-webkit-transform:scale(0);transform:scale(0);opacity:0.0}25%{-webkit-transform:scale(0);transform:scale(0);opacity:0.1}50%{-webkit-transform:scale(0.1);transform:scale(0.1);opacity:0.3}75%{-webkit-transform:scale(0.5);transform:scale(0.5);opacity:0.5}100%{-webkit-transform:scale(1);transform:scale(1);opacity:0.0}}@keyframes introjspulse{0%{-webkit-transform:scale(0);transform:scale(0);opacity:0.0}25%{-webkit-transform:scale(0);transform:scale(0);opacity:0.1}50%{-webkit-transform:scale(0.1);transform:scale(0.1);opacity:0.3}75%{-webkit-transform:scale(0.5);transform:scale(0.5);opacity:0.5}100%{-webkit-transform:scale(1);transform:scale(1);opacity:0.0}}
