/* Dynamic Table Component Styles */

.dynamic-table-container {
    background: white;
    border-radius: 0.375rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Per page selector styling */
.card-header .form-control-sm {
    min-width: 70px;
    padding: 0.25rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background: white;
    color: #495057;
    font-size: 0.875rem;
}

.card-header .form-control-sm:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    white-space: nowrap;
    min-width: 120px;
    padding: 0.75rem 0.5rem !important;
}

.sortable:hover {
    background-color: #f8f9fa;
}

.sortable i {
    margin-left: 0.5rem;
    opacity: 0.5;
    transition: all 0.2s ease;
}

.sortable.sort-asc i:before {
    content: "\f0de";
    opacity: 1;
}

.sortable.sort-desc i:before {
    content: "\f0dd"; 
    opacity: 1;
}

.table-responsive {
    max-height: 70vh;
    overflow-x: auto;
    overflow-y: auto;
}

.table {
    min-width: 1200px; /* Ensure minimum width for all columns */
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.table-loading {
    position: relative;
}

.table-loading::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.table-loading .spinner-border {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 11;
}

.badge {
    font-size: 0.75rem;
}

.table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.8rem;
    color: #6c757d;
    white-space: nowrap;
    padding: 0.75rem 0.5rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.text-center {
    text-align: center !important;
}

.py-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
}

/* Column specific widths */
.table td:nth-child(1), .table th:nth-child(1) { /* Order No */
    min-width: 120px;
}

.table td:nth-child(2), .table th:nth-child(2) { /* Course */
    min-width: 200px;
    max-width: 250px;
    white-space: normal;
}

.table td:nth-child(3), .table th:nth-child(3) { /* Name */
    min-width: 150px;
}

.table td:nth-child(4), .table th:nth-child(4) { /* Email */
    min-width: 180px;
}

.table td:nth-child(5), .table th:nth-child(5) { /* Phone */
    min-width: 120px;
}

.table td:nth-child(6), .table th:nth-child(6) { /* School */
    min-width: 150px;
    max-width: 200px;
    white-space: normal;
}

.table td:nth-child(7), .table th:nth-child(7) { /* Coupon */
    min-width: 120px;
}

.table td:nth-child(8), .table th:nth-child(8) { /* Price */
    min-width: 100px;
    text-align: right;
}

.table td:nth-child(9), .table th:nth-child(9) { /* Payment Type */
    min-width: 120px;
    text-align: center;
}

.table td:nth-child(10), .table th:nth-child(10) { /* Status */
    min-width: 120px;
    text-align: center;
}

.table td:nth-child(11), .table th:nth-child(11) { /* Date */
    min-width: 100px;
}

.table td:nth-child(12), .table th:nth-child(12) { /* Actions */
    min-width: 80px;
    text-align: center;
    white-space: nowrap;
}

/* Responsive design */
@media (max-width: 1400px) {
    .table {
        min-width: 1000px;
    }
    
    .table td:nth-child(2), .table th:nth-child(2) {
        max-width: 200px;
    }
    
    .table td:nth-child(6), .table th:nth-child(6) {
        max-width: 150px;
    }
}

@media (max-width: 768px) {
    .table-controls .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .table-controls .col-auto {
        width: 100%;
    }
    
    .table-controls .form-control-sm {
        max-width: 100%;
    }
    
    .table {
        min-width: 800px;
    }
    
    .table th {
        font-size: 0.7rem;
        padding: 0.5rem 0.25rem;
    }
    
    .table td {
        font-size: 0.8rem;
        padding: 0.5rem 0.25rem;
    }
}

@media (max-width: 576px) {
    .card-header h5 {
        font-size: 1rem;
    }
    
    .table-controls {
        padding: 0.5rem;
    }
}