var sampleAction = new function() {

  var _this = this;
  var _root, stage, lib, $scope;
  var funcObj = {};
  this._root;
  var responsivePos = {};

  this.setupStart = function(root, _stage, _lib, _$scope) {
    _root = root;
    stage = _stage;
    lib = _lib;
    $scope = _$scope;
    this._root = root;
    _this.resizeContent();
    setup()
  }

  this.resizeContent = function(iw, ih, pRatio, sRatio, _type, xRatio, yRatio) {
    var _type = _type || 'fix';

    var now_width = jQuery(window).width();
    var now_hieght = jQuery(window).height();
    if(pRatio<2 || (now_width > now_hieght)){
      var scale =  pRatio * sRatio;
      var top = 0-(ih/(2*scale));
      var bottom = (ih/(2*scale));
      var left = 0-(iw/(2*scale));
      var right = (iw/(2*scale));
    }else{
      var scale =  pRatio * sRatio;
      var def_x = (now_width)-(960*sRatio);
      var def_y = (now_hieght)-(640*sRatio);
      var all_height = (now_hieght*scale);
      var top = 0-(def_y/2)
      var bottom = 960+(def_y/2)
      var left = 0-(def_x/2)
      var right = 960+(def_x/2)
    }
    responsivePos['scale'] = scale;
    responsivePos['top'] = top;
    responsivePos['bottom'] = bottom;
    responsivePos['left'] = left;
    responsivePos['right'] = right;
    responsivePos['iw'] = iw;
    responsivePos['ih'] = ih;
    responsivePos['pRatio'] = pRatio;
    responsivePos['sRatio'] = sRatio;
    responsivePos['yRatio'] = yRatio;


    if (_type == 'full') {
      _root.mcContent.x = (iw / 2)
      _root.mcContent.y = (ih / 2);
      _root.mcContent.scaleX = _root.mcContent.scaleY = _pRatio * _sRatio
    }

    console.log(responsivePos)
  }

  function setup() {
      setTimeout(function(){ _scope.loadComplete(); }, 1000);
  }
  function setupBt(mc, st) {
    mc.cursor = 'pointer'
    mc.addEventListener('click', onClick)
    mc.addEventListener('mouseover', onover)
    mc.addEventListener('mouseout', onout)

    function onover(e){

    }

    function onout(e){

    }

    function onClick(e) {
      console.log(st)
      if (st == 'alert') {
        // alert('ALERT')
      }
    }
  }

  function setup() {
    setupBt(_root.mcContent.mcBtn,'alert');
    kidkarnmaiCreateJs.addDom(_root.mcContent.mcText,'txtText','true')
  }

  function setupBt(mc, st) {
    mc.cursor = 'pointer'
    mc.addEventListener('click', onClick)

    function onClick(e) {
      if (st == 'alert') {
        alert('ALERT')
      }
    }
  }
  this.removeAllEvent = function() {

  }

}
