var photoEditorCore = Class.extend(function() {

  var _this = this;
  var _root, stage, lib, $scope;
  this.root;
  this.stage;
  var now_type = '';
  var mcPic, mcUpload, mcControl, mcIntroControl, mcClose, mcBtnUpload;
  var filebol = false;
  var iw, ih, pRatio, sRatio

  var save_width = 600;
  var save_height = 900;
  var preview_width = 600;
  var preview_height = 900;
  var original_x, original_y;


  var objActive = {};

  var FileData = 'FileData'

  this.setupStart = function(root, _stage, _lib, _$scope) {
    _root = root;
    stage = _stage;
    lib = _lib;
    $scope = _$scope;
    this.root = root;
    this.stage = stage;
    setup();
  }

  this.resizeContent = function(_iw, _ih, _pRatio, _sRatio) {
    iw = _iw;
    ih = _ih;
    pRatio = _pRatio
    sRatio = _sRatio
  }

  function setup() {
    mcPic = _root.mcContent.mcCon.mcUpload.mcPic;
    mcControl = _root.mcContent.mcCon.mcUpload.mcControl;
    mcIntroControl = _root.mcContent.mcCon.mcIntroControl;
    mcClose = _root.mcContent.mcCon.mcClose;
    mcBtnUpload = _root.mcContent.mcCon.mcCam;
    mcUpload = _root.mcContent.mcCon.mcUpload;
    mcPic.visible = false;
    mcClose.visible = false;
    mcBtnUpload.visible = true;
    original_x = mcPic.mcSub.x;
    original_y = mcPic.mcSub.y;
    $("#FileData").on("change", previewFile);
    setupBt(mcClose, 'close');
    setupBt(mcBtnUpload, 'browse');

    kidkarnmaiCreateJs.addDom(_root.mcContent.mcCon.mcUpload.mcText,'txtText','true')

  }

  function setupBt(mc, st) {
    mc.cursor = 'pointer'
    mc.addEventListener('click', onClick)

    function onClick(e) {
      trace('onClick : ' + st)
      if (st == 'browse') {
        _this.browseImage();
      } else if (st == 'save') {
        _this.saveCanvas();
      } else if (st == 'close') {
        mcPic.visible = false;
        mcClose.visible = false;
        mcBtnUpload.visible = true;
        try{
          mcControlTool.unselect();
        }catch(e){}
        while (mcControl.getNumChildren()) {
          mcControl.removeChildAt(0);
        }
      }
    }
  }

  this.loadFbProfile = function(id) {
    var _id = localStorage.getItem("fbid") || id;
    kidkarnmaiLoader.getJson('http://graph.facebook.com/' + _id + '/picture?type=large&redirect=false', function(obj) {
      crossOriginFacebookPhoto(obj['data']['url'])
    });

  }

  function crossOriginFacebookPhoto(source) {
    trace("source : " + source);

    var img = new Image,
      src = source.replace("https", "http"),
      cvs = document.getElementById('canvasX'),
      ctx = cvs.getContext('2d');

    img.crossOrigin = "Anonymous";

    img.onload = function() {
      ctx.drawImage(img, 0, 0);
      var dataURL = cvs.toDataURL();
      setPicInside(dataURL, mcPic, "Facebook", 0);
    }

    img.src = src;
    picOriginal = src;
  }

  this.browseImage = function() {
    $("#" + FileData).trigger("click");
  }

  this.fromCam = function(data) {
    // window.open(data,'_blank');
    setPicInside(data, mcPic, "Upload", 0);
  }

  function previewFile(event) {
    event.preventDefault();
    var file = event.target.files[0]
    if (!file.type.match("image.*")) {
      return;
    } else {
      var reader = new FileReader();
      reader.onerror = errorHandler;
      reader.onprogress = updateProgress;

      reader.addEventListener("load", function(event) {
        getOrientation(file, function(orientation) {
          var _rotation = 0;
          if (orientation == 6) {
            _rotation = 90
          } else if (orientation == 8) {
            _rotation = -90
          } else if (orientation == 3) {
            _rotation = 180
          }
          trace('previewFile')
          setPicInside(event.target.result, mcPic, "Upload", _rotation);
        });

      }, false);
      reader.readAsDataURL(file);
    }

    function errorHandler(evt) {
      switch (evt.target.error.code) {
        case evt.target.error.NOT_FOUND_ERR:
          //$("body").append('</br>File Not Found!');
          break;
        case evt.target.error.NOT_READABLE_ERR:
          //$("body").append('</br>File is not readable');
          break;
        case evt.target.error.ABORT_ERR:
          break; // noop
        default:
          //$("body").append('</br>An error occurred reading this file.');
      };
    }

    function updateProgress(evt) {
      // evt is an ProgressEvent.
      if (evt.lengthComputable) {
        var percentLoaded = Math.round((evt.loaded / evt.total) * 100);
        if (percentLoaded < 100) {
          //$("body").append('</br>'+percentLoaded + '%');
        }
      }
    }
  }

  function setPicInside(bm, mc, _type, _rotation) {
    var bitmap = new createjs.Bitmap(bm);
    // mc.scaleX = mc.scaleY = 1;
    mc.mcSub.scaleX = mc.mcSub.scaleY = 1;
    while (mc.mcSub.getNumChildren()) {
      mc.mcSub.removeChildAt(0);
    }
    mc.mcSub.addChild(bitmap);

    var image = new Image();
    image.src = bm;

    image.onload = function() {
      filebol = true;
      console.log('onload : ' + this.width + ' : ' + this.height + ' : ' + _rotation)
      var toSX
      if (_type == 'Facebook') {

      } else {
        resizeBitmap(mc, preview_width, preview_height, this.width, this.height, _rotation)
      }
      mc.rotation = _rotation;
      mcPic.visible = true;
      mcClose.visible = true;
      mcBtnUpload.visible = false;
    };
  }

  function resizeBitmap(mc, _w, _h, _ow, _oh, _ro) {
    vvar myW = _w;
    var myH = _h;
    var toSX, toSY, toX, toY;
    var nw = _ow || mc.mcSub.getBounds()["width"]
    var nh = _oh || mc.mcSub.getBounds()["height"]

    if(_ro==90||_ro==-90){
      if (nh > nw) {
          toSY = myW / nh;
          toSX = toSY;
      } else {
        toSX = myH / nw
        toSY = toSX
      }
      toX = Number(nw * toSY) / 2 - (myW * toSY / 2);
      toY = Number(nh * toSX) / 2 - (myH * toSX / 2);

    }else{
      if (nh > nw) {
        toSX = myW / nw;
        toSY = toSX;
      } else {
        toSY = myH / nh
        toSX = toSY
      }
      toX = Number(nw * toSX) / 2 - (myW * toSX / 2);
      toY = Number(nh * toSY) / 2 - (myH * toSY / 2);

    }
    // console.log('------->resizeBitmap<--------')
    // console.log('myW : ' + myW+' | nw : ' + nw)
    // console.log('myH : ' + myH+' | nh : ' + nh)
    // console.log('_ro : ' + _ro)
    // console.log('toX : ' + toX+' | toY : ' + toY+' | original_x : '+original_x+' | original_y : '+original_y)
    // console.log('mc.x : ' + mc.mcSub.x+' | mc.y : ' + mc.mcSub.y)
    // console.log('toSX : ' + toSX+' | toSX : ' + toSY)

    mc.mcSub.scaleX = toSX
    mc.mcSub.scaleY = toSY
    mc.mcSub.x = original_x - toX
    mc.mcSub.y = original_y - toY

    objActive['scaleX'] = toSX
    objActive['scaleY'] = toSY
    objActive['width'] = nw
    objActive['height'] = nh
    objActive['original_x'] = original_x
    objActive['original_y'] = original_y
    objActive['set_x'] = toX
    objActive['set_y'] = toY
    objActive['n_width'] = mc.mcSub.getBounds()["width"] * toSX
    objActive['n_height'] = mc.mcSub.getBounds()["height"] * toSY
    // console.log('------->set mc<--------')
    // trace(mc)
    // trace(mc.getBounds())
    // console.log('mc.x : ' + mc.x+' | mc.y : ' + mc.y)
    // trace(objActive)
    // _this.resetTransform(_ow, _oh)

    var mcTool = new createjs.util.FreeTranformTool("#000", false, "rgba(255,255,255,0.8)", 1, {
      "move": true,
      "rotate": true,
      "scale": true,
      "hScale": false,
      "vScale": false,
      "icon_scale": 0.3,
      "icon_scale": 1,
      "original_scale":toSX
    });
    //
    while (mcControl.getNumChildren()) {
      mcControl.removeChildAt(0);
    }
    mcControl.addChild(mcTool);
    mcTool.select(mc)
    mcControlTool = mcTool;
    if($(window).width()<=1024){
      mcIntroControl.gotoAndPlay('in')
    }
  }

  function getOrientation(file, callback) {
    var reader = new FileReader();
    reader.onload = function(e) {

      var view = new DataView(e.target.result);
      if (view.getUint16(0, false) != 0xFFD8) return callback(-2);
      var length = view.byteLength,
        offset = 2;
      while (offset < length) {
        var marker = view.getUint16(offset, false);
        offset += 2;
        if (marker == 0xFFE1) {
          if (view.getUint32(offset += 2, false) != 0x45786966) return callback(-1);
          var little = view.getUint16(offset += 6, false) == 0x4949;
          offset += view.getUint32(offset + 4, little);
          var tags = view.getUint16(offset, little);
          offset += 2;
          for (var i = 0; i < tags; i++)
            if (view.getUint16(offset + (i * 12), little) == 0x0112)
              return callback(view.getUint16(offset + (i * 12) + 8, little));
        } else if ((marker & 0xFF00) != 0xFF00) break;
        else offset += view.getUint16(offset, false);
      }
      return callback(-1);
    };
    reader.readAsArrayBuffer(file.slice(0, 64 * 1024));
  }

  this.saveCanvas = function(cb) {
    cb = cb || null;
    if (filebol) {
      // kkmFlow.showWait();
      mcControlTool.unselect();
      mcUpload.cache(0, 0, save_width, save_height);
      var dataURL = mcUpload.cacheCanvas.toDataURL("image/jpeg", 0.8);
      mcUpload.uncache();
      console.log(dataURL);

      // kidkarnmaiLoader.loadJson('/save_pic.php', function(obj) {
      //   photo_path = obj['url'];
      //   if (cb != null) {
      //     cb(obj);
      //   }
      // }, {
      //   'png': dataURL
      // });

    }
  }

  this.checkFile = function() {
    return filebol;
  }

  this.setValue = function(st, val) {
    if (st == "rotation") {
      mcPic.mcSub.rotation = val;
    } else if (st == "scale") {
      mcPic.mcSub.scaleX = mcPic.mcSub.scaleY = val;
    }
  }


});


var photoeditorAction = new photoEditorCore()
