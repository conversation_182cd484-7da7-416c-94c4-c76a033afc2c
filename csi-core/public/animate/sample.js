(function (cjs, an) {

var p; // shortcut to reference prototypes
var lib={};var ss={};var img={};
lib.ssMetadata = [];


// symbols:



(lib.blank = function() {
	this.initialize(img.blank);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,1,1);// helper functions:

function mc_symbol_clone() {
	var clone = this._cloneProps(new this.constructor(this.mode, this.startPosition, this.loop));
	clone.gotoAndStop(this.currentFrame);
	clone.paused = this.paused;
	clone.framerate = this.framerate;
	return clone;
}

function getMCSymbolPrototype(symbol, nominalBounds, frameBounds) {
	var prototype = cjs.extend(symbol, cjs.MovieClip);
	prototype.clone = mc_symbol_clone;
	prototype.nominalBounds = nominalBounds;
	prototype.frameBounds = frameBounds;
	return prototype;
	}


(lib.mcText = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#000000").ss(1,1,1).p("AlGlGIKNAAIAAKNIqNAAg");

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#FFFFFF").s().p("AlGFHIAAqNIKNAAIAAKNg");

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcText, new cjs.Rectangle(-33.7,-33.7,67.4,67.4), null);


(lib.mcSample = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#000000").s().p("Al9F+QieieAAjgQAAjfCeieQCeieDfAAQDgAACeCeQCeCeAADfQAADgieCeQieCejgAAQjfAAieieg");

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcSample, new cjs.Rectangle(-54,-54,108,108), null);


(lib.mcBtn = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_2
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AjSBeQgHgFgDgMIgGgUIhMAAIgHAVQgDALgGAEQgGAFgJgCQgJgCgCgIQgCgJADgKIAyiIQAFgOAHgGQAIgHALAAQAMAAAGAGQAGAGAEAMIAxCKIADAKIgBAIIgDAHQgDADgEACIgHABQgGAAgEgDgAjtAXIgahWIgdBWIA3AAgAEKBaQgFgHAAgNIAAiAIgUAAQgNAAgHgEQgHgEAAgKQAAgJAHgEQAHgEANAAIBOAAQANAAAHAEQAHAEAAAJQAAAKgHAEQgHAEgNAAIgVAAIAACAQAAANgEAHQgEAGgKAAQgKAAgEgGgABXBaQgFgHAAgNIAAiHIABgMQABgGADgDQADgEAGgBIANgCIAiAAQARAAANAEQANAEAJAIQAJAHAEALQAFALAAAMQAAAagMANQgLANgZAFIAtAtIAFAJIAAAIQgBAEgDAEQgEAEgEABIgJAAIgKgFIgJgIIg3g9IAAAwQAAANgFAHQgEAGgKAAQgKAAgEgGgAB4gFIAYAAQAPAAAHgHQAHgIAAgMQAAgNgHgHQgHgHgPAAIgYAAgAgUBeIgNgBQgFgCgDgCQgDgDgBgFIgBgMIAAiGIABgMQABgGADgDQADgEAFgBIANgCIAyAAQANAAAHAEQAHAEAAAJQAAAKgHAEQgHAEgNAAIgnAAIAAApIAiAAQAOAAAHAEQAGAEAAAJQAAAIgGAEQgHAEgOAAIgiAAIAAArIAoAAQANAAAHAEQAHAEAAAKQAAAJgHAEQgHAEgNAAgAiKBeIgNgBQgFgCgDgCQgDgDgBgFIgBgMIAAiKQAAgMAEgHQAFgHAJAAQAKAAAFAHQAEAHAAAMIAACAIApAAQAOAAAHAEQAGAFAAAJQAAAJgHAEQgGAEgOAAg");
	this.shape.setTransform(-0.7364,1.5313);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

	// Layer_1
	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#000000").s().p("AtbEnIAApNIa3AAIAAJNg");

	this.timeline.addTween(cjs.Tween.get(this.shape_1).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcBtn, new cjs.Rectangle(-86,-29.5,172,59), null);


(lib.mcContent = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_5
	this.mcText = new lib.mcText();
	this.mcText.name = "mcText";
	this.mcText.parent = this;
	this.mcText.setTransform(-366.9,-250.5,1,1,0,0,0,-32.7,-32.7);

	this.timeline.addTween(cjs.Tween.get(this.mcText).to({x:-58.3,y:-133.55},25,cjs.Ease.quadOut).to({x:-366.9,y:-250.5},28,cjs.Ease.quadOut).wait(67));

	// Layer_2
	this.mcBtn = new lib.mcBtn();
	this.mcBtn.name = "mcBtn";
	this.mcBtn.parent = this;
	this.mcBtn.setTransform(4.05,6.05);

	this.timeline.addTween(cjs.Tween.get(this.mcBtn).wait(120));

	// Layer 1
	this.instance = new lib.mcSample();
	this.instance.parent = this;
	this.instance.setTransform(-1693.3,-373.55);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({x:-27.95,y:259.45},19,cjs.Ease.quadIn).to({regY:54,y:313.45},1).to({scaleX:1.3148,scaleY:0.6574,x:6.05,y:320.45},3).to({scaleX:1,scaleY:1,x:22.05,y:312.45},3).to({regY:0,y:258.45},1,cjs.Ease.quadOut).to({x:1617.75,y:-418.55},26,cjs.Ease.quadOut).wait(67));

	// Layer_3
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#CCCCCC").s().p("EhK/AyAMAAAhj/MCV/AAAMAAABj/g");
	this.shape.setTransform(0.45,0.45);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(120));

	// Layer_4
	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#999999").s().p("EjStCMeMAAAkY7MGlbAAAMAAAEY7g");
	this.shape_1.setTransform(0.45,0.5);

	this.timeline.addTween(cjs.Tween.get(this.shape_1).wait(120));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1747.3,-898.5,3419.1,1798);


// stage content:
(lib.sample = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.mcContent = new lib.mcContent();
	this.mcContent.name = "mcContent";
	this.mcContent.parent = this;
	this.mcContent.setTransform(479.55,319.5);

	this.instance = new lib.blank();
	this.instance.parent = this;

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance},{t:this.mcContent}]}).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-787.7,-259,2616.3,1478);
// library properties:
lib.properties = {
	id: '7F19FD8FEC2B44C6AAEC920B63D5D775',
	width: 960,
	height: 640,
	fps: 30,
	color: "#FFFFFF",
	opacity: 1.00,
	manifest: [
		{src:"images/sample/blank.jpg?1562833867017", id:"blank"}
	],
	preloads: []
};



// bootstrap callback support:

(lib.Stage = function(canvas) {
	createjs.Stage.call(this, canvas);
}).prototype = p = new createjs.Stage();

p.setAutoPlay = function(autoPlay) {
	this.tickEnabled = autoPlay;
}
p.play = function() { this.tickEnabled = true; this.getChildAt(0).gotoAndPlay(this.getTimelinePosition()) }
p.stop = function(ms) { if(ms) this.seek(ms); this.tickEnabled = false; }
p.seek = function(ms) { this.tickEnabled = true; this.getChildAt(0).gotoAndStop(lib.properties.fps * ms / 1000); }
p.getDuration = function() { return this.getChildAt(0).totalFrames / lib.properties.fps * 1000; }

p.getTimelinePosition = function() { return this.getChildAt(0).currentFrame / lib.properties.fps * 1000; }

an.bootcompsLoaded = an.bootcompsLoaded || [];
if(!an.bootstrapListeners) {
	an.bootstrapListeners=[];
}

an.bootstrapCallback=function(fnCallback) {
	an.bootstrapListeners.push(fnCallback);
	if(an.bootcompsLoaded.length > 0) {
		for(var i=0; i<an.bootcompsLoaded.length; ++i) {
			fnCallback(an.bootcompsLoaded[i]);
		}
	}
};

an.compositions = an.compositions || {};
an.compositions['7F19FD8FEC2B44C6AAEC920B63D5D775'] = {
	getStage: function() { return exportRoot.getStage(); },
	getLibrary: function() { return lib; },
	getSpriteSheet: function() { return ss; },
	getImages: function() { return img; }
};

an.compositionLoaded = function(id) {
	an.bootcompsLoaded.push(id);
	for(var j=0; j<an.bootstrapListeners.length; j++) {
		an.bootstrapListeners[j](id);
	}
}

an.getComposition = function(id) {
	return an.compositions[id];
}


an.makeResponsive = function(isResp, respDim, isScale, scaleType, domContainers) {		
	var lastW, lastH, lastS=1;		
	window.addEventListener('resize', resizeCanvas);		
	resizeCanvas();		
	function resizeCanvas() {			
		var w = lib.properties.width, h = lib.properties.height;			
		var iw = window.innerWidth, ih=window.innerHeight;			
		var pRatio = window.devicePixelRatio || 1, xRatio=iw/w, yRatio=ih/h, sRatio=1;			
		if(isResp) {                
			if((respDim=='width'&&lastW==iw) || (respDim=='height'&&lastH==ih)) {                    
				sRatio = lastS;                
			}				
			else if(!isScale) {					
				if(iw<w || ih<h)						
					sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==1) {					
				sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==2) {					
				sRatio = Math.max(xRatio, yRatio);				
			}			
		}			
		domContainers[0].width = w * pRatio * sRatio;			
		domContainers[0].height = h * pRatio * sRatio;			
		domContainers.forEach(function(container) {				
			container.style.width = w * sRatio + 'px';				
			container.style.height = h * sRatio + 'px';			
		});			
		stage.scaleX = pRatio*sRatio;			
		stage.scaleY = pRatio*sRatio;			
		lastW = iw; lastH = ih; lastS = sRatio;            
		stage.tickOnUpdate = false;            
		stage.update();            
		stage.tickOnUpdate = true;		
	}
}


})(createjs = createjs||{}, AdobeAn = AdobeAn||{});
var createjs, AdobeAn;