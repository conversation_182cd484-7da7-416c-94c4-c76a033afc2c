(function (cjs, an) {

var p; // shortcut to reference prototypes
var lib={};var ss={};var img={};
lib.ssMetadata = [];


// symbols:



(lib.camera_icon = function() {
	this.initialize(img.camera_icon);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,377,451);


(lib.cancel = function() {
	this.initialize(img.cancel);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,128,128);


(lib.move = function() {
	this.initialize(img.move);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,512,512);


(lib.pinch = function() {
	this.initialize(img.pinch);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,512,512);// helper functions:

function mc_symbol_clone() {
	var clone = this._cloneProps(new this.constructor(this.mode, this.startPosition, this.loop));
	clone.gotoAndStop(this.currentFrame);
	clone.paused = this.paused;
	clone.framerate = this.framerate;
	return clone;
}

function getMCSymbolPrototype(symbol, nominalBounds, frameBounds) {
	var prototype = cjs.extend(symbol, cjs.MovieClip);
	prototype.clone = mc_symbol_clone;
	prototype.nominalBounds = nominalBounds;
	prototype.frameBounds = frameBounds;
	return prototype;
	}


(lib.RoundedRectangle4 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 2
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FFFFFF").s().p("AnZCvQgeAAgVgWQgWgVAAgeIAAjLQAAgeAWgWQAVgVAeAAIOzAAQAeAAAVAVQAWAWAAAeIAADLQAAAegWAVQgVAWgeAAg");
	this.shape.setTransform(54.95,17.35);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.RoundedRectangle4, new cjs.Rectangle(0.3,-0.1,109.4,35), null);


(lib.Rectangle4 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 2
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#DEDEDE").s().p("A3bXcMAAAgu3MAu3AAAMAAAAu3g");
	this.shape.setTransform(0,150.4,0.6667,1.003,0,0,0,-150,0);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.Rectangle4, new cjs.Rectangle(0,0,200,300.9), null);


(lib.mcText = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#000000").s().p("AgUCBIAAjfIhTAAIAAgiIDPAAIAAAiIhSAAIAADfg");
	this.shape.setTransform(85.1,33.125);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#000000").s().p("ABDCBIhDhfIhCBfIgzAAIBciBIhaiAIAyAAIBBBgIBChgIAyAAIhaCAIBcCBg");
	this.shape_1.setTransform(60.2,33.125);

	this.shape_2 = new cjs.Shape();
	this.shape_2.graphics.f("#000000").s().p("AhZCBIAAkBICzAAIAAAiIiHAAIAABOIB4AAIAAAhIh4AAIAABOICHAAIAAAig");
	this.shape_2.setTransform(36.2,33.125);

	this.shape_3 = new cjs.Shape();
	this.shape_3.graphics.f("#000000").s().p("AgVCBIAAjfIhSAAIAAgiIDPAAIAAAiIhSAAIAADfg");
	this.shape_3.setTransform(13.35,33.125);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_3},{t:this.shape_2},{t:this.shape_1},{t:this.shape}]}).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcText, new cjs.Rectangle(0,0,98.7,63.8), null);


(lib.mcSub = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#DEDEDE").s().p("Egu3BGUMAAAiMnMBdvAAAMAAACMng");
	this.shape.setTransform(300,450);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcSub, new cjs.Rectangle(0,0,600,900), null);


(lib.mcIntroControl2 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.pinch();
	this.instance.parent = this;
	this.instance.setTransform(-256,-256);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcIntroControl2, new cjs.Rectangle(-256,-256,512,512), null);


(lib.mcIntroControl_1 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer_1
	this.instance = new lib.move();
	this.instance.parent = this;
	this.instance.setTransform(-256,-256);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcIntroControl_1, new cjs.Rectangle(-256,-256,512,512), null);


(lib.mcFrame = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("rgba(0,255,0,0.298)").s().p("Egu3BGUMAAAiMnMBdvAAAMAAACMngEgi+A3qMBHQAAAMAAAhqkMhHQAAAg");
	this.shape.setTransform(300,450);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcFrame, new cjs.Rectangle(0,0,600,900), null);


(lib.mcControl = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("rgba(80,80,80,0)").s().p("Egu3BGUMAAAiMnMBdvAAAMAAACMng");
	this.shape.setTransform(300,450);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcControl, new cjs.Rectangle(0,0,600,900), null);


(lib.Layer2 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.instance = new lib.camera_icon();
	this.instance.parent = this;
	this.instance.setTransform(1,1,0.2947,0.2947);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = getMCSymbolPrototype(lib.Layer2, new cjs.Rectangle(1,1,111.1,132.9), null);


(lib.cancel_1 = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.instance = new lib.cancel();
	this.instance.parent = this;
	this.instance.setTransform(0,0,0.3906,0.3906);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

}).prototype = getMCSymbolPrototype(lib.cancel_1, new cjs.Rectangle(0,0,50,50), null);


(lib.mcPic = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.mcSub = new lib.mcSub();
	this.mcSub.name = "mcSub";
	this.mcSub.parent = this;
	this.mcSub.setTransform(300,450,1,1,0,0,0,300,450);

	this.timeline.addTween(cjs.Tween.get(this.mcSub).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcPic, new cjs.Rectangle(0,0,600,900), null);


(lib.mcIntroControl = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{"in":1});

	// timeline functions:
	this.frame_0 = function() {
		this.stop();
	}
	this.frame_60 = function() {
		this.stop();
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).call(this.frame_0).wait(60).call(this.frame_60).wait(1));

	// Layer_1
	this.instance = new lib.mcIntroControl_1();
	this.instance.parent = this;
	this.instance.setTransform(0,0,0.0656,0.0656);
	this.instance.alpha = 0;
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({_off:false},0).to({scaleX:0.5,scaleY:0.5,alpha:1},5,cjs.Ease.bounceOut).wait(19).to({scaleX:0.0656,scaleY:0.0656,alpha:0},5,cjs.Ease.quadIn).to({_off:true},1).wait(30));

	// pinch.png
	this.instance_1 = new lib.mcIntroControl2();
	this.instance_1.parent = this;
	this.instance_1.setTransform(0,0,0.0305,0.0305);
	this.instance_1.alpha = 0;
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(31).to({_off:false},0).to({scaleX:0.5,scaleY:0.5,alpha:1},5,cjs.Ease.bounceOut).wait(19).to({scaleX:0.0305,scaleY:0.0305,alpha:0},5).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-128,-128,256,256);


(lib.mcCam = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// อัพโหลดภาพ
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#65614D").s().p("AAGAjQgEgCgCgDIgFAFQgEADgEAAQgLAAgGgGQgFgGAAgMIAAgsQAAgDACgCIAFgCQADAAACACQACACAAADIAAAtQABAFABADQACACAEAAQADAAACgCQACgCAAgFIAAguQAAgDACgCQACgCACAAIAFACQACACAAADIAAAuQAAAFACACQACACADAAQADAAADgCQACgDAAgFIAAgtQgBgDADgCQABgBAAAAQABAAAAAAQABgBAAAAQABAAABAAQADAAABACQADACAAADIAAAsQAAAMgGAGQgFAGgLAAQgEAAgEgDg");
	this.shape.setTransform(131.75,231.375);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#65614D").s().p("AAMAlIgDgBIgCgCIAAgEIAAgpIgBgEIgCgEIgEgCIgDgBIgEAAIgCABQgEACgDgBIgEgEQAAgCAAgDQABgDADgCIAFgCIAIgBQAFAAAFACQAEACADADQAEAEABAEQACAFAAAFIAAAqQAAAEgCACQgCACgDAAg");
	this.shape_1.setTransform(124.9,231.375);

	this.shape_2 = new cjs.Shape();
	this.shape_2.graphics.f("#65614D").s().p("AAVAjQgCgBAAgDIAAgnQAAgDgBgDIgDgGIgFgCQgDgCgDAAQgEAAgEACQgDADgBADIAAACIACABIACACIABACIgBADIgCACIgCACIgBADIAAAdQAAACgCACQgBACgEABIgJAAQgEgBgBgCQgCgCAAgCQAAgDACgDQABgCAEABIACAAIAAgXIABgCIACgCIACgCIAAgBIgBgCIgCgBIgCgCIgBgCQABgGADgDIAHgIQADgCAFgCQAFgCAEAAQAGAAAGADQAFABAEAEQAEADACAGQACAGAAAHIAAAmQAAADgCABQgCADgDAAQgCAAgDgDg");
	this.shape_2.setTransform(118.825,231.3);

	this.shape_3 = new cjs.Shape();
	this.shape_3.graphics.f("#65614D").s().p("AARAjQgDgBAAgDIAAgpQAAgHgDgDQgEgEgHAAQgGAAgEAEQgDADAAAHIAAAhIAEAAQAEgBABACQACADAAADIAAACIgCACIgCACIgDABIgJAAQgFAAgDgDQgCgCAAgEIAAglQAAgGACgGQADgEADgEQAEgEAFgBQAGgDAFAAQAGAAAFADQAGABAEAEQADAEADAEQACAGAAAGIAAAoQAAADgCABQgDADgCAAQgDAAgCgDg");
	this.shape_3.setTransform(112.175,231.3);

	this.shape_4 = new cjs.Shape();
	this.shape_4.graphics.f("#65614D").s().p("AARAkQgCgCAAgEIAAgbIgEABIgFABIgEABIgFAAIgDABIgEADIgCACIgBAEIABAEIABACIACABIADAAQADgBACABIADAEIAAADIAAADIgCACIgDACQgDABgFgBQgFAAgEgDQgDgCgDgFQgCgEgBgHQAAgFACgEQACgFAEgCIAHgEIAIgCIAGgBIAGgBIAGgCQACgCABgDIgBgDIgDgDIgEgBIgFgBIgEAAIgDACIgEACIgBADIgCADIgCACIgDAAIgEAAIgDgEQgBgCABgDQAAgDADgEIAGgFQADgCAFgBQAEgCAFAAQAGAAAFACQAFACAEADQAEADABAEQADAFAAAFIAAArQAAAEgDACQgCACgCAAQgEAAgCgCg");
	this.shape_4.setTransform(105.2,231.325);

	this.shape_5 = new cjs.Shape();
	this.shape_5.graphics.f("#65614D").s().p("AARAkQgDgBAAgEIAAgYQAAgHgDgEQgDgEgHAAIgEABIgFADQgDABgBADQgBACAAAEIAAAZQAAAEgDABQgCACgDAAIgFgCQgCgBAAgEIAAg9QAAgDACgBQADgDACAAIADABIADACIACACIAAACIAAAQQAEgEAFgBIAIgCQgDgCAAgEQAAgEADgCQADgEAHAAQAHAAADAEQAEACAAAEQAAADgDADQgDADgGAAQAGADADAGQAEAGAAAJIAAAZQAAAEgCABIgFACQgDAAgCgCg");
	this.shape_5.setTransform(98.625,231.3);

	this.shape_6 = new cjs.Shape();
	this.shape_6.graphics.f("#65614D").s().p("AAAA8QgFAAgCgDQgCgCAAgEIAAhOIgBgEIgCgEIgDgCIgDgDIgCgDIgBgEIABgFIAEgDIAEgDIAFgBIAVAAIAEABIACACIABACIABACQAAAEgCACQgCACgEAAIgSAAIABABIADACIADAFQABADAAAEIAABLIAFAAQADgBACACQACADAAADIgBACIgBACIgCACIgDABg");
	this.shape_6.setTransform(93.475,229);

	this.shape_7 = new cjs.Shape();
	this.shape_7.graphics.f("#65614D").s().p("AgLAyQgFgDgEgDQgDgEgDgEQgCgGAAgGIAAgoQAAgEACgBIAFgCQADAAACACQADABAAAEIAAApQAAAHADADQAEAEAGAAQAHAAAEgEQADgDAAgHIAAhFQAAgDADgBQACgDADAAQACAAADADQACABAAADIAABEQAAAGgCAGQgDAEgDAEQgEADgGADQgFACgGAAQgFAAgGgCg");
	this.shape_7.setTransform(88.125,230);

	this.shape_8 = new cjs.Shape();
	this.shape_8.graphics.f("#65614D").s().p("AgPAKIgEgBIgDgCIgDgDIgBgEIABgDIADgDIADgCIAEgBQAEAAADACQADACABACIAXAAIAEABIACACIABABIAAACQAAADgBACQgCACgEAAg");
	this.shape_8.setTransform(83.4,225.925);

	this.shape_9 = new cjs.Shape();
	this.shape_9.graphics.f("#65614D").s().p("AgMAjQgGgCgEgEIgHgKQgDgFgBgGIAAgHIACgDIAEgDIAFgBIAPAAQADAAACADQACACAAABIgCAGQgCACgDAAIgKAAIACAGIAEAFIAFAEQADABADABQAFAAADgCIAGgGIAEgIIABgJIgBgIIgEgIIgGgFQgDgDgFAAIgFABIgFADIgDADIgDAEIgCADIgCABIgDAAIgDAAQgBgBgBAAQAAAAAAgBQgBAAAAgBQgBAAAAgBQgBgCACgDQABgEADgEIAHgGIAJgEQAFgBAEAAQAIAAAGADQAGADAFAFQAEAFADAHQACAHAAAHQAAAIgCAHQgCAHgFAFQgEAFgHADQgGADgIAAQgGAAgGgDg");
	this.shape_9.setTransform(80.9375,231.35);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_9},{t:this.shape_8},{t:this.shape_7},{t:this.shape_6},{t:this.shape_5},{t:this.shape_4},{t:this.shape_3},{t:this.shape_2},{t:this.shape_1},{t:this.shape}]}).wait(1));

	// Rounded Rectangle 4
	this.instance = new lib.RoundedRectangle4();
	this.instance.parent = this;
	this.instance.setTransform(43.6,212.6,1.0789,1.0789,0,0,0,0.1,0);
	this.instance.alpha = 0.7188;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	// Layer 2
	this.instance_1 = new lib.Layer2();
	this.instance_1.parent = this;
	this.instance_1.setTransform(100.05,129.9,1.0366,1.0366,0,0,0,56.6,68);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1));

	// Rectangle 4
	this.instance_2 = new lib.Rectangle4();
	this.instance_2.parent = this;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcCam, new cjs.Rectangle(0,0,200,300.9), null);


(lib.mcUpload = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// mcText
	this.mcText = new lib.mcText();
	this.mcText.name = "mcText";
	this.mcText.parent = this;
	this.mcText.setTransform(78.35,125.65);

	this.timeline.addTween(cjs.Tween.get(this.mcText).wait(1));

	// mcControl
	this.mcControl = new lib.mcControl();
	this.mcControl.name = "mcControl";
	this.mcControl.parent = this;
	this.mcControl.setTransform(300,450,1,1,0,0,0,300,450);

	this.timeline.addTween(cjs.Tween.get(this.mcControl).wait(1));

	// mcFrame
	this.mcFrame = new lib.mcFrame();
	this.mcFrame.name = "mcFrame";
	this.mcFrame.parent = this;

	this.timeline.addTween(cjs.Tween.get(this.mcFrame).wait(1));

	// mcPic
	this.mcPic = new lib.mcPic();
	this.mcPic.name = "mcPic";
	this.mcPic.parent = this;
	this.mcPic.setTransform(300,450,1,1,0,0,0,300,450);

	this.timeline.addTween(cjs.Tween.get(this.mcPic).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcUpload, new cjs.Rectangle(0,0,600,900), null);


(lib.mcCon = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// mcClose
	this.mcClose = new lib.cancel_1();
	this.mcClose.name = "mcClose";
	this.mcClose.parent = this;
	this.mcClose.setTransform(506.35,9,1.641,1.641);
	this.mcClose.shadow = new cjs.Shadow("#FFFFFF",2,1,4);

	this.timeline.addTween(cjs.Tween.get(this.mcClose).wait(1));

	// mcCam
	this.mcCam = new lib.mcCam();
	this.mcCam.name = "mcCam";
	this.mcCam.parent = this;
	this.mcCam.setTransform(0,0,3,2.991);

	this.timeline.addTween(cjs.Tween.get(this.mcCam).wait(1));

	// mcIntroControl
	this.mcIntroControl = new lib.mcIntroControl();
	this.mcIntroControl.name = "mcIntroControl";
	this.mcIntroControl.parent = this;
	this.mcIntroControl.setTransform(301,450);

	this.timeline.addTween(cjs.Tween.get(this.mcIntroControl).wait(1));

	// mcUpload
	this.mcUpload = new lib.mcUpload();
	this.mcUpload.name = "mcUpload";
	this.mcUpload.parent = this;
	this.mcUpload.setTransform(300,450,1,1,0,0,0,300,450);

	this.timeline.addTween(cjs.Tween.get(this.mcUpload).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcCon, new cjs.Rectangle(0,0,600,900), null);


(lib.mcContent = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// mcCon
	this.mcCon = new lib.mcCon();
	this.mcCon.name = "mcCon";
	this.mcCon.parent = this;
	this.mcCon.setTransform(300,450,1,1,0,0,0,300,450);

	this.timeline.addTween(cjs.Tween.get(this.mcCon).wait(1));

}).prototype = getMCSymbolPrototype(lib.mcContent, new cjs.Rectangle(0,0,600,900), null);


// stage content:
(lib.photoeditor = function(mode,startPosition,loop) {
	this.initialize(mode,startPosition,loop,{});

	// Layer 1
	this.mcContent = new lib.mcContent();
	this.mcContent.name = "mcContent";
	this.mcContent.parent = this;
	this.mcContent.setTransform(300,450,1,1,0,0,0,300,450);

	this.timeline.addTween(cjs.Tween.get(this.mcContent).wait(1));

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(300,450,300,450);
// library properties:
lib.properties = {
	id: 'BC66CE655B224F2FA9D990DCFB572F80',
	width: 600,
	height: 900,
	fps: 30,
	color: "#FFFFFF",
	opacity: 1.00,
	manifest: [
		{src:"images/photoeditor/camera_icon.png?1561738381805", id:"camera_icon"},
		{src:"images/photoeditor/cancel.png?1561738381805", id:"cancel"},
		{src:"images/photoeditor/move.png?1561738381805", id:"move"},
		{src:"images/photoeditor/pinch.png?1561738381805", id:"pinch"}
	],
	preloads: []
};



// bootstrap callback support:

(lib.Stage = function(canvas) {
	createjs.Stage.call(this, canvas);
}).prototype = p = new createjs.Stage();

p.setAutoPlay = function(autoPlay) {
	this.tickEnabled = autoPlay;
}
p.play = function() { this.tickEnabled = true; this.getChildAt(0).gotoAndPlay(this.getTimelinePosition()) }
p.stop = function(ms) { if(ms) this.seek(ms); this.tickEnabled = false; }
p.seek = function(ms) { this.tickEnabled = true; this.getChildAt(0).gotoAndStop(lib.properties.fps * ms / 1000); }
p.getDuration = function() { return this.getChildAt(0).totalFrames / lib.properties.fps * 1000; }

p.getTimelinePosition = function() { return this.getChildAt(0).currentFrame / lib.properties.fps * 1000; }

an.bootcompsLoaded = an.bootcompsLoaded || [];
if(!an.bootstrapListeners) {
	an.bootstrapListeners=[];
}

an.bootstrapCallback=function(fnCallback) {
	an.bootstrapListeners.push(fnCallback);
	if(an.bootcompsLoaded.length > 0) {
		for(var i=0; i<an.bootcompsLoaded.length; ++i) {
			fnCallback(an.bootcompsLoaded[i]);
		}
	}
};

an.compositions = an.compositions || {};
an.compositions['BC66CE655B224F2FA9D990DCFB572F80'] = {
	getStage: function() { return exportRoot.getStage(); },
	getLibrary: function() { return lib; },
	getSpriteSheet: function() { return ss; },
	getImages: function() { return img; }
};

an.compositionLoaded = function(id) {
	an.bootcompsLoaded.push(id);
	for(var j=0; j<an.bootstrapListeners.length; j++) {
		an.bootstrapListeners[j](id);
	}
}

an.getComposition = function(id) {
	return an.compositions[id];
}


an.makeResponsive = function(isResp, respDim, isScale, scaleType, domContainers) {		
	var lastW, lastH, lastS=1;		
	window.addEventListener('resize', resizeCanvas);		
	resizeCanvas();		
	function resizeCanvas() {			
		var w = lib.properties.width, h = lib.properties.height;			
		var iw = window.innerWidth, ih=window.innerHeight;			
		var pRatio = window.devicePixelRatio || 1, xRatio=iw/w, yRatio=ih/h, sRatio=1;			
		if(isResp) {                
			if((respDim=='width'&&lastW==iw) || (respDim=='height'&&lastH==ih)) {                    
				sRatio = lastS;                
			}				
			else if(!isScale) {					
				if(iw<w || ih<h)						
					sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==1) {					
				sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==2) {					
				sRatio = Math.max(xRatio, yRatio);				
			}			
		}			
		domContainers[0].width = w * pRatio * sRatio;			
		domContainers[0].height = h * pRatio * sRatio;			
		domContainers.forEach(function(container) {				
			container.style.width = w * sRatio + 'px';				
			container.style.height = h * sRatio + 'px';			
		});			
		stage.scaleX = pRatio*sRatio;			
		stage.scaleY = pRatio*sRatio;			
		lastW = iw; lastH = ih; lastS = sRatio;            
		stage.tickOnUpdate = false;            
		stage.update();            
		stage.tickOnUpdate = true;		
	}
}


})(createjs = createjs||{}, AdobeAn = AdobeAn||{});
var createjs, AdobeAn;