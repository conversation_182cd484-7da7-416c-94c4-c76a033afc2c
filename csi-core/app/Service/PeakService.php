<?php

namespace App\Service;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class PeakService
{
    private string $connectId;
    private string $password;
    private string $userToken;
    private string $clientToken;
    private string $baseUrl;
    private string $paymentMethodId;

    private array $endpoints = [
        'clienttoken' => 'api/v1/clienttoken',
        'contacts' => 'api/v1/contacts',
        'edit_contact' => 'api/v1/Contacts/edit',
        'invoices' => 'api/v1/invoices',
        'receipts' => 'api/v1/receipts',
        'quotations' => 'api/v1/quotations',
        'paymentmethods' => 'api/v1/paymentmethods',
        'approve_invoice' => 'api/v1/invoices/approve',
        'receipts_invoice' => 'api/v1/receipts/createbyinvoice',
        'services' => 'api/v1/services',
    ];

    public function __construct()
    {
        $this->loadConfiguration();
    }

    private function loadConfiguration(): void
    {
        $this->connectId = config('peak.connect_id');
        $this->password = config('peak.password');
        $this->userToken = config('peak.user_token');

        // Try to get client token from cache only, no config fallback
        $this->clientToken = Cache::get('peak_client_token', '');

        $this->baseUrl = config('peak.base_url');
        $this->paymentMethodId = config('peak.payment_method_id');
    }

    public function processOrderCompletion(array $orderData): array
    {
        try {
            $order = $this->normalizeOrderData($orderData);

            if ($order['billId']) {
                return $this->createSuccessResponse('Order already has bill ID, no processing needed');
            }

            $this->processOrderWorkflow($order);
            $this->processReceiptsIfNeeded($order);

            return $this->createSuccessResponse('Order processed successfully');
        } catch (\Exception $e) {
            Log::error('Error processing order completion: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    private function getRequest(string $url, array $headers = []): string
    {
        try {
            $response = Http::withHeaders($this->formatHeaders($headers))
                ->timeout(config('peak.timeout', 400))
                ->get($url);

            if ($response->failed()) {
                throw new \Exception('HTTP request failed: ' . $response->status());
            }

            return $response->body();
        } catch (\Exception $e) {
            Log::error('GET request failed: ' . $e->getMessage());
            throw $e;
        }
    }

    private function postRequest(string $url, array $headers = [], array $data = []): string
    {
        try {
            $response = Http::withHeaders($this->formatHeaders($headers))
                ->timeout(config('peak.timeout', 400))
                ->post($url, $data);

            if ($response->failed()) {
                throw new \Exception('HTTP request failed: ' . $response->status());
            }

            Log::info('Peak API Response: ' . $response->body());
            return $response->body();
        } catch (\Exception $e) {
            Log::error('POST request failed: ' . $e->getMessage());
            throw $e;
        }
    }

    private function formatHeaders(array $headers): array
    {
        $formatted = [];
        foreach ($headers as $header) {
            $parts = explode(': ', $header, 2);
            if (count($parts) === 2) {
                $formatted[$parts[0]] = $parts[1];
            }
        }
        return $formatted;
    }

    public function ensureValidToken(): bool
    {
        if (Cache::has('peak_client_token')) {
            $this->clientToken = Cache::get('peak_client_token');
            return true;
        }

        // ถ้าไม่มี token ใน cache ให้สร้างใหม่เลย
        $result = $this->clientToken();
        return $result['status'];
    }

    public function setHeader(): array
    {
        $this->ensureValidToken();

        $timeStamp = Carbon::now('UTC')->format('YmdHis');
        $timeSignature = hash_hmac('sha1', $timeStamp, $this->connectId);

        return [
            "Content-Type: Application/json",
            "Client-Token: " . $this->clientToken,
            "User-Token: " . $this->userToken,
            "Time-Stamp: " . $timeStamp,
            "Time-Signature: " . $timeSignature
        ];
    }

    public function clientToken(): array
    {
        $url = $this->getEndpoint('clienttoken');
        $timeStamp = Carbon::now('UTC')->format('YmdHis');
        $timeSignature = hash_hmac('sha1', $timeStamp, $this->connectId);

        $headers = [
            "Time-Stamp: " . $timeStamp,
            "Time-Signature: " . $timeSignature,
            "Content-Type: Application/json"
        ];

        $data = [
            'PeakClientToken' => [
                'connectId' => $this->connectId,
                'password' => $this->password
            ]
        ];

        try {
            $response = $this->postRequest($url, $headers, $data);
            $responseObj = json_decode($response, true);
            $peakClientToken = $responseObj['PeakClientToken'];

            if ($peakClientToken['resCode'] == 200) {
                $this->updatePeakToken($peakClientToken['token']);
                return $this->createSuccessResponse('Token generated successfully', [
                    'token' => $peakClientToken['token'],
                    'headers' => $headers,
                ]);
            } else {
                return $this->createErrorResponse($peakClientToken['resDesc'], $peakClientToken['resCode']);
            }
        } catch (\Exception $e) {
            Log::error('Error generating client token: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function createContact(array $contactData): array
    {
        try {
            $url = $this->getEndpoint('contacts');
            $data = [
                "PeakContacts" => [
                    "contacts" => [
                        [
                            "name" => $contactData['name'],
                            "type" => $contactData['type'] ?? config('peak.defaults.contact_type'),
                            "address" => $contactData['address'],
                            "taxNumber" => $contactData['tax_number'] ?? '',
                            "subDistrict" => $contactData['sub_district'],
                            "district" => $contactData['district'],
                            "province" => $contactData['province'],
                            "country" => $contactData['country'] ?? config('peak.defaults.country'),
                            "postCode" => $contactData['post_code'],
                            "email" => $contactData['email'],
                            "contactFirstName" => $contactData['first_name'],
                            "contactLastName" => $contactData['last_name'],
                            "contactPhoneNumber" => $contactData['phone'],
                            "branchCode" => $contactData['branch_code'] ?? config('peak.defaults.branch_code'),
                        ]
                    ]
                ]
            ];

            $response = $this->postRequest($url, $this->setHeader(), $data);
            $responseObj = json_decode($response, true);

            return $this->handlePeakApiResponse($responseObj, 'PeakContacts', 'create contact');
        } catch (\Exception $e) {
            Log::error('Error creating contact: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function updateContact(int $contactId, array $contactData): array
    {
        try {
            $url = $this->getEndpoint('edit_contact');
            $data = [
                "PeakContacts" => [
                    "id" => (string)$contactId,
                    "code" => "",
                    "contacts" => [
                        "name" => $contactData['name'],
                        "type" => $contactData['type'] ?? config('peak.defaults.contact_type', 0),
                        "prefixNameType" => 0,
                        "prefixNameOther" => "",
                        "taxNumber" => $contactData['tax_number'] ?? '',
                        "branchCode" => $contactData['branch_code'] ?? config('peak.defaults.branch_code', '00000'),
                        "address" => $contactData['address'],
                        "subDistrict" => $contactData['sub_district'],
                        "district" => $contactData['district'],
                        "province" => $contactData['province'],
                        "country" => $contactData['country'] ?? config('peak.defaults.country', 'TH'),
                        "postCode" => $contactData['post_code'],
                        "callCenterNumber" => "",
                        "faxNumber" => "",
                        "email" => $contactData['email'],
                        "website" => "",
                        "contactFirstName" => $contactData['first_name'],
                        "contactLastName" => $contactData['last_name'],
                        "contactNickName" => "",
                        "contactPosition" => "",
                        "contactPhoneNumber" => $contactData['phone'],
                        "contactEmail" => $contactData['email'],
                        "purchaseAccount" => "",
                        "sellAccount" => "",
                        "bankAccount" => [
                            "bankId" => 0,
                            "bankBranch" => "",
                            "bankAccountNo" => "",
                            "bankAccountName" => ""
                        ],
                        "id" => (string)$contactId,
                        "code" => ""
                    ]
                ]
            ];

            $response = $this->postRequest($url, $this->setHeader(), $data);
            $responseObj = json_decode($response, true);

            return $this->handlePeakApiResponse($responseObj, 'PeakContacts', 'update contact');
        } catch (\Exception $e) {
            Log::error('Error updating contact: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function getContactByTaxNumber(string $taxNumber): array
    {
        try {
            $url = $this->getEndpoint('contacts') . '/?taxId=' . $taxNumber;
            $response = $this->getRequest($url, $this->setHeader());
            $responseObj = json_decode($response, true);
            $peakContacts = $responseObj['PeakContacts'];

            if ($peakContacts['resCode'] == 200) {
                if ($peakContacts['contacts'][0]['resCode'] == 200) {
                    return [
                        'status' => true,
                        'message' => $peakContacts['contacts'][0]['resDesc'],
                        'id' => $peakContacts['contacts'][0]['id'],
                        'contact' => $peakContacts['contacts'][0]
                    ];
                } else {
                    return [
                        'status' => false,
                        'code' => $peakContacts['contacts'][0]['resCode'],
                        'message' => $peakContacts['contacts'][0]['resDesc'],
                    ];
                }
            } else {
                return [
                    'status' => false,
                    'message' => 'Contact not found',
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error getting contact by tax number: ' . $e->getMessage());
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getContacts(array $params = []): array
    {
        try {
            $url = $this->getEndpoint('contacts');

            if (!empty($params)) {
                $url .= '?' . http_build_query($params);
            }

            $response = $this->getRequest($url, $this->setHeader());
            $responseObj = json_decode($response, true);
            $peakContacts = $responseObj['PeakContacts'];

            if ($peakContacts['resCode'] == 200) {
                return [
                    'status' => true,
                    'contacts' => $peakContacts['contacts'],
                    'totalContact' => $peakContacts['totalContact'] ?? 0,
                ];
            } else {
                return [
                    'status' => false,
                    'code' => $peakContacts['resCode'],
                    'message' => $peakContacts['resDesc'],
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error getting contacts: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function createProduct(array $productData): array
    {
        try {
            $url = $this->getEndpoint('services');

            $data = [
                "PeakServices" => [
                    "services" => [
                        [
                            "name" => $productData['name'],
                            "purchaseValue" => $productData['price'],
                            "purchaseVatType" => $productData['purchase_vat_type'] ?? config('peak.defaults.purchase_vat_type', 3),
                            "sellValue" => $productData['price'],
                            "sellVatType" => $productData['sell_vat_type'] ?? config('peak.defaults.sell_vat_type', 3),
                            "description" => $productData['description'] ?? ''
                        ]
                    ]
                ]
            ];

            $response = $this->postRequest($url, $this->setHeader(), $data);
            $responseObj = json_decode($response, true);

            return $this->handlePeakApiResponse($responseObj, 'PeakServices', 'create product');
        } catch (\Exception $e) {
            Log::error('Error creating product: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function createProductWithCallback(array $productData, callable $updateCallback = null): array
    {
        try {
            $result = $this->createProduct($productData);

            if ($result['status'] && $updateCallback) {
                $updateCallback([
                    'peak_product_id' => $result['id'],
                    'peak_product_code' => $result['code'],
                    'original_id' => $productData['original_id'] ?? null
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error creating product with callback: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function getProductByCode(string $code): array
    {
        try {
            $url = $this->getEndpoint('services') . '?code=' . $code;
            $response = $this->getRequest($url, $this->setHeader());
            $responseObj = json_decode($response, true);

            return $this->handlePeakApiResponse($responseObj, 'PeakServices', 'get product');
        } catch (\Exception $e) {
            Log::error('Error getting product by code: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function getProducts(array $params = []): array
    {
        try {
            $url = $this->getEndpoint('services');

            if (!empty($params)) {
                $url .= '?' . http_build_query($params);
            }

            $response = $this->getRequest($url, $this->setHeader());
            $responseObj = json_decode($response, true);
            $peakServices = $responseObj['PeakServices'];

            if ($peakServices['resCode'] == 200) {
                return [
                    'status' => true,
                    'message' => $peakServices['resDesc'],
                    'services' => $peakServices['services'] ?? [],
                    'totalService' => $peakServices['totalService'] ?? 0
                ];
            } else {
                return [
                    'status' => false,
                    'code' => $peakServices['resCode'],
                    'message' => $peakServices['resDesc']
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error getting products: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function createQuotation(array $quotationData): array
    {
        try {
            $url = $this->getEndpoint('quotations');
            $withholdingTaxAmount = $this->calculateWithholdingTax($quotationData);
            $issuedDate = Carbon::parse($quotationData['created_at'])->format('Ymd');
            $dueDate = $quotationData['due_date'] ?? $issuedDate;

            $data = [
                "PeakQuotations" => [
                    "quotations" => [
                        [
                            "issuedDate" => $issuedDate,
                            "dueDate" => $dueDate,
                            "contactId" => $quotationData['contact_id'],
                            "discountTotal" => $quotationData['discount'] ?? 0,
                            "products" => [
                                [
                                    "productId" => $quotationData['product_id'],
                                    "quantity" => $quotationData['quantity'] ?? 1,
                                    "price" => $quotationData['subtotal'],
                                    "withholdingTaxAmount" => $withholdingTaxAmount,
                                    "vatType" => config('peak.defaults.vat_type')
                                ]
                            ],
                            "paidPayments" => [
                                "paymentDate" => Carbon::now()->format('Ymd'),
                                "withholdingTaxAmount" => $withholdingTaxAmount,
                                "payments" => [
                                    [
                                        "paymentMethodId" => $this->paymentMethodId,
                                        "amount" => $quotationData['subtotal']
                                    ]
                                ]
                            ],
                        ]
                    ]
                ]
            ];

            $response = $this->postRequest($url, $this->setHeader(), $data);
            $responseObj = json_decode($response, true);

            return $this->handlePeakApiResponse($responseObj, 'PeakQuotations', 'create quotation');
        } catch (\Exception $e) {
            Log::error('Error creating quotation: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function createInvoice(array $invoiceData): array
    {
        try {
            $url = $this->getEndpoint('invoices');
            $withholdingTaxAmount = $this->calculateWithholdingTax($invoiceData);
            $issuedDate = Carbon::parse($invoiceData['created_at'])->format('Ymd');
            $dueDate = $invoiceData['due_date'] ?? $issuedDate;
            $isTaxInvoice = $invoiceData['is_tax_invoice'] ?? '0';

            $data = [
                "PeakInvoices" => [
                    "invoices" => [
                        [
                            "issuedDate" => $issuedDate,
                            "dueDate" => $dueDate,
                            "contactId" => $invoiceData['contact_id'],
                            "taxStatus" => $isTaxInvoice,
                            "discountTotal" => $invoiceData['discount'] ?? 0,
                            "products" => [
                                [
                                    "productCode" => $invoiceData['product_code'],
                                    "quantity" => $invoiceData['quantity'] ?? 1,
                                    "price" => $invoiceData['subtotal'],
                                    "withholdingTaxAmount" => $withholdingTaxAmount,
                                    "vatType" => config('peak.defaults.vat_type', 3)
                                ]
                            ],
                            "paidPayments" => [
                                "paymentDate" => Carbon::now()->format('Ymd'),
                                "withholdingTaxAmount" => $withholdingTaxAmount,
                                "payments" => [
                                    [
                                        "paymentMethodId" => $this->paymentMethodId,
                                        "amount" => $invoiceData['subtotal']
                                    ]
                                ]
                            ],
                            "istaxInvoice" => $isTaxInvoice
                        ]
                    ]
                ]
            ];

            $response = $this->postRequest($url, $this->setHeader(), $data);
            $responseObj = json_decode($response, true);

            return $this->handlePeakApiResponse($responseObj, 'PeakInvoices', 'create invoice');
        } catch (\Exception $e) {
            Log::error('Error creating invoice: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function createReceipt(array $receiptData): array
    {
        try {
            $url = $this->getEndpoint('receipts');
            $withholdingTaxAmount = $this->calculateWithholdingTax($receiptData);
            $issuedDate = Carbon::now()->format('Ymd');
            $dueDate = $receiptData['due_date'] ?? $issuedDate;
            $taxStatus = $receiptData['tax_status'] ?? config('peak.defaults.tax_status', 0);

            $data = [
                "PeakReceipts" => [
                    "receipts" => [
                        [
                            "issuedDate" => $issuedDate,
                            "dueDate" => $dueDate,
                            "contactId" => $receiptData['contact_id'],
                            "taxStatus" => $taxStatus,
                            "products" => [
                                [
                                    "productCode" => $receiptData['product_code'],
                                    "quantity" => $receiptData['quantity'] ?? 1,
                                    "price" => $receiptData['total'],
                                    "withholdingTaxAmount" => $withholdingTaxAmount,
                                    "vatType" => config('peak.defaults.vat_type', 3),
                                ]
                            ],
                            "paidPayments" => [
                                "paymentDate" => Carbon::now()->format('Ymd'),
                                "withholdingTaxAmount" => $withholdingTaxAmount,
                                "payments" => [
                                    [
                                        "paymentMethodId" => $this->paymentMethodId,
                                        "amount" => $receiptData['total']
                                    ]
                                ]
                            ],
                            "istaxInvoice" => 1
                        ]
                    ]
                ]
            ];

            $response = $this->postRequest($url, $this->setHeader(), $data);
            $responseObj = json_decode($response, true);

            return $this->handlePeakApiResponse($responseObj, 'PeakReceipts', 'create receipt');
        } catch (\Exception $e) {
            Log::error('Error creating receipt: ' . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    public function updatePeakToken(string $clientToken): void
    {
        $this->clientToken = $clientToken;
        $ttl = config('peak.cache.client_token_ttl', 120);
        Cache::put('peak_client_token', $clientToken, now()->addMinutes($ttl));
    }

    public function calculateWithholdingTax(array $orderData): float
    {
        $subtotal = $orderData['subtotal'] ?? $orderData['total'] ?? 0;
        $discount = $orderData['discount'] ?? 0;
        $hasTax = $orderData['has_withholding_tax'] ?? false;
        $rate = config('peak.defaults.withholding_tax_rate', 0.03);

        return $hasTax ? (($subtotal - $discount) * $rate) : 0;
    }

    private function buildEndpointUrl(string $endpoint): string
    {
        return rtrim($this->baseUrl, '/') . '/' . ltrim($this->endpoints[$endpoint], '/');
    }

    public function getEndpoint(string $name): string
    {
        if (!isset($this->endpoints[$name])) {
            throw new \InvalidArgumentException("Endpoint '{$name}' not found");
        }

        return $this->buildEndpointUrl($name);
    }

    private function normalizeOrderData(array $orderData): array
    {
        return [
            'id' => $orderData['id'],
            'invoicesLink' => $orderData['invoices_link'] ?? null,
            'billId' => $orderData['bill_id'] ?? null,
            'quotationsId' => $orderData['quotations_id'] ?? null,
            'billingRequired' => $orderData['billing_required'] ?? false,
            'paymentMethod' => $orderData['payment_method'] ?? null,
            'hasTaxFee' => $orderData['has_tax_fee'] ?? false,
        ];
    }

    private function processOrderWorkflow(array $order): void
    {
        if ($order['invoicesLink']) {
            $this->processExistingInvoiceWorkflow($order['id']);
        } elseif ($order['quotationsId']) {
            $this->processQuotationWorkflow($order['id']);
        } elseif ($order['billingRequired']) {
            $this->processFullBillingWorkflow($order['id']);
        }
    }

    private function processExistingInvoiceWorkflow(int $orderId): void
    {
        $this->addPaymentInvoices($orderId);
        $this->approveInvoices($orderId);
        $this->createReceiptsInvoices($orderId);
    }

    private function processQuotationWorkflow(int $orderId): void
    {
        $this->createInvoicesQuotation($orderId);
        $this->processExistingInvoiceWorkflow($orderId);
    }

    private function processFullBillingWorkflow(int $orderId): void
    {
        $this->createQuotation($orderId);
        $this->processQuotationWorkflow($orderId);
    }

    private function processReceiptsIfNeeded(array $order): void
    {
        if ($this->shouldCreateReceipts($order)) {
            Log::info('Creating receipts for order: ' . $order['id']);
            $this->createReceipts($order['id']);
        }
    }

    private function shouldCreateReceipts(array $order): bool
    {
        $supportedPaymentMethods = config('peak.supported_payment_methods', []);

        return in_array($order['paymentMethod'], $supportedPaymentMethods)
            && !$order['billId']
            && $order['hasTaxFee'];
    }

    private function createSuccessResponse(string $message, array $data = []): array
    {
        return array_merge(['status' => true, 'message' => $message], $data);
    }

    private function createErrorResponse(string $message, ?int $code = null): array
    {
        $response = ['status' => false, 'message' => $message];
        if ($code !== null) {
            $response['code'] = $code;
        }
        return $response;
    }

    private function handlePeakApiResponse(array $response, string $entityKey, string $operation = ''): array
    {
        $entity = $response[$entityKey] ?? null;

        if (!$entity) {
            return $this->createErrorResponse('Invalid response format');
        }

        if ($entity['resCode'] == 200) {
            $entityName = strtolower(str_replace('Peak', '', $entityKey));
            $firstItem = $entity[$entityName][0] ?? null;

            if ($firstItem && $firstItem['resCode'] == 200) {
                $result = ['id' => $firstItem['id']];

                if (isset($firstItem['code'])) {
                    $result['code'] = $firstItem['code'];
                }

                if (isset($firstItem['documentLink'])) {
                    $result['doc_link'] = $firstItem['documentLink'];
                    $result['documentLink'] = $firstItem['documentLink'];

                    if ($entityKey === 'PeakInvoices') {
                        $result['invoices_link'] = $firstItem['documentLink'];
                    }

                    if ($entityKey === 'PeakReceipts') {
                        $result['receipt_link'] = $firstItem['documentLink'];
                    }
                }

                return $this->createSuccessResponse($firstItem['resDesc'], $result);
            } else {
                return $this->createErrorResponse(
                    $firstItem['resDesc'] ?? 'Unknown error',
                    $firstItem['resCode'] ?? null
                );
            }
        } else {
            return $this->createErrorResponse($entity['resDesc'], $entity['resCode']);
        }
    }

    // Legacy method stubs for backward compatibility
    public function createReceipts(int $orderId): array
    {
        // This would need to be implemented based on your order model
        // For now, returning a stub
        return $this->createErrorResponse('Method needs implementation');
    }

    public function addPaymentInvoices(int $orderId): array
    {
        return $this->createErrorResponse('Method needs implementation');
    }

    public function approveInvoices(int $orderId): array
    {
        return $this->createErrorResponse('Method needs implementation');
    }

    public function createReceiptsInvoices(int $orderId): array
    {
        return $this->createErrorResponse('Method needs implementation');
    }

    public function createInvoicesQuotation(int $orderId): array
    {
        return $this->createErrorResponse('Method needs implementation');
    }
}
