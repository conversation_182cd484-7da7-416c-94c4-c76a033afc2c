<?php

namespace App\Service;

use Exception;

class LineService
{
    private $lineApiUrl = 'https://api.line.me/v2/bot/message/push';

    /**
     * Send message to Line OA user
     * 
     * @param string $userId Line User ID
     * @param string $message Message to send
     * @return array
     */
    public function sendMessage($channelAccessToken, $userId, $message)
    {
        try {
            if (!$channelAccessToken) {
                return [
                    'status' => false,
                    'message' => 'Line Channel Access Token not configured'
                ];
            }

            $data = [
                'to' => $userId,
                'messages' => [
                    [
                        'type' => 'text',
                        'text' => $message
                    ]
                ]
            ];

            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => $this->lineApiUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $channelAccessToken,
                    'Content-Type: application/json'
                ],
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);

            curl_close($curl);

            if ($error) {
                return [
                    'status' => false,
                    'message' => 'CURL Error: ' . $error
                ];
            }

            if ($httpCode === 200) {
                return [
                    'status' => true,
                    'message' => 'Message sent successfully',
                    'response' => json_decode($response, true)
                ];
            } else {
                return [
                    'status' => false,
                    'message' => 'Failed to send message',
                    'response' => json_decode($response, true),
                    'http_code' => $httpCode
                ];
            }

        } catch (Exception $e) {
            return [
                'status' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
}
