<?php

namespace App\Exports;

use App\Helpers\AppHelper;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\DB;
use App\Models\Core\CourseCateLog;
use App\Models\Core\CourseLeranerLog;
use App\Models\Core\DynamicCert;
use App\Models\Core\CourseExamLog;
use App\Models\Core\CourseLessonLog;

class CourseExport implements FromArray, WithHeadings
{
    public function __construct()
    {
    }

    public function array(): array
    {
        $data = DB::table('course')
            ->leftjoin('categories_year', 'categories_year.id', 'course.year')
            ->leftjoin('categories_level', 'categories_level.id', 'course.level')
            ->leftjoin('categories_learner', 'categories_learner.id', 'course.learner')
            ->leftjoin('categories_course_type', 'categories_course_type.id', 'course.course_general')
            ->leftjoin('categories', 'categories.id', 'course.categories')
            ->select(
                'course.id',
                'course.title_th',
                'course.price',
                'course.course_key',
                'course.speaker',
                'course.pro_price',
                'course.trailer_media',
                'course.receive_point',
                'course.started_date',
                'course.end_date',
                'course.status',
                'course.group_code',
                'course.created_at',
                'course.last_sync',
                'categories_year.title_th as c_year',
                'categories_level.title_th as c_level',
                'categories_learner.title_th as c_learner',
                'categories.title_th as c_cate',
                'course.course_general'
            )
            ->orderBy('course.id', 'asc')
            ->get();

        foreach ($data as $e) {


            $category = CourseCateLog::join('categories', 'categories.id', 'course_categories_log.cate_id')
                ->where('course_categories_log.course_id', $e->id)
                ->select('categories.title_th')
                ->get();

            $category_txt = '';
            foreach ($category as $key => $val) {
                if ($category_txt != '') {
                    $category_txt .= ',';
                }
                $category_txt .= $val->title_th;
            }


            $learner = CourseLeranerLog::join('categories_learner', 'categories_learner.id', 'course_learner_log.learner')
                ->where('course_learner_log.course_id', $e->id)
                ->select('categories_learner.title_th')
                ->get();

            $learner_txt = '';
            foreach ($learner as $key => $val) {
                if ($learner_txt != '') {
                    $learner_txt .= ',';
                }
                $learner_txt .= $val->title_th;
            }

            $cert = DynamicCert::where('course_id', $e->id)->where('status', 1)->first();
            if ($cert) {
                $cert = 'Yes';
            } else {
                $cert = 'No';
            }

            $exam = CourseExamLog::where('course_id', $e->id)->where('status', 1)->count();
            if ($exam > 0) {
                $exam = $exam;
            } else {
                $exam = strval($exam);
            }

            $duration = CourseLessonLog::where('course_id', $e->id)->sum(DB::raw('TIME_TO_SEC(duration)'));

            if ($duration) {
                $total_duration = round($duration / 60, 2);
            } else {
                $total_duration = '0';
            }

            $ep = CourseLessonLog::where('course_id', $e->id)->count();
            if ($ep > 0) {
                $ep = $ep;
            } else {
                $ep = '0';
            }

            if ($e->status == 1) {
                $e->status = 'Active';
            } else {
                $e->status = 'Draft';
            }

            if ($e->end_date != null || $e->end_date != '') {
                $e->end_date = $e->end_date;
            } else {
                $e->end_date = '-';
            }

            if ($e->receive_point != 0) {
                $e->receive_point = $e->receive_point;
            } else {
                $e->receive_point = strval($e->receive_point);
            }

            if ($e->trailer_media == 1) {
                $e->trailer_media = 'Course';
            } else if ($e->trailer_media == 2) {
                $e->trailer_media = 'Zoom';
            } else if ($e->trailer_media == 3) {
                $e->trailer_media = 'Podcast';
            } else if ($e->trailer_media == 4) {
                $e->trailer_media = 'Seminar';
            } else if ($e->trailer_media == 5) {
                $e->trailer_media = 'Infographic';
            }

            if ($e->last_sync == null) {
                $last_sync = $e->created_at;
            } else {
                $last_sync = $e->last_sync;
            }

            if ($e->course_general == 1) {
                $e->course_general = 'Med';
            } else {
                $e->course_general = 'Non-Med';
            }


            $ex_array[] = array(
                'id' => $e->id,
                'Group_ID' => $e->group_code,
                'Content_ID' => $e->course_key,
                'Content_Type' => $e->trailer_media,
                'Content_Group' => $category_txt,
                'Content_Year' => $e->c_year,
                'Media_Type' => $e->trailer_media,
                'CourseName' => $e->title_th,
                'Speaker' => $e->speaker,
                'Level' => $e->c_level,
                'Tags_Learner' => $learner_txt,
                'Category' => '-',
                'Sub-Category' => '-',
                'Med_Status' => $e->course_general,
                'CME_Point' => $e->receive_point,
                'Certificate' => $cert,
                'Examination' => $exam,
                'Set_Price' => $e->price,
                'Price' => $e->pro_price,
                'SKU_Type' => '-',
                'No_of_SKU_in_group' => '-',
                'SKU_in_group' => '-',
                'Year_Member_Pack' => AppHelper::instance()->courseSubscriptionCourse($e->id),
                'Content_Format' => '-',
                'Total_Durations_(mins)' => $total_duration,
                'Number_of_EP' => $ep,
                'Public_Date' => $e->started_date,
                'Expire_Date' => $e->end_date,
                'Status' => $e->status,
                'Production_Source' => '',
                'Last_Sync' => $last_sync,
                'Remark' => '',
            );
        }
        return $ex_array;
    }

    public function headings(): array
    {
        return [
            'id',
            'Group_ID',
            'Content_ID',
            'Content_Type',
            'Content_Group',
            'Content_Year',
            'Media_Type',
            'CourseName',
            'Speaker',
            'Level',
            'Tags_Learner',
            'Category',
            'Sub-Category',
            'Med_Status',
            'CME_Point',
            'Certificate',
            'Examination',
            'Set_Price',
            'Price',
            'SKU_Type',
            'No_of_SKU_in_group',
            'SKU_in_group',
            'Year_Member_Pack',
            'Content_Format',
            'Total_Durations_(mins)',
            'Number_of_EP',
            'Public_Date',
            'Expire_Date',
            'Status',
            'Production_Source',
            'Last_Sync',
            'Remark',
        ];
    }
}
