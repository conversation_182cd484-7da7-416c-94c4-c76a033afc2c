<?php

namespace App\Exports;
 
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\DB;

class SpeakerExport implements FromCollection, WithHeadings
{

    public function __construct()
    {
        
    }

    public function collection()
    {
        return DB::table('categories_speaker')
                ->select('categories_speaker.id', 'categories_speaker.name', 'categories_speaker.title_th')
                ->orderBy('categories_speaker.id', 'asc')
                ->get();
    }

    public function headings(): array
    {
        return [
            'id',
            'Name',
            'Full_Name',
        ];
    }

}