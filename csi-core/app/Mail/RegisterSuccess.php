<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Request;
use App\Models\Dummy;
use Carbon\Carbon;
use App\Helpers\AppHelper;

class RegisterSuccess extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        //
         $this->request = $request;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $obj = new Dummy();

        $obj['request'] = $this->request;
        return $this->view('mail.register_success')
                     ->with([
                           'obj' => $obj
                       ])
                     ->subject($this->request['subject']);
    }
}
