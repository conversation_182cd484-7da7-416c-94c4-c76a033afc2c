<?php

namespace App\Exceptions;

use Throwable;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    public function render($request, Throwable $exception)
    {
        if(!config('app.debug')) {
          if($this->isHttpException($exception))
          {
            switch (intval($exception->getStatusCode())) {
                case 404:
                      return redirect()->route('home');
                    break;
                case 500:
                      return redirect()->route('sorry');
                    break;

                default:
                    return $this->renderHttpException($exception);
                    break;
            }
          }
          else
          {
              return parent::render($request, $exception);
          }
        }
        else{
            if ($request->wantsJson()) {
                // Define the response
                $response = [
                    'errors' => 'Sorry, something went wrong.'
                ];
  
                $response['exception'] = get_class($exception); // Reflection might be better here
                $response['message'] = $exception->getMessage();
                $response['trace'] = $exception->getTrace();
  
                // Default response of 400
                $status = 400;
  
                // If this exception is an instance of HttpException
                if ($this->isHttpException($exception)) {
                    // Grab the HTTP status code from the Exception
                    $status = $exception->getStatusCode();
                }
  
                // Return a JSON response with the response array and status code
                return response()->json($response, $status);
            }
  
            // Default to the parent class' implementation of handler
            return parent::render($request, $exception);
        }
    }

    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
