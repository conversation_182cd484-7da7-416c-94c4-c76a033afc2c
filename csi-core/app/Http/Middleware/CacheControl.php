<?php

namespace App\Http\Middleware;

use Closure;

class CacheControl
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0, s-maxage=0');
        $response->headers->set('Expires', '0');
        $response->headers->set('Pragma', 'no-cache');

        return $response;
    }
}