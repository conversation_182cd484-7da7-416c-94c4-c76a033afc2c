<?php

namespace App\Http\Controllers;

use App\Models\Core\CoursePromotionLog;
use Illuminate\Http\Request;
use App\Models\Dummy;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;


class Select2AutocompleteController extends Controller
{

  public function __construct()
  {
      $this->middleware('auth:admin');
  }

    public function layout()
    {
    	return view('select2');
    }

    public function dataType(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("types")
          ->select("*")
          ->where('name','LIKE',"%$search%")
          ->get();

      foreach ($lists as $list)
      {
        array_push($data,array(
          'id'=>$list->id,
          'name'=>$list->name,
          'result'=>'รายละเอียด​ : '.$list->name)
        );
      }
      return response()->json($data);

    }

    public function dataTag(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("tag")
          ->select("id","title as name")
          ->where('title','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataInteresting(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("interesting")
          ->select("id","name")
          ->where('name','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataSponsor(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("sponsor")
          ->select("id","name_th as name")
          ->where('name_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataDepartment(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("categories_department")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataJobFunction(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("categories_job_function")
          ->select("id","name")
          ->where('name','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataCourseInteresting(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("categories_interesting")
          ->select("id","name")
          ->where('name','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataLearner(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("categories_learner")
          ->select("id","title_th as name")
          ->where('status', 1)
          ->where('title_th','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataSeminar(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("categories_seminar")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataSpeaker(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("categories_speaker")
          ->select("id","name")
          ->where('name','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataHost(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_host")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();

      array_push($data,array(
        'id'=>0,
        'name'=>'ไม่ระบุ')
      );
      foreach ($lists as $list)
      {
        array_push($data,array(
          'id'=>$list->id,
          'name'=>$list->name)
        );
      }
      return response()->json($data);

    }

    public function dataAge(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_age")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataGender(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_gender")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataLevel(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_level")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataYear(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_year")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataCategories(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->whereNotIn('title_en', ['course-live', 'course-event', 'company-visit'])
          ->get();
      return response()->json($lists);

    }

    public function dataCourse(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('trailer_media', 1)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataCourseNotFree(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('is_free', 2)
          ->where('status', 1);
      if(isset($request->pro_id)){
        $log = CoursePromotionLog::where('promotion_id',$request->pro_id)
        ->pluck('course_id')->toArray();
        $lists->where(function ($lists) use($log) {
          $lists->whereIn('id',$log);
          $lists->orWhere('is_promotion',2);
          $lists->orWhere('pro_end','<', Carbon::now());
          $lists->orWhere('pro_period', 2);
        });
      }else{
        $lists->where('is_promotion', 2);
      }
      $lists = $lists->get();
      return response()->json($lists);

    }

    public function dataSeminarTag(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('trailer_media', 3)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataArticle(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("article")
          ->select("id","title_th as name")
          ->where('is_article', 1)
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataNews(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("article")
          ->select("id","title_th as name")
          ->where('is_article', 2)
          ->where('title_th','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataSeminarPin(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('trailer_media', 4)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataCourseGroup(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('trailer_media', '!=',5)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataInfoPin(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("article")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('is_article', 3)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataAdmin88(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("admin")
          ->select("id","username as name")
          ->where('username','LIKE',"%$search%")
          ->where('level', 88)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataTagLearner(Request $request)
    {
      $data = array (
        0 => 
        array (
          'id' => '1',
          'name' => 'แพทย์',
        ),
        1 => 
        array (
          'id' => '2',
          'name' => 'พยาบาล',
        ),
        2 => 
        array (
          'id' => '3',
          'name' => 'เภสัช',
        ),
        3 => 
        array (
          'id' => '4',
          'name' => 'ทันตะ',
        ),
        4 => 
        array (
          'id' => '5',
          'name' => 'นิสิต',
        ),
        5 => 
        array (
          'id' => '6',
          'name' => 'สถาบันทางการแพทย์',
        ),
        6 => 
        array (
          'id' => '7',
          'name' => 'สถาบันสุขภาพ',
        ),
        7 => 
        array (
          'id' => '8',
          'name' => 'อื่นๆ',
        ),
      );
      return response()->json($data);

    }

    public function dataUserEmail(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("user")
          ->select("id","email as name")
          ->where('email','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataAssignCourse(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('trailer_media', '!=', 5)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataCertGroup(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("course_group")
          ->select("id","title as name")
          ->where('group_cert', 2)
          ->where('title','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataHomeGroup(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("course_group")
          ->select("id","title as name")
          ->where('group_cert', 2)
          ->where('title','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }
    
    public function dataHomeSeries(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("course_group")
          ->select("id","title as name")
          ->where('title','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataUType(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("categories_department")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->get();
      return response()->json($data);
    }

    public function dataSubCourse(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('trailer_media', '!=', '5')
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataCouponSub(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("subscription")
          ->select("id","title as name")
          ->where('title','LIKE',"%$search%")
          ->where('type', '1')
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataCouponGroup(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course_group")
          ->select("id","title as name")
          ->where('title','LIKE',"%$search%")
          ->where('is_free', '2')
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataLogo(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_logo")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          // ->where('logo', '!=', null)
          ->where('status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataVolumnCourse(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->where('trailer_media', '!=', 5)
          // ->where('status', 1)
          ->get();
      return response()->json($lists);

    }
    
    public function dataLot(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("volumn_lot_log")
          ->select("id","lot_name as name")
          ->where('lot_name','LIKE',"%$search%")
          ->where('company_id', $request->company_id)
          ->get();
      return response()->json($lists);

    }

    public function dataDomain(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_domain")
          ->select("id","title as name")
          ->where('title','LIKE',"%$search%")
          ->get();
      return response()->json($lists);

    }

    public function dataBusinessUnit(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("business_unit")
          ->select("id","name")
          ->where('name','LIKE',"%$search%")
          ->get();
      return response()->json($lists);

    }
    
    public function dataSubUnit(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("sub_unit")
          ->select("id","name")
          ->where('name','LIKE',"%$search%")
          ->get();
      return response()->json($lists);

    }

    public function dataLesson(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->course_id)){
        $data = DB::table("course_lesson")
            ->select("id","title_th as name")
            ->where('course_lesson.title_th','LIKE',"%$search%")
            ->where('course_lesson.course_id',$request->course_id)
            ->get();
      }else{
        
      }
      return response()->json($data);
    }

    public function dataFile(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->course_id)){
        $data = DB::table("course_file")
            ->select("id","title_th as name")
            ->where('course_file.title_th','LIKE',"%$search%")
            ->where('course_file.course_id',$request->course_id)
            ->get();
      }
      return response()->json($data);
    }
    
    public function dataJobLevel(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("category_job_level")
          ->select("id","name")
          ->where('name','LIKE',"%$search%")
          ->get();
      return response()->json($lists);

    }

    public function dataCourseDept(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("course_job_function_log")
          ->join('course', 'course.id', 'course_job_function_log.course_id')
          ->select("course.id","course.title_th as name")
          ->where('course.title_th','LIKE',"%$search%")
          ->where('course_job_function_log.job_function_id', $request->job_id)
          ->where('course.status', 1)
          ->get();
      return response()->json($lists);

    }

    public function dataLeader(Request $request)
    {
    	$data = [];
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $data = DB::table("user")
          ->select("user.id","user.name as user_name","user.lastname as user_lastname","user.position as user_position","user.department as user_dept")
          ->where(function ($query) use($search) {
            $query->orWhere('user.name', 'LIKE',"%$search%");
            $query->orWhere('user.lastname', 'LIKE',"%$search%");
            $query->orWhere('user.position', 'LIKE',"%$search%");
            $query->orWhere('user.department', 'LIKE',"%$search%");
          })
          ->where('level', 2)
          ->get();
          
      $arr = array();

      foreach ($data as $key => $value) {
        $obj = new Dummy();
        $obj['id'] = $value->id;
        $obj['name'] = $value->user_name.' '.$value->user_lastname.' || แผนก '.$value->user_dept.' || ตำแหน่ง '.$value->user_position;
        array_push($arr,$obj);
      }
      return response()->json($arr);
    }

    public function dataPlant(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_department")
          ->select("categories_department.plant as id","categories_department.plant as name")
          ->where('categories_department.plant','LIKE',"%$search%")
          ->where('categories_department.status', 1)
          ->groupby('categories_department.plant')
          ->get();
      return response()->json($lists);

    }

    public function dataGroup(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->plant_id)){
        $data = DB::table("categories_department")
        ->select("categories_department.group as id","categories_department.group as name")
        ->where('categories_department.group','LIKE',"%$search%")
        ->where('categories_department.status', 1)
        ->where('categories_department.plant' ,$request->plant_id)
        ->groupby('categories_department.group')
        ->get();
      }
      return response()->json($data);

    }

    public function dataAssType(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->plant_id) && isset($request->group_id)){
        $data = DB::table("categories_department")
        ->select("categories_department.type as id","categories_department.type as name")
        ->where('categories_department.type','LIKE',"%$search%")
        ->where('categories_department.status', 1)
        ->where('categories_department.plant' ,$request->plant_id)
        ->where('categories_department.group' ,$request->group_id)
        ->groupby('categories_department.type')
        ->get();
      }
      return response()->json($data);

    }

    public function dataCent(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->plant_id) && isset($request->group_id) && isset($request->type_id)){
        $data = DB::table("categories_department")
        ->select("categories_department.cent as id","categories_department.cent as name")
        ->where('categories_department.cent','LIKE',"%$search%")
        ->where('categories_department.status', 1)
        ->where('categories_department.plant' ,$request->plant_id)
        ->where('categories_department.group' ,$request->group_id)
        ->where('categories_department.type' ,$request->type_id)
        ->groupby('categories_department.cent')
        ->get();
      }
      return response()->json($data);

    }

    public function dataDiv(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->plant_id) && isset($request->group_id) && isset($request->type_id) && isset($request->cent_id)){
        $data = DB::table("categories_department")
        ->select("categories_department.div as id","categories_department.div as name")
        ->where('categories_department.div','LIKE',"%$search%")
        ->where('categories_department.status', 1)
        ->where('categories_department.plant' ,$request->plant_id)
        ->where('categories_department.group' ,$request->group_id)
        ->where('categories_department.type' ,$request->type_id)
        ->where('categories_department.cent' ,$request->cent_id)
        ->groupby('categories_department.div')
        ->get();
      }
      return response()->json($data);

    }

    public function dataDept(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->plant_id) && isset($request->group_id) && isset($request->type_id) && isset($request->cent_id) && isset($request->div_id)){
        $data = DB::table("categories_department")
        ->select("categories_department.dept as id","categories_department.dept as name")
        ->where('categories_department.dept','LIKE',"%$search%")
        ->where('categories_department.status', 1)
        ->where('categories_department.plant' ,$request->plant_id)
        ->where('categories_department.group' ,$request->group_id)
        ->where('categories_department.type' ,$request->type_id)
        ->where('categories_department.cent' ,$request->cent_id)
        ->where('categories_department.div' ,$request->div_id)
        ->groupby('categories_department.dept')
        ->get();
      }
      return response()->json($data);

    }

    public function dataSec(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      if(isset($request->plant_id) && isset($request->group_id) && isset($request->type_id) && isset($request->cent_id) && isset($request->div_id) && isset($request->dept_id)){
        $data = DB::table("categories_department")
        ->select("categories_department.sec as id","categories_department.sec as name")
        ->where('categories_department.sec','LIKE',"%$search%")
        ->where('categories_department.status', 1)
        ->where('categories_department.plant' ,$request->plant_id)
        ->where('categories_department.group' ,$request->group_id)
        ->where('categories_department.type' ,$request->type_id)
        ->where('categories_department.cent' ,$request->cent_id)
        ->where('categories_department.div' ,$request->div_id)
        ->where('categories_department.dept' ,$request->dept_id)
        ->groupby('categories_department.sec')
        ->get();
      }
      return response()->json($data);

    }
    
    public function dataCourseCate(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->get();
      return response()->json($lists);

    }
    
    public function dataSubscriptionMain(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      
      $lists = DB::table("subscription_main")
          ->select("id","title as name")
          ->where('title','LIKE',"%$search%")
          ->where('status', 1)
          ->get();
      
      return response()->json($lists);
    }
    
    public function dataCourseLevel(Request $request)
    {
      $data = array();
      $search = '';
      if($request->has('q')){
          $search = $request->q;
      }
      $lists = DB::table("categories_level")
          ->select("id","title_th as name")
          ->where('title_th','LIKE',"%$search%")
          ->get();
      return response()->json($lists);

    }

}
