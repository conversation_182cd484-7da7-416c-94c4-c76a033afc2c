<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\CategoriesSpeakerService;
use App\Models\Dummy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CategoriesSpeakerApiController extends Controller
{
    protected $service;

    public function __construct(CategoriesSpeakerService $service)
    {
        $this->service = $service;
    }

    public function index(): JsonResponse
    {
        $response = new Dummy();
        $response['status'] = 'false';

        try {
            $speakers = $this->service->getAllSpeakers();

            $response['data'] = $speakers;
            $response['total'] = $speakers->count();
            $response['message'] = 'Speakers retrieved successfully';
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['error'] = $e->getMessage();
            $response['message'] = 'Error occurred while fetching speakers';
        }

        return response()->json($response, 200, [
            'Content-Type' => 'application/json;charset=UTF-8', 
            'Charset' => 'utf-8'
        ], JSON_UNESCAPED_UNICODE);
    }

    public function show(Request $request, $id = null): JsonResponse
    {
        $response = new Dummy();
        $response['status'] = 'false';

        try {
            // รับ ID จาก URL parameter หรือจาก request body
            $speakerId = $id ?? $request->id ?? $request->speaker_id;
            
            if (!$speakerId) {
                $response['message'] = 'Speaker ID is required';
                return response()->json($response, 400, [
                    'Content-Type' => 'application/json;charset=UTF-8', 
                    'Charset' => 'utf-8'
                ], JSON_UNESCAPED_UNICODE);
            }

            $speaker = $this->service->getSpeakerById((int)$speakerId);

            if (!$speaker) {
                $response['message'] = 'Speaker not found';
                return response()->json($response, 404, [
                    'Content-Type' => 'application/json;charset=UTF-8', 
                    'Charset' => 'utf-8'
                ], JSON_UNESCAPED_UNICODE);
            }

            $response['data'] = $speaker;
            $response['message'] = 'Speaker retrieved successfully';
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['error'] = $e->getMessage();
            $response['message'] = 'Error occurred while fetching speaker';
        }

        return response()->json($response, 200, [
            'Content-Type' => 'application/json;charset=UTF-8', 
            'Charset' => 'utf-8'
        ], JSON_UNESCAPED_UNICODE);
    }

    public function search(Request $request): JsonResponse
    {
        $response = new Dummy();
        $response['status'] = 'false';

        try {
            if (!isset($request->search) || empty($request->search)) {
                $response['message'] = 'Search term is required';
                return response()->json($response, 400, [
                    'Content-Type' => 'application/json;charset=UTF-8', 
                    'Charset' => 'utf-8'
                ], JSON_UNESCAPED_UNICODE);
            }

            $speakers = $this->service->searchSpeakers($request->search);

            $response['data'] = $speakers;
            $response['total'] = $speakers->count();
            $response['search_term'] = $request->search;
            $response['message'] = 'Search completed successfully';
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['error'] = $e->getMessage();
            $response['message'] = 'Error occurred while searching speakers';
        }

        return response()->json($response, 200, [
            'Content-Type' => 'application/json;charset=UTF-8', 
            'Charset' => 'utf-8'
        ], JSON_UNESCAPED_UNICODE);
    }

    public function activeList(): JsonResponse
    {
        $response = new Dummy();
        $response['status'] = 'false';

        try {
            $speakers = $this->service->getSpeakersByPosition();

            $response['data'] = $speakers;
            $response['total'] = $speakers->count();
            $response['message'] = 'Active speakers retrieved successfully';
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['error'] = $e->getMessage();
            $response['message'] = 'Error occurred while fetching active speakers';
        }

        return response()->json($response, 200, [
            'Content-Type' => 'application/json;charset=UTF-8', 
            'Charset' => 'utf-8'
        ], JSON_UNESCAPED_UNICODE);
    }

    public function getCoursesBySpeaker(Request $request): JsonResponse
    {
        $response = new Dummy();
        $response['status'] = 'false';

        try {
            $speakerId = $request->speaker_id ?? $request->id;
            
            if (!$speakerId) {
                $response['message'] = 'Speaker ID is required';
                return response()->json($response, 400, [
                    'Content-Type' => 'application/json;charset=UTF-8', 
                    'Charset' => 'utf-8'
                ], JSON_UNESCAPED_UNICODE);
            }

            // ตรวจสอบว่า speaker มีอยู่จริงหรือไม่
            $speaker = $this->service->getSpeakerById((int)$speakerId);
            if (!$speaker) {
                $response['message'] = 'Speaker not found';
                return response()->json($response, 404, [
                    'Content-Type' => 'application/json;charset=UTF-8', 
                    'Charset' => 'utf-8'
                ], JSON_UNESCAPED_UNICODE);
            }

            $courses = $this->service->getCoursesBySpeakerId((int)$speakerId);

            $response['data'] = $courses;
            $response['total'] = $courses->count();
            $response['speaker_id'] = (int)$speakerId;
            $response['speaker_name'] = $speaker->name ?? $speaker->title_th;
            $response['message'] = 'Courses retrieved successfully';
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['error'] = $e->getMessage();
            $response['message'] = 'Error occurred while fetching speaker courses';
        }

        return response()->json($response, 200, [
            'Content-Type' => 'application/json;charset=UTF-8', 
            'Charset' => 'utf-8'
        ], JSON_UNESCAPED_UNICODE);
    }
}
