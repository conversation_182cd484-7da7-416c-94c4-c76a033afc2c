<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use Illuminate\Support\Facades\Session;

class GrapesjsController extends Controller
{

    public function __construct()
    {

    }

    public function grapesjs_upload(Request $request){
      if($_FILES){
        $resultArray = array();
        foreach ( $_FILES as $file){
          $fileName = $file['name'];
          $tmpName = $file['tmp_name'];
          $fileSize = $file['size'];
          $fileType = $file['type'];
          if ($file['error'] != UPLOAD_ERR_OK)
          {
                error_log($file['error']);
                echo JSON_encode(null);
          }
          $fp = fopen($tmpName, 'r');
          $content = fread($fp, filesize($tmpName));
          fclose($fp);


          $path='';
          $img_base_64 = "data:".$fileType.";base64,".base64_encode($content);
          if (preg_match('/^data:image\/(\w+);base64,/', $img_base_64, $type)) {
  
            $data = substr($img_base_64, strpos($img_base_64, ',') + 1);
            $type = strtolower($type[1]); // jpg, png, gif

            if (in_array($type, [ 'jpg', 'jpeg', 'gif', 'png' ])) {
              
                $data = str_replace( ' ', '+', $data );
                $data = base64_decode($data);
                $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                $charactersLength = strlen($characters);
                $randomString = '';
                for ($i = 0; $i < 10; $i++) {
                    $randomString .= $characters[rand(0, $charactersLength - 1)];
                }
                $image_name = Carbon::now('Asia/Bangkok')->format('Ymd').'_s_'.$randomString.'.'.$type;
                $dir = public_path('upload/page_builder_image');
                if(!File::isDirectory($dir)){
                  File::makeDirectory($dir,493,true);
                }
                $path = public_path('upload/page_builder_image/' . $image_name);
                $path_real = url('/upload/page_builder_image/' . $image_name);

                $img_size = getimagesize("data:".$fileType.";base64,".base64_encode($content));

                $size = $img_size[0];
                if($size>1280){
                  Image::make($data)->resize(1280, null, function ($constraint) {
                      $constraint->aspectRatio();
                  })->save($path);
                  $img_size = getimagesize($path);
                }else{
                  Image::make($data)->resize($size, null, function ($constraint) {
                    $constraint->aspectRatio();
                  })->save($path);
                }
            }
          }

          $img_base_64 = "data:".$fileType.";base64,".base64_encode($content);

          $result=array(
                'name'=>$file['name'],
                'type'=>'image',
                'src'=>$path_real,
                'height'=>$img_size[1],
                'width'=>$img_size[0]
          ); 
          // we can also add code to save images in database here.
          array_push($resultArray,$result);
        }    
        $response = new Dummy();
        $response['data'] = $resultArray;
        return json_decode($response, true);
      }
    }
    public function clear_session(Request $request){
      $response = new Dummy();
      Session::flush();
      $response['status'] = 'success';
      return json_decode($response, true);
    }
    

}
