<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use App\Services\HomeContent\HomeContentService;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\BannerHome;
use App\Models\Core\BannerSponsorPin;
use App\Models\Core\CateDepartment;
use App\Models\Core\Categories;
use App\Models\Core\SettingContent;
use App\Models\Core\IntroWord;

class HomeController extends Controller
{
    protected $homeContentService;

    public function __construct(HomeContentService $homeContentService)
    {
        $this->homeContentService = $homeContentService;
    }

  public function homeSponsor(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';


    $sponsor = BannerSponsorPin::join('sponsor', 'sponsor.id', 'sponsor_pin.sponsor')->where('sponsor_pin.status', 1)->orderBy('position', 'asc')
      ->select('sponsor_pin.id', 'sponsor.image', 'sponsor_pin.status', 'sponsor_pin.position', 'sponsor_pin.sponsor')->get();

    $response['sponsor'] = $sponsor;

    $response['status'] = 'success';
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function Bannerlist(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    $banner = BannerHome::where('page',$request->page)->where('status', 1)->orderBy('position', 'asc')->get();
    $response['heroBanner'] = $banner;

    $response['status'] = 'success';
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function settingCenter(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';
    if(isset($request->key)){
      $setting = SettingContent::where('status',1)->where('key',$request->key)
      ->where(function ($course) {
        $course->where('end','>=',Carbon::now());
        $course->orWhere('end',null);
      })
      ->where(function ($course) {
        $course->where('start','<=',Carbon::now());
        $course->orWhere('start',null);
      })
      ->first();
      if($setting){
        $response['thumb'] = $setting->thumb;
        $response['type'] = $setting->type;
        $response['value'] = $setting->value;
        $response['status'] = 'success';
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function introWord(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';
    if(isset($request->type)){
      $intro = IntroWord::where('status',1)->where('type',$request->type)->first();
      if($intro){
        $response['data'] = $intro;
        $response['status'] = 'success';
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function Suggestionlist(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    $cache_key = 'home_suggestion_' . md5($request->utoken ?? 'guest');
    
    try {
      $suggestions = Cache::remember($cache_key, 300, function() use ($request) {
        return $this->homeContentService->getSuggestionList($request->utoken ?? null);
      });
      
      $response['data'] = $suggestions;
      $response['status'] = 'true';
    } catch (\Exception $e) {
      $response['message'] = 'Failed to load suggestions';
      $response['error'] = $e->getMessage();
      $response['line'] = $e->getLine();
      $response['file'] = $e->getFile();
    }

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  

  public function homeSearch(Request $request)
  {
    $response = new Dummy();
    $categories = Categories::join('course_categories_log','course_categories_log.cate_id','categories.id')
    ->join('course','course.id','course_categories_log.course_id')
    ->select('categories.id','categories.title_th','categories.title_th as label', 'categories.id as value', 'categories.id as key')
    ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories.status',1);
    $categories->where(function ($categories) {
      $categories->where('course.end_date','>=',Carbon::now());
      $categories->orWhere('course.end_date',null);
    });
    $categories->groupby('categories.id')->orderby('categories.position','asc');
    $categories = $categories->get();
    $group = json_decode(json_encode($categories), true);
    $group_dummy = new Dummy();
    $group_dummy['label'] = 'หมวดหมู่';
    $group_dummy['value'] = '';
    $group_dummy['key'] = '';
    array_unshift($group, $group_dummy);
    $response['group'] = $group;

    

    $department = CateDepartment::join('course_department_log','course_department_log.department','categories_department.id')
    ->join('course','course.id','course_department_log.course_id')
    ->select('categories_department.id','categories_department.title_th','categories_department.title_th as label', 'categories_department.id as value', 'categories_department.id as key')
    ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_department.status',1);
    $department->where(function ($department) {
      $department->where('course.end_date','>=',Carbon::now());
      $department->orWhere('course.end_date',null);
    });
    $department->groupby('categories_department.id')->orderby('categories_department.position','asc');
    $department = $department->get();
    $dep = json_decode(json_encode($department), true);
    $group_dummy = new Dummy();
    $group_dummy['label'] = 'ประเภท';
    $group_dummy['value'] = '';
    $group_dummy['key'] = '';
    array_unshift($dep, $group_dummy);
    $response['department'] = $dep;

    $response['status'] = 'success';

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
}
