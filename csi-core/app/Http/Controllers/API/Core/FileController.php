<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;

use Carbon\Carbon;
use App\Models\Dummy;

class FileController extends Controller
{

    public function __construct()
    {

    }

    public function save(Request $request){
      $response = new Dummy();
      $response['status'] = 'false';
      

      if($request->file('data')->isValid() && 
      (
        strpos($request->file('data')->getClientMimeType(), 'image') !== FALSE
        ||  
        strpos($request->file('data')->getClientMimeType(), 'pdf')  !== FALSE
        ||  
        strpos($request->file('data')->getClientMimeType(), 'officedocument')  !== FALSE
        ||  
        strpos($request->file('data')->getClientMimeType(), 'csv')  !== FALSE
      ) 
      )
      {
        // Storage::disk('s3')->putFileAs('assets/tmp',$request->file('data'),'thaismile.jpg' ,'public');

        // $path = $request->file('data')->store('assets', 's3');
        $path = AppHelper::instance()->saveImage($request->file('data'),'test_script',500);

        $response['path'] = $path;
        $response['status'] = 'true';
      }

      $response['extension'] = $request->file('data')->extension();
      $response['getClientOriginalExtension'] = $request->file('data')->getClientOriginalExtension();
      $response['getClientMimeType'] = $request->file('data')->getClientMimeType();
      $response['extension image'] = strpos($request->file('data')->getClientMimeType(), 'image');
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function saveImage(Request $request){
      $response = new Dummy();
      $response['status'] = 'false';
      
      if(isset($request->image)){
        $img = AppHelper::instance()->saveImageBase64($request->image,'upload/file');
        $response['path'] =  $img;
        $response['status'] = 'true';
      }

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function saveVideo(Request $request){
      $response = new Dummy();
      $response['status'] = 'false';
      
      if(isset($request->video)){
        $video = AppHelper::instance()->saveVideoBase64($request->video);
        $response['path'] =  $video;
        $response['status'] = 'true';
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

}
