<?php


namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

use App\Helpers\AppHelper;

use App\Helpers\SimpleEnDeHelper;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\District;
use App\Models\Core\Province;
use App\Models\Core\SubDistrict;

class AreaController extends Controller
{
    public function __construct()
    {
        
    }

    public function getProvince(Request $request){
        $response = new Dummy();
        $response['status'] = 'success';
        $data = Province::orderBy('name')->select('id as value','name as text')->get();
        $response['data'] = $data;
        return json_decode($response, true);
    }

    public function getDistrict(Request $request){
        $response = new Dummy();
        $response['status'] = 'success';
        if(isset($request->province_id) && !empty($request->province_id)){ 
            $data = District::where('province_id',$request->province_id)->where('name','NOT LIKE','%*%')->orderBy('name')->select('id as value','name as text')->get();
            $response['data'] = $data; 
        }
        return json_decode($response, true);
    }

    public function getSubDistrict(Request $request){
        $response = new Dummy();
        $response['status'] = 'success';
        if(isset($request->district_id) && !empty($request->district_id)){ 
            $data = SubDistrict::where('district_id',$request->district_id)->where('name','NOT LIKE','%*%')->orderBy('name')->select('id as value','name as text')->get();
            $response['data'] = $data; 
        }
        return json_decode($response, true);
    }

}
