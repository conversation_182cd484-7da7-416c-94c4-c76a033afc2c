<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Ya<PERSON>ra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Models\Admin;
use App\Models\Core\Course;
use App\Models\Core\CourseGroup;
use App\Models\Core\EmailDynamic;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\UserHistory;
use App\Models\Core\UserLearnedTime;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{

    public function __construct()
    {

    }

    public function jwt(Request $request){
      $secretkey = env('PAYMENT_MERCHANT_SECRET_KEY');
      
      $payload = array(
          "merchantID" => env('PAYMENT_MERCHANT_ID'),
          "invoiceNo" => $request->invoiceNo,
          "description" => $request->description,
          "amount" => $request->amount,
          "currencyCode" => $request->currencyCode,
          "frontendReturnUrl" => $request->frontendReturnUrl,
          "locale" => $request->locale
      );

      if ($request->credit_payment_type === 'installment') {
          $payload["merchantID"] = env('PAYMENT_MERCHANT_ID_INSTALLMENT');
          $payload["paymentChannel"] = ["IPP"];
          $payload["installmentPeriodFilter"] = [3];
          $payload["recurring"] = false;
          $payload["ippInterestType"] = "M";
      }

      
      Log::info('Payload sent to 2C2P:', $payload);

      $jwt = JWT::encode($payload, $secretkey, 'HS256');
      Log::info('JWT Token generated:', ['jwt' => $jwt]);

      $response = new Dummy();
      $response['payload'] = $jwt;
      
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function token(Request $request){
      $secretkey = env('PAYMENT_MERCHANT_SECRET_KEY');
      $curl = curl_init();

      curl_setopt_array($curl, array(
        CURLOPT_URL => env('PAYMENT_URL').'paymentToken',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
          "payload": "'.$request->payload.'"
      }',
        CURLOPT_HTTPHEADER => array(
          'Content-Type: application/json'
        ),
      ));

      $res = curl_exec($curl);
      $res = json_decode($res);

      curl_close($curl);
      // echo $res;
      if(isset($res) && isset($res->payload)){
        $response = new Dummy();
        $decode = JWT::decode($res->payload, new Key($secretkey, 'HS256'));
        try {
          if(isset($decode) && isset($decode->respDesc) && $decode->respDesc=='Success' 
          && isset($decode->paymentToken) && isset($decode->webPaymentUrl) && isset($request->payment_order_id)){
            $order = UsersOrderLog::where('id',$request->payment_order_id)->first();
            if($order){
              $order->payment_token = $decode->paymentToken;
              $order->save();
              $response['status'] = 'success';
              $response['payload'] = $res->payload;
              $response['decode'] = $decode;
            }else{
              $response['status'] = 'fail';
            }
          }
        } catch (\Throwable $th) {
          $response['status'] = 'fail';
        }
      }else{
        $response['status'] = 'connection fail';
        $response['res'] =$res;
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }


    public function complete(Request $request){
      $response = new Dummy();
      $response['paymentResponse'] = $request->paymentResponse;
      $decode = json_decode(base64_decode($request->paymentResponse));
      $response['decode'] = $decode;
      if($decode && $decode->respCode && $decode->respCode=='2000'){
        if(isset($request->id)){
          $order = UsersOrderLog::where('id',$request->id)->where('status','!=',2)->first();
          if($order){
            $curl = curl_init();
            curl_setopt_array($curl, array(
              CURLOPT_URL => env('PAYMENT_URL').'TransactionStatus',
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'POST',
              CURLOPT_POSTFIELDS =>'{
                "paymentToken": "'.$order->payment_token.'",
                "additionalInfo": true
              }',
              CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
              ),
            ));
            $res = curl_exec($curl);
            $res = json_decode($res);
            curl_close($curl);
            try {
              if($res && $res->respCode && $res->respCode=='2000' && $res->additionalInfo && $res->additionalInfo->paymentResultDetails && $res->additionalInfo->paymentResultDetails->code && $res->additionalInfo->paymentResultDetails->code=='00'){
                $order->status = 2;
                $order->created_receipt_date = Carbon::now();
                $order->receipt_path = env('APP_URL').'/receipt/'.SimpleEnDeHelper::instance()->encryptString($order->id);
                $order->save();

                $order_history_arr = UsersOrderLog::join('user_order_list','user_order_list.order_id','user_order_log.id')
                ->select('user_order_log.id','user_order_log.receive_email','user_order_log.receive_message','user_order_log.user_id','user_order_log.buy_type','user_order_log.created_receipt_date'
                ,'user_order_list.type','user_order_list.course_id','user_order_list.group_id','user_order_list.subscription_id','user_order_list.id as list_id')
                ->where('user_order_log.status',2)->where('user_order_log.id',$order->id)->get();
                foreach($order_history_arr as $order_history){
                  if($order_history && $order_history->buy_type==1){
                    if($order_history->type==1){
                      $course = Course::where('id',$order_history->course_id)->first();
                      if($course){
                        $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$order_history->course_id)
                        ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                        if(!$check){
                          $history_log = new UserHistory();
                          if($course->trailer_media==2){
                            $history_log->zoom_join_url = $course->zoom_join_url;
                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                          }
                          $history_log->get_type = 2;
                          $history_log->order_list_id = $order_history->list_id;
                          $history_log->user_id = $order_history->user_id;
                          $history_log->course_id = $order_history->course_id;
                          $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                          $history_log->save();
                          $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                          if($del_limit){
                            $del_limit->delete();
                          }
                          $response['status'] = 'success';
                        }
                      }
                    }else if($order_history->type==2){
                      $group = CourseGroup::where('id',$order_history->group_id)->first();
                      if($group){
                        $course = Course::join('group_log','group_log.course_id','course.id')->select('course.id','course.duration_time','course.trailer_media','course.zoom_join_url')->where('group_id',$group->id)->get();
                        foreach($course as $val_course){
                          $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$val_course->id)
                          ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                          if(!$check){
                            $history_log = new UserHistory();
                            if($val_course->trailer_media==2){
                              $history_log->zoom_join_url = $val_course->zoom_join_url;
                              $response['zoom_join_url'] = $history_log->zoom_join_url;
                            }
                            $history_log->get_type = 2;
                            $history_log->order_list_id = $order_history->list_id;
                            $history_log->user_id = $order_history->user_id;
                            $history_log->course_id = $val_course->id;
                            $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                            $history_log->save();
                            $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                            if($del_limit){
                              $del_limit->delete();
                            }
                            $response['status'] = 'success';
                          }
                        }
                      }
                    }else if($order_history->type==3){
                      $subscription = Subscription::where('id',$order_history->subscription_id)->first();
                      if($subscription){
                        $last_sub = SubscriptionLog::where('expired','>=',Carbon::now())->where('user_id',$order_history->user_id)->where('subscription_id',$subscription->id)->orderby('expired','desc')->first();
                        $subscription_log = new SubscriptionLog();
                        $subscription_log->order_list_id = $order_history->list_id;
                        $subscription_log->user_id = $order_history->user_id;
                        $subscription_log->subscription_id = $subscription->id;
                        if($last_sub){
                          $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                        }else{
                          $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                        }
                        $subscription_log->save();
                      }
                    }
                  }
                }

                $email = User::select('id', 'name', 'lastname', 'email')->where('id', $order->user_id)->first();
                $order_list = UsersOrderList::join('user_order_log','user_order_log.id','user_order_list.order_id')
                ->select('user_order_list.*','user_order_log.web_price as discount_web')
                ->where('user_order_list.order_id', $order->id)->get();

                $order_total = 0;
                $discount_web = 0;
                foreach($order_list as $key=>$value){
                  $order_list[$key]['title_th'] = null;
                  $order_list[$key]['subtitle_th'] = null;
                  $order_list[$key]['image_th'] = null;
                  $order_list[$key]['course_link'] = null;
                  if($value->type==1){
                    $course_log = Course::where('id',$value->course_id)->first();
                    if($course_log){
                      $order_list[$key]['title_th'] = $course_log->title_th;
                      $order_list[$key]['subtitle_th'] = $course_log->subtitle_th;
                      $order_list[$key]['image_th'] = $course_log->image_th;
                      $order_list[$key]['course_link'] = 'https://csisociety.com/course/'.$course_log->slug;
                    }
                  }else if($value->type==2){
                    $course_log = CourseGroup::where('id',$value->group_id)->first();
                    if($course_log){
                      $order_list[$key]['title_th'] = $course_log->title;
                      $order_list[$key]['subtitle_th'] = $course_log->details;
                      $order_list[$key]['image_th'] = $course_log->thumb;
                    }
                  }
                  if($value->discount_web!=null&&$value->discount_web!=''&&$value->discount_web!=0&&$value->discount_web!='null'&&$value->discount_web>$discount_web){
                    $discount_web = $value->discount_web;
                  }
                  $order_total += $value->total_price;
                  $order_list[$key]['total_price'] = number_format($value->total_price,2);
                }

                $order_total -= $discount_web;

                if($email){
                  $email_dynamic = EmailDynamic::where('id',2)->where('status',1)->first();
                  if($email_dynamic){
                    $replace_detail = $email_dynamic->details;
                    $replace_detail = str_replace("{{name}}",$email->name,$replace_detail);
                    $replace_detail = str_replace("{{lastname}}",$email->lastname,$replace_detail);
                    $replace_detail = str_replace("{{order_no}}",$order->order_no,$replace_detail);
                    $replace_detail = str_replace("{{order_date}}",$order->created_at,$replace_detail);
                    if($discount_web==0){
                      $replace_detail = str_replace("{{discount_web}}",'',$replace_detail);
                    }else{
                      $replace_detail = str_replace("{{discount_web}}",'ส่วนลดคูปองเงินสด : -'.number_format($discount_web,2).' THB',$replace_detail);
                    }
                    $replace_detail = str_replace("{{link}}",'<a href="https://csisociety.com/dashboard/history" target="_blank">https://csisociety.com/dashboard/history</a>',$replace_detail);
                    if($order->payment_type==1){
                      $replace_detail = str_replace("{{payment_channel}}",'QR Code',$replace_detail);
                    }else if($order->payment_type==2){
                      $replace_detail = str_replace("{{payment_channel}}",'Credit/Debit',$replace_detail);
                    }else{
                      $replace_detail = str_replace("{{payment_channel}}",'โอนเงิน',$replace_detail);
                    }
                    $replace_detail = str_replace("{{order_total}}",number_format($order_total,2),$replace_detail);
                    $order_replace = '';
                    foreach($order_list as $value){
                      if($value->type==1){
                        $order_replace.=
                        '<tr>
                            <td style="width:30px;"></td>
                            <td style="width: 140px">
                              <a href="'.$value->course_link.'">
                                <img src="'.$value->image_th.'" style="width: 140px;">
                              </a>
                            </td>
                            <td style="padding: 10px;">
                              <a href="'.$value->course_link.'">
                                <b>'.$value->title_th.'</b><br>'.$value->subtitle_th.'
                              </a>
                            </td>
                            <td style="width: 100px;text-align: right;"><b>'.$value->total_price.' THB</b></td>
                            <td style="width:30px;"></td>
                        </tr>';
                      }else{
                        $order_replace.=
                        '<tr>
                            <td style="width:30px;"></td>
                            <td style="width: 140px">
                                <img src="'.$value->image_th.'" style="width: 140px;">
                            </td>
                            <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
                            '.$value->subtitle_th.'
                            </td>
                            <td style="width: 100px;text-align: right;"><b>'.$value->total_price.' THB</b></td>
                            <td style="width:30px;"></td>
                        </tr>';
                      }
                    }
                    $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
                    $obj = new Dummy();
                    $obj['subject'] = $email_dynamic->subject;
                    $obj['code'] = $replace_detail;
                    $obj['email'] = $email->email;
                    $obj['cc'] = [];
                    $obj['bcc'] = [];
                    AppHelper::instance()->mailTemplate($obj);
                  }
                }
              }
            } catch (\Throwable $th) {
            }
          }
        }
      }
      return redirect('https://csisociety.com/dashboard/history');
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }


    public function check(Request $request){
      $curl = curl_init();

      curl_setopt_array($curl, array(
        CURLOPT_URL => env('PAYMENT_URL').'TransactionStatus',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
          "paymentToken": "'.$request->paymentToken.'",
          "additionalInfo": true
        }',
        CURLOPT_HTTPHEADER => array(
          'Content-Type: application/json'
        ),
      ));

      $res = curl_exec($curl);
      $res = json_decode($res);

      curl_close($curl);
      // echo $res;

      $response = new Dummy();
      try {
        $response['status'] = 'success';
        $response['res'] = $res;
      } catch (\Throwable $th) {
        $response['status'] = 'fail';
      }
     
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    
    public function paymentCronjob(Request $request){
      $response = new Dummy();
      $response['success_count'] = 0;
      $response['update_info'] = 0;
      $order_get = UsersOrderLog::whereNotNull('user_order_log.payment_token')->where('user_order_log.status',1)->where('user_order_log.updated_at','>=',Carbon::now()->subHours(1))->get();
      foreach($order_get as $order){
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => env('PAYMENT_URL').'TransactionStatus',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>'{
            "paymentToken": "'.$order->payment_token.'",
            "additionalInfo": true
          }',
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
          ),
        ));

        $res = curl_exec($curl);
        $res = json_decode($res);
  
        curl_close($curl);

        try {
          if($res && $res->respCode && $res->respCode=='2000' && $res->additionalInfo && $res->additionalInfo->paymentResultDetails && $res->additionalInfo->paymentResultDetails->code && $res->additionalInfo->paymentResultDetails->code=='00'){
            $order->status = 2;
            $order->created_receipt_date = Carbon::now();
            $order->receipt_path = env('APP_URL').'/receipt/'.SimpleEnDeHelper::instance()->encryptString($order->id);
            $order->save();

            $order_history_arr = UsersOrderLog::join('user_order_list','user_order_list.order_id','user_order_log.id')
            ->select('user_order_log.id','user_order_log.receive_email','user_order_log.receive_message','user_order_log.user_id','user_order_log.buy_type','user_order_log.created_receipt_date'
            ,'user_order_list.type','user_order_list.course_id','user_order_list.group_id','user_order_list.subscription_id','user_order_list.id as list_id')
            ->where('user_order_log.status',2)->where('user_order_log.id',$order->id)->get();
            foreach($order_history_arr as $order_history){
              if($order_history && $order_history->buy_type==1){
                if($order_history->type==1){
                  $course = Course::where('id',$order_history->course_id)->first();
                  if($course){
                    $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$order_history->course_id)
                    ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                    if(!$check){
                      $history_log = new UserHistory();
                      if($course->trailer_media==2){
                        $history_log->zoom_join_url = $course->zoom_join_url;
                        $response['zoom_join_url'] = $history_log->zoom_join_url;
                      }
                      $history_log->get_type = 2;
                      $history_log->order_list_id = $order_history->list_id;
                      $history_log->user_id = $order_history->user_id;
                      $history_log->course_id = $order_history->course_id;
                      $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                      $history_log->save();
                      $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                      if($del_limit){
                        $del_limit->delete();
                      }
                      $response['status'] = 'success';
                    }
                  }
                }else if($order_history->type==2){
                  $group = CourseGroup::where('id',$order_history->group_id)->first();
                  if($group){
                    $course = Course::join('group_log','group_log.course_id','course.id')->select('course.id','course.duration_time','course.trailer_media','course.zoom_join_url')->where('group_id',$group->id)->get();
                    foreach($course as $val_course){
                      $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$val_course->id)
                      ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                      if(!$check){
                        $history_log = new UserHistory();
                        if($val_course->trailer_media==2){
                          $history_log->zoom_join_url = $val_course->zoom_join_url;
                          $response['zoom_join_url'] = $history_log->zoom_join_url;
                        }
                        $history_log->get_type = 2;
                        $history_log->order_list_id = $order_history->list_id;
                        $history_log->user_id = $order_history->user_id;
                        $history_log->course_id = $val_course->id;
                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                        $history_log->save();
                        $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                        if($del_limit){
                          $del_limit->delete();
                        }
                        $response['status'] = 'success';
                      }
                    }
                  }
                }else if($order_history->type==3){
                  $subscription = Subscription::where('id',$order_history->subscription_id)->first();
                  if($subscription){
                    $last_sub = SubscriptionLog::where('expired','>=',Carbon::now())->where('user_id',$order_history->user_id)->where('subscription_id',$subscription->id)->orderby('expired','desc')->first();
                    $subscription_log = new SubscriptionLog();
                    $subscription_log->order_list_id = $order_history->list_id;
                    $subscription_log->user_id = $order_history->user_id;
                    $subscription_log->subscription_id = $subscription->id;
                    if($last_sub){
                      $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                    }else{
                      $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                    }
                    $subscription_log->save();
                  }
                }
              }
            }

            $email = User::select('id', 'name', 'lastname', 'email')->where('id', $order->user_id)->first();
            $order_list = UsersOrderList::join('user_order_log','user_order_log.id','user_order_list.order_id')
            ->select('user_order_list.*','user_order_log.web_price as discount_web')
            ->where('user_order_list.order_id', $order->id)->get();

            $order_total = 0;
            $discount_web = 0;
            foreach($order_list as $key=>$value){
              $order_list[$key]['title_th'] = null;
              $order_list[$key]['subtitle_th'] = null;
              $order_list[$key]['image_th'] = null;
              $order_list[$key]['course_link'] = null;
              if($value->type==1){
                $course_log = Course::where('id',$value->course_id)->first();
                if($course_log){
                  $order_list[$key]['title_th'] = $course_log->title_th;
                  $order_list[$key]['subtitle_th'] = $course_log->subtitle_th;
                  $order_list[$key]['image_th'] = $course_log->image_th;
                  $order_list[$key]['course_link'] = 'https://csisociety.com/course/'.$course_log->slug;
                }
              }else if($value->type==2){
                $course_log = CourseGroup::where('id',$value->group_id)->first();
                if($course_log){
                  $order_list[$key]['title_th'] = $course_log->title;
                  $order_list[$key]['subtitle_th'] = $course_log->details;
                  $order_list[$key]['image_th'] = $course_log->thumb;
                }
              }
              if($value->discount_web!=null&&$value->discount_web!=''&&$value->discount_web!=0&&$value->discount_web!='null'&&$value->discount_web>$discount_web){
                $discount_web = $value->discount_web;
              }
              $order_total += $value->total_price;
              $order_list[$key]['total_price'] = number_format($value->total_price,2);
            }

            $order_total -= $discount_web;

            if($email){
              $email_dynamic = EmailDynamic::where('id',2)->where('status',1)->first();
              if($email_dynamic){
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}",$email->name,$replace_detail);
                $replace_detail = str_replace("{{lastname}}",$email->lastname,$replace_detail);
                $replace_detail = str_replace("{{order_no}}",$order->order_no,$replace_detail);
                $replace_detail = str_replace("{{order_date}}",$order->created_at,$replace_detail);
                if($discount_web==0){
                  $replace_detail = str_replace("{{discount_web}}",'',$replace_detail);
                }else{
                  $replace_detail = str_replace("{{discount_web}}",'ส่วนลดคูปองเงินสด : -'.number_format($discount_web,2).' THB',$replace_detail);
                }
                $replace_detail = str_replace("{{link}}",'<a href="https://csisociety.com/dashboard/history" target="_blank">https://csisociety.com/dashboard/history</a>',$replace_detail);
                if($order->payment_type==1){
                  $replace_detail = str_replace("{{payment_channel}}",'QR Code',$replace_detail);
                }else if($order->payment_type==2){
                  $replace_detail = str_replace("{{payment_channel}}",'Credit/Debit',$replace_detail);
                }else{
                  $replace_detail = str_replace("{{payment_channel}}",'โอนเงิน',$replace_detail);
                }
                $replace_detail = str_replace("{{order_total}}",number_format($order_total,2),$replace_detail);
                $order_replace = '';
                foreach($order_list as $value){
                  if($value->type==1){
                    $order_replace.=
                    '<tr>
                        <td style="width:30px;"></td>
                        <td style="width: 140px">
                          <a href="'.$value->course_link.'">
                            <img src="'.$value->image_th.'" style="width: 140px;">
                          </a>
                        </td>
                        <td style="padding: 10px;">
                          <a href="'.$value->course_link.'">
                            <b>'.$value->title_th.'</b><br>'.$value->subtitle_th.'
                          </a>
                        </td>
                        <td style="width: 100px;text-align: right;"><b>'.$value->total_price.' THB</b></td>
                        <td style="width:30px;"></td>
                    </tr>';
                  }else{
                    $order_replace.=
                    '<tr>
                        <td style="width:30px;"></td>
                        <td style="width: 140px">
                            <img src="'.$value->image_th.'" style="width: 140px;">
                        </td>
                        <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
                        '.$value->subtitle_th.'
                        </td>
                        <td style="width: 100px;text-align: right;"><b>'.$value->total_price.' THB</b></td>
                        <td style="width:30px;"></td>
                    </tr>';
                  }
                }
                $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
                $obj = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code'] = $replace_detail;
                $obj['email'] = $email->email;
                $obj['cc'] = [];
                $obj['bcc'] = [];
                AppHelper::instance()->mailTemplate($obj);
              }
            }
            
            $response['success_count'] += 1;
          }
        } catch (\Throwable $th) {
          
        }
      }
     
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
}
