<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use App\Models\Admin;
use App\Models\Core\Article;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\DynamicPage;
use App\Models\Core\Review;
use App\Models\Core\SettingCenterReview;
use App\Models\Core\SettingContent;
use App\Models\Core\StaticSeminar;
use App\Models\Core\ReviewVideoLog;
use Illuminate\Support\Facades\Auth;

class PageController extends Controller
{

    public function __construct()
    {

    }
    
    public function page(Request $request){
      $response = new Dummy();
      $response['status'] = 'false';
      $response['key'] = $request->key;
      $page = DynamicPage::where('status', 1)->where('key', $request->key)->first();
      if($page){
        $response['data'] = $page;
        $response['status'] = 'success';
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function getReview(Request $request){
      $response = new Dummy();
      $response['status'] = 'success';
      $video = SettingCenterReview::where('status',1)
      ->where(function ($sub) {
        $sub->where('end','>=',Carbon::now());
        $sub->orWhere('end',null);
      })
      ->where(function ($sub) {
        $sub->where('start','<=',Carbon::now());
        $sub->orWhere('start',null);
      })
      ->get();
      foreach($video as $item){
        $item->log = ReviewVideoLog::where('video_id',$item->id)->where('status',1)->orderby('position','asc')->get();
      }
      $response['video'] = $video;
      $response['data'] = Review::where('status',1)->orderby('position','asc')->limit(60)->get();
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function getArticle(Request $request){
      $response = new Dummy();
      $response['status'] = 'success';
      $response['data'] = Article::where('status',1)->orderby('position','asc')->limit(60)->get();
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
}
