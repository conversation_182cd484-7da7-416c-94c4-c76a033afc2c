<?php


namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

use App\Helpers\AppHelper;

use App\Helpers\SimpleEnDeHelper;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\SEO;
use App\Models\Core\Article;
use App\Models\Core\Course;
use App\Models\Core\VolumnBy;

class SeoController extends Controller
{
    public function __construct()
    {
    }

    public function getData(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';
        if (isset($request->id) && ($request->key == 'course' || $request->key == 'article' || $request->key == 'company' )) {
            if ($request->key == 'article') {
                $data = Article::select(
                    'article.id',
                    'article.title_th as title',
                    'article.banner as cover',
                    'article.subtitle_th as description'
                )
                    ->where('article.slug', $request->id)
                    ->first();
            } else if ($request->key == 'course') {
                $data = Course::select(
                    'course.id',
                    'course.title_th as title',
                    'course.subtitle_th as description',
                    'course.og_image as cover',
                    'course.image_th'
                )
                    ->where('course.slug', $request->id)
                    ->first();
                if($data&&($data->cover==null||$data->cover=='')){
                    $data->cover = $data->image_th;
                }
            }else if ($request->key == 'company') {
                $data = VolumnBy::select(
                    'volumn_by.id',
                    'volumn_by.company_title as title',
                    'volumn_by.company_subtitle as description',
                    'volumn_by.meta_image as cover',
                    'volumn_by.company_image as image_th'
                )
                    ->where('volumn_by.slug', $request->id)
                    ->first();
                if($data&&($data->cover==null||$data->cover=='')){
                    $data->cover = $data->image_th;
                }
            }
            if ($data) {
                $response['status'] = 'success';
                $arr = [];
                $obj = new Dummy();
                $obj['name'] = 'title';
                $obj['content'] = $data->title;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'description';
                $obj['content'] = $data->description;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'image';
                $obj['content'] = $data->cover;
                array_push($arr, $obj);


                $obj = new Dummy();
                $obj['name'] = 'author';
                $obj['content'] = null;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'copyright';
                $obj['content'] = null;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'url';
                $obj['content'] = env('APP_NEXT_URL').'/' . $request->key . '/' . $request->id;
                array_push($arr, $obj);

                $response['seo'] = $arr;
                $response['data'] = $data;
            }
        } else {
            $data = SEO::where('key', $request->key)->first();
            if (!$data)
            {
                $data = SEO::where('key','home')->first();
            }
            if ($data) {
                $response['status'] = 'success';
                $arr = [];
                $obj = new Dummy();
                $obj['name'] = 'title';
                $obj['content'] = $data->meta_title;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'description';
                $obj['content'] = $data->meta_description;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'image';
                $obj['content'] = $data->meta_image;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'keywords';
                $obj['content'] = $data->meta_keywords;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'author';
                $obj['content'] = null;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'copyright';
                $obj['content'] = null;
                array_push($arr, $obj);

                $obj = new Dummy();
                $obj['name'] = 'url';
                if ($data->key == 'home') {
                    $obj['content'] = env('APP_NEXT_URL');
                } else {
                    $obj['content'] = env('APP_NEXT_URL').'/' . $data->key;
                }
                array_push($arr, $obj);

                $response['seo'] = $arr;
                $response['data'] = $data;
            }
        }
        return json_decode($response, true);
    }

    public function allCousre(Request $request)
    {
        $dataCourse = Course::select(
            'course.id',
            'course.title_th as title',
            'course.slug as slug'
        )->whereNotNull('slug')->where('course_type',1)->where('trailer_media','!=',5)->where('status',1)->get()->toArray();

        $dataArticle = Article::select(
            'article.id',
            'article.title_th as title',
            'article.slug as slug'
        )->whereNotNull('slug')->where('status',1)->get()->toArray();

        $allData = array();
        foreach($dataCourse as $key=>$val){
            $val['link'] = 'https://csisociety.com/course/'.$val['slug'];
            array_push($allData,$val);
        }
        foreach($dataArticle as $key=>$val){
            $val['link'] = 'https://csisociety.com/article/'.$val['slug'];
            array_push($allData,$val);
        }

        return response()->json($allData, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function allInfographic(Request $request)
    {
        $dataCourse = Course::select(
            'course.id',
            'course.title_th as title',
            'course.slug as slug'
        )->whereNotNull('slug')->where('trailer_media',5)->where('status',1)->get()->toArray();

        return response()->json($dataCourse, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function allArticle(Request $request)
    {
        $dataCourse = Article::select(
                'article.id',
                'article.title_th as title',
                'article.slug as slug'
            )->whereNotNull('slug')->where('status',1)->get()->toArray();

        return response()->json($dataCourse, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }


    
}
