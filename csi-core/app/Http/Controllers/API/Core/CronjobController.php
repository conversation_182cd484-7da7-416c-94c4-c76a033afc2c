<?php


namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

use App\Helpers\AppHelper;

use App\Helpers\SimpleEnDeHelper;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CateSpeaker;
use App\Models\Core\Course;
use App\Models\Core\LessonView;
use App\Models\Core\TopCourseLog;
use App\Models\Core\TopSpeakerLog;

class CronjobController extends Controller
{
    public function __construct()
    {
    }
    public function topCourse(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if(isset($request->page)&&is_numeric($request->page)==1&&isset($request->limit)&&is_numeric($request->limit)==1){
            $offset = (intval($request->page)-1)*intval($request->limit);

            $course = Course::select('course.id')->where('course.started_date','<=',Carbon::now())->where('course.status', 1);
            $course->where(function ($course) {
                $course->where('course.end_date','>=',Carbon::now());
                $course->orWhere('course.end_date',null);
            });
            $course->limit($request->limit)->offset($offset);
            $course = $course->get();

            foreach($course as $key=>$value){
                $count = LessonView::where('course_id',$value->id)->whereDate('created_at', Carbon::today()->subdays(1))->count();
                if($count>0){
                    $log = TopCourseLog::where('course_id',$value->id)->whereDate('date',Carbon::today()->subdays(1))->first();
                    if(!$log){
                        $log = new TopCourseLog();
                        $log->course_id = $value->id;
                        $log->date = Carbon::today()->subdays(1);
                    }
                    $log->count = $count;
                    $log->save();
                }
            }
            $response['data'] = $course;

            $response['status'] = 'success';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function topSpeaker(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if(isset($request->page)&&is_numeric($request->page)==1&&isset($request->limit)&&is_numeric($request->limit)==1){
            $offset = (intval($request->page)-1)*intval($request->limit);

            $speaker = CateSpeaker::join('course_speaker_log','course_speaker_log.speaker','categories_speaker.id')
            ->join('course','course.id','course_speaker_log.course_id')
            ->join('lesson_view','lesson_view.course_id','course.id')
            ->select('categories_speaker.id',DB::raw('COUNT(lesson_view.course_id) as count'))
            ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)
            ->whereDate('lesson_view.created_at', Carbon::today()->subdays(1));
            $speaker->where(function ($speaker) {
                $speaker->where('course.end_date','>=',Carbon::now());
                $speaker->orWhere('course.end_date',null);
            });
            $speaker->limit($request->limit)->offset($offset);
            $speaker = $speaker->get();

            foreach($speaker as $key=>$value){
                if($value->speaker_id!=null && $value->count!=0){
                    $log = TopSpeakerLog::where('speaker_id',$value->id)->whereDate('date',Carbon::today()->subdays(1))->first();
                    if(!$log){
                        $log = new TopSpeakerLog();
                        $log->speaker_id = $value->id;
                        $log->date = Carbon::today()->subdays(1);
                    }
                    $log->count = $value->count;
                    $log->save();
                }
            }
            $response['data'] = $speaker;

            $response['status'] = 'success';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
