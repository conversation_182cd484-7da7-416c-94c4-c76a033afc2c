<?php
namespace App\Http\Controllers\API\Core;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use App\Http\Controllers\Controller;
use App\Mail\ForgotTemplate;
use App\Mail\RegisterSuccess;
use App\Mail\VerifyEmail;
use App\Mail\VerifyTemplate;
use App\Models\Core\CateDepartment;
use App\Models\Core\Categories;
use App\Models\Core\CateInteresting;
use App\Models\Core\CateJobFunction;
use App\Models\Core\CateLearner;
use App\Models\Core\CateLevel;
use App\Models\Core\CateSpeaker;
use App\Models\Core\Course;
use App\Models\Core\CourseGroup;
use App\Models\Core\District;
use App\Models\Core\DynamicCert;
use App\Models\Core\DynamicCertGroup;
use App\Models\Core\EmailDynamic;
use App\Models\Core\GlobalCert;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\NotiGlobal;
use App\Models\Core\OculusPin;
use App\Models\Core\Province;
use App\Models\Core\SubDistrict;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\UserGiftLog;
use App\Models\Core\UserGraduateLog;
use App\Models\Core\UserHistory;
use App\Models\Core\UserInterestingLog;
use App\Models\Core\UserLearnedTime;
use App\Models\Core\UserPlaylist;
use App\Models\Core\Users;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\UsersLearningLog;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\Core\UsersPointLog;
use App\Models\Core\UserVerify;
use App\Models\Core\VolumnBy;
use App\Models\Core\VolumnByCourse;
use App\Models\Core\VolumnByLot;
use App\Models\Core\VolumnByUser;
use App\Models\Dummy;
use App\Models\User;
use App\Models\UserResetPassword;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Intervention\Image\ImageManagerStatic as Image;

class UserController extends Controller
{

    public function __construct()
    {}

    public function login(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        $ip = getenv('HTTP_CLIENT_IP') ?:
        getenv('HTTP_X_FORWARDED_FOR') ?:
        getenv('HTTP_X_FORWARDED') ?:
        getenv('HTTP_FORWARDED_FOR') ?:
        getenv('HTTP_FORWARDED') ?:
        getenv('REMOTE_ADDR');

        $remember   = false;
        $login_type = 'username';
        if (isset($request->remember)) {
            if ($request->remember == 'true') {
                $remember = true;
            }
        }
        if (isset($request->login_type)) {
            $login_type = $request->login_type;
        }

        if (
            $login_type == 'username' ||
            $login_type == 'facebook' ||
            $login_type == 'line' ||
            $login_type == 'google' ||
            $login_type == 'twitter' ||
            $login_type == 'email'
        ) {
            if ($login_type == 'username') {
                $user = User::where('username', (strtolower($request->username)))->first();
            } else if ($login_type == 'email') {
                $user = User::where('email', (strtolower($request->email)))->first();
            } else if ($login_type == 'facebook') {
                $user = User::where('facebook_id', $request->facebook_id)->first();
            } else if ($login_type == 'line') {
                $user = User::where('line_id', $request->line_id)->first();
            } else if ($login_type == 'google') {
                $user = User::where('google_id', $request->google_id)->first();
            } else if ($login_type == 'twitter') {
                $user = User::where('twitter_id', $request->twitter_id)->first();
            }

            if ($user) {
                if ($login_type == 'facebook' || $login_type == 'line' || $login_type == 'google' || $login_type == 'twitter') {
                    if (Auth::guard('user')->loginUsingId($user->id, $remember)) {
                        $response['status'] = 'success';
                        $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);
                    }
                    if ($login_type == 'line') {
                        if (strpos($user->avatar, env('APP_NEXT_URL')) === false) {
                            $user->avatar = isset($request->avatar) ? AppHelper::instance()->cleanInput($request->avatar) : '';
                            $user->save();
                        }
                    }
                } else if ($login_type == 'username') {
                    if (strlen($request->password) >= 6) {
                        if (Auth::guard('user')
                            ->attempt([
                                'username' => (strtolower($request->username)),
                                'password' => $request->password,
                            ], $remember)
                        ) {
                            $response['status'] = 'success';
                            $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);
                        } else {
                            $response['error'] = 'can not login via ' . $login_type;
                        }
                    } else {
                        $response['error'] = $login_type;
                    }
                } else if ($login_type == 'email') {
                    if (filter_var($request->email, FILTER_VALIDATE_EMAIL) && strlen($request->password) >= 6) {
                        if (Auth::guard('user')
                            ->attempt([
                                'email'    => (strtolower($request->email)),
                                'password' => $request->password,
                            ], $remember)
                        ) {
                            if ($user->is_confirm != 1) {
                                $response['status'] = 'unverify';
                            } else {
                                $response['status'] = 'success';
                                $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);
                            }
                        } else {
                            $response['error'] = 'can not login via ' . $login_type;
                        }
                    } else {
                        $response['error'] = $login_type;
                    }
                }
            } else {
                if ($login_type == 'facebook' || $login_type == 'line' || $login_type == 'google' || $login_type == 'twitter') {
                    $user = new User();
                    if ($login_type == 'facebook') {
                        $user->facebook_id = $request->facebook_id;
                    } else if ($login_type == 'line') {
                        $user->line_id = $request->line_id;
                    } else if ($login_type == 'google') {
                        $user->google_id = $request->google_id;
                    } else if ($login_type == 'twitter') {
                        $user->twitter_id = $request->twitter_id;
                    }
                    $user->name   = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : '';
                    $user->avatar = isset($request->avatar) ? AppHelper::instance()->cleanInput($request->avatar) : '';
                    $user->save();
                    if (Auth::guard('user')->loginUsingId($user->id)) {
                        $response['status'] = 'success';
                        $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);
                    } else {
                        $response['status'] = 'error';
                        $response['error']  = 'can not login via ' . $login_type;
                    }
                } else {
                    $response['error'] = 'not_found';
                }
            }
        }
        if ($response['status'] == 'success') {
            if ($user->is_confirm == 2) {
                $response['is_confirm'] = 'false';
            } else {
                $response['is_confirm'] = 'success';
            }
            if ($user->email == null || $user->email == '' || $user->email == 'null' || $user->mobile == null || $user->mobile == '' || $user->mobile == 'null') {
                $response['is_complete'] = 'false';
            } else {
                $response['is_complete'] = 'success';
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function register(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $response['type']   = 'update';
        $response['all']    = $request->all();
        $ip                 = getenv('HTTP_CLIENT_IP') ?:
        getenv('HTTP_X_FORWARDED_FOR') ?:
        getenv('HTTP_X_FORWARDED') ?:
        getenv('HTTP_FORWARDED_FOR') ?:
        getenv('HTTP_FORWARDED') ?:
        getenv('REMOTE_ADDR');

        $response['change_medical'] = false;
        $remember                   = false;
        $login_type                 = '';
        if (isset($request->remember)) {
            if ($request->remember == 'true') {
                $remember = true;
            }
        }
        if (isset($request->login_type)) {
            $login_type = $request->login_type;
        }

        if (
            $login_type == 'username' ||
            $login_type == 'facebook' ||
            $login_type == 'line' ||
            $login_type == 'google' ||
            $login_type == 'twitter' ||
            $login_type == 'email'
        ) {
            $user = false;
            if (isset($request->utoken)) {
                $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            }
            if (! $user) {
                if ($login_type == 'username') {
                    $response['status'] = 'duplicate';
                    $user               = User::where('username', (strtolower($request->username)))->first();
                } else if ($login_type == 'email') {
                    $response['status'] = 'duplicate_email';
                    $user               = User::where('email', (strtolower($request->email)))->first();
                } else if ($login_type == 'facebook') {
                    $user = User::where('facebook_id', $request->facebook_id)->first();
                } else if ($login_type == 'line') {
                    $user = User::where('line_id', $request->line_id)->first();
                } else if ($login_type == 'google') {
                    $user = User::where('google_id', $request->google_id)->first();
                } else if ($login_type == 'twitter') {
                    $user = User::where('twitter_id', $request->twitter_id)->first();
                }
            }

            if ($user && ($login_type != 'facebook' && $login_type != 'line' && $login_type != 'google' && $login_type != 'twitter')) {
                $response['error'] = $login_type;
            } else {
                if ($login_type == 'facebook' || $login_type == 'line' || $login_type == 'google' || $login_type == 'twitter') {
                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                    if ($user) {
                        $user_email = User::where('email', strtolower($request->email))->where('id', '!=', $user->id)->first();
                        if ($user_email) {
                            $response['status'] = 'duplicate_email';
                            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
                        }
                    }
                    if (! $user) {
                        $user                   = new User();
                        $response['type']       = 'create';
                        $response['user_email'] = $request->email;
                    }
                    if ($login_type == 'facebook' && isset($request->facebook_id)) {
                        $user->facebook_id = $request->facebook_id;
                    } else if ($login_type == 'line' && isset($request->line_id)) {
                        $user->line_id = $request->line_id;
                    } else if ($login_type == 'google' && isset($request->google_id)) {
                        $user->google_id = $request->google_id;
                    } else if ($login_type == 'twitter' && isset($request->twitter_id)) {
                        $user->twitter_id = $request->twitter_id;
                    }
                    $user->email            = isset($request->email) ? (strtolower(AppHelper::instance()->cleanInput($request->email))) : '';
                    $user->name             = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : '';
                    $user->lastname         = isset($request->lastname) ? AppHelper::instance()->cleanInput($request->lastname) : '';
                    $user->nickname         = isset($request->nickname) ? AppHelper::instance()->cleanInput($request->nickname) : '';
                    $user->school           = isset($request->school) ? AppHelper::instance()->cleanInput($request->school) : '';
                    $user->class            = isset($request->class) ? AppHelper::instance()->cleanInput($request->class) : '';
                    $user->major            = isset($request->major) ? AppHelper::instance()->cleanInput($request->major) : '';
                    $user->know_ch          = isset($request->know_ch) ? AppHelper::instance()->cleanInput($request->know_ch) : '';
                    $user->mobile           = isset($request->mobile) ? AppHelper::instance()->cleanInput($request->mobile) : '';

                    $user->birthday         = isset($request->birthday) ? AppHelper::instance()->cleanInput($request->birthday) : null;
                    $user->occupation       = isset($request->occupation) ? AppHelper::instance()->cleanInput($request->occupation) : '';
                    $user->company_name     = isset($request->company_name) ? AppHelper::instance()->cleanInput($request->company_name) : '';
                    $user->business_type    = isset($request->business_type) ? AppHelper::instance()->cleanInput($request->business_type) : '';
                    
                    $user->user_type        = isset($request->user_type) ? AppHelper::instance()->cleanInput($request->user_type) : '';
                    $user->province_name    = isset($request->province_name) ? AppHelper::instance()->cleanInput($request->province_name) : '';
                    $user->district_name    = isset($request->district_name) ? AppHelper::instance()->cleanInput($request->district_name) : '';
                    $user->subdistrict_name = isset($request->subdistrict_name) ? AppHelper::instance()->cleanInput($request->subdistrict_name) : '';
                    $user->avatar           = isset($request->avatar) ? AppHelper::instance()->cleanInput($request->avatar) : '';
                    $user->is_confirm       = 1;
                    $user->save();

                    if (isset($request->interesting)) {
                        $tag_id  = [];
                        $all_tag = json_decode($request->interesting);
                        if (is_array($all_tag)) {
                            foreach ($all_tag as $key => $value) {
                                $get_tag = CateInteresting::where('id', $value->key)->where('status', 1)->first();
                                if ($get_tag) {
                                    $tag_log = UserInterestingLog::where('user_id', $user->id)->where('interesting_id', $get_tag->id)->first();
                                    if (! $tag_log) {
                                        $tag_log                 = new UserInterestingLog();
                                        $tag_log->user_id        = $user->id;
                                        $tag_log->interesting_id = $get_tag->id;
                                        $tag_log->save();
                                    }
                                    array_push($tag_id, $get_tag->id);
                                }
                            }
                        }
                        if (count($tag_id) > 0) {
                            $old_tag = UserInterestingLog::where('user_id', $user->id)->whereNotIn('interesting_id', $tag_id)->get();
                            foreach ($old_tag as $del) {
                                $del->delete();
                            }
                        }
                        $user->interesting = implode(",", $tag_id);
                    }
                    if (isset($request->job_function)) {
                        $all_job = json_decode($request->job_function);
                        if (is_array($all_job)) {
                            foreach ($all_job as $key => $value) {
                                $get_job = CateJobFunction::where('id', $value->key)->where('status', 1)->first();
                                if ($get_job) {
                                    $user->job_function = $get_job->id;
                                }
                            }
                        }
                    }
                    $user->save();

                    $response['status'] = 'success';
                    $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);

                    if ($user->verify_email == 2) {
                        $user_verify          = new UserVerify();
                        $user_verify->user_id = $user->id;
                        $user_verify->code    = AppHelper::instance()->generateRandomString(15);
                        $user_verify->save();

                        Mail::to(
                            strtolower($user->email)
                        )->send(new VerifyEmail(
                            [
                                'subject' => 'ยืนยันอีเมล์',
                                'name'    => $user->name . ' ' . $user->lastname,
                                'code'    => $user_verify->code,
                                'email'   => strtolower($user->email),
                            ]
                        ));
                    }
                } else {
                    $user_email = User::where('email', strtolower($request->email))->first();
                    if ($user_email) {
                        $response['status'] = 'duplicate_email';
                        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
                    }

                    $user                   = new User();
                    $response['type']       = 'create';
                    $response['user_email'] = $request->email;
                    $error                  = false;
                    $user->name             = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : '';
                    $user->lastname         = isset($request->lastname) ? AppHelper::instance()->cleanInput($request->lastname) : '';
                    $user->nickname         = isset($request->nickname) ? AppHelper::instance()->cleanInput($request->nickname) : '';
                    $user->school           = isset($request->school) ? AppHelper::instance()->cleanInput($request->school) : '';
                    $user->class            = isset($request->class) ? AppHelper::instance()->cleanInput($request->class) : '';
                    $user->major            = isset($request->major) ? AppHelper::instance()->cleanInput($request->major) : '';
                    $user->know_ch          = isset($request->know_ch) ? AppHelper::instance()->cleanInput($request->know_ch) : '';
                    $user->avatar           = isset($request->avatar) ? AppHelper::instance()->cleanInput($request->avatar) : '';
                    
                    // ฟิลด์ใหม่ที่เพิ่มมา
                    $user->birthday         = isset($request->birthday) ? AppHelper::instance()->cleanInput($request->birthday) : null;
                    $user->occupation       = isset($request->occupation) ? AppHelper::instance()->cleanInput($request->occupation) : '';
                    $user->company_name     = isset($request->company_name) ? AppHelper::instance()->cleanInput($request->company_name) : '';
                    $user->business_type    = isset($request->business_type) ? AppHelper::instance()->cleanInput($request->business_type) : '';
                    
                    if (isset($request->email) && filter_var($request->email, FILTER_VALIDATE_EMAIL)) {
                        $user->email = (strtolower(AppHelper::instance()->cleanInput($request->email)));
                    }
                    if (isset($request->identity_no) && is_numeric($request->identity_no) && strlen($request->identity_no) == 13) {
                        $user->identity_no = AppHelper::instance()->cleanInput($request->identity_no);
                    }
                    if (is_numeric($request->mobile) && strlen($request->mobile) > 9) {
                        $user->mobile = AppHelper::instance()->cleanInput($request->mobile);
                    }
                    $user->user_type = isset($request->user_type) ? AppHelper::instance()->cleanInput($request->user_type) : '';

                    // $user->address = isset($request->address) ? AppHelper::instance()->cleanInput($request->address) : '';
                    // $user->province = isset($request->province) ? AppHelper::instance()->cleanInput($request->province) : '';
                    // $user->district = isset($request->district) ? AppHelper::instance()->cleanInput($request->district) : '';
                    // $user->subdistrict = isset($request->subdistrict) ? AppHelper::instance()->cleanInput($request->subdistrict) : '';
                    // $user->postcode = isset($request->postcode) ? AppHelper::instance()->cleanInput($request->postcode) : '';
                    $user->province_name    = isset($request->province_name) ? AppHelper::instance()->cleanInput($request->province_name) : '';
                    $user->district_name    = isset($request->district_name) ? AppHelper::instance()->cleanInput($request->district_name) : '';
                    $user->subdistrict_name = isset($request->subdistrict_name) ? AppHelper::instance()->cleanInput($request->subdistrict_name) : '';
                    $user->tel              = isset($request->tel) ? AppHelper::instance()->cleanInput($request->tel) : '';
                    $user->id_card          = isset($request->id_card) ? AppHelper::instance()->cleanInput($request->id_card) : '';
                    $user->id_card_file     = isset($request->id_card_file) ? AppHelper::instance()->cleanInput($request->id_card_file) : '';
                    $user->birthday         = isset($request->birthday) ? $request->birthday : null;
                    $user->line_id          = isset($request->line_id) ? $request->line_id : null;
                    $user->is_confirm       = 1;

                    $user_verify = new UserVerify();

                    if ($login_type == 'username') {
                        if (strlen($request->password) >= 6) {
                            $user->username = AppHelper::instance()->cleanInput($request->username);
                            $user->password = Hash::make(AppHelper::instance()->cleanInput($request->password));
                            $user->save();

                            if (isset($request->interesting)) {
                                $tag_id  = [];
                                $all_tag = json_decode($request->interesting);
                                if (is_array($all_tag)) {
                                    foreach ($all_tag as $key => $value) {
                                        $get_tag = CateInteresting::where('id', $value->key)->where('status', 1)->first();
                                        if ($get_tag) {
                                            $tag_log = UserInterestingLog::where('user_id', $user->id)->where('interesting_id', $get_tag->id)->first();
                                            if (! $tag_log) {
                                                $tag_log                 = new UserInterestingLog();
                                                $tag_log->user_id        = $user->id;
                                                $tag_log->interesting_id = $get_tag->id;
                                                $tag_log->save();
                                            }
                                            array_push($tag_id, $get_tag->id);
                                        }
                                    }
                                }
                                if (count($tag_id) > 0) {
                                    $old_tag = UserInterestingLog::where('user_id', $user->id)->whereNotIn('interesting_id', $tag_id)->get();
                                    foreach ($old_tag as $del) {
                                        $del->delete();
                                    }
                                }
                                $user->interesting = implode(",", $tag_id);
                            }
                            if (isset($request->job_function)) {
                                $all_job = json_decode($request->job_function);
                                if (is_array($all_job)) {
                                    foreach ($all_job as $key => $value) {
                                        $get_job = CateJobFunction::where('id', $value->key)->where('status', 1)->first();
                                        if ($get_job) {
                                            $user->job_function = $get_job->id;
                                        }
                                    }
                                }
                            }
                            $user->save();
                            $response['status']   = 'success';
                            $user_verify->user_id = $user->id;
                            $user_verify->code    = AppHelper::instance()->generateRandomString(15);
                            $user_verify->save();
                            $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);

                            Mail::to(
                                strtolower($user->email)
                            )->send(new VerifyTemplate(
                                [
                                    'subject' => 'ยืนยันการลงทะเบียน',
                                    'name'    => $user->name . ' ' . $user->lastname,
                                    'code'    => $user_verify->code,
                                    'email'   => strtolower($user->email),
                                ]
                            ));

                        } else {
                            $response['status'] = 'error';
                            $response['error']  = 'can not login via ' . $login_type;
                        }
                    } else if ($login_type == 'email') {
                        if (filter_var($request->email, FILTER_VALIDATE_EMAIL) && strlen($request->password) >= 6) {
                            $user->email    = (strtolower(AppHelper::instance()->cleanInput($request->email)));
                            $user->password = Hash::make(AppHelper::instance()->cleanInput($request->password));

                            $user->save();

                            if (isset($request->interesting)) {
                                $tag_id  = [];
                                $all_tag = json_decode($request->interesting);
                                if (is_array($all_tag)) {
                                    foreach ($all_tag as $key => $value) {
                                        $get_tag = CateInteresting::where('id', $value->key)->where('status', 1)->first();
                                        if ($get_tag) {
                                            $tag_log = UserInterestingLog::where('user_id', $user->id)->where('interesting_id', $get_tag->id)->first();
                                            if (! $tag_log) {
                                                $tag_log                 = new UserInterestingLog();
                                                $tag_log->user_id        = $user->id;
                                                $tag_log->interesting_id = $get_tag->id;
                                                $tag_log->save();
                                            }
                                            array_push($tag_id, $get_tag->id);
                                        }
                                    }
                                }
                                if (count($tag_id) > 0) {
                                    $old_tag = UserInterestingLog::where('user_id', $user->id)->whereNotIn('interesting_id', $tag_id)->get();
                                    foreach ($old_tag as $del) {
                                        $del->delete();
                                    }
                                }
                                $user->interesting = implode(",", $tag_id);
                            }
                            if (isset($request->job_function)) {
                                $all_job = json_decode($request->job_function);
                                if (is_array($all_job)) {
                                    foreach ($all_job as $key => $value) {
                                        $get_job = CateJobFunction::where('id', $value->key)->where('status', 1)->first();
                                        if ($get_job) {
                                            $user->job_function = $get_job->id;
                                        }
                                    }
                                }
                            }
                            $user->save();
                            $response['status']   = 'success';
                            $user_verify->user_id = $user->id;
                            $user_verify->code    = AppHelper::instance()->generateRandomString(15);
                            $user_verify->save();
                            $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);

                            Mail::to(
                                strtolower($user->email)
                            )->send(new VerifyTemplate(
                                [
                                    'subject' => 'ยืนยันการลงทะเบียน',
                                    'name'    => $user->name . ' ' . $user->lastname,
                                    'code'    => $user_verify->code,
                                    'email'   => strtolower($user->email),
                                ]
                            ));
                        } else {
                            $response['status'] = 'error';
                            $response['error']  = 'can not login via ' . $login_type;
                        }
                    }
                }
            }
        }
        if ($response['type'] == 'create' && $response['status'] == 'success' && isset($user) && $user && $user->email != null && $user->email != 'null' && $user->email != '') {
            Mail::to(
                strtolower($user->email)
            )->send(new RegisterSuccess(
                [
                    'subject' => 'สมัครสมาชิกสำเร็จ',
                    'name'    => $user->name . ' ' . $user->lastname,
                    'email'   => strtolower($user->email),
                ]
            ));
        }
        if (isset($user) && $user) {
            $history = UserHistory::where('user_id', $user->id)->where('get_type', 4)->where('expired', '>=', Carbon::now())->get();
            foreach ($history as $val_his) {
                $val_his->expired = Carbon::now()->subdays(1);
                $val_his->save();
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function profileEdit(Request $request)
    {
        $response                   = new Dummy();
        $response['status']         = 'false';
        $response['change_medical'] = false;
        $ip                         = getenv('HTTP_CLIENT_IP') ?:
        getenv('HTTP_X_FORWARDED_FOR') ?:
        getenv('HTTP_X_FORWARDED') ?:
        getenv('HTTP_FORWARDED_FOR') ?:
        getenv('HTTP_FORWARDED') ?:
        getenv('REMOTE_ADDR');

        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();

            if ($user) {
                $user_email = User::where('email', strtolower($request->email))->where('id', '!=', $user->id)->first();
                if ($user_email) {
                    $response['status'] = 'duplicate_email';
                    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
                }
                if ($user->verify_email == 2) {
                    $user->email = isset($request->email) ? (strtolower(AppHelper::instance()->cleanInput($request->email))) : '';
                }
                $user->name      = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : '';
                $user->lastname  = isset($request->lastname) ? AppHelper::instance()->cleanInput($request->lastname) : '';
                $user->nickname  = isset($request->nickname) ? AppHelper::instance()->cleanInput($request->nickname) : '';
                $user->school    = isset($request->school) ? AppHelper::instance()->cleanInput($request->school) : '';
                $user->class     = isset($request->class) ? AppHelper::instance()->cleanInput($request->class) : '';
                $user->major     = isset($request->major) ? AppHelper::instance()->cleanInput($request->major) : '';
                $user->know_ch   = isset($request->know_ch) ? AppHelper::instance()->cleanInput($request->know_ch) : '';
                $user->mobile    = isset($request->mobile) ? AppHelper::instance()->cleanInput($request->mobile) : '';
                $user->user_type = isset($request->user_type) ? AppHelper::instance()->cleanInput($request->user_type) : '';
                $user->position     = isset($request->position) ? AppHelper::instance()->cleanInput($request->position) : '';
                $user->birthday      = isset($request->birthday) ? AppHelper::instance()->cleanInput($request->birthday) : null;
                $user->occupation    = isset($request->occupation) ? AppHelper::instance()->cleanInput($request->occupation) : '';
                $user->company_name  = isset($request->company_name) ? AppHelper::instance()->cleanInput($request->company_name) : '';
                $user->business_type = isset($request->business_type) ? AppHelper::instance()->cleanInput($request->business_type) : '';

                $user->address          = isset($request->address) ? AppHelper::instance()->cleanInput($request->address) : '';
                $user->province         = isset($request->province) ? AppHelper::instance()->cleanInput($request->province) : '';
                $user->district         = isset($request->district) ? AppHelper::instance()->cleanInput($request->district) : '';
                $user->subdistrict      = isset($request->subdistrict) ? AppHelper::instance()->cleanInput($request->subdistrict) : '';
                $user->postcode         = isset($request->postcode) ? AppHelper::instance()->cleanInput($request->postcode) : '';
                $user->province_name    = isset($request->province_name) ? AppHelper::instance()->cleanInput($request->province_name) : '';
                $user->district_name    = isset($request->district_name) ? AppHelper::instance()->cleanInput($request->district_name) : '';
                $user->subdistrict_name = isset($request->subdistrict_name) ? AppHelper::instance()->cleanInput($request->subdistrict_name) : '';
                $user->avatar           = isset($request->avatar) ? AppHelper::instance()->cleanInput($request->avatar) : '';
                $user->is_confirm       = 1;
                // $user->password = Hash::make(AppHelper::instance()->cleanInput($request->password));
                $user->save();
                $response['status'] = 'success';
                $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);
            }
        }
        if (isset($user) && $user) {
            $history = UserHistory::where('user_id', $user->id)->where('get_type', 4)->where('expired', '>=', Carbon::now())->get();
            foreach ($history as $val_his) {
                $val_his->expired = Carbon::now()->subdays(1);
                $val_his->save();
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function logout(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->encryptString($request->utoken));
            if ($user) {
                $response['status'] = 'success';
                $response['utoken'] = null;
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function forgot(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->email)) {

            if (! empty($request->email)) {
                $user = User::where('email', (strtolower(AppHelper::instance()->cleanInput($request->email))))->first();
                if ($user) {
                    $allow = false;
                    $dup   = UserResetPassword::where('user_id', $user->id)->orderby('id', 'desc')->first();
                    if ($dup) {
                        if (Carbon::now('Asia/Bangkok')->diffInMinutes(Carbon::parse($dup->updated_at)) >= 3) {
                            $allow = true;
                        }
                    } else {
                        $allow = true;
                    }
                    $response['allow'] = $allow;

                    if ($allow) {
                        $response['status'] = 'success';
                        UserResetPassword::where('user_id', $user->id)->delete();
                        $code           = AppHelper::instance()->generateRandomString(30);
                        $model          = new UserResetPassword();
                        $model->user_id = $user->id;
                        $model->token   = $code;
                        $model->save();

                        Mail::to(
                            strtolower($user->email)
                        )->send(new ForgotTemplate(
                            [
                                'subject' => 'เปลี่ยนรหัสผ่านของคุณ',
                                'name'    => $user->name . ' ' . $user->lastname,
                                'code'    => $model->token,
                                'email'   => strtolower($user->email),
                            ]
                        ));
                    } else {
                        $response['status'] = 'dup';
                    }
                } else {
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function resetpass(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->token) && isset($request->password)) {
            if (! empty($request->token) && ! empty($request->password)) {
                $model = UserResetPassword::where('token', $request->token)->first();
                if ($model) {
                    $response['status'] = 'success';
                    $user               = User::find($model->user_id);
                    if ($user) {
                        $user->password = Hash::make($request->password);
                        $user->save();
                    }
                    UserResetPassword::where('user_id', $user->id)->delete();
                } else {
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function profile(Request $request)
    {
        $response               = new Dummy();
        $response['status']     = 'false';
        $response['noti_count'] = 0;
        $response['noti_data']  = [];
        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $user_dept = CateDepartment::select('title_th')->where('id', $user->dept_id)->first();
                if ($user_dept) {
                    $user->department = $user_dept->title_th;
                } else {
                    $user->department = '';
                }
                $user->interesting = UserInterestingLog::join('categories_interesting', 'categories_interesting.id', 'user_interesting_log.interesting_id')
                    ->select('categories_interesting.id as key', 'categories_interesting.id as value', 'categories_interesting.name as label')
                    ->where('categories_interesting.status', 1)->where('user_interesting_log.user_id', $user->id)->get();
                $user->job_function = CateJobFunction::select('categories_job_function.id as key', 'categories_job_function.id as value', 'categories_job_function.name as label')
                    ->where('categories_job_function.status', 1)->where('categories_job_function.id', $user->job_function)->get();
                $response['status'] = 'success';
                $response['user']   = $user;
                $my_course          = UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')->where('user_history.user_id', $user->id)
                // ->where('user_history.expired','>=',Carbon::now())
                // ->where('course.started_date', '<=', Carbon::now())
                    ->where('course.status', '!=', 2)
                    ->where(function ($course) {
                        $course->where('course.end_date', '>=', Carbon::now());
                        $course->orWhere('course.end_date', null);
                    })
                    ->groupby('course.id')->get();
                $response['lesson'] = count($my_course);

                $noti = NotiGlobal::select('image', 'title as title', 'description as description', 'link', 'started_date as date_time')->where('status', 1)->where('started_date', '<=', Carbon::now())->where('end_date', '>=', Carbon::now())->orderby('started_date', 'desc')->limit(99)->get();

                $noti_auto = NotiAutoLog::leftjoin('noti_auto', 'noti_auto.id', 'noti_auto_log.noti_auto_id')
                    ->select(
                        'noti_auto_log.id',
                        'noti_auto.image',
                        'noti_auto.title',
                        'noti_auto.description',
                        'noti_auto.cta',
                        'noti_auto.link',
                        'noti_auto_log.created_at as date_time',
                        'noti_auto_log.noti_auto_id',
                        'noti_auto_log.image as image_log',
                        'noti_auto_log.title as title_log',
                        'noti_auto_log.description as description_log',
                        'noti_auto_log.status as status',
                        'noti_auto_log.link as link_log'
                    )
                    ->where('user_id', $user->id)->orderby('noti_auto_log.created_at', 'desc')->limit(99)->get();

                $notification_data = [];
                foreach ($noti as $key => $val) {
                    if (count($notification_data) < 99) {
                        $val->date = AppHelper::instance()->DateThaiOnly($val->date_time);
                        array_push($notification_data, $val);
                    }
                }

                foreach ($noti_auto as $key_auto => $val_auto) {
                    if (count($notification_data) < 99) {
                        if ($val_auto->noti_auto_id == null || $val_auto->noti_auto_id == '') {
                            $val_auto->image       = $val_auto->image_log;
                            $val_auto->title       = $val_auto->title_log;
                            $val_auto->description = $val_auto->description_log;
                            $val_auto->link        = $val_auto->link_log;
                        }
                        $val_auto->date = AppHelper::instance()->DateThaiOnly($val_auto->date_time);
                        array_push($notification_data, $val_auto);
                    }
                }

                usort($notification_data, function ($a1, $a2) {
                    $v1 = $a1['date_time'];
                    $v2 = $a2['date_time'];
                    return strtotime($v2) - strtotime($v1);
                });

                $response['noti_data'] = $notification_data;
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function getInteresting(Request $request)
    {
        $response         = new Dummy();
        $response['data'] = CateInteresting::select('id as key', 'id as value', 'name as label')
            ->where('status', 1)->get();
        $response['job_function'] = CateJobFunction::select('id as key', 'id as value', 'name as label')
            ->where('status', 1)->get();
        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function verify_email(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->email) && isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $user_email = User::where('email', strtolower($request->email))->where('id', '!=', $user->id)->first();
                if ($user_email) {
                    $response['status'] = 'duplicate_email';
                    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
                }
                $user->email = strtolower(AppHelper::instance()->cleanInput($request->email));
                $user->save();
                $user_verify          = new UserVerify();
                $user_verify->user_id = $user->id;
                $user_verify->code    = AppHelper::instance()->generateRandomString(15);
                $user_verify->save();

                Mail::to(
                    strtolower($user->email)
                )->send(new VerifyEmail(
                    [
                        'subject' => 'ยืนยันอีเมล์',
                        'name'    => $user->name . ' ' . $user->lastname,
                        'code'    => $user_verify->code,
                        'email'   => strtolower($user->email),
                    ]
                ));
                $response['status'] = 'success';
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function verify(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->code)) {

            if (! empty($request->code)) {

                $user_verify = UserVerify::where('code', $request->code)->first();
                if ($user_verify) {
                    if ($user_verify->status == 1) {
                        $user               = User::where('id', $user_verify->user_id)->first();
                        $user->is_confirm   = 1;
                        $user->verify_email = 1;
                        $user->save();

                        $user_verify->status = 2;
                        $user_verify->save();
                        $response['status'] = 'success';
                    } else {
                        $response['status'] = 'already';
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function gift(Request $request)
    {
        $response            = new Dummy();
        $response['status']  = 'false';
        $response['request'] = $request->all();
        if (isset($request->code) && ! empty($request->code)) {
            if (isset($request->utoken)) {
                $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $gift = UserGiftLog::where('id', SimpleEnDeHelper::instance()->decryptString($request->code))->where('receive_email', $user->email)->first();
                    if ($gift) {
                        if ($gift->status == 2) {
                            $gift->status          = 1;
                            $gift->receive_date    = Carbon::now('Asia/Bangkok');
                            $gift->receive_user_id = $user->id;
                            $gift->save();
                            $response['status'] = 'success';

                            $order_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
                                ->select(
                                    'user_order_log.id',
                                    'user_order_log.receive_email',
                                    'user_order_log.receive_message',
                                    'user_order_log.user_id',
                                    'user_order_log.buy_type',
                                    'user_order_log.created_receipt_date',
                                    'user_order_log.created_at',
                                    'user_order_list.type',
                                    'user_order_list.course_id',
                                    'user_order_list.group_id',
                                    'user_order_list.subscription_id',
                                    'user_order_list.id as list_id'
                                )
                                ->where('user_order_log.status', 2)->where('user_order_log.id', $gift->order_id)->get();
                            foreach ($order_arr as $order) {
                                if ($order && $order->buy_type == 2) {
                                    $user_id   = '';
                                    $user_date = '';
                                    $user_data = User::where('email', $order->receive_email)->first();
                                    if ($user_data) {
                                        $user_id   = $user_data->id;
                                        $gift_data = UserGiftLog::where('receive_user_id', $user_data->id)->where('order_id', $order->id)->where('status', 1)->first();
                                        if ($gift_data) {
                                            $user_date = $gift_data->receive_date;
                                        }
                                    }
                                    if ($user_id != '' && $user_date != '') {
                                        if ($order->type == 1) {
                                            $course = Course::where('id', $order->course_id)->first();
                                            if ($course) {
                                                $check = UserHistory::where('order_list_id', $order->list_id)->where('user_id', $user_id)->where('course_id', $order->course_id)
                                                    ->where('expired', Carbon::parse($user_date)->addDays($course->duration_time))->first();
                                                if (! $check) {
                                                    $history_log                = new UserHistory();
                                                    $history_log->get_type      = 3;
                                                    $history_log->order_list_id = $order->list_id;
                                                    $history_log->user_id       = $user_id;
                                                    $history_log->course_id     = $order->course_id;
                                                    $history_log->expired       = Carbon::parse($user_date)->addDays($course->duration_time);
                                                    $history_log->save();
                                                    $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                                                    if ($del_limit) {
                                                        $del_limit->delete();
                                                    }
                                                    $response['status'] = 'success';
                                                }
                                            }
                                        } else if ($order->type == 2) {
                                            $group = CourseGroup::where('id', $order->group_id)->first();
                                            if ($group) {
                                                $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time')->where('group_id', $group->id)->get();
                                                foreach ($course as $val_course) {
                                                    $check = UserHistory::where('order_list_id', $order->list_id)->where('user_id', $user_id)->where('course_id', $val_course->id)
                                                        ->where('expired', Carbon::parse($user_date)->addDays($val_course->duration_time))->first();
                                                    if (! $check) {
                                                        $history_log                = new UserHistory();
                                                        $history_log->get_type      = 3;
                                                        $history_log->order_list_id = $order->list_id;
                                                        $history_log->user_id       = $user_id;
                                                        $history_log->course_id     = $val_course->id;
                                                        $history_log->expired       = Carbon::parse($user_date)->addDays($val_course->duration_time);
                                                        $history_log->save();
                                                        $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                                                        if ($del_limit) {
                                                            $del_limit->delete();
                                                        }
                                                        $response['status'] = 'success';
                                                    }
                                                }
                                            }
                                        } else if ($order->type == 3) {
                                            $subscription = Subscription::where('id', $order->subscription_id)->first();
                                            if ($subscription) {
                                                $last_sub                          = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                                                $subscription_log                  = new SubscriptionLog();
                                                $subscription_log->order_list_id   = $order->list_id;
                                                $subscription_log->user_id         = $user_id;
                                                $subscription_log->subscription_id = $subscription->id;
                                                if ($last_sub) {
                                                    $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                                                } else {
                                                    $subscription_log->expired = Carbon::parse($user_date)->addDays($subscription->period);
                                                }
                                                $subscription_log->save();
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            $response['status'] = 'limit';
                        }
                    }
                } else {
                    $response['status'] = 'not-login';
                }
            } else {
                $response['status'] = 'not-login';
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myFavourite(Request $request)
    {
        $response            = new Dummy();
        $response['utoken']  = $request->utoken;
        $response['orderby'] = $request->orderby;
        $response['status']  = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                if ($request->orderby == 'date') {
                    $user_fap = UsersFavoriteLog::join('course', 'course.id', 'user_favorite_log.course_id')->where('user_id', $user->id)
                        ->select('course.id', 'course.slug', 'course.image_th')
                        ->orderBy('user_favorite_log.created_at', 'desc')
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $user_fap->where(function ($user_fap) {
                        $user_fap->where('course.end_date', '>=', Carbon::now());
                        $user_fap->orWhere('course.end_date', null);
                    });

                    $user_fap = $user_fap->get();

                    $response['data']   = $user_fap;
                    $response['status'] = 'success';
                } else if ($request->orderby == 'price') {
                    $user_fap = UsersFavoriteLog::join('course', 'course.id', 'user_favorite_log.course_id')->where('user_id', $user->id)
                        ->select('course.id', 'course.slug', 'course.image_th')
                        ->orderBy('course.price', 'desc')
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $user_fap->where(function ($user_fap) {
                        $user_fap->where('course.end_date', '>=', Carbon::now());
                        $user_fap->orWhere('course.end_date', null);
                    });

                    $user_fap = $user_fap->get();

                    $response['data']   = $user_fap;
                    $response['status'] = 'success';
                } else if ($request->orderby == 'popular') {
                    $user_fap = UsersFavoriteLog::join('course', 'course.id', 'user_favorite_log.course_id')->where('user_id', $user->id)
                        ->select('course.id', 'course.slug', 'course.image_th', 'course.rate')
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $user_fap->where(function ($user_fap) {
                        $user_fap->where('course.end_date', '>=', Carbon::now());
                        $user_fap->orWhere('course.end_date', null);
                    });

                    $user_fap = $user_fap->get();

                    // $data_rate = array();
                    // foreach ($user_fap as $key => $val) {
                    //   $arr = new Dummy();
                    //   $arr = CourseRate::where('course_id', $val->id)->avg('rate');
                    //   $user_fap[$key]['rate'] = $arr;

                    //   if (!$user_fap[$key]['rate']) {
                    //     $user_fap[$key]['rate'] = 0;
                    //   }
                    //   array_push($data_rate, $val);
                    // }
                    usort($user_fap, function ($a1, $a2) {
                        $v1 = $a1['rate'];
                        $v2 = $a2['rate'];
                        return $v2 - $v1;
                    });
                    $response['data']   = $user_fap;
                    $response['status'] = 'success';
                } else if ($request->orderby == 'atoz') {
                    $user_fap = UsersFavoriteLog::join('course', 'course.id', 'user_favorite_log.course_id')->where('user_id', $user->id)
                        ->select('course.id', 'course.slug', 'course.image_th', 'course.title_th')
                        ->orderBy('course.title_th', 'desc')
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $user_fap->where(function ($user_fap) {
                        $user_fap->where('course.end_date', '>=', Carbon::now());
                        $user_fap->orWhere('course.end_date', null);
                    });

                    $user_fap = $user_fap->get();

                    $response['data']   = $user_fap;
                    $response['status'] = 'success';
                } else {
                    $user_fap = UsersFavoriteLog::join('course', 'course.id', 'user_favorite_log.course_id')->where('user_id', $user->id)
                        ->select('course.id', 'course.slug', 'course.image_th', 'course.title_th')
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $user_fap->where(function ($user_fap) {
                        $user_fap->where('course.end_date', '>=', Carbon::now());
                        $user_fap->orWhere('course.end_date', null);
                    });

                    $user_fap = $user_fap->get();

                    $response['data']   = $user_fap;
                    $response['status'] = 'success';
                }

                $mycourse          = AppHelper::instance()->getMyCourse($user->id);
                $response['count'] = count($mycourse);

                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.started_date',null);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.end_date',null);
                });
                $volume->groupby('volumn_by.id');
                $volume = $volume->pluck('volumn_by.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_by.id', $volume);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                });
                $vol_domain->groupby('volumn_by.id');
                $vol_domain = $vol_domain->pluck('volumn_by.id')->toArray();

                $response['company_count'] = count($volume) + count($vol_domain);

                $my_oculus = count(UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')
                        ->where('course.is_oculus', 1)->where('user_history.user_id', $user->id)
                        ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id')->get());
                $response['oculus_count'] = $my_oculus;
            } else {
                $response['status'] = 'false';
            }
        } else {
            $response['status'] = 'false';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myPlaylist(Request $request)
    {
        $response            = new Dummy();
        $response['utoken']  = $request->utoken;
        $response['orderby'] = $request->orderby;
        $response['status']  = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                if (isset($request->orderby)) {
                    $data_course = UserPlaylist::join('user_playlist_log', 'user_playlist_log.playlist_id', 'user_playlist.id')
                        ->join('course', 'course.id', 'user_playlist_log.course_id')
                        ->select('user_playlist.id as playlist_id', 'course.id', 'course.slug', 'course.image_th', 'course.title_th')
                        ->where('user_playlist.id', $request->orderby)->where('user_playlist.user_id', $user->id)
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $data_course->where(function ($data_course) {
                        $data_course->where('course.end_date', '>=', Carbon::now());
                        $data_course->orWhere('course.end_date', null);
                    });

                    $data_course = $data_course->get();
                } else {
                    $data_course = UserPlaylist::join('user_playlist_log', 'user_playlist_log.playlist_id', 'user_playlist.id')
                        ->join('course', 'course.id', 'user_playlist_log.course_id')
                        ->select('user_playlist.id as playlist_id', 'course.id', 'course.slug', 'course.image_th', 'course.title_th')
                        ->where('user_playlist.user_id', $user->id)
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now())
                        ->groupby('course.id');
                    $data_course->where(function ($data_course) {
                        $data_course->where('course.end_date', '>=', Carbon::now());
                        $data_course->orWhere('course.end_date', null);
                    });

                    $data_course = $data_course->get();
                }
                $response['data']   = $data_course;
                $response['status'] = 'success';

                $mycourse          = AppHelper::instance()->getMyCourse($user->id);
                $response['count'] = count($mycourse);

                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.started_date',null);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.end_date',null);
                });
                $volume->groupby('volumn_by.id');
                $volume = $volume->pluck('volumn_by.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_by.id', $volume);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                });
                $vol_domain->groupby('volumn_by.id');
                $vol_domain = $vol_domain->pluck('volumn_by.id')->toArray();

                $response['company_count'] = count($volume) + count($vol_domain);

                $my_oculus = count(UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')
                        ->where('course.is_oculus', 1)->where('user_history.user_id', $user->id)
                        ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id')->get());
                $response['oculus_count'] = $my_oculus;

                $playlist                = UserPlaylist::select('id as key', 'id as value', 'title as text')->where('user_id', $user->id)->where('status', 1)->get();
                $playlist                = json_decode(json_encode($playlist), true);
                $playlist_dummy          = new Dummy();
                $playlist_dummy['text']  = 'เลือกเพลย์ลิสต์';
                $playlist_dummy['value'] = '';
                $playlist_dummy['key']   = '';
                array_unshift($playlist, $playlist_dummy);

                $response['userPlaylist'] = $playlist;
            } else {
                $response['status'] = 'false';
            }
        } else {
            $response['status'] = 'false';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myCompany(Request $request)
    {
        $response            = new Dummy();
        $response['utoken']  = $request->utoken;
        $response['orderby'] = $request->orderby;
        $response['status']  = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $mycourse          = AppHelper::instance()->getMyCourse($user->id);
                $response['count'] = count($mycourse);

                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.started_date',null);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.end_date',null);
                });
                $volume->groupby('volumn_by.id');
                $volume = $volume->pluck('volumn_by.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_by.id', $volume);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                });
                $vol_domain->groupby('volumn_by.id');
                $vol_domain = $vol_domain->pluck('volumn_by.id')->toArray();

                $response['company_count'] = count($volume) + count($vol_domain);

                $my_oculus = count(UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')
                        ->where('course.is_oculus', 1)->where('user_history.user_id', $user->id)
                        ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id')->get());
                $response['oculus_count'] = $my_oculus;

                $vol_list = [];
                foreach ($volume as $val_q) {
                    $query = VolumnBy::select('volumn_by.id as key', 'volumn_by.id as value', 'volumn_by.company_name as text')
                        ->where('volumn_by.id', $val_q)->where('volumn_by.status', 1)->first();
                    if ($query) {
                        array_push($vol_list, $query);
                    }
                }
                foreach ($vol_domain as $val_q) {
                    $query = VolumnBy::select('volumn_by.id as key', 'volumn_by.id as value', 'volumn_by.company_name as text')
                        ->where('volumn_by.id', $val_q)->where('volumn_by.status', 1)->first();
                    if ($query) {
                        array_push($vol_list, $query);
                    }
                }
                $response['userCompany'] = $vol_list;

                $response['company_link'] = '';

                $course_data = [];
                $comp_id     = '';
                if (isset($request->orderby)) {
                    $comp_id = $request->orderby;
                } else {
                    if (count($vol_list) > 0) {
                        $comp_id = $vol_list[0]['value'];
                    }
                }
                if ($comp_id != '') {
                    $company_data = VolumnBy::where('id', $comp_id)->first();
                    if ($company_data) {
                        $data_course = UserHistory::join('course', 'course.id', 'user_history.course_id')
                            ->select('course.id', 'course.slug', 'course.image_th', 'course.title_th')
                            ->where('user_history.company_id', $comp_id)->where('user_history.user_id', $user->id)->where('user_history.get_type', 6)
                            ->where('course.status', 1)
                            ->where('course.started_date', '<=', Carbon::now())
                            ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id');
                        $data_course->where(function ($data_course) {
                            $data_course->where('course.end_date', '>=', Carbon::now());
                            $data_course->orWhere('course.end_date', null);
                        });

                        $course_data              = $data_course->get();
                        $response['company_link'] = '/company/' . $company_data->slug;
                    }
                }
                $response['data']   = $course_data;
                $response['status'] = 'success';
            }
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myOculus(Request $request)
    {
        $response           = new Dummy();
        $response['utoken'] = $request->utoken;
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $mycourse          = AppHelper::instance()->getMyCourse($user->id);
                $response['count'] = count($mycourse);

                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.started_date',null);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.end_date',null);
                });
                $volume->groupby('volumn_by.id');
                $volume = $volume->pluck('volumn_by.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_by.id', $volume);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                });
                $vol_domain->groupby('volumn_by.id');
                $vol_domain = $vol_domain->pluck('volumn_by.id')->toArray();

                $response['company_count'] = count($volume) + count($vol_domain);

                $my_oculus = UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id', 'course.image_th', 'course.title_th', 'course.subtitle_th', 'course.slug')
                    ->where('course.is_oculus', 1)->where('user_history.user_id', $user->id)
                    ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id')->get();
                $response['oculus_count'] = count($my_oculus);

                $response['data']   = $my_oculus;
                $response['status'] = 'success';
            }
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myCompanyPage(Request $request)
    {
        $response            = new Dummy();
        $response['utoken']  = $request->utoken;
        $response['orderby'] = $request->orderby;
        $response['status']  = 'false';
        $response['limit']   = false;

        if (isset($request->utoken) && isset($request->company_id)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_lot_log.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('volumn_by.id', $request->company_id);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.started_date',null);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.end_date',null);
                });
                $volume->groupby('volumn_lot_log.id');
                $volume = $volume->pluck('volumn_lot_log.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_lot_log.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_lot_log.id', $volume)
                    ->where('volumn_by.id', $request->company_id);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                });
                $vol_domain->groupby('volumn_lot_log.id');
                $vol_domain = $vol_domain->pluck('volumn_lot_log.id')->toArray();

                $vol_list = [];
                foreach ($volume as $val_q) {
                    $query = VolumnByLot::select('volumn_lot_log.*', 'volumn_lot_log.id as key', 'volumn_lot_log.id as value', 'volumn_lot_log.lot_name as text')
                        ->where('volumn_lot_log.id', $val_q)->where('volumn_lot_log.status', 1)->first();
                    if ($query) {
                        array_push($vol_list, $query);
                    }
                }
                foreach ($vol_domain as $val_q) {
                    $query = VolumnByLot::select('volumn_lot_log.*', 'volumn_lot_log.id as key', 'volumn_lot_log.id as value', 'volumn_lot_log.lot_name as text')
                        ->where('volumn_lot_log.id', $val_q)->where('volumn_lot_log.status', 1)->first();
                    if ($query) {
                        array_push($vol_list, $query);
                    }
                }
                $response['userLot'] = $vol_list;

                $course_data = [];
                $lot_id      = '';
                if (isset($request->orderby)) {
                    $lot_id = $request->orderby;
                } else {
                    if (count($vol_list) > 0) {
                        $lot_id = $vol_list[0]['value'];
                    }
                }
                if ($lot_id != '') {
                    $lot_data = VolumnByLot::where('id', $lot_id)->first();
                    if ($lot_data) {
                        $count_user_in = VolumnByUser::where('lot_id', $lot_data->id)->count();
                        if ($lot_data->limit <= $count_user_in) {
                            $check_user_in = VolumnByUser::where('lot_id', $lot_data->id);
                            $check_user_in->where(function ($check_user_in) use ($user) {
                                $check_user_in->where('volumn_user_log.email', $user->email);
                            });
                            $check_user_in = $check_user_in->first();
                            if (! $check_user_in) {
                                $response['limit'] = true;
                            }
                        }
                    }
                    $data_course = VolumnByLot::join('volumn_course_log', 'volumn_course_log.volumn_id', 'volumn_lot_log.id')
                        ->join('course', 'course.id', 'volumn_course_log.course_id')
                        ->select(
                            'course.is_doc',
                            'course.doc_text',
                            'course.is_time',
                            'course.time_set',
                            'course.is_promotion',
                            'course.pro_price',
                            'course.pro_started',
                            'course.pro_end',
                            'course.pro_period',
                            'course.price',
                            'course.started_date',
                            'course.rate',
                            'course.started_learning',
                            'course.is_hot',
                            'course.is_new',
                            'course.receive_point',
                            'course.title_th',
                            'course.topic_type',
                            'course.id',
                            'course.slug',
                            'course.image_th',
                            'course.subtitle_th',
                            'course.is_free',
                            'course.duration_time',
                            'course.created_at',
                            'course.course_duration'
                        )
                        ->where('volumn_lot_log.id', $lot_id)
                        ->where('course.status', 1)
                        ->where('course.started_date', '<=', Carbon::now())
                        ->groupby('course.id');
                    $data_course->where(function ($data_course) {
                        $data_course->where('course.end_date', '>=', Carbon::now());
                        $data_course->orWhere('course.end_date', null);
                    });

                    $course_data = $data_course->get();
                    $course_data = AppHelper::instance()->convertCourse($course_data, $request->utoken);
                }
                $response['data']   = $course_data;
                $response['status'] = 'success';
            }
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myCertificate(Request $request)
    {
        $response           = new Dummy();
        $response['utoken'] = $request->utoken;
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {

                $certificate = [];

                $cert_course = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                    ->select('course.title_th', 'course.image_th', 'cert_dynamic.id', 'cert_dynamic.lang_type', 'user_graduate_log.created_at', 'course.id as course_id')
                    ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                    ->where('user_graduate_log.user_id', $user->id)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->orderby('user_graduate_log.created_at', 'desc');
                $cert_course->where(function ($cert_course) {
                    $cert_course->where('course.end_date', '>=', Carbon::now());
                    $cert_course->orWhere('course.end_date', null);
                });

                //กั้นห้อง
                // $cert_course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                // ->where(function ($cert_course) use($user) {
                //   $cert_course->where('categories_department.id',$user->dept_id);
                //   $cert_course->orWhere('course.is_option',1);
                // })
                // ->where(function ($cert_course){
                //   $cert_course->where('categories_department.status',1);
                //   $cert_course->orWhere('course.is_option',1);
                // });

                $cert_course = $cert_course->get();

                $cert_arr = [];

                foreach ($cert_course as $value) {
                    array_push($cert_arr, $value->course_id);
                    if ($value->lang_type == 1) {
                        $data              = new Dummy();
                        $data['image_th']  = $value->image_th;
                        $data['title_th']  = $value->title_th;
                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                        $data['type']      = 'course';
                        $data['lang_type'] = 'th';
                        array_push($certificate, $data);
                    } else if ($value->lang_type == 2) {
                        $data              = new Dummy();
                        $data['image_th']  = $value->image_th;
                        $data['title_th']  = $value->title_th;
                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                        $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                        $data['type']      = 'course';
                        $data['lang_type'] = 'en';
                        array_push($certificate, $data);
                    } else {
                        $data              = new Dummy();
                        $data['image_th']  = $value->image_th;
                        $data['title_th']  = $value->title_th;
                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                        $data['type']      = 'course';
                        $data['lang_type'] = 'th';
                        array_push($certificate, $data);

                        $data_en              = new Dummy();
                        $data_en['image_th']  = $value->image_th;
                        $data_en['title_th']  = $value->title_th;
                        $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                        $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                        $data_en['type']      = 'course';
                        $data_en['lang_type'] = 'en';
                        array_push($certificate, $data_en);
                    }
                }

                $cert_global = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                    ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                    ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                    ->join('course', 'course.id', 'course_department_log.course_id')
                    ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                    ->select('course.title_th', 'course.image_th', 'cert_global.id', 'cert_global.lang_type', 'user_graduate_log.created_at', 'course.id as course_id')
                    ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                // ->where('categories_department.status',1)
                    ->where('user_graduate_log.user_id', $user->id)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->whereNotIn('course.id', $cert_arr)
                    ->orderby('user_graduate_log.created_at', 'desc');
                $cert_global->where(function ($cert_global) {
                    $cert_global->where('course.end_date', '>=', Carbon::now());
                    $cert_global->orWhere('course.end_date', null);
                });

                //กั้นห้อง
                // $cert_global->where(function ($cert_global) use($user) {
                //   $cert_global->where('categories_department.id',$user->dept_id);
                //   $cert_global->orWhere('course.is_option',1);
                // })
                // ->where(function ($cert_global){
                //   $cert_global->where('categories_department.status',1);
                //   $cert_global->orWhere('course.is_option',1);
                // });

                $cert_global = $cert_global->get();

                foreach ($cert_global as $value) {
                    if ($value->lang_type == 1) {
                        $data              = new Dummy();
                        $data['image_th']  = $value->image_th;
                        $data['title_th']  = $value->title_th;
                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                        $data['type']      = 'global';
                        $data['lang_type'] = 'th';
                        array_push($certificate, $data);
                    } else if ($value->lang_type == 2) {
                        $data              = new Dummy();
                        $data['image_th']  = $value->image_th;
                        $data['title_th']  = $value->title_th;
                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                        $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                        $data['type']      = 'global';
                        $data['lang_type'] = 'en';
                        array_push($certificate, $data);
                    } else {
                        $data              = new Dummy();
                        $data['image_th']  = $value->image_th;
                        $data['title_th']  = $value->title_th;
                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                        $data['type']      = 'global';
                        $data['lang_type'] = 'th';
                        array_push($certificate, $data);

                        $data_en              = new Dummy();
                        $data_en['image_th']  = $value->image_th;
                        $data_en['title_th']  = $value->title_th;
                        $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                        $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                        $data_en['type']      = 'global';
                        $data_en['lang_type'] = 'en';
                        array_push($certificate, $data_en);
                    }
                }

                $cert_group = DynamicCertGroup::join('course_group', 'course_group.id', 'cert_group_dynamic.series_id')
                    ->join('group_log', 'group_log.group_id', 'course_group.id')
                    ->join('course', 'course.id', 'group_log.course_id')
                    ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                    ->select('cert_group_dynamic.series_name as title_th', 'course_group.thumb as image_th', 'cert_group_dynamic.id', 'cert_group_dynamic.lang_type', 'user_graduate_log.created_at', 'course_group.id as group_id')
                    ->where('cert_group_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->where('user_graduate_log.user_id', $user->id)
                    ->orderby('user_graduate_log.created_at', 'desc')->groupby('course_group.id');
                $cert_group->where(function ($cert_group) {
                    $cert_group->where('course.end_date', '>=', Carbon::now());
                    $cert_group->orWhere('course.end_date', null);
                });

                //กั้นห้อง
                // $cert_group->leftjoin('course_department_log','course_department_log.course_id','course.id')
                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                // ->where(function ($cert_group) use($user) {
                //   $cert_group->where('categories_department.id',$user->dept_id);
                //   $cert_group->orWhere('course.is_option',1);
                // })
                // ->where(function ($cert_group){
                //   $cert_group->where('categories_department.status',1);
                //   $cert_group->orWhere('course.is_option',1);
                // });

                $cert_group = $cert_group->get();

                foreach ($cert_group as $value) {
                    $check_fail = 0;
                    $group_data = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                        ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $group_data->where(function ($group_data) {
                        $group_data->where('course.end_date', '>=', Carbon::now());
                        $group_data->orWhere('course.end_date', null);
                    });

                    $group_data->leftjoin('course_department_log', 'course_department_log.course_id', 'course.id')
                        ->leftjoin('categories_department', 'categories_department.id', 'course_department_log.department')
                        ->where(function ($group_data) use ($user) {
                            $group_data->where('categories_department.id', $user->dept_id);
                            $group_data->orWhere('course.is_option', 1);
                        })
                        ->where(function ($group_data) {
                            $group_data->where('categories_department.status', 1);
                            $group_data->orWhere('course.is_option', 1);
                        });

                    $group_data = $group_data->get();
                    foreach ($group_data as $sub) {
                        $check_pass = UserGraduateLog::where('user_id', $user->id)->where('course_id', $sub->id)->first();
                        if (! $check_pass) {
                            $check_fail++;
                            break;
                        }
                    }
                    if ($check_fail == 0 && count($group_data) > 0) {
                        if ($value->lang_type == 1) {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data['type']      = 'group';
                            $data['lang_type'] = 'th';
                            array_push($certificate, $data);
                        } else if ($value->lang_type == 2) {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                            $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data['type']      = 'group';
                            $data['lang_type'] = 'en';
                            array_push($certificate, $data);
                        } else {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data['type']      = 'group';
                            $data['lang_type'] = 'th';
                            array_push($certificate, $data);

                            $data_en              = new Dummy();
                            $data_en['image_th']  = $value->image_th;
                            $data_en['title_th']  = $value->title_th;
                            $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                            $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data_en['type']      = 'group';
                            $data_en['lang_type'] = 'en';
                            array_push($certificate, $data_en);
                        }
                    }
                }

                usort($certificate, function ($a1, $a2) {
                    $v1 = $a1['created_at'];
                    $v2 = $a2['created_at'];
                    return strtotime($v2) - strtotime($v1);
                });

                $response['data'] = $certificate;

                $response['status'] = 'success';
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function mySubscription(Request $request)
    {
        $response           = new Dummy();
        $response['utoken'] = $request->utoken;
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                // ถ้า type = subscription_main จะแสดงข้อมูลแบบ subscription log
                if (isset($request->type) && $request->type === 'subscription_main') {
                    $subscription_main_data = \DB::table('subscription_log')
                        ->join('user', 'user.id', '=', 'subscription_log.user_id')
                        ->join('subscription', 'subscription.id', '=', 'subscription_log.subscription_id')
                        ->leftJoin('subscription_main', 'subscription_main.id', '=', 'subscription.subscription_main_id')
                        ->select(
                            \DB::raw('GROUP_CONCAT(subscription_log.id ORDER BY subscription_log.created_at DESC) as grouped_ids'),
                            'user.email',
                            'user.name as u_name',
                            'user.lastname as u_lastname',
                            'user.mobile',
                            \DB::raw('COALESCE(subscription_main.title, subscription.title) as package'),
                            'subscription_main.description as description',
                            'subscription_main.short_description as short_description',
                            \DB::raw('SUM(subscription.period) as period_days'),
                            \DB::raw('MAX(subscription_log.expired) as end_date'),
                            \DB::raw('MIN(subscription_log.created_at) as first_created_at'),
                            \DB::raw('MAX(subscription_log.created_at) as latest_created_at'),
                            \DB::raw('COUNT(*) as subscription_count')
                        )
                        ->where('subscription_log.user_id', $user->id)
                        ->groupBy([
                            'user.email',
                            \DB::raw('COALESCE(subscription_main.title, subscription.title)'),
                            'subscription_main.description',
                            'subscription_main.short_description',
                            'user.name',
                            'user.lastname', 
                            'user.mobile'
                        ])
                        ->orderBy('latest_created_at', 'desc')
                        ->get();

                    $formatted_subscription_main = [];
                    foreach ($subscription_main_data as $row) {
                        $startDate = null;
                        if (!empty($row->end_date) && !empty($row->period_days)) {
                            $startDate = \Carbon\Carbon::parse($row->end_date)->copy()->subDays((int) $row->period_days);
                        }

                        $leftDays = 0;
                        if (!empty($row->end_date)) {
                            $leftDays = max(0, \Carbon\Carbon::now()->diffInDays(\Carbon\Carbon::parse($row->end_date), false));
                        }

                        $packageDisplay = $row->package;

                        $isBuy = false;
                        if (!empty($row->grouped_ids)) {
                            $subscriptionLogIds = explode(',', $row->grouped_ids);
                            $activeSubscription = \DB::table('subscription_log')
                                ->whereIn('subscription_log.id', $subscriptionLogIds)
                                ->where('subscription_log.user_id', $user->id)
                                ->where('subscription_log.expired', '>=', \Carbon\Carbon::now())
                                ->exists();
                            $isBuy = $activeSubscription;
                        }

                        $subscriptions = [];
                        if (!empty($row->grouped_ids)) {
                            $subscriptionLogIds = explode(',', $row->grouped_ids);
                            $subscriptionDetails = \DB::table('subscription_log')
                                ->join('subscription', 'subscription.id', '=', 'subscription_log.subscription_id')
                                ->whereIn('subscription_log.id', $subscriptionLogIds)
                                ->where('subscription_log.user_id', $user->id)
                                ->select(
                                    'subscription.id',
                                    'subscription.title',
                                    'subscription.price',
                                    'subscription.period',
                                    'subscription.details',
                                    'subscription.type',
                                    'subscription.status',
                                    'subscription.position',
                                    'subscription_log.expired',
                                    'subscription_log.created_at as purchased_at'
                                )
                                ->orderBy('subscription_log.created_at', 'desc')
                                ->get();

                            foreach ($subscriptionDetails as $subDetail) {
                                $isSubBuy = \Carbon\Carbon::parse($subDetail->expired) >= \Carbon\Carbon::now();
                                
                                $subscriptions[] = [
                                    'id' => $subDetail->id,
                                    'title' => $subDetail->title,
                                    'price' => $subDetail->price,
                                    'period' => $subDetail->period,
                                    'description' => $subDetail->details,
                                    'type' => $subDetail->type,
                                    'status' => $subDetail->status,
                                    'position' => $subDetail->position,
                                    'is_buy' => $isSubBuy,
                                    'expire_date' => $subDetail->expired,
                                    'expire_count' => $isSubBuy ? \Carbon\Carbon::now()->diffInDays(\Carbon\Carbon::parse($subDetail->expired)) : 0,
                                    'purchased_at' => $subDetail->purchased_at,
                                ];
                            }
                        }

                        $formatted_subscription_main[] = [
                            'email' => $row->email,
                            'u_name' => $row->u_name,
                            'u_lastname' => $row->u_lastname,
                            'mobile' => $row->mobile,
                            'package' => $packageDisplay,
                            'detail' => $row->description ?? '',
                            'short_detail' => $row->short_description ?? '',
                            'start_date' => $startDate ? $startDate->toDateTimeString() : '-',
                            'end_date' => $row->end_date,
                            'period_days' => $row->period_days,
                            'left_days' => $leftDays,
                            'subscription_count' => $row->subscription_count,
                            'grouped_ids' => $row->grouped_ids,
                            'is_buy' => $isBuy,
                            'subscriptions' => $subscriptions,
                        ];
                    }

                    $response['data'] = $formatted_subscription_main;
                    $response['type'] = 'subscription_main';
                    $response['status'] = 'success';
                    
                    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
                }

                $subscription_data = Subscription::where('type', 1)
                    ->where('status', 1)
                    ->orderby('position', 'asc')
                    ->get();

                foreach ($subscription_data as $key => $subscription) {
                    $check_sub = SubscriptionLog::where('user_id', $user->id)
                        ->where('subscription_id', $subscription->id)
                        ->where('expired', '>=', Carbon::now())
                        ->orderby('expired', 'desc')
                        ->first();

                    $subscription_data[$key]['is_buy'] = $check_sub ? true : false;

                    if ($check_sub) {
                        $subscription_data[$key]['expire_date']  = $check_sub->expired;
                        $subscription_data[$key]['expire_count'] = Carbon::now()->diffInDays(Carbon::parse($check_sub->expired));
                    }
                }

                $response['subscription'] = $subscription_data;

                $mysub                    = AppHelper::instance()->getMySubscription($user->id);
                $response['data']         = $mysub;
                $response['expire_count'] = '';
                $check_last_sub           = SubscriptionLog::join('subscription', 'subscription.id', 'subscription_log.subscription_id')
                    ->select('subscription_log.*')
                    ->where('subscription.status', 1)
                    ->where('subscription_log.user_id', $user->id)
                    ->where('subscription_log.expired', '>=', Carbon::now())
                    ->orderby('subscription_log.expired', 'desc')
                    ->first();

                if ($check_last_sub) {
                    $response['expire_count'] = Carbon::now()->diffInDays(Carbon::parse($check_last_sub->expired));
                }

                $mycourse          = AppHelper::instance()->getMyCourse($user->id);
                $response['count'] = count($mycourse);

                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                });
                $volume->groupby('volumn_by.id');
                $volume = $volume->pluck('volumn_by.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_by.id', $volume);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                });
                $vol_domain->groupby('volumn_by.id');
                $vol_domain = $vol_domain->pluck('volumn_by.id')->toArray();

                $response['company_count'] = count($volume) + count($vol_domain);

                $my_oculus = count(UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')
                        ->where('course.is_oculus', 1)->where('user_history.user_id', $user->id)
                        ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id')->get());
                $response['oculus_count'] = $my_oculus;

                $response['status'] = 'success';
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function pageSubscription(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        $subscription_data = Subscription::where('type', 1)->where('status', 1)->orderby('position', 'asc')->get();
        if ($subscription_data) {
            $response['data']   = $subscription_data;
            $response['status'] = 'success';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myOrder(Request $request)
    {
        $response           = new Dummy();
        $response['utoken'] = $request->utoken;
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {

                $order = UsersOrderLog::where('user_order_log.user_id', $user->id)
                    ->select(
                        'user_order_log.status as order_status',
                        'user_order_log.total_price as price',
                        'user_order_log.created_at as order_date',
                        'user_order_log.receipt_path as receipt',
                        'user_order_log.id as order_id',
                        'user_order_log.order_no as order_number',
                        'user_order_log.payment_type as payment_type',
                        'user_order_log.qr_id as qr_id'
                    )
                    ->where('user_order_log.is_free', 2)
                    ->get();
                foreach ($order as $key => $val) {
                    $order[$key]['order_item'] = '';
                    $order_log                 = UsersOrderList::where('order_id', $val->order_id)->get();
                    foreach ($order_log as $key_log => $val_log) {
                        if ($order[$key]['order_item'] != '') {
                            $order[$key]['order_item'] .= '<br>';
                        }
                        if ($val_log->type == 1) {
                            $val_item = Course::where('id', $val_log->course_id)->first();
                            if ($val_item) {
                                $order[$key]['order_item'] .= $val_item->title_th;
                            }
                        } else if ($val_log->type == 2) {
                            $val_item = CourseGroup::where('id', $val_log->group_id)->first();
                            if ($val_item) {
                                $order[$key]['order_item'] .= $val_item->title;
                            }
                        } else if ($val_log->type == 3) {
                            $val_item = Subscription::where('id', $val_log->subscription_id)->first();
                            if ($val_item) {
                                $order[$key]['order_item'] .= $val_item->title;
                            }
                        }
                    }
                    if ($val->order_status == 1) {
                        if ($val->payment_type == 1 || $val->payment_type == 2) {
                            $order[$key]['order_status'] = 'ชำระเงิน';
                        } else {
                            $order[$key]['order_status'] = 'ยืนยันชำระเงิน';
                        }
                    } else if ($val->order_status == 2) {
                        $order[$key]['order_status'] = 'ชำระเงินสำเร็จ';
                    } else if ($val->order_status == 3) {
                        $order[$key]['order_status'] = 'ยกเลิก';
                    } else if ($val->order_status == 4) {
                        $order[$key]['order_status'] = 'รอการตรวจสอบ';
                    }
                    $order[$key]['order_no'] = SimpleEnDeHelper::instance()->encryptString($val->order_id);
                }

                $response['data']   = $order;
                $response['status'] = 'success';

                $mycourse          = AppHelper::instance()->getMyCourse($user->id);
                $response['count'] = count($mycourse);

                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.started_date',null);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.end_date',null);
                });
                $volume->groupby('volumn_by.id');
                $volume = $volume->pluck('volumn_by.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_by.id', $volume);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                });
                $vol_domain->groupby('volumn_by.id');
                $vol_domain = $vol_domain->pluck('volumn_by.id')->toArray();

                $response['company_count'] = count($volume) + count($vol_domain);

                $my_oculus = count(UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')
                        ->where('course.is_oculus', 1)->where('user_history.user_id', $user->id)
                        ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id')->get());
                $response['oculus_count'] = $my_oculus;
            } else {
                $response['status'] = 'false';
            }
        } else {
            $response['status'] = 'false';
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function myCourse(Request $request)
    {
        $response           = new Dummy();
        $response['utoken'] = $request->utoken;
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $mycourse          = AppHelper::instance()->getMyCourse($user->id);
                $response['count'] = count($mycourse);

                $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1);
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.email', $user->email);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.started_date',null);
                });
                $volume->where(function ($volume) {
                    $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $volume->orWhere('volumn_lot_log.end_date',null);
                });
                $volume->groupby('volumn_by.id');
                $volume = $volume->pluck('volumn_by.id')->toArray();

                $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                    ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                    ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                    ->select('volumn_by.id')
                    ->where('volumn_lot_log.status', 1)
                    ->where('volumn_by.status', 1)
                    ->where('categories_domain.status', 1)
                    ->whereNotIn('volumn_by.id', $volume);
                $vol_domain->where(function ($vol_domain) use ($user) {
                    if (count(explode("@", $user->email)) == 2) {
                        $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                    }
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                });
                $vol_domain->where(function ($vol_domain) {
                    $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                });
                $vol_domain->groupby('volumn_by.id');
                $vol_domain = $vol_domain->pluck('volumn_by.id')->toArray();

                $response['company_count'] = count($volume) + count($vol_domain);

                $my_oculus = count(UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')
                        ->where('course.is_oculus', 1)->where('user_history.user_id', $user->id)
                        ->where('user_history.expired', '>=', Carbon::now())->groupby('course.id')->get());
                $response['oculus_count'] = $my_oculus;

                $response['data']   = $mycourse;
                $response['status'] = 'success';
            } else {
                $response['status'] = 'false';
            }
        } else {
            $response['status'] = 'false';
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function courseExpire(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $email_dynamic      = EmailDynamic::where('id', 7)->where('status', 1)->first();
        if ($email_dynamic) {

            $expire_365 = UserHistory::join('course', 'course.id', 'user_history.course_id')
                ->join('user', 'user.id', 'user_history.user_id')
                ->select('user.name', 'user.lastname', 'user.email', 'course.slug', 'course.title_th', 'course.id as course_id')
                ->where('user.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)
                ->where('course.duration_time', 365)
                ->whereDate('user_history.expired', Carbon::now()->addDays(30))
                ->where('user_history.type', 1);
            $expire_365->where(function ($expire_365) {
                $expire_365->where('course.end_date', '>=', Carbon::now());
                $expire_365->orWhere('course.end_date', null);
            });

            $expire_365 = $expire_365->get();
            foreach ($expire_365 as $value) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $value->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $value->lastname, $replace_detail);
                $replace_detail = str_replace("{{course}}", $value->title_th, $replace_detail);
                $replace_detail = str_replace("{{date}}", '30', $replace_detail);
                $replace_detail = str_replace("{{link}}", '<a href="' . env('APP_NEXT_URL') . '/course/' . $value->slug . '" target="_blank">' . env('APP_NEXT_URL') . '/course/' . $value->slug . '</a>', $replace_detail);
                $obj            = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code']    = $replace_detail;
                $obj['email']   = $value->email;
                $obj['cc']      = [];
                $obj['bcc']     = [];
                AppHelper::instance()->mailTemplate($obj);
                $response['send'] = 'success';
            }

            $expire_180 = UserHistory::join('course', 'course.id', 'user_history.course_id')
                ->join('user', 'user.id', 'user_history.user_id')
                ->select('user.name', 'user.lastname', 'user.email', 'course.slug', 'course.title_th', 'course.id as course_id')
                ->where('user.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)
                ->where('course.duration_time', 180)
                ->whereDate('user_history.expired', Carbon::now()->addDays(15))
                ->where('user_history.type', 1);
            $expire_180->where(function ($expire_180) {
                $expire_180->where('course.end_date', '>=', Carbon::now());
                $expire_180->orWhere('course.end_date', null);
            });

            $expire_180 = $expire_180->get();
            foreach ($expire_180 as $value) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $value->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $value->lastname, $replace_detail);
                $replace_detail = str_replace("{{course}}", $value->title_th, $replace_detail);
                $replace_detail = str_replace("{{date}}", '15', $replace_detail);
                $replace_detail = str_replace("{{link}}", '<a href="' . env('APP_NEXT_URL') . '/course/' . $value->slug . '" target="_blank">' . env('APP_NEXT_URL') . '/course/' . $value->slug . '</a>', $replace_detail);
                $obj            = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code']    = $replace_detail;
                $obj['email']   = $value->email;
                $obj['cc']      = [];
                $obj['bcc']     = [];
                AppHelper::instance()->mailTemplate($obj);
                $response['send'] = 'success';
            }

            $expire_90 = UserHistory::join('course', 'course.id', 'user_history.course_id')
                ->join('user', 'user.id', 'user_history.user_id')
                ->select('user.name', 'user.lastname', 'user.email', 'course.slug', 'course.title_th', 'course.id as course_id')
                ->where('user.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)
                ->where('course.duration_time', 90)
                ->whereDate('user_history.expired', Carbon::now()->addDays(10))
                ->where('user_history.type', 1);
            $expire_90->where(function ($expire_90) {
                $expire_90->where('course.end_date', '>=', Carbon::now());
                $expire_90->orWhere('course.end_date', null);
            });

            $expire_90 = $expire_90->get();
            foreach ($expire_90 as $value) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $value->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $value->lastname, $replace_detail);
                $replace_detail = str_replace("{{course}}", $value->title_th, $replace_detail);
                $replace_detail = str_replace("{{date}}", '10', $replace_detail);
                $replace_detail = str_replace("{{link}}", '<a href="' . env('APP_NEXT_URL') . '/course/' . $value->slug . '" target="_blank">' . env('APP_NEXT_URL') . '/course/' . $value->slug . '</a>', $replace_detail);
                $obj            = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code']    = $replace_detail;
                $obj['email']   = $value->email;
                $obj['cc']      = [];
                $obj['bcc']     = [];
                AppHelper::instance()->mailTemplate($obj);
                $response['send'] = 'success';
            }

            $expire_60 = UserHistory::join('course', 'course.id', 'user_history.course_id')
                ->join('user', 'user.id', 'user_history.user_id')
                ->select('user.name', 'user.lastname', 'user.email', 'course.slug', 'course.title_th', 'course.id as course_id')
                ->where('user.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)
                ->where('course.duration_time', 60)
                ->whereDate('user_history.expired', Carbon::now()->addDays(7))
                ->where('user_history.type', 1);
            $expire_60->where(function ($expire_60) {
                $expire_60->where('course.end_date', '>=', Carbon::now());
                $expire_60->orWhere('course.end_date', null);
            });

            $expire_60 = $expire_60->get();
            foreach ($expire_60 as $value) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $value->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $value->lastname, $replace_detail);
                $replace_detail = str_replace("{{course}}", $value->title_th, $replace_detail);
                $replace_detail = str_replace("{{date}}", '7', $replace_detail);
                $replace_detail = str_replace("{{link}}", '<a href="' . env('APP_NEXT_URL') . '/course/' . $value->slug . '" target="_blank">' . env('APP_NEXT_URL') . '/course/' . $value->slug . '</a>', $replace_detail);
                $obj            = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code']    = $replace_detail;
                $obj['email']   = $value->email;
                $obj['cc']      = [];
                $obj['bcc']     = [];
                AppHelper::instance()->mailTemplate($obj);
                $response['send'] = 'success';
            }

            $expire_30 = UserHistory::join('course', 'course.id', 'user_history.course_id')
                ->join('user', 'user.id', 'user_history.user_id')
                ->select('user.name', 'user.lastname', 'user.email', 'course.slug', 'course.title_th', 'course.id as course_id')
                ->where('user.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)
                ->where('course.duration_time', 30)
                ->whereDate('user_history.expired', Carbon::now()->addDays(7))
                ->where('user_history.type', 1);
            $expire_30->where(function ($expire_30) {
                $expire_30->where('course.end_date', '>=', Carbon::now());
                $expire_30->orWhere('course.end_date', null);
            });

            $expire_30 = $expire_30->get();
            foreach ($expire_30 as $value) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $value->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $value->lastname, $replace_detail);
                $replace_detail = str_replace("{{course}}", $value->title_th, $replace_detail);
                $replace_detail = str_replace("{{date}}", '7', $replace_detail);
                $replace_detail = str_replace("{{link}}", '<a href="' . env('APP_NEXT_URL') . '/course/' . $value->slug . '" target="_blank">' . env('APP_NEXT_URL') . '/course/' . $value->slug . '</a>', $replace_detail);
                $obj            = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code']    = $replace_detail;
                $obj['email']   = $value->email;
                $obj['cc']      = [];
                $obj['bcc']     = [];
                AppHelper::instance()->mailTemplate($obj);
                $response['send'] = 'success';
            }

            $expire_10 = UserHistory::join('course', 'course.id', 'user_history.course_id')
                ->join('user', 'user.id', 'user_history.user_id')
                ->select('user.name', 'user.lastname', 'user.email', 'course.slug', 'course.title_th', 'course.id as course_id')
                ->where('user.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)
                ->where('course.duration_time', 10)
                ->whereDate('user_history.expired', Carbon::now()->addDays(3))
                ->where('user_history.type', 1);
            $expire_10->where(function ($expire_10) {
                $expire_10->where('course.end_date', '>=', Carbon::now());
                $expire_10->orWhere('course.end_date', null);
            });

            $expire_10 = $expire_10->get();
            foreach ($expire_10 as $value) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $value->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $value->lastname, $replace_detail);
                $replace_detail = str_replace("{{course}}", $value->title_th, $replace_detail);
                $replace_detail = str_replace("{{date}}", '3', $replace_detail);
                $replace_detail = str_replace("{{link}}", '<a href="' . env('APP_NEXT_URL') . '/course/' . $value->slug . '" target="_blank">' . env('APP_NEXT_URL') . '/course/' . $value->slug . '</a>', $replace_detail);
                $obj            = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code']    = $replace_detail;
                $obj['email']   = $value->email;
                $obj['cc']      = [];
                $obj['bcc']     = [];
                AppHelper::instance()->mailTemplate($obj);
                $response['send'] = 'success';
            }

            $expire_7 = UserHistory::join('course', 'course.id', 'user_history.course_id')
                ->join('user', 'user.id', 'user_history.user_id')
                ->select('user.name', 'user.lastname', 'user.email', 'course.slug', 'course.title_th', 'course.id as course_id')
                ->where('user.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)
                ->where('course.duration_time', 7)
                ->whereDate('user_history.expired', Carbon::now()->addDays(3))
                ->where('user_history.type', 1);
            $expire_7->where(function ($expire_7) {
                $expire_7->where('course.end_date', '>=', Carbon::now());
                $expire_7->orWhere('course.end_date', null);
            });

            $expire_7 = $expire_7->get();
            foreach ($expire_7 as $value) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $value->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $value->lastname, $replace_detail);
                $replace_detail = str_replace("{{course}}", $value->title_th, $replace_detail);
                $replace_detail = str_replace("{{date}}", '3', $replace_detail);
                $replace_detail = str_replace("{{link}}", '<a href="' . env('APP_NEXT_URL') . '/course/' . $value->slug . '" target="_blank">' . env('APP_NEXT_URL') . '/course/' . $value->slug . '</a>', $replace_detail);
                $obj            = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code']    = $replace_detail;
                $obj['email']   = $value->email;
                $obj['cc']      = [];
                $obj['bcc']     = [];
                AppHelper::instance()->mailTemplate($obj);
                $response['send'] = 'success';
            }

            $response['status'] = 'success';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function userType(Request $request)
    {
        $response            = new Dummy();
        $cate                = CateLearner::select('id as key', 'id as value', 'title_th as text')->where('status', 1)->get();
        $cate                = json_decode(json_encode($cate), true);
        $cate_dummy          = new Dummy();
        $cate_dummy['text']  = 'ประเภทสมาชิก';
        $cate_dummy['value'] = '';
        $cate_dummy['key']   = '';
        array_unshift($cate, $cate_dummy);
        $response['data']   = $cate;
        $response['status'] = 'success';

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function userHistory(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $order              = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
            ->select(
                'user_order_log.id',
                'user_order_log.receive_email',
                'user_order_log.receive_message',
                'user_order_log.user_id',
                'user_order_log.buy_type',
                'user_order_log.created_receipt_date',
                'user_order_log.created_at',
                'user_order_list.type',
                'user_order_list.course_id',
                'user_order_list.group_id',
                'user_order_list.subscription_id',
                'user_order_list.id as list_id'
            )
            ->where('user_order_log.status', 2)->get();
        foreach ($order as $value) {
            $user_id   = '';
            $user_date = '';
            if ($value->buy_type == 1) {
                $user_id = $value->user_id;
                if ($value->created_receipt_date != null && $value->created_receipt_date != '') {
                    $user_date = $value->created_receipt_date;
                } else {
                    $user_date = $value->created_at;
                }
            } else {
                $user_data = User::where('email', $value->receive_email)->first();
                if ($user_data) {
                    $user_id   = $user_data->id;
                    $gift_data = UserGiftLog::where('receive_user_id', $user_data->id)->where('order_id', $value->id)->where('status', 1)->first();
                    if ($gift_data) {
                        $user_date = $gift_data->receive_date;
                    }
                }
            }
            if ($user_id != '' && $user_date != '') {
                if ($value->type == 1) {
                    $course = Course::where('id', $value->course_id)->first();
                    if ($course) {
                        $check = UserHistory::where('order_list_id', $value->list_id)->where('user_id', $user_id)->where('course_id', $value->course_id)
                            ->where('expired', Carbon::parse($user_date)->addDays($course->duration_time))->first();
                        if (! $check) {
                            $history_log = new UserHistory();
                            if ($value->buy_type == 1) {
                                $history_log->get_type = 2;
                            } else {
                                $history_log->get_type = 3;
                            }
                            $history_log->order_list_id = $value->list_id;
                            $history_log->user_id       = $user_id;
                            $history_log->course_id     = $value->course_id;
                            $history_log->expired       = Carbon::parse($user_date)->addDays($course->duration_time);
                            $history_log->save();
                            $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                            if ($del_limit) {
                                $del_limit->delete();
                            }
                            $response['status'] = 'success';
                        }
                    }
                } else if ($order->type == 2) {
                    $group = CourseGroup::where('id', $value->group_id)->first();
                    if ($group) {
                        $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time')->where('group_id', $group->id)->get();
                        foreach ($course as $val_course) {
                            $check = UserHistory::where('order_list_id', $value->list_id)->where('user_id', $user_id)->where('course_id', $val_course->id)
                                ->where('expired', Carbon::parse($user_date)->addDays($val_course->duration_time))->first();
                            if (! $check) {
                                $history_log = new UserHistory();
                                if ($value->buy_type == 1) {
                                    $history_log->get_type = 2;
                                } else {
                                    $history_log->get_type = 3;
                                }
                                $history_log->order_list_id = $value->list_id;
                                $history_log->user_id       = $user_id;
                                $history_log->course_id     = $val_course->id;
                                $history_log->expired       = Carbon::parse($user_date)->addDays($val_course->duration_time);
                                $history_log->save();
                                $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                                if ($del_limit) {
                                    $del_limit->delete();
                                }
                                $response['status'] = 'success';
                            }
                        }
                    }
                } else if ($order->type == 3) {
                    $subscription = Subscription::where('id', $order->subscription_id)->first();
                    if ($subscription) {
                        $last_sub                          = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                        $subscription_log                  = new SubscriptionLog();
                        $subscription_log->order_list_id   = $order->list_id;
                        $subscription_log->user_id         = $user_id;
                        $subscription_log->subscription_id = $subscription->id;
                        if ($last_sub) {
                            $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                        } else {
                            $subscription_log->expired = Carbon::parse($user_date)->addDays($subscription->period);
                        }
                        $subscription_log->save();
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function cert_name(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken) && isset($request->cert_name_th) && isset($request->cert_name_en)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                if ($request->cert_name_th == 'null') {
                    $request->cert_name_th = null;
                }
                if ($request->cert_name_en == 'null') {
                    $request->cert_name_en = null;
                }
                $user->cert_name_th = isset($request->cert_name_th) ? AppHelper::instance()->cleanInput($request->cert_name_th) : null;
                $user->cert_name_en = isset($request->cert_name_en) ? AppHelper::instance()->cleanInput($request->cert_name_en) : null;
                $user->save();
                $response['name_th'] = $user->cert_name_th;
                $response['name_en'] = $user->cert_name_en;
                $response['status']  = 'success';
            }
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function learnExpire(Request $request)
    {
        $response     = new Dummy();
        $log_id       = [];
        $learning_log = UsersLearningLog::where('log_file', '!=', null)->where('updated_at', '<=', Carbon::now()->subDays(365))->get();
        foreach ($learning_log as $value) {
            $remove = AppHelper::instance()->removeFile($value->log_file);
            if ($remove) {
                $value->log_file      = null;
                $value->watching_time = '00:00:00';
                $value->save();
                array_push($log_id, $value->id);
            }
        }
        $response['removed_file_log_id'] = $log_id;
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function genOculus(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->course_id) && isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $check = UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.id')
                    ->where('course.is_oculus', 1)->where('course.id', $request->course_id)->where('user_history.user_id', $user->id)
                    ->where('user_history.expired', '>=', Carbon::now())->first();
                if ($check) {
                    $disable_old = OculusPin::where('user_id', $user->id)->where('course_id', $check->id)->where('status', 1)->get();
                    foreach ($disable_old as $dis) {
                        $dis->status = 2;
                        $dis->save();
                    }
                    $gen            = new OculusPin();
                    $gen->user_id   = $user->id;
                    $gen->course_id = $check->id;
                    $gen->pin       = AppHelper::instance()->generateRandomNumber(6);
                    $gen->save();
                    $response['status'] = 'success';
                    $response['pin']    = $gen->pin;
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function authOculus(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->pin)) {
            $expire_time = Carbon::now()->subMinutes(10);
            $get_pin     = OculusPin::where('pin', $request->pin)->where('status', 1)->where('created_at', '>=', $expire_time)->first();
            if ($get_pin) {
                $get_pin->status = 2;
                $get_pin->save();

                $check = UserHistory::join('course', 'course.id', 'user_history.course_id')->join('user', 'user.id', 'user_history.user_id')
                    ->select(
                        'course.course_key',
                        'course.title_th',
                        'course.subtitle_th',
                        'course.image_th',
                        'user_history.expired',
                        'user.name as user_name',
                        'user.lastname as user_lastname',
                        'user.avatar as user_avatar',
                        'user.email as user_email',
                        'user.mobile as user_mobile'
                    )
                    ->where('course.is_oculus', 1)->where('course.id', $get_pin->course_id)->where('user_history.user_id', $get_pin->user_id)
                    ->where('user_history.expired', '>=', Carbon::now())->first();
                if ($check) {
                    $course               = new Dummy();
                    $course['content_id'] = $check->course_key;
                    $course['title']      = $check->title_th;
                    $course['subtitle']   = $check->subtitle_th;
                    $course['image']      = $check->image_th;

                    if ($check->user_email != null && $check->user_email != '') {
                        $em         = explode("@", $check->user_email);
                        $name       = implode('@', array_slice($em, 0, count($em) - 1));
                        $len        = floor(strlen($name) / 2);
                        $user_email = substr($name, 0, $len) . str_repeat('*', $len) . "@" . end($em);
                    } else {
                        $user_email = '';
                    }

                    $user                    = new Dummy();
                    $user['name']            = $check->user_name;
                    $user['lastname']        = $check->user_lastname;
                    $user['avatar']          = $check->user_avatar;
                    $user['email']           = $user_email;
                    $user['mobile']          = substr($check->user_mobile, 0, -4) . 'xxxx';
                    $response['course']      = $course;
                    $response['user']        = $user;
                    $response['expire_date'] = $check->expired;
                    $response['status']      = 'success';
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function userStat(Request $request)
    {
        $response                          = new Dummy();
        $response['team_course_all']       = 0;
        $response['compulsory_course_all'] = 0;
        $response['optional_course_all']   = 0;

        $response['team_course_get']       = 0;
        $response['compulsory_course_get'] = 0;
        $response['optional_course_get']   = 0;

        $response['certificate_all'] = 0;
        $response['certificate_get'] = 0;
        $response['status']          = 'false';
        $response['is_team']         = false;

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $is_team = VolumnBy::where('volumn_by.status', 1)->where('volumn_by.user_id', $user->id)->first();
                if ($is_team) {
                    $response['is_team'] = true;
                }

                $cert_course = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                    ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                    ->where('user_graduate_log.user_id', $user->id)
                    ->where('course.started_date', '<=', Carbon::now());
                $cert_course->where(function ($cert_course) {
                    $cert_course->where('course.end_date', '>=', Carbon::now());
                    $cert_course->orWhere('course.end_date', null);
                });

                $cert_course = $cert_course->pluck('course.id')->toArray();

                $cert_course_count = count($cert_course);

                $cert_group = DynamicCertGroup::join('course_group', 'course_group.id', 'cert_group_dynamic.series_id')
                    ->join('group_log', 'group_log.group_id', 'course_group.id')
                    ->join('course', 'course.id', 'group_log.course_id')
                    ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                    ->select('course_group.id as group_id')
                    ->where('cert_group_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->where('user_graduate_log.user_id', $user->id)
                    ->orderby('user_graduate_log.created_at', 'desc')->groupby('course_group.id');
                $cert_group->where(function ($cert_group) {
                    $cert_group->where('course.end_date', '>=', Carbon::now());
                    $cert_group->orWhere('course.end_date', null);
                });

                $cert_group       = $cert_group->get();
                $cert_group_count = 0;
                foreach ($cert_group as $value) {
                    $check_fail = 0;
                    $group_data = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                        ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $group_data->where(function ($group_data) {
                        $group_data->where('course.end_date', '>=', Carbon::now());
                        $group_data->orWhere('course.end_date', null);
                    });

                    $group_data = $group_data->get();
                    foreach ($group_data as $sub) {
                        $check_pass = UserGraduateLog::where('user_id', $user->id)->where('course_id', $sub->id)->first();
                        if (! $check_pass) {
                            $check_fail++;
                            break;
                        }
                    }
                    if ($check_fail == 0 && count($group_data) > 0) {
                        $cert_group_count++;
                    }
                }

                $volume = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                    ->join('course', 'course.id', 'volumn_course_log.course_id')
                    ->select('volumn_course_log.course_id')
                    ->where('volumn_course_log.type', 2)
                    ->where('volumn_by.status', 1)
                    ->where('course.status', 1)
                    ->where('course.started_date', '<=', Carbon::now());
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.user_id', $user->id);
                });
                $volume->groupby('volumn_course_log.course_id');

                $volume->where(function ($volume) {
                    $volume->where('course.end_date', '>=', Carbon::now());
                    $volume->orWhere('course.end_date', null);
                });
                if (isset($request->utoken)) {
                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                    if ($user) {
                        //กั้นห้อง
                        // $volume->leftjoin('course_department_log','course_department_log.course_id','course.id')
                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                        // ->where(function ($volume) use($user) {
                        //   $volume->where('categories_department.id',$user->dept_id);
                        //   $volume->orWhere('course.is_option',1);
                        // })
                        // ->where(function ($volume){
                        //   $volume->where('categories_department.status',1);
                        //   $volume->orWhere('course.is_option',1);
                        // });
                    }
                }

                $volume = $volume->pluck('volumn_course_log.course_id')->toArray();

                $team          = $volume;
                $team_graduate = UserGraduateLog::where('user_id', $user->id)->whereIn('course_id', $team)->pluck('course_id')->toArray();

                $compulsory = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where('user_history.get_type', 7)
                    ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();
                $compulsory_graduate = UserGraduateLog::where('user_id', $user->id)->whereIn('course_id', $compulsory)->pluck('course_id')->toArray();

                $optional = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where(function ($optional) {
                    $optional->where('user_history.get_type', 1);
                    $optional->orWhere('user_history.get_type', 2);
                    $optional->orWhere('user_history.get_type', 3);
                })
                    ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();
                $optional_graduate = UserGraduateLog::where('user_id', $user->id)->whereIn('course_id', $optional)->pluck('course_id')->toArray();

                $all_course      = array_merge($team, $compulsory, $optional);
                $cert_course_all = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')
                    ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->whereIn('course.id', $all_course);
                $cert_course_all->where(function ($cert_course_all) {
                    $cert_course_all->where('course.end_date', '>=', Carbon::now());
                    $cert_course_all->orWhere('course.end_date', null);
                });
                $cert_course_all       = $cert_course_all->pluck('course.id')->toArray();
                $cert_course_all_count = count($cert_course_all);

                $cert_global = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                    ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                    ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                    ->join('course', 'course.id', 'course_department_log.course_id')
                    ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                    ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                    ->where('categories_department.status', 1)
                    ->where('user_graduate_log.user_id', $user->id)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->whereNotIn('course.id', $cert_course)
                    ->whereIn('course.id', $all_course);
                $cert_global->where(function ($cert_global) {
                    $cert_global->where('course.end_date', '>=', Carbon::now());
                    $cert_global->orWhere('course.end_date', null);
                });

                $cert_global = $cert_global->pluck('course.id')->toArray();

                $cert_global_count = count($cert_global);

                $cert_global_all = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                    ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                    ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                    ->join('course', 'course.id', 'course_department_log.course_id')
                    ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                    ->where('categories_department.status', 1)
                    ->where('categories_department.id', $user->dept_id)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->whereNotIn('course.id', $cert_course_all)
                    ->whereIn('course.id', $all_course);
                $cert_global_all->where(function ($cert_global_all) {
                    $cert_global_all->where('course.end_date', '>=', Carbon::now());
                    $cert_global_all->orWhere('course.end_date', null);
                });

                $cert_global_all = $cert_global_all->pluck('course.id')->toArray();

                $cert_global_all_count = count($cert_global_all);

                $cert_group_all = DynamicCertGroup::join('course_group', 'course_group.id', 'cert_group_dynamic.series_id')
                    ->join('group_log', 'group_log.group_id', 'course_group.id')
                    ->join('course', 'course.id', 'group_log.course_id')
                    ->select('course_group.id as group_id')
                    ->where('cert_group_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->whereIn('course.id', $all_course)
                    ->groupby('course_group.id');
                $cert_group_all->where(function ($cert_group_all) {
                    $cert_group_all->where('course.end_date', '>=', Carbon::now());
                    $cert_group_all->orWhere('course.end_date', null);
                });

                $cert_group_all       = $cert_group_all->get();
                $cert_group_all_count = 0;
                foreach ($cert_group_all as $value) {
                    $group_data_all = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                        ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $group_data_all->where(function ($group_data_all) {
                        $group_data_all->where('course.end_date', '>=', Carbon::now());
                        $group_data_all->orWhere('course.end_date', null);
                    });
                    $group_data_all = $group_data_all->count();

                    $group_data_have = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                        ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                        ->where('course.started_date', '<=', Carbon::now())
                        ->whereIn('course.id', $all_course);
                    $group_data_have->where(function ($group_data_have) {
                        $group_data_have->where('course.end_date', '>=', Carbon::now());
                        $group_data_have->orWhere('course.end_date', null);
                    });
                    $group_data_have = $group_data_have->count();

                    if ($group_data_all == $group_data_have) {
                        $cert_group_all_count++;
                    }
                }
                $response['team_course_all']       = count($team);
                $response['compulsory_course_all'] = count($compulsory);
                $response['optional_course_all']   = count($optional);

                $response['team_course_get']       = count($team_graduate);
                $response['compulsory_course_get'] = count($compulsory_graduate);
                $response['optional_course_get']   = count($optional_graduate);

                $response['certificate_all'] = $cert_course_all_count + $cert_group_all_count + $cert_global_all_count;
                $response['certificate_get'] = $cert_group_count + $cert_course_count + $cert_global_count;

                $response['status'] = 'success';
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function userDashboard(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken) && isset($request->key)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $response['data'] = [];
                if ($request->key == 'compulsory') {
                    $course = UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->where('user_history.user_id', $user->id)->where('user_history.get_type', 7)
                        ->where('user_history.expired', '>=', Carbon::now())
                        ->where('course.started_date', '<=', Carbon::now())
                        ->where('course.status', '!=', 2)
                        ->where(function ($course) {
                            $course->where('course.end_date', '>=', Carbon::now());
                            $course->orWhere('course.end_date', null);
                        })->orderby('user_history.created_at', 'desc')->groupby('user_history.course_id');
                    if (isset($request->utoken)) {
                        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            //กั้นห้อง
                            // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                            // ->where(function ($course) use($user) {
                            //   $course->where('categories_department.id',$user->dept_id);
                            //   $course->orWhere('course.is_option',1);
                            // })
                            // ->where(function ($course){
                            //   $course->where('categories_department.status',1);
                            //   $course->orWhere('course.is_option',1);
                            // });
                        }
                    }
                    $course           = $course->get();
                    $response['data'] = AppHelper::instance()->convertCourse($course, $request->utoken);
                } else if ($request->key == 'team') {
                    $volume_comp = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                        ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                        ->join('course', 'course.id', 'volumn_course_log.course_id')
                        ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->where('volumn_by.status', 1)
                        ->where('course.status', 1)
                        ->where('volumn_course_log.type', 2)
                        ->where('course.started_date', '<=', Carbon::now());
                    $volume_comp->where(function ($volume_comp) use ($user) {
                        $volume_comp->where('volumn_user_log.user_id', $user->id);
                    });
                    $volume_comp->groupby('volumn_course_log.course_id');

                    $volume_comp->where(function ($volume_comp) {
                        $volume_comp->where('course.end_date', '>=', Carbon::now());
                        $volume_comp->orWhere('course.end_date', null);
                    });
                    if (isset($request->utoken)) {
                        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            //กั้นห้อง
                            // $volume_comp->leftjoin('course_department_log','course_department_log.course_id','course.id')
                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                            // ->where(function ($volume_comp) use($user) {
                            //   $volume_comp->where('categories_department.id',$user->dept_id);
                            //   $volume_comp->orWhere('course.is_option',1);
                            // })
                            // ->where(function ($volume_comp){
                            //   $volume_comp->where('categories_department.status',1);
                            //   $volume_comp->orWhere('course.is_option',1);
                            // });
                        }
                    }

                    $volume_comp = $volume_comp->get();

                    $volume_rec = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                        ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                        ->join('course', 'course.id', 'volumn_course_log.course_id')
                        ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->where('volumn_by.status', 1)
                        ->where('course.status', 1)
                        ->where('volumn_course_log.type', 1)
                        ->where('course.started_date', '<=', Carbon::now());
                    $volume_rec->where(function ($volume_rec) use ($user) {
                        $volume_rec->where('volumn_user_log.user_id', $user->id);
                    });
                    $volume_rec->groupby('volumn_course_log.course_id');

                    $volume_rec->where(function ($volume_rec) {
                        $volume_rec->where('course.end_date', '>=', Carbon::now());
                        $volume_rec->orWhere('course.end_date', null);
                    });
                    if (isset($request->utoken)) {
                        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            //กั้นห้อง
                            // $volume_rec->leftjoin('course_department_log','course_department_log.course_id','course.id')
                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                            // ->where(function ($volume_rec) use($user) {
                            //   $volume_rec->where('categories_department.id',$user->dept_id);
                            //   $volume_rec->orWhere('course.is_option',1);
                            // })
                            // ->where(function ($volume_rec){
                            //   $volume_rec->where('categories_department.status',1);
                            //   $volume_rec->orWhere('course.is_option',1);
                            // });
                        }
                    }

                    $volume_rec = $volume_rec->get();

                    $response['data']     = AppHelper::instance()->convertCourse($volume_comp, $request->utoken);
                    $response['data_rec'] = AppHelper::instance()->convertCourse($volume_rec, $request->utoken);
                } else if ($request->key == 'favourite') {
                    $course = UsersFavoriteLog::join('course', 'course.id', 'user_favorite_log.course_id')->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->where('user_favorite_log.user_id', $user->id)
                        ->where('course.started_date', '<=', Carbon::now())
                        ->where('course.status', 1)
                        ->where(function ($course) {
                            $course->where('course.end_date', '>=', Carbon::now());
                            $course->orWhere('course.end_date', null);
                        })
                        ->groupby('user_favorite_log.course_id')->orderby('user_favorite_log.created_at', 'desc');
                    if (isset($request->utoken)) {
                        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            //กั้นห้อง
                            // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                            // ->where(function ($course) use($user) {
                            //   $course->where('categories_department.id',$user->dept_id);
                            //   $course->orWhere('course.is_option',1);
                            // })
                            // ->where(function ($course){
                            //   $course->where('categories_department.status',1);
                            //   $course->orWhere('course.is_option',1);
                            // });
                        }
                    }
                    $course           = $course->get();
                    $response['data'] = AppHelper::instance()->convertCourse($course, $request->utoken);
                } else if ($request->key == 'certificate') {
                    $certificate = [];
                    $course_arr  = [];
                    $cert_course = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                        ->select('course.title_th', 'course.image_th', 'cert_dynamic.id', 'cert_dynamic.lang_type', 'user_graduate_log.created_at', 'course.id as course_id')
                        ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                        ->where('user_graduate_log.user_id', $user->id)
                        ->where('course.started_date', '<=', Carbon::now())
                        ->orderby('user_graduate_log.created_at', 'desc');
                    $cert_course->where(function ($cert_course) {
                        $cert_course->where('course.end_date', '>=', Carbon::now());
                        $cert_course->orWhere('course.end_date', null);
                    });

                    //กั้นห้อง
                    // $cert_course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                    // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                    // ->where(function ($cert_course) use($user) {
                    //   $cert_course->where('categories_department.id',$user->dept_id);
                    //   $cert_course->orWhere('course.is_option',1);
                    // })
                    // ->where(function ($cert_course){
                    //   $cert_course->where('categories_department.status',1);
                    //   $cert_course->orWhere('course.is_option',1);
                    // });

                    $cert_course = $cert_course->get();

                    foreach ($cert_course as $value) {
                        array_push($course_arr, $value->course_id);
                        if ($value->lang_type == 1) {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data['type']      = 'course';
                            $data['lang_type'] = 'th';
                            array_push($certificate, $data);
                        } else if ($value->lang_type == 2) {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                            $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data['type']      = 'course';
                            $data['lang_type'] = 'en';
                            array_push($certificate, $data);
                        } else {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data['type']      = 'course';
                            $data['lang_type'] = 'th';
                            array_push($certificate, $data);

                            $data_en              = new Dummy();
                            $data_en['image_th']  = $value->image_th;
                            $data_en['title_th']  = $value->title_th;
                            $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                            $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                            $data_en['type']      = 'course';
                            $data_en['lang_type'] = 'en';
                            array_push($certificate, $data_en);
                        }
                    }

                    $cert_global = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                        ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                        ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                        ->join('course', 'course.id', 'course_department_log.course_id')
                        ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                        ->select('course.title_th', 'course.image_th', 'cert_global.id', 'cert_global.lang_type', 'user_graduate_log.created_at', 'course.id as course_id')
                        ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                    // ->where('categories_department.status',1)
                        ->where('user_graduate_log.user_id', $user->id)
                        ->where('course.started_date', '<=', Carbon::now())
                        ->whereNotIn('course.id', $course_arr)
                        ->orderby('user_graduate_log.created_at', 'desc');
                    $cert_global->where(function ($cert_global) {
                        $cert_global->where('course.end_date', '>=', Carbon::now());
                        $cert_global->orWhere('course.end_date', null);
                    });

                    //กั้นห้อง
                    // $cert_global->where(function ($cert_global) use($user) {
                    //   $cert_global->where('categories_department.id',$user->dept_id);
                    //   $cert_global->orWhere('course.is_option',1);
                    // })
                    // ->where(function ($cert_global){
                    //   $cert_global->where('categories_department.status',1);
                    //   $cert_global->orWhere('course.is_option',1);
                    // });

                    $cert_global = $cert_global->get();

                    foreach ($cert_global as $value) {
                        if ($value->lang_type == 1) {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                            $data['type']      = 'global';
                            $data['lang_type'] = 'th';
                            array_push($certificate, $data);
                        } else if ($value->lang_type == 2) {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                            $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                            $data['type']      = 'global';
                            $data['lang_type'] = 'en';
                            array_push($certificate, $data);
                        } else {
                            $data              = new Dummy();
                            $data['image_th']  = $value->image_th;
                            $data['title_th']  = $value->title_th;
                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                            $data['type']      = 'global';
                            $data['lang_type'] = 'th';
                            array_push($certificate, $data);

                            $data_en              = new Dummy();
                            $data_en['image_th']  = $value->image_th;
                            $data_en['title_th']  = $value->title_th;
                            $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                            $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                            $data_en['type']      = 'global';
                            $data_en['lang_type'] = 'en';
                            array_push($certificate, $data_en);
                        }
                    }

                    $cert_group = DynamicCertGroup::join('course_group', 'course_group.id', 'cert_group_dynamic.series_id')
                        ->join('group_log', 'group_log.group_id', 'course_group.id')
                        ->join('course', 'course.id', 'group_log.course_id')
                        ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                        ->select('cert_group_dynamic.series_name as title_th', 'course_group.thumb as image_th', 'cert_group_dynamic.id', 'cert_group_dynamic.lang_type', 'user_graduate_log.created_at', 'course_group.id as group_id')
                        ->where('cert_group_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                        ->where('course.started_date', '<=', Carbon::now())
                        ->where('user_graduate_log.user_id', $user->id)
                        ->orderby('user_graduate_log.created_at', 'desc')->groupby('course_group.id');
                    $cert_group->where(function ($cert_group) {
                        $cert_group->where('course.end_date', '>=', Carbon::now());
                        $cert_group->orWhere('course.end_date', null);
                    });

                    //กั้นห้อง
                    // $cert_group->leftjoin('course_department_log','course_department_log.course_id','course.id')
                    // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                    // ->where(function ($cert_group) use($user) {
                    //   $cert_group->where('categories_department.id',$user->dept_id);
                    //   $cert_group->orWhere('course.is_option',1);
                    // })
                    // ->where(function ($cert_group){
                    //   $cert_group->where('categories_department.status',1);
                    //   $cert_group->orWhere('course.is_option',1);
                    // });

                    $cert_group = $cert_group->get();

                    foreach ($cert_group as $value) {
                        $check_fail = 0;
                        $group_data = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                            ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                            ->where('course.started_date', '<=', Carbon::now());
                        $group_data->where(function ($group_data) {
                            $group_data->where('course.end_date', '>=', Carbon::now());
                            $group_data->orWhere('course.end_date', null);
                        });

                        $group_data->leftjoin('course_department_log', 'course_department_log.course_id', 'course.id')
                            ->leftjoin('categories_department', 'categories_department.id', 'course_department_log.department')
                            ->where(function ($group_data) use ($user) {
                                $group_data->where('categories_department.id', $user->dept_id);
                                $group_data->orWhere('course.is_option', 1);
                            })
                            ->where(function ($group_data) {
                                $group_data->where('categories_department.status', 1);
                                $group_data->orWhere('course.is_option', 1);
                            });

                        $group_data = $group_data->get();
                        foreach ($group_data as $sub) {
                            $check_pass = UserGraduateLog::where('user_id', $user->id)->where('course_id', $sub->id)->first();
                            if (! $check_pass) {
                                $check_fail++;
                                break;
                            }
                        }
                        if ($check_fail == 0 && count($group_data) > 0) {
                            if ($value->lang_type == 1) {
                                $data              = new Dummy();
                                $data['image_th']  = $value->image_th;
                                $data['title_th']  = $value->title_th;
                                $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                $data['type']      = 'group';
                                $data['lang_type'] = 'th';
                                array_push($certificate, $data);
                            } else if ($value->lang_type == 2) {
                                $data              = new Dummy();
                                $data['image_th']  = $value->image_th;
                                $data['title_th']  = $value->title_th;
                                $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                $data['type']      = 'group';
                                $data['lang_type'] = 'en';
                                array_push($certificate, $data);
                            } else {
                                $data              = new Dummy();
                                $data['image_th']  = $value->image_th;
                                $data['title_th']  = $value->title_th;
                                $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                $data['type']      = 'group';
                                $data['lang_type'] = 'th';
                                array_push($certificate, $data);

                                $data_en              = new Dummy();
                                $data_en['image_th']  = $value->image_th;
                                $data_en['title_th']  = $value->title_th;
                                $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                $data_en['type']      = 'group';
                                $data_en['lang_type'] = 'en';
                                array_push($certificate, $data_en);
                            }
                        }
                    }

                    usort($certificate, function ($a1, $a2) {
                        $v1 = $a1['created_at'];
                        $v2 = $a2['created_at'];
                        return strtotime($v2) - strtotime($v1);
                    });

                    $response['data'] = $certificate;
                } else if ($request->key == 'other') {
                    $course = UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->where('user_history.user_id', $user->id)
                    // ->where(function ($course){
                    //   $course->where('user_history.get_type',1);
                    //   $course->orWhere('user_history.get_type',2);
                    //   $course->orWhere('user_history.get_type',3);
                    // })
                        ->where('course.started_date', '<=', Carbon::now())
                        ->where('course.status', '!=', 2)
                        ->where(function ($course) {
                            $course->where('course.end_date', '>=', Carbon::now());
                            $course->orWhere('course.end_date', null);
                        })
                    // ->where('user_history.expired','>=',Carbon::now())
                        ->orderby('user_history.created_at', 'desc')->groupby('user_history.course_id');
                    if (isset($request->utoken)) {
                        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            //กั้นห้อง
                            // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                            // ->where(function ($course) use($user) {
                            //   $course->where('categories_department.id',$user->dept_id);
                            //   $course->orWhere('course.is_option',1);
                            // })
                            // ->where(function ($course){
                            //   $course->where('categories_department.status',1);
                            //   $course->orWhere('course.is_option',1);
                            // });
                        }
                    }
                    $response['test'] = $user->id;
                    $course           = $course->get();
                    $response['data'] = AppHelper::instance()->convertCourse($course, $request->utoken);
                } else if ($request->key == 'curriculum') {
                    $group_arr = [];
                    $group     = CourseGroup::join('course_group_department', 'course_group_department.group_id', 'course_group.id')
                        ->where('course_group_department.dept_id', $user->dept_id)->where('course_group.status', 1)->get();
                    foreach ($group as $key => $value) {
                        $course_1 = Course::join('group_log', 'group_log.course_id', 'course.id')
                            ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                            ->where('course.started_date', '<=', Carbon::now())
                            ->where('course.status', 1)
                            ->where('group_log.group_id', $value->id)
                            ->where(function ($course_1) {
                                $course_1->where('course.started_learning', '<=', Carbon::now())
                                    ->orWhereNull('course.started_learning');
                            })
                            ->groupby('course.id');
                        $course_1->where(function ($course_1) {
                            $course_1->where('course.end_date', '>=', Carbon::now());
                            $course_1->orWhere('course.end_date', null);
                        });
                        if (isset($request->utoken)) {
                            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                            if ($user) {
                                //กั้นห้อง
                                // $course_1->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                // ->where(function ($course_1) use($user) {
                                //   $course_1->where('categories_department.id',$user->dept_id);
                                //   $course_1->orWhere('course.is_option',1);
                                // })
                                // ->where(function ($course_1){
                                //   $course_1->where('categories_department.status',1);
                                //   $course_1->orWhere('course.is_option',1);
                                // });
                            }
                        }

                        $course_1 = $course_1->get();
                        if (count($course_1) > 0) {
                            $course_1       = AppHelper::instance()->convertCourse($course_1, $request->utoken);
                            $dummy          = new Dummy();
                            $dummy['title'] = $value->title;
                            $dummy['data']  = $course_1;
                            array_push($group_arr, $dummy);
                        }
                    }
                    $response['data'] = $group_arr;
                } else if ($request->key == 'history') {
                    $order = UsersOrderLog::where('user_order_log.user_id', $user->id)
                        ->leftJoin('user_order_list', 'user_order_list.order_id', '=', 'user_order_log.id')
                        ->leftJoin('course', 'course.id', '=', 'user_order_list.course_id')
                        ->select(
                            'user_order_log.status as order_status',
                            'user_order_log.total_price as price',
                            'user_order_log.created_at as order_date',
                            'user_order_log.receipt_path as receipt',
                            'user_order_log.peak_receipt_link as peak_receipt_link',
                            'user_order_log.peak_invoice_link as peak_invoice_link',
                            'user_order_log.id as order_id',
                            'user_order_log.order_no as order_number',
                            'user_order_log.payment_type as payment_type',
                            'user_order_log.qr_id as qr_id',
                            'course.slug as course_slug',
                        )
                        ->where('user_order_log.is_free', 2)
                        ->get();
                    foreach ($order as $key => $val) {
                        $order[$key]['order_item'] = '';
                        $order_log                 = UsersOrderList::where('order_id', $val->order_id)->get();
                        foreach ($order_log as $key_log => $val_log) {
                            if ($order[$key]['order_item'] != '') {
                                $order[$key]['order_item'] .= '<br>';
                            }
                            if ($val_log->type == 1) {
                                $val_item = Course::where('id', $val_log->course_id)->first();
                                if ($val_item) {
                                    $order[$key]['order_item'] .= $val_item->title_th;
                                }
                            } else if ($val_log->type == 2) {
                                $val_item = CourseGroup::where('id', $val_log->group_id)->first();
                                if ($val_item) {
                                    $order[$key]['order_item'] .= $val_item->title;
                                }
                            } else if ($val_log->type == 3) {
                                $val_item = Subscription::where('id', $val_log->subscription_id)->first();
                                if ($val_item) {
                                    $order[$key]['order_item'] .= $val_item->title;
                                }
                            }
                        }
                        if ($val->order_status == 1) {
                            if ($val->payment_type == 1 || $val->payment_type == 2) {
                                $order[$key]['order_status'] = 'ชำระเงิน';
                            } else {
                                $order[$key]['order_status'] = 'ยืนยันชำระเงิน';
                            }
                        } else if ($val->order_status == 2) {
                            $order[$key]['order_status'] = 'ชำระเงินสำเร็จ';
                        } else if ($val->order_status == 3) {
                            $order[$key]['order_status'] = 'ยกเลิก';
                        } else if ($val->order_status == 4) {
                            $order[$key]['order_status'] = 'รอการตรวจสอบ';
                        }
                        $order[$key]['order_no'] = SimpleEnDeHelper::instance()->encryptString($val->order_id);
                    }

                    $response['data'] = $order;
                }
                $response['status'] = 'success';
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function jobFunction(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $job_function = CateJobFunction::join('user', 'user.job_function', 'categories_job_function.id')->select('categories_job_function.*')
                    ->where('categories_job_function.status', 1)->where('user.status', 1)->where('user.id', $user->id)->first();
                if ($job_function) {
                    $course_pin = Course::join('job_function_log', 'job_function_log.course_id', 'course.id')
                        ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->orderby('job_function_log.position', 'asc')
                        ->where('course.started_date', '<=', Carbon::now())
                        ->where('job_function_log.job_id', $job_function->id)
                        ->where(function ($course_pin) {
                            $course_pin->where('course.started_learning', '<=', Carbon::now())
                                ->orWhereNull('course.started_learning');
                        })
                        ->where('job_function_log.status', 1)
                        ->where('course.status', 1)->limit(10);
                    $course_pin->where(function ($course_pin) {
                        $course_pin->where('course.end_date', '>=', Carbon::now());
                        $course_pin->orWhere('course.end_date', null);
                    });

                    if (isset($request->utoken)) {
                        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            //กั้นห้อง
                            // $course_pin->leftjoin('course_department_log','course_department_log.course_id','course.id')
                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                            // ->where(function ($course_pin) use($user) {
                            //   $course_pin->where('categories_department.id',$user->dept_id);
                            //   $course_pin->orWhere('course.is_option',1);
                            // })
                            // ->where(function ($course_pin){
                            //   $course_pin->where('categories_department.status',1);
                            //   $course_pin->orWhere('course.is_option',1);
                            // });
                        }
                    }

                    $course_pin = $course_pin->get();

                    $course_arr = [];

                    foreach ($course_pin as $val) {
                        array_push($course_arr, $val->id);
                    }

                    $response['job_function'] = $job_function;

                    $response['course_pin'] = AppHelper::instance()->convertCourse($course_pin, $request->utoken);

                    if (! isset($request->page)) {
                        $request->page = 1;
                    }
                    if (! isset($request->limit)) {
                        $request->limit = 9;
                    }
                    if ($request->limit > 9) {
                        $request->limit = 9;
                    }
                    $offset             = (intval($request->page) - 1) * intval($request->limit);
                    $response['offset'] = $offset;

                    $course = Course::join('course_job_function_log', 'course_job_function_log.course_id', 'course.id')
                        ->join('categories_job_function', 'categories_job_function.id', 'course_job_function_log.job_function_id')
                        ->where('categories_job_function.status', 1)
                        ->where('categories_job_function.id', $job_function->id)
                        ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
                    $course->where(function ($course) {
                        $course->where('course.end_date', '>=', Carbon::now());
                        $course->orWhere('course.end_date', null);
                    });
                    $course->whereNotIn('course.id', $course_arr);

                    if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
                        $course->leftjoin('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        $course->leftjoin('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
                        $val_keyword = $request->keyword;
                        $course->where(function ($course) use ($val_keyword) {
                            $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
                        });
                    }
                    $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration');
                    if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
                        $val_sort = $request->sort;
                        if ($val_sort == 'date') {
                            $course->orderby('course.started_date', 'desc');
                        } else if ($val_sort == 'a_to_z') {
                            $course->orderby('course.title_th', 'asc');
                        } else if ($val_sort == 'popular') {
                            $course->orderby('course.rate', 'desc');
                        }
                    }
                    $course->groupby('course.id');
                    $course_all = ceil((count($course->get())) / $request->limit);
                    $course->offset($offset);
                    $course->limit($request->limit);
                    $course->orderby('course.started_date', 'desc');
                    $course->orderby('course.id', 'desc');
                    $course = $course->get();

                    $response['course_data'] = AppHelper::instance()->convertCourse($course, $request->utoken);
                    $response['all_page']    = $course_all;

                    $response['status'] = 'success';
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function userSetting(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $user->name          = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : '';
                $user->lastname      = isset($request->lastname) ? AppHelper::instance()->cleanInput($request->lastname) : '';
                $user->nickname      = isset($request->nickname) ? AppHelper::instance()->cleanInput($request->nickname) : '';
                $user->school        = isset($request->school) ? AppHelper::instance()->cleanInput($request->school) : '';
                $user->class         = isset($request->class) ? AppHelper::instance()->cleanInput($request->class) : '';
                $user->major         = isset($request->major) ? AppHelper::instance()->cleanInput($request->major) : '';
                $user->know_ch       = isset($request->know_ch) ? AppHelper::instance()->cleanInput($request->know_ch) : '';
                $user->avatar        = isset($request->avatar) ? AppHelper::instance()->cleanInput($request->avatar) : '';
                $user->mobile        = isset($request->mobile) ? AppHelper::instance()->cleanInput($request->mobile) : '';
                $user->position         = isset($request->position) ? AppHelper::instance()->cleanInput($request->position) : '';
                $user->birthday      = isset($request->birthday) ? AppHelper::instance()->cleanInput($request->birthday) : null;
                $user->occupation    = isset($request->occupation) ? AppHelper::instance()->cleanInput($request->occupation) : '';
                $user->company_name  = isset($request->company_name) ? AppHelper::instance()->cleanInput($request->company_name) : '';
                $user->business_type = isset($request->business_type) ? AppHelper::instance()->cleanInput($request->business_type) : '';
                
                $user->province      = null;
                $user->province_name = null;
                if (isset($request->province)) {
                    $province = Province::where('id', $request->province)->first();
                    if ($province) {
                        $user->province      = $province->id;
                        $user->province_name = $province->name;
                    }
                }

                $user->district      = null;
                $user->district_name = null;
                if (isset($request->district)) {
                    $district = District::where('id', $request->district)->first();
                    if ($district) {
                        $user->district      = $district->id;
                        $user->district_name = $district->name;
                    }
                }

                $user->subdistrict      = null;
                $user->subdistrict_name = null;
                if (isset($request->subdistrict)) {
                    $subdistrict = SubDistrict::where('id', $request->subdistrict)->first();
                    if ($subdistrict) {
                        $user->subdistrict      = $subdistrict->id;
                        $user->subdistrict_name = $subdistrict->name;
                    }
                }

                $user->postcode = isset($request->postcode) ? AppHelper::instance()->cleanInput($request->postcode) : null;
                $user->address  = isset($request->address) ? AppHelper::instance()->cleanInput($request->address) : null;

                if (isset($request->password)) {
                    $user->password = Hash::make(AppHelper::instance()->cleanInput($request->password));
                }

                if (isset($request->interesting)) {
                    $tag_id  = [];
                    $all_tag = json_decode($request->interesting);
                    if (is_array($all_tag)) {
                        foreach ($all_tag as $key => $value) {
                            $get_tag = CateInteresting::where('id', $value->key)->where('status', 1)->first();
                            if ($get_tag) {
                                $tag_log = UserInterestingLog::where('user_id', $user->id)->where('interesting_id', $get_tag->id)->first();
                                if (! $tag_log) {
                                    $tag_log                 = new UserInterestingLog();
                                    $tag_log->user_id        = $user->id;
                                    $tag_log->interesting_id = $get_tag->id;
                                    $tag_log->save();
                                }
                                array_push($tag_id, $get_tag->id);
                            }
                        }
                    }
                    if (count($tag_id) > 0) {
                        $old_tag = UserInterestingLog::where('user_id', $user->id)->whereNotIn('interesting_id', $tag_id)->get();
                        foreach ($old_tag as $del) {
                            $del->delete();
                        }
                    }
                    $user->interesting = implode(",", $tag_id);
                }
                if (isset($request->job_function)) {
                    $all_job = json_decode($request->job_function);
                    if (is_array($all_job)) {
                        foreach ($all_job as $key => $value) {
                            $get_job = CateJobFunction::where('id', $value->key)->where('status', 1)->first();
                            if ($get_job) {
                                $user->job_function = $get_job->id;
                            }
                        }
                    }
                }

                $user->save();
                $response['status'] = 'success';
                $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function profileStat(Request $request)
    {
        $response                          = new Dummy();
        $response['team_course_all']       = 0;
        $response['compulsory_course_all'] = 0;
        $response['optional_course_all']   = 0;

        $response['team_course_get']       = 0;
        $response['compulsory_course_get'] = 0;
        $response['optional_course_get']   = 0;

        $response['certificate_all'] = 0;
        $response['certificate_get'] = 0;
        $response['status']          = 'false';
        $response['is_team']         = false;
        $response['user']            = [];

        if (isset($request->utoken) && isset($request->e_id)) {
            $user_admin = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            $user       = Users::where('e_id', $request->e_id)->first();
            if ($user_admin && $user) {
                $user_dept = CateDepartment::select('title_th')->where('id', $user->dept_id)->first();
                if ($user_dept) {
                    $user->department = $user_dept->title_th;
                } else {
                    $user->department = '';
                }
                $response['user'] = $user;
                $allow_dashboard  = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->where('volumn_by.status', 1)
                    ->where('volumn_by.user_id', $user_admin->id)
                    ->where('volumn_user_log.user_id', $user->id)->first();
                if ($allow_dashboard) {
                    $response['is_team'] = true;
                    $request->utoken     = SimpleEnDeHelper::instance()->encryptString($user->id);
                    if (isset($request->utoken)) {
                        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            $cert_course = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                                ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                                ->where('user_graduate_log.user_id', $user->id)
                                ->where('course.started_date', '<=', Carbon::now());
                            $cert_course->where(function ($cert_course) {
                                $cert_course->where('course.end_date', '>=', Carbon::now());
                                $cert_course->orWhere('course.end_date', null);
                            });

                            $cert_course = $cert_course->pluck('course.id')->toArray();

                            $cert_course_count = count($cert_course);

                            $cert_group = DynamicCertGroup::join('course_group', 'course_group.id', 'cert_group_dynamic.series_id')
                                ->join('group_log', 'group_log.group_id', 'course_group.id')
                                ->join('course', 'course.id', 'group_log.course_id')
                                ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                                ->select('course_group.id as group_id')
                                ->where('cert_group_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                                ->where('course.started_date', '<=', Carbon::now())
                                ->where('user_graduate_log.user_id', $user->id)
                                ->orderby('user_graduate_log.created_at', 'desc')->groupby('course_group.id');
                            $cert_group->where(function ($cert_group) {
                                $cert_group->where('course.end_date', '>=', Carbon::now());
                                $cert_group->orWhere('course.end_date', null);
                            });

                            $cert_group       = $cert_group->get();
                            $cert_group_count = 0;
                            foreach ($cert_group as $value) {
                                $check_fail = 0;
                                $group_data = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                                    ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                                    ->where('course.started_date', '<=', Carbon::now());
                                $group_data->where(function ($group_data) {
                                    $group_data->where('course.end_date', '>=', Carbon::now());
                                    $group_data->orWhere('course.end_date', null);
                                });

                                $group_data = $group_data->get();
                                foreach ($group_data as $sub) {
                                    $check_pass = UserGraduateLog::where('user_id', $user->id)->where('course_id', $sub->id)->first();
                                    if (! $check_pass) {
                                        $check_fail++;
                                        break;
                                    }
                                }
                                if ($check_fail == 0 && count($group_data) > 0) {
                                    $cert_group_count++;
                                }
                            }

                            $volume = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                                ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                                ->join('course', 'course.id', 'volumn_course_log.course_id')
                                ->select('volumn_course_log.course_id')
                                ->where('volumn_course_log.type', 2)
                                ->where('volumn_by.status', 1)
                                ->where('course.status', 1)
                                ->where('course.started_date', '<=', Carbon::now());
                            $volume->where(function ($volume) use ($user) {
                                $volume->where('volumn_user_log.user_id', $user->id);
                            });
                            $volume->groupby('volumn_course_log.course_id');

                            $volume->where(function ($volume) {
                                $volume->where('course.end_date', '>=', Carbon::now());
                                $volume->orWhere('course.end_date', null);
                            });
                            if (isset($request->utoken)) {
                                $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                                if ($user) {
                                    //กั้นห้อง
                                    // $volume->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                    // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                    // ->where(function ($volume) use($user) {
                                    //   $volume->where('categories_department.id',$user->dept_id);
                                    //   $volume->orWhere('course.is_option',1);
                                    // })
                                    // ->where(function ($volume){
                                    //   $volume->where('categories_department.status',1);
                                    //   $volume->orWhere('course.is_option',1);
                                    // });
                                }
                            }

                            $volume = $volume->pluck('volumn_course_log.course_id')->toArray();

                            $team          = $volume;
                            $team_graduate = UserGraduateLog::where('user_id', $user->id)->whereIn('course_id', $team)->pluck('course_id')->toArray();

                            $compulsory = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where('user_history.get_type', 7)
                                ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();
                            $compulsory_graduate = UserGraduateLog::where('user_id', $user->id)->whereIn('course_id', $compulsory)->pluck('course_id')->toArray();

                            $optional = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where(function ($optional) {
                                $optional->where('user_history.get_type', 1);
                                $optional->orWhere('user_history.get_type', 2);
                                $optional->orWhere('user_history.get_type', 3);
                            })
                                ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();
                            $optional_graduate = UserGraduateLog::where('user_id', $user->id)->whereIn('course_id', $optional)->pluck('course_id')->toArray();

                            $all_course      = array_merge($team, $compulsory, $optional);
                            $cert_course_all = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')
                                ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                                ->where('course.started_date', '<=', Carbon::now())
                                ->whereIn('course.id', $all_course);
                            $cert_course_all->where(function ($cert_course_all) {
                                $cert_course_all->where('course.end_date', '>=', Carbon::now());
                                $cert_course_all->orWhere('course.end_date', null);
                            });
                            $cert_course_all       = $cert_course_all->pluck('course.id')->toArray();
                            $cert_course_all_count = count($cert_course_all);

                            $cert_global = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                                ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                                ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                                ->join('course', 'course.id', 'course_department_log.course_id')
                                ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                                ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                                ->where('categories_department.status', 1)
                                ->where('user_graduate_log.user_id', $user->id)
                                ->where('course.started_date', '<=', Carbon::now())
                                ->whereNotIn('course.id', $cert_course)
                                ->whereIn('course.id', $all_course);
                            $cert_global->where(function ($cert_global) {
                                $cert_global->where('course.end_date', '>=', Carbon::now());
                                $cert_global->orWhere('course.end_date', null);
                            });

                            $cert_global = $cert_global->pluck('course.id')->toArray();

                            $cert_global_count = count($cert_global);

                            $cert_global_all = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                                ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                                ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                                ->join('course', 'course.id', 'course_department_log.course_id')
                                ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                                ->where('categories_department.status', 1)
                                ->where('categories_department.id', $user->dept_id)
                                ->where('course.started_date', '<=', Carbon::now())
                                ->whereNotIn('course.id', $cert_course_all)
                                ->whereIn('course.id', $all_course);
                            $cert_global_all->where(function ($cert_global_all) {
                                $cert_global_all->where('course.end_date', '>=', Carbon::now());
                                $cert_global_all->orWhere('course.end_date', null);
                            });

                            $cert_global_all = $cert_global_all->pluck('course.id')->toArray();

                            $cert_global_all_count = count($cert_global_all);

                            $cert_group_all = DynamicCertGroup::join('course_group', 'course_group.id', 'cert_group_dynamic.series_id')
                                ->join('group_log', 'group_log.group_id', 'course_group.id')
                                ->join('course', 'course.id', 'group_log.course_id')
                                ->select('course_group.id as group_id')
                                ->where('cert_group_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                                ->where('course.started_date', '<=', Carbon::now())
                                ->whereIn('course.id', $all_course)
                                ->groupby('course_group.id');
                            $cert_group_all->where(function ($cert_group_all) {
                                $cert_group_all->where('course.end_date', '>=', Carbon::now());
                                $cert_group_all->orWhere('course.end_date', null);
                            });

                            $cert_group_all       = $cert_group_all->get();
                            $cert_group_all_count = 0;
                            foreach ($cert_group_all as $value) {
                                $group_data_all = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                                    ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                                    ->where('course.started_date', '<=', Carbon::now());
                                $group_data_all->where(function ($group_data_all) {
                                    $group_data_all->where('course.end_date', '>=', Carbon::now());
                                    $group_data_all->orWhere('course.end_date', null);
                                });
                                $group_data_all = $group_data_all->count();

                                $group_data_have = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                                    ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                                    ->where('course.started_date', '<=', Carbon::now())
                                    ->whereIn('course.id', $all_course);
                                $group_data_have->where(function ($group_data_have) {
                                    $group_data_have->where('course.end_date', '>=', Carbon::now());
                                    $group_data_have->orWhere('course.end_date', null);
                                });
                                $group_data_have = $group_data_have->count();

                                if ($group_data_all == $group_data_have) {
                                    $cert_group_all_count++;
                                }
                            }
                            $response['team_course_all']       = count($team);
                            $response['compulsory_course_all'] = count($compulsory);
                            $response['optional_course_all']   = count($optional);

                            $response['team_course_get']       = count($team_graduate);
                            $response['compulsory_course_get'] = count($compulsory_graduate);
                            $response['optional_course_get']   = count($optional_graduate);

                            $response['certificate_all'] = $cert_course_all_count + $cert_group_all_count + $cert_global_all_count;
                            $response['certificate_get'] = $cert_group_count + $cert_course_count + $cert_global_count;

                            $response['status'] = 'success';
                        }
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function profileDashboard(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken) && isset($request->e_id) && isset($request->key)) {
            $user_admin = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            $user       = Users::where('e_id', $request->e_id)->first();
            if ($user_admin && $user) {
                $allow_dashboard = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->where('volumn_by.status', 1)
                    ->where('volumn_by.user_id', $user_admin->id)
                    ->where('volumn_user_log.user_id', $user->id)->first();
                if ($allow_dashboard) {
                    $request->utoken = SimpleEnDeHelper::instance()->encryptString($user->id);
                    if (isset($request->utoken) && isset($request->key)) {
                        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            $response['data'] = [];
                            if ($request->key == 'compulsory') {
                                $course = UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                                    ->where('user_history.user_id', $user->id)->where('user_history.get_type', 7)
                                    ->where('user_history.expired', '>=', Carbon::now())
                                    ->where('course.started_date', '<=', Carbon::now())
                                    ->where('course.status', '!=', 2)
                                    ->where(function ($course) {
                                        $course->where('course.end_date', '>=', Carbon::now());
                                        $course->orWhere('course.end_date', null);
                                    })
                                    ->orderby('user_history.created_at', 'desc')->groupby('user_history.course_id');
                                if (isset($request->utoken)) {
                                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                                    if ($user) {
                                        //กั้นห้อง
                                        // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                        // ->where(function ($course) use($user) {
                                        //   $course->where('categories_department.id',$user->dept_id);
                                        //   $course->orWhere('course.is_option',1);
                                        // })
                                        // ->where(function ($course){
                                        //   $course->where('categories_department.status',1);
                                        //   $course->orWhere('course.is_option',1);
                                        // });
                                    }
                                }
                                $course           = $course->get();
                                $response['data'] = AppHelper::instance()->convertCourse($course, $request->utoken);
                            } else if ($request->key == 'team') {
                                $volume_comp = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                                    ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                                    ->join('course', 'course.id', 'volumn_course_log.course_id')
                                    ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                                    ->where('volumn_by.status', 1)
                                    ->where('course.status', 1)
                                    ->where('volumn_course_log.type', 2)
                                    ->where('course.started_date', '<=', Carbon::now());
                                $volume_comp->where(function ($volume_comp) use ($user) {
                                    $volume_comp->where('volumn_user_log.user_id', $user->id);
                                });
                                $volume_comp->groupby('volumn_course_log.course_id');

                                $volume_comp->where(function ($volume_comp) {
                                    $volume_comp->where('course.end_date', '>=', Carbon::now());
                                    $volume_comp->orWhere('course.end_date', null);
                                });
                                if (isset($request->utoken)) {
                                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                                    if ($user) {
                                        //กั้นห้อง
                                        // $volume_comp->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                        // ->where(function ($volume_comp) use($user) {
                                        //   $volume_comp->where('categories_department.id',$user->dept_id);
                                        //   $volume_comp->orWhere('course.is_option',1);
                                        // })
                                        // ->where(function ($volume_comp){
                                        //   $volume_comp->where('categories_department.status',1);
                                        //   $volume_comp->orWhere('course.is_option',1);
                                        // });
                                    }
                                }

                                $volume_comp = $volume_comp->get();

                                $volume_rec = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                                    ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                                    ->join('course', 'course.id', 'volumn_course_log.course_id')
                                    ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                                    ->where('volumn_by.status', 1)
                                    ->where('course.status', 1)
                                    ->where('volumn_course_log.type', 1)
                                    ->where('course.started_date', '<=', Carbon::now());
                                $volume_rec->where(function ($volume_rec) use ($user) {
                                    $volume_rec->where('volumn_user_log.user_id', $user->id);
                                });
                                $volume_rec->groupby('volumn_course_log.course_id');

                                $volume_rec->where(function ($volume_rec) {
                                    $volume_rec->where('course.end_date', '>=', Carbon::now());
                                    $volume_rec->orWhere('course.end_date', null);
                                });
                                if (isset($request->utoken)) {
                                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                                    if ($user) {
                                        //กั้นห้อง
                                        // $volume_rec->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                        // ->where(function ($volume_rec) use($user) {
                                        //   $volume_rec->where('categories_department.id',$user->dept_id);
                                        //   $volume_rec->orWhere('course.is_option',1);
                                        // })
                                        // ->where(function ($volume_rec){
                                        //   $volume_rec->where('categories_department.status',1);
                                        //   $volume_rec->orWhere('course.is_option',1);
                                        // });
                                    }
                                }

                                $volume_rec = $volume_rec->get();

                                $response['data']     = AppHelper::instance()->convertCourse($volume_comp, $request->utoken);
                                $response['data_rec'] = AppHelper::instance()->convertCourse($volume_rec, $request->utoken);
                            } else if ($request->key == 'favourite') {
                                $course = UsersFavoriteLog::join('course', 'course.id', 'user_favorite_log.course_id')->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                                    ->where('user_favorite_log.user_id', $user->id)
                                    ->where('course.started_date', '<=', Carbon::now())
                                    ->where('course.status', 1)
                                    ->where(function ($course) {
                                        $course->where('course.end_date', '>=', Carbon::now());
                                        $course->orWhere('course.end_date', null);
                                    })
                                    ->groupby('user_favorite_log.course_id')->orderby('user_favorite_log.created_at', 'desc');
                                if (isset($request->utoken)) {
                                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                                    if ($user) {
                                        //กั้นห้อง
                                        // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                        // ->where(function ($course) use($user) {
                                        //   $course->where('categories_department.id',$user->dept_id);
                                        //   $course->orWhere('course.is_option',1);
                                        // })
                                        // ->where(function ($course){
                                        //   $course->where('categories_department.status',1);
                                        //   $course->orWhere('course.is_option',1);
                                        // });
                                    }
                                }
                                $course           = $course->get();
                                $response['data'] = AppHelper::instance()->convertCourse($course, $request->utoken);
                            } else if ($request->key == 'certificate') {
                                $certificate = [];
                                $course_arr  = [];
                                $cert_course = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                                    ->select('course.title_th', 'course.image_th', 'cert_dynamic.id', 'cert_dynamic.lang_type', 'user_graduate_log.created_at', 'course.id as course_id')
                                    ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                                    ->where('user_graduate_log.user_id', $user->id)
                                    ->where('course.started_date', '<=', Carbon::now())
                                    ->orderby('user_graduate_log.created_at', 'desc');
                                $cert_course->where(function ($cert_course) {
                                    $cert_course->where('course.end_date', '>=', Carbon::now());
                                    $cert_course->orWhere('course.end_date', null);
                                });

                                //กั้นห้อง
                                // $cert_course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                // ->where(function ($cert_course) use($user) {
                                //   $cert_course->where('categories_department.id',$user->dept_id);
                                //   $cert_course->orWhere('course.is_option',1);
                                // })
                                // ->where(function ($cert_course){
                                //   $cert_course->where('categories_department.status',1);
                                //   $cert_course->orWhere('course.is_option',1);
                                // });

                                $cert_course = $cert_course->get();

                                foreach ($cert_course as $value) {
                                    array_push($course_arr, $value->course_id);
                                    if ($value->lang_type == 1) {
                                        $data              = new Dummy();
                                        $data['image_th']  = $value->image_th;
                                        $data['title_th']  = $value->title_th;
                                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                        $data['type']      = 'course';
                                        $data['lang_type'] = 'th';
                                        array_push($certificate, $data);
                                    } else if ($value->lang_type == 2) {
                                        $data              = new Dummy();
                                        $data['image_th']  = $value->image_th;
                                        $data['title_th']  = $value->title_th;
                                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                                        $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                        $data['type']      = 'course';
                                        $data['lang_type'] = 'en';
                                        array_push($certificate, $data);
                                    } else {
                                        $data              = new Dummy();
                                        $data['image_th']  = $value->image_th;
                                        $data['title_th']  = $value->title_th;
                                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                        $data['type']      = 'course';
                                        $data['lang_type'] = 'th';
                                        array_push($certificate, $data);

                                        $data_en              = new Dummy();
                                        $data_en['image_th']  = $value->image_th;
                                        $data_en['title_th']  = $value->title_th;
                                        $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course');
                                        $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_course_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                        $data_en['type']      = 'course';
                                        $data_en['lang_type'] = 'en';
                                        array_push($certificate, $data_en);
                                    }
                                }

                                $cert_global = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                                    ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                                    ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                                    ->join('course', 'course.id', 'course_department_log.course_id')
                                    ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                                    ->select('course.title_th', 'course.image_th', 'cert_global.id', 'cert_global.lang_type', 'user_graduate_log.created_at', 'course.id as course_id')
                                    ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                                // ->where('categories_department.status',1)
                                    ->where('user_graduate_log.user_id', $user->id)
                                    ->where('course.started_date', '<=', Carbon::now())
                                    ->whereNotIn('course.id', $course_arr)
                                    ->orderby('user_graduate_log.created_at', 'desc');
                                $cert_global->where(function ($cert_global) {
                                    $cert_global->where('course.end_date', '>=', Carbon::now());
                                    $cert_global->orWhere('course.end_date', null);
                                });

                                //กั้นห้อง
                                // $cert_global->where(function ($cert_global) use($user) {
                                //   $cert_global->where('categories_department.id',$user->dept_id);
                                //   $cert_global->orWhere('course.is_option',1);
                                // })
                                // ->where(function ($cert_global){
                                //   $cert_global->where('categories_department.status',1);
                                //   $cert_global->orWhere('course.is_option',1);
                                // });

                                $cert_global = $cert_global->get();

                                foreach ($cert_global as $value) {
                                    if ($value->lang_type == 1) {
                                        $data              = new Dummy();
                                        $data['image_th']  = $value->image_th;
                                        $data['title_th']  = $value->title_th;
                                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                                        $data['type']      = 'global';
                                        $data['lang_type'] = 'th';
                                        array_push($certificate, $data);
                                    } else if ($value->lang_type == 2) {
                                        $data              = new Dummy();
                                        $data['image_th']  = $value->image_th;
                                        $data['title_th']  = $value->title_th;
                                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                                        $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                                        $data['type']      = 'global';
                                        $data['lang_type'] = 'en';
                                        array_push($certificate, $data);
                                    } else {
                                        $data              = new Dummy();
                                        $data['image_th']  = $value->image_th;
                                        $data['title_th']  = $value->title_th;
                                        $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                                        $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                                        $data['type']      = 'global';
                                        $data['lang_type'] = 'th';
                                        array_push($certificate, $data);

                                        $data_en              = new Dummy();
                                        $data_en['image_th']  = $value->image_th;
                                        $data_en['title_th']  = $value->title_th;
                                        $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global');
                                        $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_global_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->course_id);
                                        $data_en['type']      = 'global';
                                        $data_en['lang_type'] = 'en';
                                        array_push($certificate, $data_en);
                                    }
                                }

                                $cert_group = DynamicCertGroup::join('course_group', 'course_group.id', 'cert_group_dynamic.series_id')
                                    ->join('group_log', 'group_log.group_id', 'course_group.id')
                                    ->join('course', 'course.id', 'group_log.course_id')
                                    ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                                    ->select('cert_group_dynamic.series_name as title_th', 'course_group.thumb as image_th', 'cert_group_dynamic.id', 'cert_group_dynamic.lang_type', 'user_graduate_log.created_at', 'course_group.id as group_id')
                                    ->where('cert_group_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                                    ->where('course.started_date', '<=', Carbon::now())
                                    ->where('user_graduate_log.user_id', $user->id)
                                    ->orderby('user_graduate_log.created_at', 'desc')->groupby('course_group.id');
                                $cert_group->where(function ($cert_group) {
                                    $cert_group->where('course.end_date', '>=', Carbon::now());
                                    $cert_group->orWhere('course.end_date', null);
                                });

                                //กั้นห้อง
                                // $cert_group->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                // ->where(function ($cert_group) use($user) {
                                //   $cert_group->where('categories_department.id',$user->dept_id);
                                //   $cert_group->orWhere('course.is_option',1);
                                // })
                                // ->where(function ($cert_group){
                                //   $cert_group->where('categories_department.status',1);
                                //   $cert_group->orWhere('course.is_option',1);
                                // });

                                $cert_group = $cert_group->get();

                                foreach ($cert_group as $value) {
                                    $check_fail = 0;
                                    $group_data = CourseGroup::join('group_log', 'group_log.group_id', 'course_group.id')->join('course', 'course.id', 'group_log.course_id')
                                        ->select('course.id')->where('course_group.id', $value->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
                                        ->where('course.started_date', '<=', Carbon::now());
                                    $group_data->where(function ($group_data) {
                                        $group_data->where('course.end_date', '>=', Carbon::now());
                                        $group_data->orWhere('course.end_date', null);
                                    });

                                    $group_data->leftjoin('course_department_log', 'course_department_log.course_id', 'course.id')
                                        ->leftjoin('categories_department', 'categories_department.id', 'course_department_log.department')
                                        ->where(function ($group_data) use ($user) {
                                            $group_data->where('categories_department.id', $user->dept_id);
                                            $group_data->orWhere('course.is_option', 1);
                                        })
                                        ->where(function ($group_data) {
                                            $group_data->where('categories_department.status', 1);
                                            $group_data->orWhere('course.is_option', 1);
                                        });

                                    $group_data = $group_data->get();
                                    foreach ($group_data as $sub) {
                                        $check_pass = UserGraduateLog::where('user_id', $user->id)->where('course_id', $sub->id)->first();
                                        if (! $check_pass) {
                                            $check_fail++;
                                            break;
                                        }
                                    }
                                    if ($check_fail == 0 && count($group_data) > 0) {
                                        if ($value->lang_type == 1) {
                                            $data              = new Dummy();
                                            $data['image_th']  = $value->image_th;
                                            $data['title_th']  = $value->title_th;
                                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                            $data['type']      = 'group';
                                            $data['lang_type'] = 'th';
                                            array_push($certificate, $data);
                                        } else if ($value->lang_type == 2) {
                                            $data              = new Dummy();
                                            $data['image_th']  = $value->image_th;
                                            $data['title_th']  = $value->title_th;
                                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                            $data['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                            $data['type']      = 'group';
                                            $data['lang_type'] = 'en';
                                            array_push($certificate, $data);
                                        } else {
                                            $data              = new Dummy();
                                            $data['image_th']  = $value->image_th;
                                            $data['title_th']  = $value->title_th;
                                            $data['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                            $data['link']      = env('APP_URL') . '/th/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                            $data['type']      = 'group';
                                            $data['lang_type'] = 'th';
                                            array_push($certificate, $data);

                                            $data_en              = new Dummy();
                                            $data_en['image_th']  = $value->image_th;
                                            $data_en['title_th']  = $value->title_th;
                                            $data_en['slug']      = SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group');
                                            $data_en['link']      = env('APP_URL') . '/en/certificate/' . SimpleEnDeHelper::instance()->encryptString($value->id . '_type_group_type_' . $user->id . '_type_' . $value->created_at . '_type_' . $value->id);
                                            $data_en['type']      = 'group';
                                            $data_en['lang_type'] = 'en';
                                            array_push($certificate, $data_en);
                                        }
                                    }
                                }

                                usort($certificate, function ($a1, $a2) {
                                    $v1 = $a1['created_at'];
                                    $v2 = $a2['created_at'];
                                    return strtotime($v2) - strtotime($v1);
                                });

                                $response['data'] = $certificate;
                            } else if ($request->key == 'other') {
                                $course = UserHistory::join('course', 'course.id', 'user_history.course_id')->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                                // ->where('user_history.user_id',$user->id)->where(function ($course){
                                //   $course->where('user_history.get_type',1);
                                //   $course->orWhere('user_history.get_type',2);
                                //   $course->orWhere('user_history.get_type',3);
                                // })
                                    ->where('course.started_date', '<=', Carbon::now())
                                    ->where('course.status', '!=', 2)
                                    ->where(function ($course) {
                                        $course->where('course.end_date', '>=', Carbon::now());
                                        $course->orWhere('course.end_date', null);
                                    })
                                    ->where('user_history.expired', '>=', Carbon::now())->orderby('user_history.created_at', 'desc')->groupby('user_history.course_id');
                                if (isset($request->utoken)) {
                                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                                    if ($user) {
                                        //กั้นห้อง
                                        // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                        // ->where(function ($course) use($user) {
                                        //   $course->where('categories_department.id',$user->dept_id);
                                        //   $course->orWhere('course.is_option',1);
                                        // })
                                        // ->where(function ($course){
                                        //   $course->where('categories_department.status',1);
                                        //   $course->orWhere('course.is_option',1);
                                        // });
                                    }
                                }
                                $course           = $course->get();
                                $response['data'] = AppHelper::instance()->convertCourse($course, $request->utoken);
                            } else if ($request->key == 'curriculum') {
                                $group_arr = [];
                                $group     = CourseGroup::join('course_group_department', 'course_group_department.group_id', 'course_group.id')
                                    ->where('course_group_department.dept_id', $user->dept_id)->where('course_group.status', 1)->get();
                                foreach ($group as $key => $value) {
                                    $course_1 = Course::join('group_log', 'group_log.course_id', 'course.id')
                                        ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
                                        ->where('course.started_date', '<=', Carbon::now())
                                        ->where('course.status', 1)
                                        ->where('group_log.group_id', $value->id)
                                        ->where(function ($course_1) {
                                            $course_1->where('course.started_learning', '<=', Carbon::now())
                                                ->orWhereNull('course.started_learning');
                                        })
                                        ->groupby('course.id');
                                    $course_1->where(function ($course_1) {
                                        $course_1->where('course.end_date', '>=', Carbon::now());
                                        $course_1->orWhere('course.end_date', null);
                                    });
                                    if (isset($request->utoken)) {
                                        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                                        if ($user) {
                                            //กั้นห้อง
                                            // $course_1->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                            // ->where(function ($course_1) use($user) {
                                            //   $course_1->where('categories_department.id',$user->dept_id);
                                            //   $course_1->orWhere('course.is_option',1);
                                            // })
                                            // ->where(function ($course_1){
                                            //   $course_1->where('categories_department.status',1);
                                            //   $course_1->orWhere('course.is_option',1);
                                            // });
                                        }
                                    }

                                    $course_1 = $course_1->get();
                                    if (count($course_1) > 0) {
                                        $course_1       = AppHelper::instance()->convertCourse($course_1, $request->utoken);
                                        $dummy          = new Dummy();
                                        $dummy['title'] = $value->title;
                                        $dummy['data']  = $course_1;
                                        array_push($group_arr, $dummy);
                                    }
                                }
                                $response['data'] = $group_arr;
                            }
                            $response['status'] = 'success';
                        }
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function teamStat(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $is_team = VolumnBy::where('volumn_by.status', 1)->where('volumn_by.user_id', $user->id)->first();
                if ($is_team) {
                    $response['status']          = 'success';
                    $response['team_member']     = 0;
                    $response['team_name']       = $is_team->company_name;
                    $response['team_image']      = $is_team->company_image;
                    $response['team_cert']       = 0;
                    $response['team_point']      = 0;
                    $response['team_comp_all']   = 0;
                    $response['team_comp_get']   = 0;
                    $response['team_course_all'] = 0;
                    $response['team_course_get'] = 0;
                    $team_user                   = VolumnByUser::join('user', 'user.id', 'volumn_user_log.user_id')
                        ->select('user.e_id', 'user.name', 'user.lastname', 'user.avatar', 'user.id', 'user.point', 'user.started_date', 'user.position', 'user.dept_id')
                        ->where('user.status', 1)
                        ->where('volumn_user_log.company_id', $is_team->id)->orderby('volumn_user_log.type', 'desc')->orderby('user.name', 'asc')->get();
                    foreach ($team_user as $key => $value) {
                        $volume = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                            ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                            ->join('course', 'course.id', 'volumn_course_log.course_id')
                            ->select('volumn_course_log.course_id')
                            ->where('volumn_course_log.type', 2)
                            ->where('volumn_by.status', 1)
                            ->where('course.status', 1)
                            ->where('course.started_date', '<=', Carbon::now());
                        $volume->where(function ($volume) use ($value) {
                            $volume->where('volumn_user_log.user_id', $value->id);
                        });
                        $volume->groupby('volumn_course_log.course_id');

                        $volume->where(function ($volume) {
                            $volume->where('course.end_date', '>=', Carbon::now());
                            $volume->orWhere('course.end_date', null);
                        });
                        //กั้นห้อง
                        // $volume->leftjoin('course_department_log','course_department_log.course_id','course.id')
                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                        // ->where(function ($volume) use($value) {
                        //   $volume->where('categories_department.id',$value->dept_id);
                        //   $volume->orWhere('course.is_option',1);
                        // })
                        // ->where(function ($volume){
                        //   $volume->where('categories_department.status',1);
                        //   $volume->orWhere('course.is_option',1);
                        // });

                        $volume = $volume->pluck('volumn_course_log.course_id')->toArray();

                        $team          = $volume;
                        $team_graduate = UserGraduateLog::where('user_id', $value->id)->whereIn('course_id', $team)->pluck('course_id')->toArray();

                        $compulsory = UserHistory::select('user_history.course_id')->where('user_history.user_id', $value->id)->where('user_history.get_type', 7)
                            ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();
                        $compulsory_graduate = UserGraduateLog::where('user_id', $value->id)->whereIn('course_id', $compulsory)->pluck('course_id')->toArray();

                        $all_course = array_merge($team, $compulsory);

                        $cert_course = DynamicCert::join('course', 'course.id', 'cert_dynamic.course_id')->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                            ->where('cert_dynamic.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                            ->where('user_graduate_log.user_id', $value->id)
                            ->where('course.started_date', '<=', Carbon::now())
                            ->whereIn('course.id', $all_course);
                        $cert_course->where(function ($cert_course) {
                            $cert_course->where('course.end_date', '>=', Carbon::now());
                            $cert_course->orWhere('course.end_date', null);
                        });

                        $cert_course = $cert_course->pluck('course.id')->toArray();

                        $cert_course_count = count($cert_course);

                        $cert_global = GlobalCert::join('cert_global_log', 'cert_global_log.cert_global_id', 'cert_global.id')
                            ->join('categories_department', 'categories_department.id', 'cert_global_log.category_id')
                            ->join('course_department_log', 'course_department_log.department', 'categories_department.id')
                            ->join('course', 'course.id', 'course_department_log.course_id')
                            ->join('user_graduate_log', 'user_graduate_log.course_id', 'course.id')
                            ->where('cert_global.status', 1)->where('course.status', 1)->where('course.is_cert', 1)
                            ->where('categories_department.status', 1)
                            ->where('user_graduate_log.user_id', $value->id)
                            ->where('course.started_date', '<=', Carbon::now())
                            ->whereNotIn('course.id', $cert_course)
                            ->whereIn('course.id', $all_course);
                        $cert_global->where(function ($cert_global) {
                            $cert_global->where('course.end_date', '>=', Carbon::now());
                            $cert_global->orWhere('course.end_date', null);
                        });

                        $cert_global = $cert_global->pluck('course.id')->toArray();

                        $cert_global_count = count($cert_global);

                        $user_point = UsersPointLog::whereIn('course_id', $all_course)->where('user_id', $value->id)->sum('point');

                        $team_user[$key]['started_date']          = Carbon::parse($value->started_date)->format('d/m/y');
                        $team_user[$key]['team_course_all']       = count($team);
                        $team_user[$key]['compulsory_course_all'] = count($compulsory);

                        $team_user[$key]['team_course_get']       = count($team_graduate);
                        $team_user[$key]['compulsory_course_get'] = count($compulsory_graduate);

                        $team_user[$key]['certificate_get'] = $cert_course_count + $cert_global_count;
                        $team_user[$key]['user_point']      = $user_point;

                        if ($is_team->user_id == $value->id) {
                            $team_user[$key]['role'] = 'admin';
                        } else {
                            $team_user[$key]['role'] = 'user';
                        }
                        $response['team_cert'] += $team_user[$key]['certificate_get'];
                        $response['team_point'] += $team_user[$key]['user_point'];
                        $response['team_comp_all'] += $team_user[$key]['compulsory_course_all'];
                        $response['team_comp_get'] += $team_user[$key]['compulsory_course_get'];
                        $response['team_course_all'] += $team_user[$key]['team_course_all'];
                        $response['team_course_get'] += $team_user[$key]['team_course_get'];
                    }
                    $response['team_id']     = $is_team->id;
                    $response['team_user']   = $team_user;
                    $response['team_member'] = count($team_user);
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function addTeamUser(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken) && isset($request->team_id) && isset($request->user_id)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $is_team = VolumnBy::where('volumn_by.status', 1)->where('volumn_by.user_id', $user->id)->where('volumn_by.id', $request->team_id)->first();
                if ($is_team) {
                    $check = VolumnByUser::where('company_id', $is_team->id)->where('user_id', $request->user_id)->first();
                    if (! $check) {
                        $add             = new VolumnByUser();
                        $add->company_id = $is_team->id;
                        $add->user_id    = $request->user_id;
                        $add->type       = 1;
                        $add->save();
                        $response['status'] = 'success';
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function removeTeamUser(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken) && isset($request->team_id) && isset($request->user_id)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $is_team = VolumnBy::where('volumn_by.status', 1)->where('volumn_by.user_id', $user->id)->where('volumn_by.id', $request->team_id)->first();
                if ($is_team) {
                    $vol_user = VolumnByUser::where('company_id', $is_team->id)->where('user_id', $request->user_id)->where('type', 1)->first();
                    if ($vol_user) {
                        $vol_course = UserHistory::where('user_id', $vol_user->user_id)->where('get_type', 6)->where('company_id', $is_team->id)->where('company_status', 1)->get();
                        foreach ($vol_course as $val_his) {
                            $val_his->company_status = 2;
                            $val_his->expired        = Carbon::now();
                            $val_his->save();
                        }
                        $vol_user->delete();
                        $response['status'] = 'success';
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function getTeamUser(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $is_team = VolumnBy::where('volumn_by.status', 1)->where('volumn_by.user_id', $user->id)->first();
                if ($is_team) {
                    $user_log     = VolumnByUser::pluck('user_id')->toArray();
                    $list_user    = User::select('id as value', 'id as key', 'name', 'lastname')->whereNotIn('id', $user_log)->where('dept_id', $user->dept_id)->where('status', 1)->orderby('name', 'asc')->get();
                    $dum          = new Dummy();
                    $dum['value'] = '';
                    $dum['key']   = '';
                    $dum['label'] = 'เลือกผู้ใช้งาน';
                    $user_arr     = [];
                    array_push($user_arr, $dum);
                    foreach ($list_user as $val) {
                        $val->label = $val->name . ' ' . $val->lastname;
                        array_push($user_arr, $val);
                    }
                    $response['data']   = $user_arr;
                    $response['status'] = 'success';
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function userReport(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $ex_array           = [];
        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {

                $volume = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                    ->join('course', 'course.id', 'volumn_course_log.course_id')
                    ->select('volumn_course_log.course_id')
                    ->where('volumn_course_log.type', 2)
                    ->where('volumn_by.status', 1)
                    ->where('course.status', 1)
                    ->where('course.started_date', '<=', Carbon::now());
                $volume->where(function ($volume) use ($user) {
                    $volume->where('volumn_user_log.user_id', $user->id);
                });
                $volume->groupby('volumn_course_log.course_id');

                $volume->where(function ($volume) {
                    $volume->where('course.end_date', '>=', Carbon::now());
                    $volume->orWhere('course.end_date', null);
                });
                if (isset($request->utoken)) {
                    $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                    if ($user) {
                        //กั้นห้อง
                        // $volume->leftjoin('course_department_log','course_department_log.course_id','course.id')
                        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                        // ->where(function ($volume) use($user) {
                        //   $volume->where('categories_department.id',$user->dept_id);
                        //   $volume->orWhere('course.is_option',1);
                        // })
                        // ->where(function ($volume){
                        //   $volume->where('categories_department.status',1);
                        //   $volume->orWhere('course.is_option',1);
                        // });
                    }
                }

                $volume = $volume->pluck('volumn_course_log.course_id')->toArray();

                $team = $volume;

                $compulsory = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where('user_history.get_type', 7)
                    ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();

                $optional = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where(function ($optional) {
                    $optional->where('user_history.get_type', 1);
                    $optional->orWhere('user_history.get_type', 2);
                    $optional->orWhere('user_history.get_type', 3);
                })
                    ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();

                $arr_index = 0;
                foreach ($team as $e) {
                    $name        = '';
                    $lastname    = '';
                    $course_data = AppHelper::instance()->userExportCourse($user, $e);
                    if ($course_data != null) {
                        if ($arr_index == 0) {
                            $name     = $user->name;
                            $lastname = $user->lastname;
                        }
                        $ex_array[] = [
                            'Name'            => $name,
                            'Lastname'        => $lastname,
                            'Course Name'     => $course_data->course_name,
                            'Course Type'     => 'Team Course',
                            'Course Progress' => $course_data->percent,
                            'Certificate'     => $course_data->certificate,
                            'Point'           => $course_data->point,
                            'Graduated'       => $course_data->graduated,
                        ];
                        $arr_index++;
                    }
                }
                foreach ($compulsory as $e) {
                    $name        = '';
                    $lastname    = '';
                    $course_data = AppHelper::instance()->userExportCourse($user, $e);
                    if ($course_data != null) {
                        if ($arr_index == 0) {
                            $name     = $user->name;
                            $lastname = $user->lastname;
                        }
                        $ex_array[] = [
                            'Name'            => $name,
                            'Lastname'        => $lastname,
                            'Course Name'     => $course_data->course_name,
                            'Course Type'     => 'Compulsory Course',
                            'Course Progress' => $course_data->percent,
                            'Certificate'     => $course_data->certificate,
                            'Point'           => $course_data->point,
                            'Graduated'       => $course_data->graduated,
                        ];
                        $arr_index++;
                    }
                }
                foreach ($optional as $e) {
                    $name        = '';
                    $lastname    = '';
                    $course_data = AppHelper::instance()->userExportCourse($user, $e);
                    if ($course_data != null) {
                        if ($arr_index == 0) {
                            $name     = $user->name;
                            $lastname = $user->lastname;
                        }
                        $ex_array[] = [
                            'Name'            => $name,
                            'Lastname'        => $lastname,
                            'Course Name'     => $course_data->course_name,
                            'Course Type'     => 'Optional Course',
                            'Course Progress' => $course_data->percent,
                            'Certificate'     => $course_data->certificate,
                            'Point'           => $course_data->point,
                            'Graduated'       => $course_data->graduated,
                        ];
                        $arr_index++;
                    }
                }

                $response['status'] = 'success';
                $response['data']   = $ex_array;
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function userTeamReport(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $ex_array           = [];
        if (isset($request->utoken) && isset($request->team_id)) {
            $head = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($head) {
                $is_team = VolumnBy::where('id', $request->team_id)->where('volumn_by.status', 1)->where('volumn_by.user_id', $head->id)->first();
                if ($is_team) {
                    $list_user = VolumnByUser::join('user', 'user.id', 'volumn_user_log.user_id')
                        ->select('volumn_user_log.user_id')->where('user.status', 1)
                        ->where('volumn_user_log.company_id', $is_team->id)->orderby('volumn_user_log.type', 'desc')->orderby('user.name', 'asc')->get();
                    foreach ($list_user as $val_u) {
                        $user = Users::where('id', $val_u->user_id)->first();
                        if ($user) {
                            $volume = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                                ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                                ->join('course', 'course.id', 'volumn_course_log.course_id')
                                ->select('volumn_course_log.course_id')
                                ->where('volumn_course_log.type', 2)
                                ->where('volumn_by.status', 1)
                                ->where('course.status', 1)
                                ->where('course.started_date', '<=', Carbon::now());
                            $volume->where(function ($volume) use ($user) {
                                $volume->where('volumn_user_log.user_id', $user->id);
                            });
                            $volume->groupby('volumn_course_log.course_id');

                            $volume->where(function ($volume) {
                                $volume->where('course.end_date', '>=', Carbon::now());
                                $volume->orWhere('course.end_date', null);
                            });
                            //กั้นห้อง
                            // $volume->leftjoin('course_department_log','course_department_log.course_id','course.id')
                            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                            // ->where(function ($volume) use($user) {
                            //   $volume->where('categories_department.id',$user->dept_id);
                            //   $volume->orWhere('course.is_option',1);
                            // })
                            // ->where(function ($volume){
                            //   $volume->where('categories_department.status',1);
                            //   $volume->orWhere('course.is_option',1);
                            // });

                            $volume = $volume->pluck('volumn_course_log.course_id')->toArray();

                            $team = $volume;

                            $compulsory = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where('user_history.get_type', 7)
                                ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();

                            $optional = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where(function ($optional) {
                                $optional->where('user_history.get_type', 1);
                                $optional->orWhere('user_history.get_type', 2);
                                $optional->orWhere('user_history.get_type', 3);
                            })
                                ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();

                            $arr_index = 0;
                            foreach ($team as $e) {
                                $name        = '';
                                $lastname    = '';
                                $course_data = AppHelper::instance()->userExportCourse($user, $e);
                                if ($course_data != null) {
                                    if ($arr_index == 0) {
                                        $name     = $user->name;
                                        $lastname = $user->lastname;
                                    }
                                    $ex_array[] = [
                                        'Name'            => $name,
                                        'Lastname'        => $lastname,
                                        'Course Name'     => $course_data->course_name,
                                        'Course Type'     => 'Team Course',
                                        'Course Progress' => $course_data->percent,
                                        'Certificate'     => $course_data->certificate,
                                        'Point'           => $course_data->point,
                                        'Graduated'       => $course_data->graduated,
                                    ];
                                    $arr_index++;
                                }
                            }
                            foreach ($compulsory as $e) {
                                $name        = '';
                                $lastname    = '';
                                $course_data = AppHelper::instance()->userExportCourse($user, $e);
                                if ($course_data != null) {
                                    if ($arr_index == 0) {
                                        $name     = $user->name;
                                        $lastname = $user->lastname;
                                    }
                                    $ex_array[] = [
                                        'Name'            => $name,
                                        'Lastname'        => $lastname,
                                        'Course Name'     => $course_data->course_name,
                                        'Course Type'     => 'Compulsory Course',
                                        'Course Progress' => $course_data->percent,
                                        'Certificate'     => $course_data->certificate,
                                        'Point'           => $course_data->point,
                                        'Graduated'       => $course_data->graduated,
                                    ];
                                    $arr_index++;
                                }
                            }
                            foreach ($optional as $e) {
                                $name        = '';
                                $lastname    = '';
                                $course_data = AppHelper::instance()->userExportCourse($user, $e);
                                if ($course_data != null) {
                                    if ($arr_index == 0) {
                                        $name     = $user->name;
                                        $lastname = $user->lastname;
                                    }
                                    $ex_array[] = [
                                        'Name'            => $name,
                                        'Lastname'        => $lastname,
                                        'Course Name'     => $course_data->course_name,
                                        'Course Type'     => 'Optional Course',
                                        'Course Progress' => $course_data->percent,
                                        'Certificate'     => $course_data->certificate,
                                        'Point'           => $course_data->point,
                                        'Graduated'       => $course_data->graduated,
                                    ];
                                    $arr_index++;
                                }
                            }
                        }
                    }
                    $response['team'] = $list_user;
                }
            }
            $response['status'] = 'success';
            $response['data']   = $ex_array;
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function profileReport(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $ex_array           = [];
        if (isset($request->utoken) && isset($request->e_id)) {
            $user_admin = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            $user       = Users::where('e_id', $request->e_id)->first();
            if ($user_admin && $user) {
                $allow_dashboard = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                    ->where('volumn_by.status', 1)
                    ->where('volumn_by.user_id', $user_admin->id)
                    ->where('volumn_user_log.user_id', $user->id)->first();
                if ($allow_dashboard) {
                    $request->utoken = SimpleEnDeHelper::instance()->encryptString($user->id);
                    $user            = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                    if ($user) {

                        $volume = VolumnByUser::join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                            ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_user_log.company_id')
                            ->join('course', 'course.id', 'volumn_course_log.course_id')
                            ->select('volumn_course_log.course_id')
                            ->where('volumn_course_log.type', 2)
                            ->where('volumn_by.status', 1)
                            ->where('course.status', 1)
                            ->where('course.started_date', '<=', Carbon::now());
                        $volume->where(function ($volume) use ($user) {
                            $volume->where('volumn_user_log.user_id', $user->id);
                        });
                        $volume->groupby('volumn_course_log.course_id');

                        $volume->where(function ($volume) {
                            $volume->where('course.end_date', '>=', Carbon::now());
                            $volume->orWhere('course.end_date', null);
                        });
                        if (isset($request->utoken)) {
                            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                            if ($user) {
                                //กั้นห้อง
                                // $volume->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                // ->where(function ($volume) use($user) {
                                //   $volume->where('categories_department.id',$user->dept_id);
                                //   $volume->orWhere('course.is_option',1);
                                // })
                                // ->where(function ($volume){
                                //   $volume->where('categories_department.status',1);
                                //   $volume->orWhere('course.is_option',1);
                                // });
                            }
                        }

                        $volume = $volume->pluck('volumn_course_log.course_id')->toArray();

                        $team = $volume;

                        $compulsory = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where('user_history.get_type', 7)
                            ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();

                        $optional = UserHistory::select('user_history.course_id')->where('user_history.user_id', $user->id)->where(function ($optional) {
                            $optional->where('user_history.get_type', 1);
                            $optional->orWhere('user_history.get_type', 2);
                            $optional->orWhere('user_history.get_type', 3);
                        })
                            ->where('user_history.expired', '>=', Carbon::now())->groupby('user_history.course_id')->pluck('user_history.course_id')->toArray();

                        $arr_index = 0;
                        foreach ($team as $e) {
                            $name        = '';
                            $lastname    = '';
                            $course_data = AppHelper::instance()->userExportCourse($user, $e);
                            if ($course_data != null) {
                                if ($arr_index == 0) {
                                    $name     = $user->name;
                                    $lastname = $user->lastname;
                                }
                                $ex_array[] = [
                                    'Name'            => $name,
                                    'Lastname'        => $lastname,
                                    'Course Name'     => $course_data->course_name,
                                    'Course Type'     => 'Team Course',
                                    'Course Progress' => $course_data->percent,
                                    'Certificate'     => $course_data->certificate,
                                    'Point'           => $course_data->point,
                                    'Graduated'       => $course_data->graduated,
                                ];
                                $arr_index++;
                            }
                        }
                        foreach ($compulsory as $e) {
                            $name        = '';
                            $lastname    = '';
                            $course_data = AppHelper::instance()->userExportCourse($user, $e);
                            if ($course_data != null) {
                                if ($arr_index == 0) {
                                    $name     = $user->name;
                                    $lastname = $user->lastname;
                                }
                                $ex_array[] = [
                                    'Name'            => $name,
                                    'Lastname'        => $lastname,
                                    'Course Name'     => $course_data->course_name,
                                    'Course Type'     => 'Compulsory Course',
                                    'Course Progress' => $course_data->percent,
                                    'Certificate'     => $course_data->certificate,
                                    'Point'           => $course_data->point,
                                    'Graduated'       => $course_data->graduated,
                                ];
                                $arr_index++;
                            }
                        }
                        foreach ($optional as $e) {
                            $name        = '';
                            $lastname    = '';
                            $course_data = AppHelper::instance()->userExportCourse($user, $e);
                            if ($course_data != null) {
                                if ($arr_index == 0) {
                                    $name     = $user->name;
                                    $lastname = $user->lastname;
                                }
                                $ex_array[] = [
                                    'Name'            => $name,
                                    'Lastname'        => $lastname,
                                    'Course Name'     => $course_data->course_name,
                                    'Course Type'     => 'Optional Course',
                                    'Course Progress' => $course_data->percent,
                                    'Certificate'     => $course_data->certificate,
                                    'Point'           => $course_data->point,
                                    'Graduated'       => $course_data->graduated,
                                ];
                                $arr_index++;
                            }
                        }

                        $response['status'] = 'success';
                        $response['data']   = $ex_array;
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function teamCourseData(Request $request)
    {
        $response                 = new Dummy();
        $response['status']       = 'false';
        $response['name']         = $request->name;
        $response['job_function'] = $request->job_function;
        $response['start']        = $request->start;
        $response['end']          = $request->end;
        $response['categories']   = $request->categories;
        $response['speaker']      = $request->speaker;
        $response['level']        = $request->level;
        $response['keyword']      = $request->keyword;
        $response['utoken']       = $request->utoken;
        $response['team_id']      = $request->team_id;

        if (isset($request->utoken) && isset($request->team_id)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $is_team = VolumnBy::where('volumn_by.status', 1)->where('volumn_by.user_id', $user->id)->where('id', $request->team_id)->first();
                if ($is_team) {
                    $course_data   = [];
                    $compulsory_id = [];
                    $optional_id   = [];
                    for ($i = 1; $i <= 3; $i++) {
                        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
                        if ($i == 1) {
                            $course->join('volumn_course_log', 'volumn_course_log.course_id', 'course.id');
                            $course->where('volumn_course_log.type', 2);
                            $course->where('volumn_course_log.status', 1);
                            $course->where('volumn_course_log.company_id', $is_team->id);
                        } else if ($i == 2) {
                            $course->join('volumn_course_log', 'volumn_course_log.course_id', 'course.id');
                            $course->where('volumn_course_log.type', 1);
                            $course->where('volumn_course_log.status', 1);
                            $course->where('volumn_course_log.company_id', $is_team->id);
                        } else if ($i == 3) {
                            $merge_id = array_merge($compulsory_id, $optional_id);
                            $course->whereNotIn('course.id', $merge_id);
                        }
                        $course->where(function ($course) {
                            $course->where('course.end_date', '>=', Carbon::now());
                            $course->orWhere('course.end_date', null);
                        });

                        if (isset($request->utoken)) {
                            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                            if ($user) {
                                //กั้นห้อง
                                // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                                // ->where(function ($course) use($user) {
                                //   $course->where('categories_department.id',$user->dept_id);
                                //   $course->orWhere('course.is_option',1);
                                // })
                                // ->where(function ($course){
                                //   $course->where('categories_department.status',1);
                                //   $course->orWhere('course.is_option',1);
                                // });
                            }
                        }
                        $course->leftjoin('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        $course->leftjoin('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
                        $course->select('course.title_th', 'course.id', 'course.slug', 'course.course_key');

                        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
                            $val_keyword = $request->keyword;
                            $course->where(function ($course) use ($val_keyword) {
                                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
                            });
                        }
                        if (isset($request->name) && $request->name != null && $request->name != '' && $request->name != 'null') {
                            $val_name = $request->name;
                            $course->where(function ($course) use ($val_name) {
                                $course->where(DB::raw('lower(course.title_th)'), strtolower($val_name))
                                    ->Orwhere(DB::raw('lower(course.title_en)'), strtolower($val_name));
                            });
                        }
                        if (isset($request->job_function) && $request->job_function != null && $request->job_function != '' && $request->job_function != 'null') {
                            $course->join('course_job_function_log', 'course_job_function_log.course_id', 'course.id');
                            $course->join('categories_job_function', 'categories_job_function.id', 'course_job_function_log.job_function_id');
                            $course->where('categories_job_function.status', 1);
                            $course->where('categories_job_function.id', $request->job_function);
                        }
                        if (isset($request->categories) && $request->categories != null && $request->categories != '' && $request->categories != 'null') {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                            $course->join('categories', 'categories.id', 'course_categories_log.cate_id');
                            $course->where('categories.status', 1);
                            $course->where('categories.id', $request->categories);
                        }
                        if (isset($request->speaker) && $request->speaker != null && $request->speaker != '' && $request->speaker != 'null') {
                            $course->where('categories_speaker.status', 1);
                            $course->where('categories_speaker.id', $request->speaker);
                        }
                        if (isset($request->level) && $request->level != null && $request->level != '' && $request->level != 'null') {
                            $course->join('categories_level', 'categories_level.id', 'course.level');
                            $course->where('categories_level.status', 1);
                            $course->where('course.level', $request->level);
                        }
                        if (isset($request->start) && $request->start != null && $request->start != '' && $request->start != 'null') {
                        } else {
                            $request->start = '2022-01-01';
                        }
                        if (isset($request->end) && $request->end != null && $request->end != '' && $request->end != 'null') {
                        } else {
                            $request->end = '2122-01-01';
                        }
                        $course->whereBetween('course.started_learning', [$request->start, $request->end]);

                        $course->groupby('course.id');
                        $course->orderby('course.started_date', 'desc');
                        $course->orderby('course.id', 'desc');

                        if ($i == 1) {
                            $compulsory_id = $course->pluck('course.id')->toArray();
                        } else if ($i == 2) {
                            $optional_id = $course->pluck('course.id')->toArray();
                        }

                        $course = $course->get();

                        foreach ($course as $val_c) {
                            if ($i == 1) {
                                $val_c->is_have = 2;
                            } else if ($i == 2) {
                                $val_c->is_have = 1;
                            } else if ($i == 3) {
                                $val_c->is_have = '';
                            }
                            array_push($course_data, $val_c);
                        }
                    }
                    $response['data']   = $course_data;
                    $response['status'] = 'success';
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function assignTeamCourse(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        if (isset($request->utoken) && isset($request->team_id) && isset($request->data)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $is_team = VolumnBy::where('volumn_by.status', 1)->where('volumn_by.user_id', $user->id)->where('id', $request->team_id)->first();
                if ($is_team) {
                    $data   = json_decode($request->data);
                    $in_log = [];
                    foreach ($data as $val_data) {
                        $log = VolumnByCourse::where('company_id', $is_team->id)->where('course_id', $val_data->id)->first();
                        if ($log) {
                            $log->status = 1;
                            $log->type   = $val_data->type;
                            $log->save();
                        } else {
                            $log             = new VolumnByCourse();
                            $log->company_id = $is_team->id;
                            $log->course_id  = $val_data->id;
                            $log->status     = 1;
                            $log->type       = $val_data->type;
                            $log->save();
                        }
                        array_push($in_log, $val_data->id);
                    }
                    $remove = VolumnByCourse::where('company_id', $is_team->id)->whereNotIn('course_id', $in_log)->get();
                    foreach ($remove as $log_remove) {
                        $vol_course = UserHistory::where('course_id', $log_remove->course_id)->where('get_type', 6)->where('company_id', $is_team->id)->where('company_status', 1)->get();
                        foreach ($vol_course as $val_his) {
                            $val_his->company_status = 2;
                            $val_his->expired        = Carbon::now();
                            $val_his->save();
                        }
                        $log_remove->delete();
                    }
                    $response['status'] = 'success';
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function teamCourseFilter(Request $request)
    {
        $response     = new Dummy();
        $job_function = CateJobFunction::join('course_job_function_log', 'course_job_function_log.job_function_id', 'categories_job_function.id')
            ->join('course', 'course.id', 'course_job_function_log.course_id')
            ->select('categories_job_function.id as key', 'categories_job_function.id as value', 'categories_job_function.name as text')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_job_function.status', 1);
        $job_function->where(function ($job_function) {
            $job_function->where('course.end_date', '>=', Carbon::now());
            $job_function->orWhere('course.end_date', null);
        });
        $job_function->where(function ($job_function) {
            $job_function->where('course.trailer_media', 1);
            $job_function->Orwhere('course.trailer_media', 3);
        });
        $job_function->groupby('categories_job_function.id')->orderby('categories_job_function.position', 'asc');
        $job_function = $job_function->get();

        $categories = Categories::join('course_categories_log', 'course_categories_log.cate_id', 'categories.id')
            ->join('course', 'course.id', 'course_categories_log.course_id')
            ->select('categories.id as key', 'categories.id as value', 'categories.title_th as text')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories.status', 1);
        $categories->where(function ($categories) {
            $categories->where('course.end_date', '>=', Carbon::now());
            $categories->orWhere('course.end_date', null);
        });
        $categories->where(function ($categories) {
            $categories->where('course.trailer_media', 1);
            $categories->Orwhere('course.trailer_media', 3);
        });
        $categories->groupby('categories.id')->orderby('categories.position', 'asc');
        $categories = $categories->get();

        $speaker = CateSpeaker::join('course_speaker_log', 'course_speaker_log.speaker', 'categories_speaker.id')
            ->join('course', 'course.id', 'course_speaker_log.course_id')
            ->select('categories_speaker.id as key', 'categories_speaker.id as value', 'categories_speaker.name as text')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_speaker.status', 1);
        $speaker->where(function ($speaker) {
            $speaker->where('course.end_date', '>=', Carbon::now());
            $speaker->orWhere('course.end_date', null);
        });
        $speaker->where(function ($speaker) {
            $speaker->where('course.trailer_media', 1);
            $speaker->Orwhere('course.trailer_media', 3);
        });
        $speaker->groupby('categories_speaker.id')->orderBy(DB::raw('ISNULL(categories_speaker.name), categories_speaker.name'), 'ASC');
        $speaker = $speaker->get();

        $level = CateLevel::join('course', 'course.level', 'categories_level.id')
            ->select('categories_level.id as key', 'categories_level.id as value', 'categories_level.title_th as text')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_level.status', 1);
        $level->where(function ($level) {
            $level->where('course.end_date', '>=', Carbon::now());
            $level->orWhere('course.end_date', null);
        });
        $level->where(function ($level) {
            $level->where('course.trailer_media', 1);
            $level->Orwhere('course.trailer_media', 3);
        });
        $level->groupby('categories_level.id')->orderby('categories_level.position', 'asc');
        $level = $level->get();

        $response['job_function'] = $job_function;
        $response['categories']   = $categories;
        $response['speaker']      = $speaker;
        $response['level']        = $level;
        $response['status']       = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
