<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Ya<PERSON>ra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Event;
use App\Models\Core\EventCoTraining;
use App\Models\Core\EventLog;
use App\Models\Core\EventTrainerLog;
use App\Models\User;
use PhpOffice\PhpSpreadsheet\Calculation\Financial\Coupons;

class EventController extends Controller
{

    public function __construct()
    {

    }

    public function myEvent(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['data'] = [];
      if (isset($request->utoken)) {
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user){
          $myEvent = EventLog::join('event','event.id','event_enrolment_log.event_id')
          ->select('event.title','event.subtitle','event.image','event.started_event','event.end_event','event.started_event as start','event.end_event as end')
          ->where('event_enrolment_log.status',1)
          ->where('event_enrolment_log.user_id',$user->id)
          ->where('event.status',1)
          ->where('event.public_date','<=',Carbon::now())
          ->orderby('event.end_event','asc')->get();
          $response['data'] = $myEvent;
          $response['status'] = 'success';
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function getEvent(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'success';
      if(!isset($request->page)){
        $request->page = 1;
      }
      if(!isset($request->limit)){
        $request->limit = 9;
      }
      if($request->limit>9){
        $request->limit = 9;
      }
      $offset = (intval($request->page)-1)*intval($request->limit);
      $data = Event::where('public_date','<=',Carbon::now())->where('status', 1);
      if(isset($request->keyword) && $request->keyword!=null && $request->keyword!='' && $request->keyword!='null'){
        $val_keyword = $request->keyword;
        $data->where(function ($data) use ($val_keyword) {
          $data->where(DB::raw('lower(short_title)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(title)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(subtitle)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(details)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(trainer_name)'),'LIKE','%'.strtolower($val_keyword).'%');
        });
      }
      $data->limit($request->limit)->offset($offset);
      $data = $data->get();
      foreach($data as $key=>$value){
        $trainer = array();
        $speaker = EventTrainerLog::join('categories_speaker','categories_speaker.id','event_training_log.speaker_id')
        ->select('categories_speaker.name','categories_speaker.id')->where('event_training_log.event_id',$value->id)->where('categories_speaker.status',1)->get();
        foreach($speaker as $val_s){
          $tr = new Dummy();
          $tr['name'] = $val_s->name;
          $tr['link'] = '/course?speaker='.$val_s->id;
          array_push($trainer,$tr);
        }
        $co_speaker = EventCoTraining::select('name','link')->where('event_id',$value->id)->where('status',1)->orderby('position','asc')->get();
        foreach($co_speaker as $val_s){
          array_push($trainer,$val_s);
        }
        $value->trainer = $trainer;

        $log_count = EventLog::where('event_id',$value->id)->where('status',1)->count();
        $value->book_count = $log_count;

        $value->book_status = 'available';
        if($value->is_limit==2){
          if($value->amount==null||$value->amount==''||$value->amount==0){
            $value->book_status = 'out';
          }else{
            if($log_count>=$value->amount){
              $value->book_status = 'out';
            }
          }
        }
        if($value->started_enroll>Carbon::now()){
          $value->book_status = 'not_start';
        }
        if($value->end_enroll<Carbon::now()){
          $value->book_status = 'is_end';
        }
        if (isset($request->utoken)) {
          $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
          if ($user) {
            $log = EventLog::where('user_id',$user->id)->where('event_id',$value->id)->first();
            if($log){
              $value->book_status = 'booked';
            }
          }
        }

        $value->month = Carbon::parse($value->started_event)->format('M, Y');

        $start_day = Carbon::parse($value->started_event)->format('d M Y');
        $end_day = Carbon::parse($value->end_event)->format('d M Y');
        $start_time = Carbon::parse($value->started_event)->format('H:i');
        $end_time = Carbon::parse($value->end_event)->format('H:i');
        $value->event_date = '';
        $value->event_time = '';
        $value->event_date_type = 'same';
        if($start_day==$end_day){
          $value->event_date_type = 'same';
          $value->event_date = $start_day;
          $value->event_time = $start_time.' - '.$end_time;
        }else{
          $value->event_date_type = 'notsame';
          $value->event_date = $start_day.' '.$start_time;
          $value->event_time = $end_day.' '.$end_time;
        }
      }
      $response['data'] = $data;
      $all_data = Event::where('public_date','<=',Carbon::now())->where('status', 1);
      if(isset($request->keyword) && $request->keyword!=null && $request->keyword!='' && $request->keyword!='null'){
        $val_keyword = $request->keyword;
        $all_data->where(function ($all_data) use ($val_keyword) {
          $all_data->where(DB::raw('lower(short_title)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(title)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(subtitle)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(details)'),'LIKE','%'.strtolower($val_keyword).'%')
          ->Orwhere(DB::raw('lower(trainer_name)'),'LIKE','%'.strtolower($val_keyword).'%');
        });
      }
      $all_data = $all_data->count();
      $response['all_page'] = ceil($all_data/$request->limit);
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function addEvent(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      if (isset($request->utoken)&&isset($request->event_id)) {
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user){
          $log = EventLog::where('user_id',$user->id)->where('event_id',$request->event_id)->first();
          if($log){
            $response['status'] = 'already';
          }else{
            $event = Event::where('id',$request->event_id)->where('public_date','<=',Carbon::now())
            ->where('started_enroll','<=',Carbon::now())->where('end_enroll','>=',Carbon::now())->where('status', 1)->first();
            if($event){
              if($event->is_limit==1){
                $log = new EventLog();
                $log->event_id = $request->event_id;
                $log->user_id = $user->id;
                $log->save();
                $response['status'] = 'success';
              }else{
                if($event->amount==null||$event->amount==''||$event->amount==0){
                  $response['status'] = 'out';
                }else{
                  $log_count = EventLog::where('event_id',$request->event_id)->where('status',1)->count();
                  if($log_count>=$event->amount){
                    $response['status'] = 'out';
                  }else{
                    $log = new EventLog();
                    $log->event_id = $request->event_id;
                    $log->user_id = $user->id;
                    $log->save();
                    $response['status'] = 'success';
                  }
                }
              }
            }
          }
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
}
