<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use App\Services\BillingHistoryService;
use App\Models\Core\Users;
use App\Models\Dummy;
use App\Helpers\SimpleEnDeHelper;
use Illuminate\Http\Request;

class BillingController extends Controller
{
    protected $billingService;

    public function __construct(BillingHistoryService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * Get latest billing information for auto-fill
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLastBillingInfo(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if (!isset($request->utoken)) {
            $response['message'] = 'User token is required';
            return $this->jsonResponse($response);
        }

        try {
            $user = $this->getUserFromToken($request->utoken);
            if (!$user) {
                $response['message'] = 'Invalid user token';
                return $this->jsonResponse($response);
            }

            // ลองดึงข้อมูล billing history ล่าสุด
            $billingInfo = $this->billingService->getLastBillingInfo($user->id);
            
            // หากไม่มี history ใช้ข้อมูลจาก user profile
            if (!$billingInfo) {
                $billingInfo = $this->billingService->getFallbackBillingInfo($user);
            }

            $response['billing_info'] = $billingInfo;
            $response['has_history'] = $billingInfo !== null;
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['message'] = 'An error occurred while retrieving billing information';
            $response['error'] = $e->getMessage();
        }

        return $this->jsonResponse($response);
    }

    /**
     * Get billing history list for user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBillingHistoryList(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if (!isset($request->utoken)) {
            $response['message'] = 'User token is required';
            return $this->jsonResponse($response);
        }

        try {
            $user = $this->getUserFromToken($request->utoken);
            if (!$user) {
                $response['message'] = 'Invalid user token';
                return $this->jsonResponse($response);
            }

            $limit = $request->limit ?? 10;
            if ($limit > 50) {
                $limit = 50; // จำกัดไม่เกิน 50 รายการ
            }

            $histories = $this->billingService->getBillingHistoryList($user->id, $limit);

            $response['billing_histories'] = $histories;
            $response['total'] = count($histories);
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['message'] = 'An error occurred while retrieving billing history';
            $response['error'] = $e->getMessage();
        }

        return $this->jsonResponse($response);
    }

    /**
     * Get specific billing history by ID
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBillingHistoryById(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if (!isset($request->utoken) || !isset($request->history_id)) {
            $response['message'] = 'User token and history ID are required';
            return $this->jsonResponse($response);
        }

        try {
            $user = $this->getUserFromToken($request->utoken);
            if (!$user) {
                $response['message'] = 'Invalid user token';
                return $this->jsonResponse($response);
            }

            $history = \App\Models\Core\UserBillingHistory::where('id', $request->history_id)
                ->where('user_id', $user->id)
                ->first();

            if (!$history) {
                $response['message'] = 'Billing history not found';
                return $this->jsonResponse($response);
            }

            $response['billing_info'] = [
                'name' => $history->name,
                'lastname' => $history->lastname,
                'email' => $history->email,
                'tel' => $history->tel,
                'address' => $history->address,
                'subdistrict' => $history->subdistrict,
                'district' => $history->district,
                'province' => $history->province,
                'postcode' => $history->postcode,
                'address_type' => $history->address_type,
                'iden_no' => $history->iden_no,
                'company_type' => $history->company_type,
                'company_branch' => $history->company_branch,
                'created_at' => $history->created_at->format('Y-m-d H:i:s'),
            ];
            $response['status'] = 'success';

        } catch (\Exception $e) {
            $response['message'] = 'An error occurred while retrieving billing history';
            $response['error'] = $e->getMessage();
        }

        return $this->jsonResponse($response);
    }

    /**
     * Get user from encrypted token
     *
     * @param string $token
     * @return Users|null
     */
    private function getUserFromToken($token)
    {
        try {
            $userId = SimpleEnDeHelper::instance()->decryptString($token);
            return Users::where('id', $userId)->first();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Return JSON response
     *
     * @param array $response
     * @return \Illuminate\Http\JsonResponse
     */
    private function jsonResponse($response)
    {
        return response()->json(
            $response,
            200,
            ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],
            JSON_UNESCAPED_UNICODE
        );
    }
}
