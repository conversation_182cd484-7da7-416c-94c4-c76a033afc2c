<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Dummy;
use App\Models\Core\Bank;

class BankController extends Controller
{
    public function getActiveBank(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        try {
            // ดึงบัญชีที่ใช้งานหลัก
            $bank = Bank::mainActive()->first();
            
            if ($bank) {
                $response['status'] = 'success';
                $response['data'] = [
                    'id' => $bank->id,
                    'bank_name' => $bank->bank_name,
                    'account_name' => $bank->account_name,
                    'account_number' => $bank->account_number,
                    'phone_number' => $bank->phone_number,
                    'type' => $bank->type,
                    'description' => $bank->description,
                    'qr_code' => $bank->qr_code,
                    'code' => Bank::getBankCode($bank->bank_name),
                ];
            } else {
                $response['message'] = 'ไม่พบข้อมูลบัญชีที่ใช้งาน';
            }
        } catch (\Exception $e) {
            $response['message'] = 'เกิดข้อผิดพลาดในการดึงข้อมูล';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
