<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Ya<PERSON>ra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CateFondue;
use App\Models\Core\CateFondueType;
use App\Models\Core\Faqs;
use App\Models\Core\FaqsComment;
use App\Models\Core\LearningFondueLog;
use App\Models\Core\QA;
use App\Models\Core\QALog;
use App\Models\Core\Users;
use App\Models\User;
use PhpOffice\PhpSpreadsheet\Calculation\Financial\Coupons;

class FaqController extends Controller
{

    public function __construct()
    {

    }

    public function data(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $data = Faqs::where('status',1)->orderby('position','asc')->get();
      foreach($data as $key=>$value){
        $data[$key]['log'] = FaqsComment::join('user','user.id','faqs_comment_log.user_id')
        ->select('user.nickname','faqs_comment_log.id','faqs_comment_log.user_id','faqs_comment_log.created_at','faqs_comment_log.comment')->where('user.status',1)
        ->where('faqs_comment_log.faq_id',$value->id)->where('faqs_comment_log.status',1)->orderby('faqs_comment_log.position','desc')->get();
        foreach($data[$key]['log'] as $key_log=>$value_log){
          $data[$key]['log'][$key_log]['owner'] = false;
          $data[$key]['log'][$key_log]['date'] = AppHelper::instance()->DateEngFull($value_log->created_at);
          $data[$key]['log'][$key_log]['time'] = Carbon::parse($value_log->created_at)->format('H:i');
          if(isset($request->utoken)){
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if($user){
              if($value_log->user_id == $user->id){
                $data[$key]['log'][$key_log]['owner'] = true;
              }
            }
          }
        }
      }
      $response['data'] = $data;
      $response['status'] = 'success';
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function comment(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      if(isset($request->utoken) && isset($request->faq_id) && isset($request->comment)){
        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $add_comment = new FaqsComment();
          $add_comment->user_id = $user->id;
          $add_comment->faq_id = $request->faq_id;
          $add_comment->comment = AppHelper::instance()->cleanInputBr($request->comment);
          $count = FaqsComment::where('faq_id', $request->faq_id)->count();
          $add_comment->position = $count + 1;
          $add_comment->save();
          $response['status'] = 'success';
        }
      }

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function delete_comment(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      if(isset($request->utoken) && isset($request->comment_id)){
        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $comment = FaqsComment::where('user_id',$user->id)->where('id',$request->comment_id)->first();
          if($comment){
            $comment->delete();
            $response['status'] = 'success';
          }
        }
      }

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function cateFondue(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'success';
      $response['cate'] = CateFondue::select('id as value','name as label')->where('status',1)->orderby('position','asc')->get();
      $response['type'] = CateFondueType::select('id as value','name as label')->where('status',1)->orderby('position','asc')->get();

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function addFondue(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      if(isset($request->utoken) && isset($request->cate) && isset($request->type) && isset($request->comment)){
        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $fondue = new LearningFondueLog();
          $fondue->user_id = $user->id;
          $fondue->category = $request->cate;
          $fondue->type = $request->type;
          $fondue->comment = AppHelper::instance()->cleanInputBr($request->comment);
          $fondue->save();
          $response['status'] = 'success';
        }
      }

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
}
