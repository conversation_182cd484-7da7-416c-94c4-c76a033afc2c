<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Ya<PERSON>ra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\NotiGlobal;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\UserHistory;
use App\Models\Core\UserLearnedTime;
use App\Models\Core\UserPlaylist;
use App\Models\Core\UserPlaylistLog;
use App\Models\Core\Users;
use App\Models\Core\UsersCartLog;
use App\Models\Core\UsersOrderLog;
use App\Models\User;
use PhpOffice\PhpSpreadsheet\Calculation\Financial\Coupons;
use App\Models\Core\NotiGlobalLog;
use App\Models\Core\Course;

class CartController extends Controller
{

    public function __construct()
    {

    }

    public function Cartlist(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      $response['discount_web'] = 0;
      $response['code_web'] = '';
      if (isset($request->utoken)) {
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user){
          $check_coupon = UsersCartLog::where('user_cart_log.code_id','!=',null)->where('user_cart_log.user_id',$user->id)->get();
          foreach($check_coupon as $value){
            if($value->course_id!=null){
              $check_code = AppHelper::instance()->couponAllow($user,$value->course_id,'course',$value->discount_code);
            }else if($value->group_id!=null){
              $check_code = AppHelper::instance()->couponAllow($user,$value->group_id,'group',$value->discount_code);
            }else if($value->subscription_id!=null){
              $check_code = AppHelper::instance()->couponAllow($user,$value->subscription_id,'subscription',$value->discount_code);
            }
            if(!$check_code['allow']){
              $value->code_id = null;
              $value->discount_channel = null;
              $value->discount_code = null;
              $value->discount_price = 0;
              $value->save();
            }
          }

          $check_web = UsersCartLog::where('user_cart_log.web_code_id','!=',null)->where('user_cart_log.user_id',$user->id)->get();
          foreach($check_web as $value){
            $check_code = AppHelper::instance()->couponAllowWeb($user,$value->web_code);
            if(!$check_code['allow']){
              $value->web_code_id = null;
              $value->web_code = null;
              $value->web_price = 0;
              $value->save();
            }else{
              if($check_code['coupon']->discount_type==1){
                $dis_amount = $check_code['coupon']->amount;
              }else{
                $dis_amount = $check_code['amount']*$check_code['coupon']->amount/100;
              }
              if($dis_amount>$check_code['amount']){
                $dis_amount = $check_code['amount'];
              }
              $value->web_price = $dis_amount;
              $value->save();
            }
          }

          $cart_course = UsersCartLog::join('course', 'course.id', 'user_cart_log.course_id')
          ->join('user', 'user.id', 'user_cart_log.user_id')
          ->where('user_cart_log.course_id','!=',null)->where('user_cart_log.user_id', $user->id)
          ->select('user_cart_log.web_price','user_cart_log.web_code','user_cart_log.web_code_id','user_cart_log.code_id', 'user_cart_log.discount_price', 'user_cart_log.id'
          , 'user_cart_log.created_at', 'user_cart_log.course_id','course.is_promotion'
          , 'course.image_th as image', 'course.title_th as title', 'course.details_th as description', 'course.price', 'course.pro_price','course.pro_started','course.pro_end','course.pro_period')
          ->get();
    
          $cart_group = UsersCartLog::join('course_group', 'course_group.id', 'user_cart_log.group_id')
          ->join('user', 'user.id', 'user_cart_log.user_id')
          ->where('user_cart_log.group_id','!=',null)->where('user_cart_log.user_id', $user->id)
          ->select('user_cart_log.web_price','user_cart_log.web_code','user_cart_log.web_code_id','user_cart_log.code_id', 'user_cart_log.discount_price', 'user_cart_log.id'
          , 'user_cart_log.created_at', 'user_cart_log.group_id','course_group.thumb as image'
          , 'course_group.title', 'course_group.details as description','course_group.price')
          ->get();

          $cart_subscription = UsersCartLog::join('subscription', 'subscription.id', 'user_cart_log.subscription_id')
          ->join('user', 'user.id', 'user_cart_log.user_id')
          ->where('user_cart_log.subscription_id','!=',null)->where('user_cart_log.user_id', $user->id)
          ->select('user_cart_log.web_price','user_cart_log.web_code','user_cart_log.web_code_id','user_cart_log.code_id', 'user_cart_log.discount_price', 'user_cart_log.id'
          , 'user_cart_log.created_at', 'user_cart_log.subscription_id','subscription.thumb as image'
          , 'subscription.title', 'subscription.remark as description','subscription.price')
          ->get();
    
          $response['total_price'] = 0;
          $data_course = array();
          foreach($cart_course as $key_course=>$val_course){
            $cart_course[$key_course]['type'] = 'course';
            if($val_course->is_promotion == 1 && $val_course->pro_started <= Carbon::now() && ($val_course->pro_end >= Carbon::now()||$val_course->pro_period==2)){
              $val_course->price = $val_course->pro_price;
            }
            if($val_course->discount_price!=null && $val_course->discount_price!=''){
              $val_course->price = $val_course->price - $val_course->discount_price;
            }
            $response['total_price'] += $val_course->price;
            if($val_course->web_price!=null&&$val_course->web_price!=''&&$val_course->web_price!=0&&$val_course->web_price!='null'&&$val_course->web_price>$response['discount_web']){
              $response['discount_web'] = $val_course->web_price;
              $response['code_web'] = $val_course->web_code;
            }
            array_push($data_course,$val_course);
          }
          foreach($cart_group as $key_group=>$val_group){
            $cart_group[$key_group]['type'] = 'group';
            if($val_group->discount_price!=null && $val_group->discount_price!=''){
              $val_group->price = $val_group->price - $val_group->discount_price;
            }
            $response['total_price'] += $val_group->price;
            if($val_group->web_price!=null&&$val_group->web_price!=''&&$val_group->web_price!=0&&$val_group->web_price!='null'&&$val_group->web_price>$response['discount_web']){
              $response['discount_web'] = $val_group->web_price;
              $response['code_web'] = $val_group->web_code;
            }
            array_push($data_course,$val_group);
          }
          foreach($cart_subscription as $key_subscription=>$val_subscription){
            $cart_subscription[$key_subscription]['type'] = 'subscription';
            if($val_subscription->discount_price!=null && $val_subscription->discount_price!=''){
              $val_subscription->price = $val_subscription->price - $val_subscription->discount_price;
            }
            $response['total_price'] += $val_subscription->price;
            if($val_subscription->web_price!=null&&$val_subscription->web_price!=''&&$val_subscription->web_price!=0&&$val_subscription->web_price!='null'&&$val_subscription->web_price>$response['discount_web']){
              $response['discount_web'] = $val_subscription->web_price;
              $response['code_web'] = $val_subscription->web_code;
            }
            array_push($data_course,$val_subscription);
          }

          $response['total_price'] -= $response['discount_web'];
    
          usort($data_course, function($a1, $a2){
            $v1 = $a1['created_at'];
            $v2 = $a2['created_at'];
            return strtotime($v2) - strtotime($v1);
          });

          $response['count'] = count($data_course);
          $response['data'] = $data_course;
        }else{
          $response['count'] = 0;
          $response['total_price'] = 0;
          $response['data'] = [];
        }
      }else{
        $response['count'] = 0;
        $response['total_price'] = 0;
        $response['data'] = [];
      }

      

      $response['status'] = 'success';
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function Notilist(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      
      if (isset($request->utoken)) {
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user){


          $noti_count = NotiGlobal::where('status',1)->where('started_date','<=',Carbon::now())->where('end_date','>=',Carbon::now())->limit(5)->count();

          $noti_auto_count = NotiAutoLog::leftjoin('noti_auto', 'noti_auto.id', 'noti_auto_log.noti_auto_id')->where('user_id', $user->id)->limit(5)->count();

          $count = $noti_count + $noti_auto_count;
    
          $noti = NotiGlobal::select('image', 'title as title', 'description as description', 'link','started_date as date_time')->
          where('status',1)->where('started_date','<=',Carbon::now())->where('end_date','>=',Carbon::now())->limit(5)->get();

          $noti_auto = NotiAutoLog::leftjoin('noti_auto', 'noti_auto.id', 'noti_auto_log.noti_auto_id')
          ->select('noti_auto.image', 'noti_auto.title', 'noti_auto.description', 'noti_auto.cta', 'noti_auto.link', 'noti_auto_log.created_at as date_time',
          'noti_auto_log.noti_auto_id','noti_auto_log.image as image_log', 'noti_auto_log.title as title_log', 'noti_auto_log.description as description_log', 'noti_auto_log.link as link_log')
          ->where('user_id', $user->id)->limit(5)->get();

          $notification_data = array();
          foreach($noti as $key=>$val){
            $noti[$key]['date'] = AppHelper::instance()->DateThaiOnly($val->date_time);
            array_push($notification_data,$val);
          }

          foreach($noti_auto as $key_auto=>$val_auto){
            if($val_auto->noti_auto_id==null||$val_auto->noti_auto_id==''){
              $noti_auto[$key_auto]['image'] = $val_auto->image_log;
              $noti_auto[$key_auto]['title'] = $val_auto->title_log;
              $noti_auto[$key_auto]['description'] = $val_auto->description_log;
              $noti_auto[$key_auto]['link'] = $val_auto->link_log;
            }
            $noti_auto[$key_auto]['date'] = AppHelper::instance()->DateThaiOnly($val_auto->date_time);
            array_push($notification_data,$val_auto);
          }

          usort($notification_data, function($a1, $a2){
            $v1 = $a1['date_time'];
            $v2 = $a2['date_time'];
            return strtotime($v2) - strtotime($v1);
          });

          $response['count'] = $count;
          $response['data'] = $notification_data;
        }else{
          $response['count'] = 0;
          $response['data'] = [];
        }
      }else{
        $response['count'] = 0;
        $response['data'] = [];
      }

      $response['status'] = 'success';
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function addRead(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;

      if (isset($request->utoken)&&isset($request->id)&&isset($request->type)) {
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user){
          if($request->type=='auto'){
            $noti = NotiAutoLog::where('id',$request->id)->where('user_id',$user->id)->where('status',1)->first();
            if($noti){
              $noti->status = 2;
              $noti->save();
            }
          }else{
            $noti = NotiGlobalLog::where('noti_global_id',$request->id)->where('user_id',$user->id)->first();
            if(!$noti){
              $noti = new NotiGlobalLog();
              $noti->user_id = $user->id;
              $noti->noti_global_id = $request->id;
              $noti->save();
            }
          }
          $response['status'] = 'success';
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function getCheckout(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      $response['tax_type'] = $request->tax_type;
      $is_discount = false;
      $response['discount_web'] = 0;
      $response['code_web'] = '';
      $response['is_book'] = false;
      if (isset($request->utoken)) {
        // get user by utoken
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user){
          // get data from table user_cart_log at code_id
          $check_coupon = UsersCartLog::where('user_cart_log.code_id','!=',null)->where('user_cart_log.user_id',$user->id)->get();
          // loop find discount from code_id
          foreach($check_coupon as $value){
            if($value->course_id!=null){
              $check_code = AppHelper::instance()->couponAllow($user,$value->course_id,'course',$value->discount_code);
            }else if($value->group_id!=null){
              $check_code = AppHelper::instance()->couponAllow($user,$value->group_id,'group',$value->discount_code);
            }else if($value->subscription_id!=null){
              $check_code = AppHelper::instance()->couponAllow($user,$value->subscription_id,'subscription',$value->discount_code);
            }
            if(!$check_code['allow']){
              $value->code_id = null;
              $value->discount_channel = null;
              $value->discount_code = null;
              $value->discount_price = 0;
              $value->save();
            }
          }

          // get data from table user_cart_log at web_code_id
          $check_web = UsersCartLog::where('user_cart_log.web_code_id','!=',null)->where('user_cart_log.user_id',$user->id)->get();
          // loop find discount from web_code_id
          foreach($check_web as $value){
            $check_code = AppHelper::instance()->couponAllowWeb($user,$value->web_code);
            if(!$check_code['allow']){
              $value->web_code_id = null;
              $value->web_code = null;
              $value->web_price = 0;
              $value->save();
            }else{
              if($check_code['coupon']->discount_type==1){
                $dis_amount = $check_code['coupon']->amount;
              }else{
                $dis_amount = $check_code['amount']*$check_code['coupon']->amount/100;
              }
              if($dis_amount>$check_code['amount']){
                $dis_amount = $check_code['amount'];
              }
              $value->web_price = $dis_amount;
              $value->save();
            }
          }


          $cart_course = UsersCartLog::join('course', 'course.id', 'user_cart_log.course_id')
          ->join('user', 'user.id', 'user_cart_log.user_id')
          ->where('user_cart_log.course_id','!=',null)->where('user_cart_log.user_id', $user->id)
          ->select('course.is_doc','course.requires_vat','user_cart_log.web_price','user_cart_log.web_code','user_cart_log.web_code_id','user_cart_log.code_id', 'user_cart_log.id','user_cart_log.created_at', 'user_cart_log.course_id', 'course.image_th as image', 'course.title_th as title', 
                    'course.details_th as description', 'course.price', 'course.pro_price','course.pro_started','course.pro_end','course.pro_period', 'user_cart_log.discount_code', 
                    'user_cart_log.discount_price', 'course.is_promotion')
          ->get();
    
          $cart_group = UsersCartLog::join('course_group', 'course_group.id', 'user_cart_log.group_id')
          ->join('user', 'user.id', 'user_cart_log.user_id')
          ->where('user_cart_log.group_id','!=',null)->where('user_cart_log.user_id', $user->id)
          ->select('user_cart_log.web_price','user_cart_log.web_code','user_cart_log.web_code_id','user_cart_log.code_id', 'user_cart_log.id','user_cart_log.created_at', 'user_cart_log.group_id', 'course_group.thumb as image', 'course_group.title', 
                    'course_group.details as description', 'course_group.price','user_cart_log.discount_price', 'user_cart_log.discount_code')
          ->get();

          $cart_subscription = UsersCartLog::join('subscription', 'subscription.id', 'user_cart_log.subscription_id')
          ->join('user', 'user.id', 'user_cart_log.user_id')
          ->where('user_cart_log.subscription_id','!=',null)->where('user_cart_log.user_id', $user->id)
          ->select('subscription.requires_vat','user_cart_log.web_price','user_cart_log.web_code','user_cart_log.web_code_id','user_cart_log.code_id', 'user_cart_log.id','user_cart_log.created_at', 'user_cart_log.subscription_id', 'subscription.thumb as image', 'subscription.title', 
                    'subscription.remark as description', 'subscription.price','user_cart_log.discount_price', 'user_cart_log.discount_code')
          ->get();

          $response['cart_type'] = 'course';
          $response['item_id'] = '';
          $response['item_cart'] = '';
          if(count($cart_group)>0){
            $response['cart_type'] = 'group';
            $response['item_id'] = $cart_group[0]['group_id'];
            $response['item_cart'] = $cart_group[0]['id'];
          }
          if(count($cart_subscription)>0){
            $response['cart_type'] = 'subscription';
            $response['item_id'] = $cart_subscription[0]['subscription_id'];
            $response['item_cart'] = $cart_subscription[0]['id'];
          }
    
          $response['total_tax'] = 0;
          $response['total_price'] = 0;
          $data_course = array();
          foreach($cart_course as $key_course=>$val_course){
            $cart_course[$key_course]['type'] = 'course';
            $cart_course[$key_course]['discount_code'] = $val_course->discount_code;
            $cart_course[$key_course]['discount_price'] = $val_course->discount_price;
            if($request->tax_type==1){
              if($val_course->is_promotion == 1 && $val_course->pro_started <= Carbon::now() && ($val_course->pro_end >= Carbon::now()||$val_course->pro_period==2)){
                // Pro price in DB is before VAT, so we use it directly as pre_vat
                $val_course->pre_vat = $val_course->pro_price - $val_course->discount_price;
                $val_course->vat = $val_course->pre_vat*7/100;
                $val_course->tax = $val_course->pre_vat*3/100;
                $val_course->total = $val_course->pre_vat + $val_course->vat - $val_course->tax;
                $val_course->price = $val_course->pro_price;
              }else{
                // Price in DB is before VAT, so we use it directly as pre_vat
                $val_course->pre_vat = $val_course->price - $val_course->discount_price;
                $val_course->vat = $val_course->pre_vat*7/100;
                $val_course->tax = $val_course->pre_vat*3/100;
                $val_course->total = $val_course->pre_vat + $val_course->vat - $val_course->tax;
              }
            }else{
              if($val_course->is_promotion == 1 && $val_course->pro_started <= Carbon::now() && ($val_course->pro_end >= Carbon::now()||$val_course->pro_period==2)){
                // Pro price in DB is before VAT, so we use it directly as pre_vat
                $val_course->pre_vat = $val_course->pro_price - $val_course->discount_price;
                $val_course->vat = $val_course->pre_vat*7/100;
                $val_course->tax = 0;
                $val_course->total = $val_course->pre_vat + $val_course->vat;
                $val_course->price = $val_course->pro_price;
              }else{
                // Price in DB is before VAT, so we use it directly as pre_vat
                $val_course->pre_vat = $val_course->price - $val_course->discount_price;
                $val_course->vat = $val_course->pre_vat*7/100;
                $val_course->tax = 0;
                $val_course->total = $val_course->pre_vat + $val_course->vat;
              }
            }
            $response['total_tax']+=$val_course->tax;
            $response['total_price']+=$val_course->total;
            if($val_course->web_price!=null&&$val_course->web_price!=''&&$val_course->web_price!=0&&$val_course->web_price!='null'&&$val_course->web_price>$response['discount_web']){
              $response['discount_web'] = $val_course->web_price;
              $response['code_web'] = $val_course->web_code;
            }
            if($val_course->is_doc==1){
              $response['is_book'] = true;
            }
            array_push($data_course,$val_course);
          }
          foreach($cart_group as $key_group=>$val_group){
            $cart_group[$key_group]['type'] = 'group';
            $cart_group[$key_group]['discount_code'] = $val_group->discount_code;
            $cart_group[$key_group]['discount_price'] = $val_group->discount_price;
            if($request->tax_type==1){
                // Price in DB is before VAT, so we use it directly as pre_vat
                $val_group->pre_vat = $val_group->price - $val_group->discount_price;
                $val_group->vat = $val_group->pre_vat*7/100;
                $val_group->tax = $val_group->pre_vat*3/100;
                $val_group->total = $val_group->pre_vat + $val_group->vat - $val_group->tax;
            }else{
                // Price in DB is before VAT, so we use it directly as pre_vat
                $val_group->pre_vat = $val_group->price - $val_group->discount_price;
                $val_group->vat = $val_group->pre_vat*7/100;
                $val_group->tax = 0;
                $val_group->total = $val_group->pre_vat + $val_group->vat;
            }
            $response['total_tax']+=$val_group->tax;
            $response['total_price']+=$val_group->total;
            if($val_group->web_price!=null&&$val_group->web_price!=''&&$val_group->web_price!=0&&$val_group->web_price!='null'&&$val_group->web_price>$response['discount_web']){
              $response['discount_web'] = $val_group->web_price;
              $response['code_web'] = $val_group->web_code;
            }
            array_push($data_course,$val_group);
          }
          foreach($cart_subscription as $key_subscription=>$val_subscription){
            $cart_subscription[$key_subscription]['type'] = 'subscription';
            $cart_subscription[$key_subscription]['discount_code'] = $val_subscription->discount_code;
            $cart_subscription[$key_subscription]['discount_price'] = $val_subscription->discount_price;
            if($request->tax_type==1){
                // Price in DB is before VAT, so we use it directly as pre_vat
                $val_subscription->pre_vat = $val_subscription->price - $val_subscription->discount_price;
                $val_subscription->vat = $val_subscription->pre_vat*7/100;
                $val_subscription->tax = $val_subscription->pre_vat*3/100;
                $val_subscription->total = $val_subscription->pre_vat + $val_subscription->vat - $val_subscription->tax;
            }else{
                // Price in DB is before VAT, so we use it directly as pre_vat
                $val_subscription->pre_vat = $val_subscription->price - $val_subscription->discount_price;
                $val_subscription->vat = $val_subscription->pre_vat*7/100;
                $val_subscription->tax = 0;
                $val_subscription->total = $val_subscription->pre_vat + $val_subscription->vat;
            }
            $response['total_tax']+=$val_subscription->tax;
            $response['total_price']+=$val_subscription->total;
            if($val_subscription->web_price!=null&&$val_subscription->web_price!=''&&$val_subscription->web_price!=0&&$val_subscription->web_price!='null'&&$val_subscription->web_price>$response['discount_web']){
              $response['discount_web'] = $val_subscription->web_price;
              $response['code_web'] = $val_group->web_code;
            }
            array_push($data_course,$val_subscription);
          }

          $response['total_price'] -= $response['discount_web'];
    
          //เรียงรายการจากมากไปน้อย
          usort($data_course, function($a1, $a2){
            $v1 = $a1['created_at'];
            $v2 = $a2['created_at'];
            return strtotime($v2) - strtotime($v1);
          });

          $response['tax_type'] = $request->tax_type;
          $response['count'] = count($data_course);
          $response['data'] = $data_course;
        }else{
          $response['tax_type'] = null;
          $response['count'] = 0;
          $response['total_price'] = 0;
          $response['data'] = [];
        }
      }else{
        $response['tax_type'] = null;
        $response['count'] = 0;
        $response['total_price'] = 0;
        $response['data'] = [];
      }

      

      $response['status'] = 'success';
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function getOrder(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      $response['order_id_encrypt'] = SimpleEnDeHelper::instance()->decryptString($request->key);
      
      if (isset($request->utoken)) {
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user){
          $order = UsersOrderLog::where('id', SimpleEnDeHelper::instance()->decryptString($request->key))->first();
          if ($order){
            $response['payment'] = $order->payment_type;
            $response['order'] = $order;
            $response['order_no'] = SimpleEnDeHelper::instance()->encryptString($order->id);
            $response['status'] = 'success';
          }
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function addCart(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      
      if(isset($request->utoken)&&isset($request->course_id)&&isset($request->content_type)){   
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          if($request->content_type=='course'){
            $allow_add = true;
            $have_course = UserHistory::join('course','course.id','user_history.course_id')->select('course.id','course.is_time','course.time_set')
            ->where('user_history.user_id',$user->id)
            ->where('user_history.expired','>=',Carbon::now())->where('course.id',$request->course_id)->first();
            if($have_course){
              $is_limit_learn = false;
              if($have_course->is_time==1&&$have_course->time_set>0){
                $check_limit_time = UserLearnedTime::where('course_id',$have_course->id)->where('user_id',$user->id)->first();
                if($check_limit_time){
                  if($check_limit_time->seconds >= ($have_course->time_set*60*60)){
                    $is_limit_learn = true;
                  }
                }
              }
              if(!$is_limit_learn){
                $allow_add = false;
              }
            }
            
            $add_cart = UsersCartLog::where('user_id',$user->id)->where('course_id',$request->course_id)->first();

            if(!$allow_add){
              if($add_cart){
                $add_cart->delete();
              }
              $response['status'] = 'can_not_buy';
              return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
            }
          }else if($request->content_type=='group'){
            $add_cart = UsersCartLog::where('user_id',$user->id)->where('group_id',$request->course_id)->first();
          }else if($request->content_type=='subscription'){
            $check_sub = SubscriptionLog::where('user_id',$user->id)->where('subscription_id',$request->course_id)->where('expired','>=',Carbon::now())->orderby('expired','desc')->first();
            $data_sub = Subscription::where('id',$request->course_id)->first();
            if($check_sub && $data_sub){
              if($data_sub->period+Carbon::now()->diffInDays(Carbon::parse($check_sub->expired))>730){
                $response['status'] = 'limit';
                return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
              }
            }
            $add_cart = UsersCartLog::where('user_id',$user->id)->where('subscription_id',$request->course_id)->first();
          }

          if(!$add_cart){
            $add_cart = new UsersCartLog();
          }

          if($request->content_type=='course'){
            $add_cart->course_id = $request->course_id;
            $add_cart->discount_channel = null;
            $add_cart->discount_code = null;
            $add_cart->discount_price = 0;

            $check_code = AppHelper::instance()->couponAllow($user,$request->course_id,'course',$request->discount_code);
            if($check_code['allow']){
              if($check_code['coupon']->discount_type==1){
                $dis_amount = $check_code['coupon']->amount;
              }else{
                $dis_amount = $check_code['course']->price*$check_code['coupon']->amount/100;
              }
              if($dis_amount>$check_code['course']->price){
                $dis_amount = $check_code['course']->price;
              }

              $add_cart->discount_code = $request->discount_code;
              $add_cart->discount_price = $dis_amount;
              $add_cart->code_id = $check_code['coupon']->code_id;

              if($check_code['coupon']->sponsor_type==1&&$check_code['coupon']->coupon_type==1){
                $add_cart->discount_channel = 'sponsor';
              }else{
                $add_cart->discount_channel = 'code';
              }
            }
          }else if($request->content_type=='group'){
            $add_cart->group_id = $request->course_id;
          }else if($request->content_type=='subscription'){
            $add_cart->subscription_id = $request->course_id;
          }

          $add_cart->user_id = $user->id;
          $add_cart->save();

          $response['utoken'] = SimpleEnDeHelper::instance()->encryptString($user->id);
          $response['status'] = 'success';

        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function checkCart(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;

      if(isset($request->utoken)&&isset($request->course_id)&&isset($request->content_type)){
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          if($request->content_type=='course'){
            $course_data = Course::where('id',$request->course_id)->first();
            if($course_data && $course_data->trailer_media != 7){
              $non_course = UsersCartLog::where('user_id',$user->id)->where('course_id',null)->count();
              $live_course = UsersCartLog::join('course','course.id','user_cart_log.course_id')
              ->where('user_cart_log.user_id',$user->id)->where('course.trailer_media',7)->count();
              $count_cart = $non_course + $live_course;
            }else{
              $count_cart = UsersCartLog::where('user_id',$user->id)->count();
            }
          }else{
            $count_cart = UsersCartLog::where('user_id',$user->id)->count();
          }
          $response['count'] = $count_cart;
          $response['status'] = 'success';
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    // public function checkCart(Request $request)
    // {
    //   $response = new Dummy();
    //   $response['status'] = 'false';
    //   $response['utoken'] = $request->utoken;
      
    //   if(isset($request->utoken)&&isset($request->course_id)&&isset($request->content_type)){   
    //     $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
    //     if($user){
    //       if($request->content_type=='course'){
    //         $count_cart = UsersCartLog::where('user_id',$user->id)->where('course_id',null)->count();
    //       }else{
    //         $count_cart = UsersCartLog::where('user_id',$user->id)->count();
    //       }
    //       $response['count'] = $count_cart;
    //       $response['status'] = 'success';
    //     }
    //   }
    //   return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    // }

    public function clearCart(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      
      if(isset($request->utoken)){   
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $clear = UsersCartLog::where('user_id',$user->id)->delete();
          $response['status'] = 'success';
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function addPlaylist(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      
      if(isset($request->utoken)&&isset($request->course_id)&&(isset($request->title)||isset($request->select))){   
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          if(isset($request->select)){
            $playlist = UserPlaylist::where('user_id',$user->id)->where('id',$request->select)->where('status',1)->first();
          }
          if(isset($request->title)){
            $playlist = UserPlaylist::where('user_id',$user->id)->where('title',$request->title)->where('status',1)->first();
            if(!$playlist){
              $playlist = new UserPlaylist();
              $playlist->user_id = $user->id;
              $playlist->title = $request->title;
              $playlist->save();
            }
          }

          if($playlist){
            $playlist_log = UserPlaylistLog::where('playlist_id',$playlist->id)->where('course_id',$request->course_id)->first();
            if($playlist_log){
              $playlist_log->delete();
            }else{
              $playlist_log = new UserPlaylistLog();
              $playlist_log->playlist_id = $playlist->id;
              $playlist_log->course_id = $request->course_id;
              $playlist_log->save();
            }
            $response['status'] = 'success';
          }
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function removePlaylistCourse(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['utoken'] = $request->utoken;
      
      if(isset($request->utoken)&&isset($request->course_id)&&isset($request->playlist_id)){   
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $playlist = UserPlaylist::where('user_id',$user->id)->where('id',$request->playlist_id)->where('status',1)->first();
          if($playlist){
            $del = UserPlaylistLog::where('playlist_id',$playlist->id)->where('course_id',$request->course_id)->first();
            if($del){
              $del->delete();
              $response['status'] = 'success';
            }
          }
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function addDiscountCode(Request $request)
    {
      $response = new Dummy();

      $response['discount_channel'] = '';
      $response['is_discount'] = false;
      $response['discount_type'] = 'regular';
      $response['discount_code'] = '';
      $response['discount_value'] = 0;
      if(isset($request->utoken)&&isset($request->course_id)&&isset($request->content_type)&&isset($request->discount_code)&&isset($request->discount_channel)){
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          if($request->content_type=='course'){
            $check_code = AppHelper::instance()->couponAllow($user,$request->course_id,'course',$request->discount_code);
            if($check_code['allow']){
              if($check_code['coupon']->sponsor_type==1&&$check_code['coupon']->coupon_type==1){
                $response['discount_channel'] = 'sponsor';
              }else{
                $response['discount_channel'] = 'code';
              }
              $response['is_discount'] = true;
              $response['discount_code'] = $request->discount_code;
              if($check_code['coupon']->discount_type==1){
                $response['discount_type'] = 'regular';
              }else{
                $response['discount_type'] = 'percent';
              }
              $response['discount_value'] = $check_code['coupon']->amount;
            }
          }else if($request->content_type=='subscription'){
            $check_code = AppHelper::instance()->couponAllow($user,$request->course_id,'subscription',$request->discount_code);
            if($check_code['allow']){
              if(isset($request->item_cart)){
                $cart_item = UsersCartLog::where('id',$request->item_cart)->first();
                if($cart_item){
                  if($check_code['coupon']->discount_type==1){
                    $dis_amount = $check_code['coupon']->amount;
                  }else{
                    $dis_amount = $check_code['course']->price*$check_code['coupon']->amount/100;
                  }
                  if($dis_amount>$check_code['course']->price){
                    $dis_amount = $check_code['course']->price;
                  }

                  $cart_item->code_id = $check_code['coupon']->code_id;
                  if($check_code['coupon']->sponsor_type==1&&$check_code['coupon']->coupon_type==1){
                    $cart_item->discount_channel = 'sponsor';
                  }else{
                    $cart_item->discount_channel = 'code';
                  }
                  $cart_item->discount_code = $request->discount_code;
                  $cart_item->discount_price = $dis_amount;
                  $cart_item->save();
                  $response['is_discount'] = true;
                }
              }
            }
            if(isset($request->item_cart)&&!$response['is_discount']){
              $cart_item = UsersCartLog::where('id',$request->item_cart)->first();
              if($cart_item){
                $cart_item->code_id = null;
                $cart_item->discount_channel = null;
                $cart_item->discount_code = null;
                $cart_item->discount_price = 0;
                $cart_item->save();
              }
            }
          }else if($request->content_type=='group'){
            $check_code = AppHelper::instance()->couponAllow($user,$request->course_id,'group',$request->discount_code);
            if($check_code['allow']){
              if(isset($request->item_cart)){
                $cart_item = UsersCartLog::where('id',$request->item_cart)->first();
                if($cart_item){
                  if($check_code['coupon']->discount_type==1){
                    $dis_amount = $check_code['coupon']->amount;
                  }else{
                    $dis_amount = $check_code['course']->price*$check_code['coupon']->amount/100;
                  }
                  if($dis_amount>$check_code['course']->price){
                    $dis_amount = $check_code['course']->price;
                  }

                  $cart_item->code_id = $check_code['coupon']->code_id;
                  if($check_code['coupon']->sponsor_type==1&&$check_code['coupon']->coupon_type==1){
                    $cart_item->discount_channel = 'sponsor';
                  }else{
                    $cart_item->discount_channel = 'code';
                  }
                  $cart_item->discount_code = $request->discount_code;
                  $cart_item->discount_price = $dis_amount;
                  $cart_item->save();
                  $response['is_discount'] = true;
                }
              }
            }
            if(isset($request->item_cart)&&!$response['is_discount']){
              $cart_item = UsersCartLog::where('id',$request->item_cart)->first();
              if($cart_item){
                $cart_item->code_id = null;
                $cart_item->discount_channel = null;
                $cart_item->discount_code = null;
                $cart_item->discount_price = 0;
                $cart_item->save();
              }
            }
          }
        }
      }

      $response['status'] = 'success';
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function addDiscountWeb(Request $request)
    {
      $response = new Dummy();

      $response['is_discount'] = false;
      if(isset($request->utoken)&&isset($request->discount_code)){
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $check_code = AppHelper::instance()->couponAllowWeb($user,$request->discount_code);
          if($check_code['allow']){
            $cart_item = UsersCartLog::where('user_id',$user->id)->get();
            foreach($cart_item as $val_code){
              if($check_code['coupon']->discount_type==1){
                $dis_amount = $check_code['coupon']->amount;
              }else{
                $dis_amount = $check_code['amount']*$check_code['coupon']->amount/100;
              }
              if($dis_amount>$check_code['amount']){
                $dis_amount = $check_code['amount'];
              }

              $val_code->web_code_id = $check_code['coupon']->code_id;
              $val_code->web_code = $request->discount_code;
              $val_code->web_price = $dis_amount;
              $val_code->save();
              $response['is_discount'] = true;
            }
          }
          if(!$response['is_discount']){
            $cart_item = UsersCartLog::where('user_id',$user->id)->get();
            foreach($cart_item as $val_cart){
              $val_cart->web_code_id = null;
              $val_cart->web_code = null;
              $val_cart->web_price = 0;
              $val_cart->save();
            }
          }
        }
      }

      $response['status'] = 'success';
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function removeCart(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      if(isset($request->utoken)&& isset($request->cart_id)){
        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $delete = UsersCartLog::where('id', $request->cart_id)->where('user_id', $user->id)->first();
          if($delete){
            $delete->delete();
            $response['status'] = 'success';
          }else{
            $response['status'] = 'false';
          }
        }else{
          $response['status'] = 'false';
        }
      }else{
        $response['status'] = 'false';
      }
      
      

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
}
