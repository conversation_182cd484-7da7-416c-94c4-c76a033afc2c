<?php
namespace App\Http\Controllers\API\Core;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use App\Http\Controllers\Controller;
use App\Models\Core\Article;
use App\Models\Core\ArticleTagLog;
use App\Models\Core\AssessmentLog;
use App\Models\Core\CateDepartment;
use App\Models\Core\CateGender;
use App\Models\Core\Categories;
use App\Models\Core\CateJobFunction;
use App\Models\Core\CateLearner;
use App\Models\Core\CateLevel;
use App\Models\Core\CateSpeaker;
use App\Models\Core\CommunityLikeLog;
use App\Models\Core\CommunityRepliedLike;
use App\Models\Core\Course;
use App\Models\Core\CourseCateLog;
use App\Models\Core\CourseExamLog;
use App\Models\Core\CourseFaq;
use App\Models\Core\CourseFileLog;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\CourseSection;
use App\Models\Core\CourseSectionFileLog;
use App\Models\Core\CourseSectionLesson;
use App\Models\Core\CourseSpeakerLog;
use App\Models\Core\CourseTagLog;
use App\Models\Core\StatLog;
use App\Models\Core\SubCategories;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionMain;
use App\Models\Core\Tags;
use App\Models\Core\UserCommunityLog;
use App\Models\Core\UserCommunityRepliedLog;
use App\Models\Core\UserGraduateLog;
use App\Models\Core\UserHistory;
use App\Models\Core\UserLearnedTime;
use App\Models\Core\UserPlaylist;
use App\Models\Core\Users;
use App\Models\Core\UsersCommentLog;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\UsersLearningLog;
use App\Models\Core\VolumnBy;
use App\Models\Core\VolumnByLot;
use App\Models\Core\VolumnByUser;
use App\Models\Dummy;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CourseController extends Controller
{

    public function __construct()
    {

    }

    public function setGroupCode(Request $request)
    {
        $response = new Dummy();

        $course = Course::where('group_code', null)->get();
        foreach ($course as $c) {
            $c->group_code = "Lrk";
            $c->save();
        }

        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function courseCommunity(Request $request)
    {
        $response                = new Dummy();
        $response['status']      = 'false';
        $response['commentData'] = [];

        if (! isset($request->page)) {
            $request->page = 1;
        }
        if (! isset($request->sort)) {
            $request->sort = 'desc';
        }
        if (! isset($request->limit)) {
            $request->limit = 5;
        }

        $request->offset = (intval($request->page) - 1) * intval($request->limit);

        $course = Course::select('course.id as course_id', 'course.slug as key')
            ->where('course.id', $request->key)
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', '!=', 2);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        $course = $course->first();
        if ($course) {
            $comment = UserCommunityLog::join('user', 'user.id', 'user_community_log.user_id')
                ->leftjoin('user_community_like_log', 'user_community_like_log.feed_id', 'user_community_log.id')
                ->select(DB::raw('COUNT(user_community_like_log.feed_id) as count'), 'user_community_log.created_at', 'user_community_log.id', 'user.name as user_name', 'user.lastname as user_lastname', 'user.avatar as user_avatar', 'user_community_log.feed as comment', 'user_community_log.image')
                ->where('user_community_log.course_id', $request->key)
                ->where('user_community_log.status', 1)
                ->limit($request->limit)->offset($request->offset);

            if ($request->sort == 'desc') {
                $comment->orderby('user_community_log.id', 'desc');
            } else if ($request->sort == 'asc') {
                $comment->orderby('user_community_log.id', 'asc');
            } else if ($request->sort == 'like_desc') {
                $comment->orderby('count', 'desc');
            } else if ($request->sort == 'like_asc') {
                $comment->orderby('count', 'asc');
            } else if ($request->sort == 'recommend') {
                $comment->orderby('user_community_log.position', 'asc');
            }

            $comment->orderby('user_community_log.id', 'desc');

            $comment->groupby('user_community_log.id');

            $comment = $comment->get();

            foreach ($comment as $key => $value) {
                $value->is_like = false;
                // if($value->image!=null&&$value->image!=''&&(strpos($value->image, 'http://') !== FALSE||strpos($value->image, 'https://') !== FALSE)){
                //   list($width, $height) = getimagesize($value->image);
                //   $value->image_width = $width;
                //   $value->image_height = $height;
                // }else{
                // $value->image_width = 300;
                // $value->image_height = 300;
                // }
                if (isset($request->utoken)) {
                    $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                    if ($user) {
                        $like = CommunityLikeLog::where('user_id', $user->id)->where('feed_id', $value->id)->first();
                        if ($like) {
                            $value->is_like = true;
                        }
                    }
                }
                $log_data = UserCommunityRepliedLog::join('user', 'user.id', 'user_community_replied_log.user_id')
                    ->leftjoin('user_community_like_replied', 'user_community_like_replied.replied_id', 'user_community_replied_log.id')
                    ->select(DB::raw('COUNT(user_community_like_replied.replied_id) as count'), 'user_community_replied_log.created_at', 'user_community_replied_log.id', 'user.name as user_name', 'user.lastname as user_lastname', 'user.avatar as user_avatar', 'user_community_replied_log.comment as comment', 'user_community_replied_log.image')
                    ->where('user_community_replied_log.feed_id', $value->id)
                    ->where('user_community_replied_log.status', 1)->groupby('user_community_replied_log.id')->orderby('user_community_replied_log.id', 'desc')->get();
                foreach ($log_data as $val_log) {
                    // if($val_log->image!=null&&$val_log->image!=''&&(strpos($val_log->image, 'http://') !== FALSE||strpos($val_log->image, 'htts://') !== FALSE)){
                    //   list($width, $height) = getimagesize($val_log->image);
                    //   $val_log->image_width = $width;
                    //   $val_log->image_height = $height;
                    // }else{
                    // $val_log->image_width = 300;
                    // $val_log->image_height = 300;
                    // }
                    $val_log->is_like = false;
                    if (isset($request->utoken)) {
                        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                        if ($user) {
                            $like = CommunityRepliedLike::where('user_id', $user->id)->where('feed_id', $value->id)->where('replied_id', $val_log->id)->first();
                            if ($like) {
                                $val_log->is_like = true;
                            }
                        }
                    }
                }
                $value->log = $log_data;
            }

            $count = UserCommunityLog::join('user', 'user.id', 'user_community_log.user_id')->where('user_community_log.course_id', $request->key)->count();

            $response['commentData'] = $comment;
            $response['count']       = $count;
            $response['limit']       = $request->limit;
            $response['offset']      = $request->offset;
            $response['sort']        = $request->sort;
            $response['page']        = $request->page;
            $response['all_page']    = ceil($count / $request->limit);

            $page = [];
            for ($x = 1; $x <= $response['all_page']; $x++) {
                array_push($page, $x);
            }
            $response['page_list'] = $page;

            $response['status'] = 'success';
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function CourseComment(Request $request)
    {
        $response                = new Dummy();
        $response['status']      = 'false';
        $response['commentData'] = [];

        if (! isset($request->page)) {
            $request->page = 1;
        }
        if (! isset($request->sort)) {
            $request->sort = 'desc';
        }
        if (! isset($request->limit)) {
            $request->limit = 5;
        }

        $request->offset = (intval($request->page) - 1) * intval($request->limit);

        $course = Course::select('course.id as course_id', 'course.slug as key')
            ->where('course.id', $request->key)
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', '!=', 2);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        $course = $course->first();
        if ($course) {
            $comment = UsersCommentLog::join('user', 'user.id', 'user_comment_log.user_id')
                ->select('user_comment_log.created_at', 'user_comment_log.rate', 'user_comment_log.id', 'user.name as user_name', 'user.lastname as user_lastname', 'user.avatar as user_avatar', 'user_comment_log.comment', 'user_comment_log.admin_id', 'user_comment_log.replied_comment')
                ->where('course_id', $request->key)->limit($request->limit)->offset($request->offset);

            if ($request->sort == 'desc') {
                $comment->orderby('user_comment_log.id', 'desc');
            } else if ($request->sort == 'asc') {
                $comment->orderby('user_comment_log.id', 'asc');
            } else if ($request->sort == 'rate_desc') {
                $comment->orderby('user_comment_log.rate', 'desc');
            } else if ($request->sort == 'rate_asc') {
                $comment->orderby('user_comment_log.rate', 'asc');
            }

            $comment = $comment->get();

            $count = UsersCommentLog::join('user', 'user.id', 'user_comment_log.user_id')
                ->where('course_id', $request->key)->count();

            $response['commentData'] = $comment;
            $response['count']       = $count;
            $response['limit']       = $request->limit;
            $response['offset']      = $request->offset;
            $response['sort']        = $request->sort;
            $response['page']        = $request->page;
            $response['all_page']    = ceil($count / $request->limit);

            $page = [];
            for ($x = 1; $x <= $response['all_page']; $x++) {
                array_push($page, $x);
            }
            $response['page_list'] = $page;

            $response['status'] = 'success';
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function CourseRelate(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        $tag     = CourseTagLog::where('course_id', $request->key)->groupby('tag_id')->get();
        $tag_arr = [];
        foreach ($tag as $val) {
            array_push($tag_arr, $val->tag_id);
        }

        $course_1 = Course::select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
            ->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
        $course_1->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course_1->where(function ($course_1) {
            $course_1->where('course.end_date', '>=', Carbon::now());
            $course_1->orWhere('course.end_date', null);
        });

        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                //กั้นห้อง
                // $course_1->leftjoin('course_department_log','course_department_log.course_id','course.id')
                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                // ->where(function ($course_1) use($user) {
                //   $course_1->where('categories_department.id',$user->dept_id);
                //   $course_1->orWhere('course.is_option',1);
                // })
                // ->where(function ($course_1){
                //   $course_1->where('categories_department.status',1);
                //   $course_1->orWhere('course.is_option',1);
                // });
            }
        }

        $course_1->whereIn('course_tag_log.tag_id', $tag_arr)->where('course.id', '!=', $request->key)->groupby('course.id');
        $course_1 = $course_1->get();

        $course_1 = AppHelper::instance()->convertCourse($course_1, $request->utoken);

        $response['data'] = $course_1;

        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function CourseDetails(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $response['key']    = $request->key;
        $response['utoken'] = $request->utoken;

        $course = Course::select('course.details_th', 'course.profit', 'course.rate', 'course.rating', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.id', 'course.level', 'course.title_th as title', 'course.subtitle_th', 'course.details_th as description', 'course.trailer_media',
            'course.course_duration as duration', 'course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_due', 'course.is_promotion', 'course.price', 'course.is_free', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.banner as cover', 'course.image_th as image', 'course.banner_m as cover_m', 'course.status', 'course.duration_time', 'course.started_date', 'course.conference_date')
            ->where('course.slug', $request->key)->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                //กั้นห้อง
                // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                // ->where(function ($course) use($user) {
                //   $course->where('categories_department.id',$user->dept_id);
                //   $course->orWhere('course.is_option',1);
                // })
                // ->where(function ($course){
                //   $course->where('categories_department.status',1);
                //   $course->orWhere('course.is_option',1);
                // });
            }
        }

        $course = $course->first();

        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                if (! $course) {
                    $course = UserHistory::join('course', 'course.id', 'user_history.course_id')
                        ->select('course.details_th', 'course.profit', 'course.rate', 'course.rating', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.id', 'course.level', 'course.title_th as title', 'course.subtitle_th', 'course.details_th as description', 'course.trailer_media',
                            'course.course_duration as duration', 'course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_due', 'course.is_promotion', 'course.price', 'course.is_free', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.banner as cover', 'course.image_th as image', 'course.banner_m as cover_m', 'course.status', 'course.duration_time', 'course.started_date', 'course.conference_date')
                        ->where('course.slug', $request->key)
                        ->where('user_history.user_id', $user->id)
                        ->where('user_history.expired', '>=', Carbon::now())
                        ->where('course.started_date', '<=', Carbon::now())
                        ->where('course.status', '!=', 2)
                        ->where(function ($course) {
                            $course->where('course.end_date', '>=', Carbon::now());
                            $course->orWhere('course.end_date', null);
                        })->first();
                }
            }
        }

        if ($course) {
            $course['is_limit_time'] = false;
            if ($course->is_promotion == 1 && $course->pro_started <= Carbon::now() && ($course->pro_end >= Carbon::now() || $course->pro_period == 2)) {
                $course['is_promotion'] = true;
            } else {
                $course['is_promotion'] = false;
            }
            $course['pro_period']    = $course->pro_period;
            $course['review']        = false;
            $course['is_assessment'] = true;
            if (isset($request->utoken)) {
                $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    if ($course->is_time == 1 && $course->time_set > 0) {
                        $check_limit_time = UserLearnedTime::where('course_id', $course->id)->where('user_id', $user->id)->first();
                        if ($check_limit_time) {
                            if ($check_limit_time->seconds >= ($course->time_set * 60 * 60)) {
                                $course['is_limit_time'] = true;
                            }
                        }
                    }
                    $check = AssessmentLog::where('user_id', $user->id)->where('course_id', $course->id)->first();
                    if ($check) {
                        $course['is_assessment'] = false;
                    }
                    $graduate = UserGraduateLog::where('user_id', $user->id)->where('course_id', $course->id)->first();
                    $review   = UsersCommentLog::where('user_id', $user->id)->where('course_id', $course->id)->first();
                    if ($graduate && ! $review) {
                        $course['review'] = true;
                    }
                }
            }
            $course['is_fav']             = false;
            $count_user_learn             = UsersLearningLog::select('id')->where('course_id', $course->id)->groupby('user_id')->get();
            $course['user_learn']         = count($count_user_learn);
            $course['course_percent']     = 0;
            $course['learned_limit']      = 0;
            $course['publish_date_th']    = AppHelper::instance()->DateThaiSlash($course->started_date);
            $course['conference_date_th'] = AppHelper::instance()->DateThaiSlash($course->conference_date);
            $course['is_subscription']    = false;
            $course['is_volume']          = false;
            if ($course->started_learning <= Carbon::now()) {
                $course['is_soon'] = false;
            } else {
                $course['is_soon'] = true;
            }
            $course['faq'] = CourseFaq::select('question', 'answer')->where('course_id', $course->id)->where('status', 1)->orderby('position', 'asc')->get();
            $section       = CourseSection::select('id', 'type', 'title', 'details', 'lesson_id')->where('course_id', $course->id)->where('status', 1)->orderby('position', 'asc')->get();
            foreach ($section as $sec) {
                $lessons = CourseSectionLesson::join('course_lesson', 'course_lesson.id', 'course_section_lesson_log.lesson_id')
                    ->select('course_lesson.title_th as title', 'course_lesson.image_th as image', 'course_lesson.id', 'course_lesson.price', 'course_lesson.duration')
                    ->where('course_section_lesson_log.section_id', $sec->id)
                    ->where('course_lesson.status', 1)
                    ->orderby('course_lesson.position', 'asc')
                    ->get()
                    ->map(function ($lesson) use ($course) {
                        $lesson->exam = CourseExamLog::where('course_id', $course->id)
                            ->where('lesson_id', $lesson->id)
                            ->where('status', 1)
                            ->exists();
                        return $lesson;
                    });

                $files = CourseSectionFileLog::join('course_file', 'course_file.id', 'course_section_file_log.file_id')
                    ->select('course_file.title_th', 'course_file.file_pptx_th', 'course_file.file_xlsx_th', 'course_file.file_pdf_th')
                    ->where('course_section_file_log.section_id', $sec->id)
                    ->where('course_file.status', 1)
                    ->orderby('course_file.position', 'asc')
                    ->get();

                $sec->lessons = $lessons;
                $sec->files   = $files;
            }
            $course['section']      = $section;
            $course['document']     = CourseFileLog::select('title_th', 'file_pptx_th', 'file_xlsx_th', 'file_pdf_th')->where('course_id', $course->id)->where('status', 1)->orderby('position', 'asc')->get();
            $course['tag']          = Tags::join('course_tag_log', 'course_tag_log.tag_id', 'tag.id')->select('tag.*')->where('tag.status', 1)->where('course_tag_log.course_id', $course->id)->get();
            $course['speaker']      = CourseSpeakerLog::join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker')->select('categories_speaker.*')->where('course_speaker_log.course_id', $course->id)->groupby('categories_speaker.id')->get();
            $course['speaker_name'] = '';
            foreach ($course['speaker'] as $val_s) {
                if ($course['speaker_name'] != '') {
                    $course['speaker_name'] .= ' , ';
                }
                $course['speaker_name'] .= $val_s->title_th;
            }
            $course['category']      = CourseCateLog::join('categories', 'categories.id', 'course_categories_log.cate_id')->select('categories.*')->where('course_categories_log.course_id', $course->id)->groupby('categories.id')->get();
            $course['category_name'] = '';
            foreach ($course['category'] as $val_s) {
                if ($course['category_name'] != '') {
                    $course['category_name'] .= ' , ';
                }
                $course['category_name'] .= $val_s->title_th;
            }

            $course['order_status'] = '';

            $previous_id = Course::where('id', '<', $course->id)->where('trailer_media', $course->trailer_media)->max('id');
            $next_id     = Course::where('id', '>', $course->id)->where('trailer_media', $course->trailer_media)->min('id');

            if ($course->trailer_media == 1) {
                $course['type']   = 'course';
                $course['keySel'] = 0;
            } else if ($course->trailer_media == 2) {
                $course['type']   = 'zoom';
                $course['keySel'] = 0;
            } else if ($course->trailer_media == 3) {
                $course['type']   = 'podcast';
                $course['keySel'] = null;
            } else if ($course->trailer_media == 5) {
                $course['type']   = 'infographic';
                $course['keySel'] = 0;
            } else {
                $course['type']   = 'seminar';
                $course['keySel'] = 0;
            }
            if ($previous_id) {
                $previous_data = Course::find($previous_id);
                $previous      = '/' . $course['type'] . '/' . $previous_data->slug;
            } else {
                $previous = null;
            }
            if ($next_id) {
                $next_data = Course::find($next_id);
                $next      = '/' . $course['type'] . '/' . $next_data->slug;
            } else {
                $next = null;
            }

            $course['previous'] = $previous;
            $course['next']     = $next;

            if ($course->level != null && $course->level != '') {
                $cate_level = CateLevel::where('id', $course->level)->first();
                if ($cate_level) {
                    $course['level'] = $cate_level->title_th;
                } else {
                    $course['level'] = '';
                }
            } else {
                $course['level'] = '';
            }

            // $course['rating'] = CourseRate::where('course_id',$course->id)->count();
            // $duration = CourseLessonLog::where('course_id',$course->id)->where('status',1)->sum(DB::raw('TIME_TO_SEC(duration)'));
            // $course['duration'] = $duration;

            $lesson_count     = CourseLessonLog::where('status', 1)->where('course_id', $course->id)->count();
            $course['lesson'] = $lesson_count;
            $document         = CourseFileLog::select('title_th', 'file_pdf_th', 'file_pptx_th', 'file_xlsx_th', 'status')
                ->where('course_id', $course->id)->where('status', 1)->get();

            $data_sponsor = [];
            // if(isset($request->utoken)){
            //   $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            //   if($user){
            //     $data_sponsor = AppHelper::instance()->getSponsor($course->id,$user);
            //   }
            // }

            if (isset($request->utoken)) {
                $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $is_fav = UsersFavoriteLog::where('course_id', $course->id)->where('user_id', $user->id)->first();
                    if ($is_fav) {
                        $course['is_fav'] = true;
                    }
                    $count_lesson    = CourseLessonLog::where('status', 1)->where('course_id', $course->id)->count();
                    $count_learn_log = UsersLearningLog::join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
                        ->where('course_lesson.status', 1)
                        ->where('course_lesson.course_id', $course->id)
                        ->where('user_learning_log.user_id', $user->id)
                        ->where('user_learning_log.course_id', $course->id)->get();
                    $count_learn = 0;
                    foreach ($count_learn_log as $val_l) {
                        $count_learn += $val_l->watch_percent;
                    }
                    if ($count_learn != 0 && $count_lesson != 0) {
                        $course['course_percent'] = number_format($count_learn / $count_lesson);
                    }
                    $learned_time = UserLearnedTime::where('user_id', $user->id)->where('course_id', $course->id)->first();
                    if ($learned_time) {
                        $course['learned_limit'] = $learned_time->seconds;
                    }
                    $check_sub                 = AppHelper::instance()->courseSubscription($course, $user);
                    $course['is_subscription'] = $check_sub[0];

                    $check_volume        = AppHelper::instance()->checkVolume($course, $user);
                    $course['is_volume'] = $check_volume[0];
                    $array_check         = AppHelper::instance()->checkCourseAllow($course, $user);
                    $course['allowed']   = $array_check[0];
                    // $course['order_status'] = $array_check[1];

                    $count_learning = UsersLearningLog::where('course_id', $course->id)->where('user_learning_log.user_id', $user->id)
                        ->where('user_learning_log.action', 1)->where('user_learning_log.status', 2)->count();

                    if ($count_learning) {
                        $average = ($count_learning * 100) / $lesson_count;
                    } else {
                        $average = 0;
                    }
                    $course['learned_percent'] = $average;

                    $learned = UsersLearningLog::where('course_id', $course->id)->where('user_id', $user->id)
                        ->orderBy('lesson_id', 'desc')->first();
                    if ($learned) {
                        $course['learned_ep']       = UsersLearningLog::select('lesson_id as id')->where('course_id', $course->id)->where('user_id', $user->id)->orderby('updated_at', 'desc')->first();
                        $course['learned_duration'] = AppHelper::instance()->hoursToSecods($learned->watching_time);
                    } else {
                        $course['learned_ep']       = null;
                        $course['learned_duration'] = 0;
                    }
                } else {
                    $course['allowed']          = false;
                    $course['learned_percent']  = 0;
                    $course['learned_ep']       = null;
                    $course['learned_duration'] = 0;
                }
            } else {
                $course['allowed']          = false;
                $course['learned_percent']  = 0;
                $course['learned_ep']       = null;
                $course['learned_duration'] = 0;
            }

            $response['data']            = $course;
            $response['documentList']    = $document;
            $response['scholarshipList'] = $data_sponsor;
            $response['status']          = 'success';
        } else {
            $response['status'] = 'false';
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function CompanyDetails(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $response['key']    = $request->key;
        $response['utoken'] = $request->utoken;

        if (isset($request->utoken)) {
            $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $company = VolumnBy::where('volumn_by.slug', $request->key)->where('volumn_by.status', 1)->first();
                if ($company) {
                    $volume = VolumnByUser::join('volumn_lot_log', 'volumn_lot_log.id', 'volumn_user_log.lot_id')
                        ->join('volumn_by', 'volumn_by.id', 'volumn_user_log.company_id')
                        ->select('volumn_by.id')
                        ->where('volumn_lot_log.status', 1)
                        ->where('volumn_by.status', 1)
                        ->where('volumn_by.id', $company->id);
                    $volume->where(function ($volume) use ($user) {
                        $volume->where('volumn_user_log.email', $user->email);
                    });
                    $volume->where(function ($volume) {
                        $volume->where('volumn_lot_log.started_date', '<=', Carbon::now());
                    });
                    $volume->where(function ($volume) {
                        $volume->where('volumn_lot_log.end_date', '>=', Carbon::now());
                    });
                    $volume->groupby('volumn_by.id');
                    $volume = $volume->first();
                    if ($volume) {
                        $response['data']   = $company;
                        $response['status'] = 'success';
                    } else {
                        $vol_domain = VolumnByLot::join('volumn_domain_log', 'volumn_domain_log.lot_id', 'volumn_lot_log.id')
                            ->join('categories_domain', 'categories_domain.id', 'volumn_domain_log.domain_id')
                            ->join('volumn_by', 'volumn_by.id', 'volumn_lot_log.company_id')
                            ->select('volumn_by.id')
                            ->where('volumn_lot_log.status', 1)
                            ->where('volumn_by.status', 1)
                            ->where('categories_domain.status', 1)
                            ->where('volumn_by.id', $company->id);
                        $vol_domain->where(function ($vol_domain) use ($user) {
                            if (count(explode("@", $user->email)) == 2) {
                                $vol_domain->where('categories_domain.title', '@' . explode("@", $user->email)[1]);
                            }
                        });
                        $vol_domain->where(function ($vol_domain) {
                            $vol_domain->where('volumn_lot_log.started_date', '<=', Carbon::now());
                            // $vol_domain->orWhere('volumn_lot_log.started_date',null);
                        });
                        $vol_domain->where(function ($vol_domain) {
                            $vol_domain->where('volumn_lot_log.end_date', '>=', Carbon::now());
                            // $vol_domain->orWhere('volumn_lot_log.end_date',null);
                        });
                        $vol_domain->groupby('volumn_by.id');
                        $vol_domain = $vol_domain->first();
                        if ($vol_domain) {
                            $response['data']   = $company;
                            $response['status'] = 'success';
                        }
                    }
                }
            }
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function Infographic(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $response['key']    = $request->key;

        $disease_all = new Dummy();
        $data_d_all  = Course::select('course.*', 'course.image_th as image')->where('trailer_media', 5)->where('status', 1)
            ->where('started_date', '<=', Carbon::now())->orderby('id', 'desc')->limit(30);
        $data_d_all->where(function ($data_d_all) {
            $data_d_all->where('course.end_date', '>=', Carbon::now());
            $data_d_all->orWhere('course.end_date', null);
        });

        $data_d_all          = $data_d_all->get();
        $disease_all['data'] = $data_d_all;
        if (count($disease_all['data']) > 0) {
            array_push($data_disease, $disease_all);
        }

        $response['infographic_data'] = $data_disease;
        $response['status']           = 'success';

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function getInfographicAll(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        if (! isset($request->page)) {
            $request->page = 1;
        }
        if (! isset($request->limit)) {
            $request->limit = 24;
        }
        if ($request->limit > 24) {
            $request->limit = 24;
        }
        $offset = (intval($request->page) - 1) * intval($request->limit);

        $query = Course::select('course.slug', 'course.title_th', 'course.image_th as image')->where('course.trailer_media', 5)->where('course.status', 1)
            ->where('course.started_date', '<=', Carbon::now())->orderby('course.id', 'desc')->offset($offset)->limit($request->limit);
        $query->where(function ($query) {
            $query->where('course.end_date', '>=', Carbon::now());
            $query->orWhere('course.end_date', null);
        });

        $query = $query->get();

        $count = Course::where('course.trailer_media', 5)->where('course.status', 1)
            ->where('course.started_date', '<=', Carbon::now());
        $count->where(function ($count) {
            $count->where('course.end_date', '>=', Carbon::now());
            $count->orWhere('course.end_date', null);
        });

        $count = $count->count();

        $response['data']     = $query;
        $response['all_page'] = ceil($count / $request->limit);
        $response['page']     = $request->page;
        $response['limit']    = $request->limit;
        $response['status']   = 'success';

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function ContentList(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';

        $news_pin = Article::join('news_pin', 'news_pin.news_id', 'article.id')
            ->select('article.id', 'article.slug', 'article.image', 'article.title_th', 'article.subtitle_th', 'article.created_at as date')
            ->where('article.is_article', 2)
            ->where('article.status', 1)
            ->where('news_pin.status', 1)
            ->orderby('news_pin.position', 'asc')
            ->get();

        $news_pin_id = [];
        foreach ($news_pin as $val) {
            array_push($news_pin_id, $val->id);
        }

        $news_other = Article::select('article.slug', 'article.image', 'article.title_th', 'article.subtitle_th', 'article.created_at as date')
            ->where('article.is_article', 2)
            ->where('article.status', 1)
            ->whereNotIn('article.id', $news_pin_id)
            ->orderby('article.created_at', 'desc')
            ->get();

        $news_list = array_merge(json_decode(json_encode($news_pin), true), json_decode(json_encode($news_other), true));

        foreach ($news_list as $key => $val) {
            $news_list[$key]['date'] = AppHelper::instance()->DateThaiOnly($val['date']);
        }

        $article_pin = Article::join('article_pin', 'article_pin.article_id', 'article.id')
            ->select('article.id', 'article.slug', 'article.image', 'article.title_th', 'article.subtitle_th', 'article.created_at as date')
            ->where('article.is_article', 1)
            ->where('article.status', 1)
            ->where('article_pin.status', 1)
            ->orderby('article_pin.position', 'asc')
            ->get();

        $article_pin_id = [];
        foreach ($article_pin as $val) {
            array_push($article_pin_id, $val->id);
        }

        $article_other = Article::select('article.slug', 'article.image', 'article.title_th', 'article.subtitle_th', 'article.created_at as date')
            ->where('article.is_article', 1)
            ->where('article.status', 1)
            ->whereNotIn('article.id', $article_pin_id)
            ->orderby('article.created_at', 'desc')
            ->get();

        $article_list = array_merge(json_decode(json_encode($article_pin), true), json_decode(json_encode($article_other), true));

        foreach ($article_list as $key => $val) {
            $article_list[$key]['date'] = AppHelper::instance()->DateThaiOnly($val['date']);
        }

        $response['newsList']    = $news_list;
        $response['articleList'] = $article_list;
        $response['status']      = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function ArticleDetails(Request $request)
    {
        $response           = new Dummy();
        $response['status'] = 'false';
        $response['key']    = $request->key;

        $article_details = Article::select('article.*')
            ->where('article.slug', $request->key)
            ->where('article.status', 1)
            ->first();
        if ($article_details) {
            $article_details['date'] = AppHelper::instance()->DateThaiOnly($article_details->created_at);
            $response['data']        = $article_details;
            $response['status']      = 'success';
        }

        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function ArticleRelate(Request $request)
    {

        $response           = new Dummy();
        $response['status'] = 'false';
        $response['key']    = $request->key;
        $response['utoken'] = $request->utoken;

        $article_type = Article::where('id', $request->key)->first();
        if ($article_type->is_article == 1) {
            $relate = ArticleTagLog::where('article_id', $article_type->id)->get();

            $data_relate     = [];
            $data_relate_dup = [];
            foreach ($relate as $key => $value) {
                $arr         = new Dummy();
                $arr['data'] = ArticleTagLog::join('article', 'article.id', 'article_tag_log.article_id')
                    ->where('tag_id', $value->tag_id)
                    ->where('article_id', '!=', $value->article_id)
                    ->where('article_id', '!=', null)
                    ->where('article.is_article', 1)
                    ->where('article.status', 1)
                    ->select('article.title_th', 'article.subtitle_th', 'article.slug', 'article.image', 'article.id')
                    ->limit(20)
                    ->get();

                foreach ($arr['data'] as $key_arr => $val) {
                    if (! in_array($val['id'], $data_relate_dup)) {
                        $data_relate_dup[] = $val['id'];
                        $val['date']       = AppHelper::instance()->DateThaiOnly($val->created_at);
                        array_push($data_relate, $val);
                    }
                }
            }

            $response['data'] = $data_relate;
        } else {
            $relate = ArticleTagLog::where('article_id', $article_type->id)->get();

            $data_relate     = [];
            $data_relate_dup = [];
            foreach ($relate as $key => $value) {
                $arr         = new Dummy();
                $arr['data'] = ArticleTagLog::join('article', 'article.id', 'article_tag_log.article_id')
                    ->where('tag_id', $value->tag_id)
                    ->where('article_id', '!=', $value->article_id)
                    ->where('article_id', '!=', null)
                    ->where('article.is_article', 2)
                    ->where('article.status', 1)
                    ->select('article.title_th', 'article.subtitle_th', 'article.slug', 'article.image', 'article.id')
                    ->limit(20)
                    ->get();

                foreach ($arr['data'] as $key_arr => $val) {
                    if (! in_array($val['id'], $data_relate_dup)) {
                        $data_relate_dup[] = $val['id'];
                        $val['date']       = AppHelper::instance()->DateThaiOnly($val->created_at);
                        array_push($data_relate, $val);
                    }
                }
            }

            $response['data'] = $data_relate;
        }

        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function getCourseFilter(Request $request)
    {
        $response = new Dummy();
        // $response['utoken'] = $request->utoken;
        $response['filter']       = json_decode($request->filter);
        $response['tag']          = json_decode($request->tag);
        $response['sort']         = $request->sort;
        $response['keyword']      = $request->keyword;
        $response['page']         = $request->page;
        $response['trailer_media'] = $request->trailer_media;

        if (! isset($request->page)) {
            $request->page = 1;
        }
        if (! isset($request->limit)) {
            $request->limit = 25;
        }
        if ($request->limit > 25) {
            $request->limit = 25;
        }
        $offset = (intval($request->page) - 1) * intval($request->limit);

        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('international', $request->international);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->trailer_media) && $request->trailer_media != null && $request->trailer_media != '' && $request->trailer_media != 'null') {
            if (is_array($request->trailer_media)) {
                $course->whereIn('course.trailer_media', $request->trailer_media);
            } else {
                if (strpos($request->trailer_media, ',') !== false) {
                    $trailer_media_array = explode(',', $request->trailer_media);
                    $course->whereIn('course.trailer_media', $trailer_media_array);
                } else {
                    $course->where('course.trailer_media', $request->trailer_media);
                }
            }
        }

        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                //กั้นห้อง
                // $course->leftjoin('course_department_log','course_department_log.course_id','course.id')
                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                // ->where(function ($course) use($user) {
                //   $course->where('categories_department.id',$user->dept_id);
                //   $course->orWhere('course.is_option',1);
                // })
                // ->where(function ($course){
                //   $course->where('categories_department.status',1);
                //   $course->orWhere('course.is_option',1);
                // });
            }
        }
        $course->leftjoin('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
        $course->leftjoin('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            //$mybrowser = get_browser(null, true);
            $ip = getenv('HTTP_CLIENT_IP') ?:
            getenv('HTTP_X_FORWARDED_FOR') ?:
            getenv('HTTP_X_FORWARDED') ?:
            getenv('HTTP_FORWARDED_FOR') ?:
            getenv('HTTP_FORWARDED') ?:
            getenv('REMOTE_ADDR');
            $stat_log       = new StatLog();
            $stat_log->type = 1;
            //$stat_log->browser=$mybrowser['browser'];
            $stat_log->ip      = $ip;
            $stat_log->page    = 'course';
            $stat_log->keyword = $request->keyword;
            if (isset($request->utoken)) {
                $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $stat_log->user_id = $user->id;
                }
            }
            $stat_log->save();

            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }
        if (isset($request->tag) && $request->is_tag == 1) {
            $val_tag = json_decode($request->tag);
            if (count($val_tag) > 0) {
                $course->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
                $course->where(function ($course) use ($val_tag) {
                    foreach ($val_tag as $key => $value) {
                        if ($key == 0) {
                            $course->where('course_tag_log.tag_id', $value->id);
                        } else {
                            $course->Orwhere('course_tag_log.tag_id', $value->id);
                        }
                    }
                });
            }
        }
        if (isset($request->filter)) {
            $val_filter = json_decode($request->filter);
            if (count($val_filter) > 0) {
                foreach ($val_filter as $key => $value) {
                    if ($value->category_key == 'job_function') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_job_function_log', 'course_job_function_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'is_certificate') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('cert_dynamic', 'cert_dynamic.course_id', 'course.id');
                            $course->where('cert_dynamic.status', 1);
                        }
                    }
                }
                $course->where(function ($course) use ($val_filter) {
                    $keywhere = 0;
                    foreach ($val_filter as $key => $value) {
                        if ($value->category_key == 'job_function') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_job_function_log.job_function_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_job_function_log.job_function_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'speaker') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_speaker_log.speaker', $value_sub);
                                } else {
                                    $course->Orwhere('course_speaker_log.speaker', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'level') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.level', $value_sub);
                                } else {
                                    $course->Orwhere('course.level', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'duration') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        }
                    }
                });
            }
        }
        $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration');
        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }

        $course->groupby('course.id');
        $course_all = ceil((count($course->get())) / $request->limit);
        $course->offset($offset);
        $course->limit($request->limit);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');
        $course = $course->get();

        $course_data                 = [];
        $course_dummy                = new Dummy();
        $course_dummy['group_title'] = 'คอร์สทั้งหมด';
        $course_dummy['data']        = AppHelper::instance()->convertCourse($course, $request->utoken);
        if (count($course_dummy['data']) > 0) {
            array_push($course_data, $course_dummy);
        }

        $response['course_data'] = $course_data;
        $response['all_page']    = $course_all;

        $course_id = [];
        foreach ($course_data as $value) {
            foreach ($value['data'] as $value_data) {
                array_push($course_id, $value_data->id);
            }
        }
        $data_tag = CourseTagLog::join('tag', 'tag.id', 'course_tag_log.tag_id')->select('tag.id', 'tag.title')->whereIn('course_tag_log.course_id', $course_id)->limit(10)->get();

        if ($request->is_tag == 1) {
            $response['tag_data'] = json_decode($request->tag);
        } else {
            $response['tag_data'] = $data_tag;
        }

        $start_day = Carbon::now()->subdays(30);
        $course_1  = Course::join('top_course_log', 'top_course_log.course_id', 'course.id')
            ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', DB::raw('SUM(top_course_log.count) as top_view'), 'course.started_date', 'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th', 'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th', 'course.duration_time', 'course.created_at', 'course.course_duration')
            ->orderby('top_view', 'desc')
            ->where('course.started_date', '<=', Carbon::now())
            ->where('top_course_log.date', '>=', $start_day)
            ->where(function ($course_1) {
                $course_1->where('course.started_learning', '<=', Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where('course.status', 1)->where('international', $request->international)->limit(10);

        // เพิ่มการกรองตาม trailer_media สำหรับ recommend section ด้วย
        if (isset($request->trailer_media) && $request->trailer_media != null && $request->trailer_media != '' && $request->trailer_media != 'null') {
            if (is_array($request->trailer_media)) {
                $course_1->whereIn('course.trailer_media', $request->trailer_media);
            } else {
                if (strpos($request->trailer_media, ',') !== false) {
                    $trailer_media_array = explode(',', $request->trailer_media);
                    $course_1->whereIn('course.trailer_media', $trailer_media_array);
                } else {
                    $course_1->where('course.trailer_media', $request->trailer_media);
                }
            }
        }

        $course_1->where(function ($course_1) {
            $course_1->where('course.end_date', '>=', Carbon::now());
            $course_1->orWhere('course.end_date', null);
        });
        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                //กั้นห้อง
                // $course_1->leftjoin('course_department_log','course_department_log.course_id','course.id')
                // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
                // ->where(function ($course_1) use($user) {
                //   $course_1->where('categories_department.id',$user->dept_id);
                //   $course_1->orWhere('course.is_option',1);
                // })
                // ->where(function ($course_1){
                //   $course_1->where('categories_department.status',1);
                //   $course_1->orWhere('course.is_option',1);
                // });
            }
        }
        $course_1->groupby('course.id');
        $course_1              = $course_1->get();
        $response['recommend'] = AppHelper::instance()->convertCourse($course_1, $request->utoken);

        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function getCourseBySubscriptionMain(Request $request)
{
    $response = new Dummy();
    $response['status'] = 'false';

    if (!isset($request->page)) {
        $request->page = 1;
    }
    if (!isset($request->limit)) {
        $request->limit = 25;
    }
    if ($request->limit > 25) {
        $request->limit = 25;
    }
    $offset = (intval($request->page) - 1) * intval($request->limit);

    try {
        // เช็คว่าต้องการข้อมูลแบบ subscription_filter หรือไม่
        if (isset($request->fields) && $request->fields == 'subscription_filter') {
            $subscription_mains = SubscriptionMain::select('id', 'title')
                ->where('status', 1)
                ->orderBy('position', 'asc')
                ->get();

            foreach ($subscription_mains as $main) {
                $subscriptions = Subscription::select('id', 'title', 'only_cate')
                    ->where('subscription_main_id', $main->id)
                    ->where('type', 1)
                    ->where('status', 1)
                    ->get();
                
                $main->subscriptions = $subscriptions;
            }

            $response['subscription_mains'] = $subscription_mains;
            $response['status'] = 'success';

            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
        }

        if (!isset($request->subscription_main_id) || empty($request->subscription_main_id)) {
            
            $subscription_mains = SubscriptionMain::select(
                'id', 'title', 'short_description', 'description', 'cover_image', 'period', 
                 'slug', 'position', 
                'is_popular', 'status', 'created_at'
            )
            ->where('status', 1)
            ->orderBy('position', 'asc')
            ->get();

            foreach ($subscription_mains as $main) {
                $subscriptions = Subscription::where('subscription_main_id', $main->id)
                    ->where('type', 1)
                    ->where('status', 1)
                    ->get();
                
                $main->subscriptions = $subscriptions;
                
                $categories = [];
                foreach ($subscriptions as $sub) {
                    if (!empty($sub->only_cate)) {
                        $cats = explode(',', $sub->only_cate);
                        foreach ($cats as $cat_id) {
                            $cat_id = trim($cat_id);
                            if (!empty($cat_id) && is_numeric($cat_id)) {
                                $categories[] = (int)$cat_id;
                            }
                        }
                    }
                }
                $main->category_ids = array_unique($categories);

                $allowed_categories = array_unique($categories);
                if (!empty($allowed_categories)) {
                    $course = Course::select(
                        'course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set',
                        'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end',
                        'course.pro_period', 'course.price', 'course.started_date', 'course.rate',
                        'course.started_learning', 'course.receive_point', 'course.title_th',
                        'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th',
                        'course.duration_time', 'course.created_at', 'course.course_duration'
                    )
                    ->join('course_categories_log', 'course_categories_log.course_id', 'course.id')
                    ->whereIn('course_categories_log.cate_id', $allowed_categories)
                    ->where('course.started_date', '<=', Carbon::now())
                    ->where('course.status', 1)
                    ->where(function ($course) {
                        $course->where('course.end_date', '>=', Carbon::now());
                        $course->orWhere('course.end_date', null);
                    })
                    ->groupby('course.id')
                    ->orderby('course.started_date', 'desc')
                    ->orderby('course.id', 'desc')
                    ->get();

                    $main->course_data = AppHelper::instance()->convertCourse($course, $request->utoken);
                } else {
                    $main->course_data = [];
                }
            }

            $response['subscription_mains'] = $subscription_mains;
            $response['total_mains'] = $subscription_mains->count();
            $response['message'] = 'Subscription main list retrieved successfully';
            $response['status'] = 'success';

            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
        }

        $response['subscription_main_id'] = $request->subscription_main_id;

        $subscription_main = SubscriptionMain::where('id', $request->subscription_main_id)
            ->where('status', 1)
            ->first();

        if (!$subscription_main) {
            $response['message'] = 'Subscription Main not found or inactive';
            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
        }

        $subscriptions = Subscription::where('subscription_main_id', $request->subscription_main_id)
            ->where('type', 1)
            ->where('status', 1)
            ->get();

        if ($subscriptions->isEmpty()) {
            $response['message'] = 'No active subscriptions found in this subscription main';
            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
        }

        $allowed_categories = [];
        foreach ($subscriptions as $subscription) {
            if (!empty($subscription->only_cate)) {
                $categories = explode(',', $subscription->only_cate);
                foreach ($categories as $cat_id) {
                    $cat_id = trim($cat_id);
                    if (!empty($cat_id) && is_numeric($cat_id)) {
                        $allowed_categories[] = (int)$cat_id;
                    }
                }
            }
        }

        $allowed_categories = array_unique($allowed_categories);

        if (empty($allowed_categories)) {
            $response['message'] = 'No categories specified in subscriptions';
            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
        }

        $course = Course::select(
            'course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set',
            'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end',
            'course.pro_period', 'course.price', 'course.started_date', 'course.rate',
            'course.started_learning', 'course.receive_point', 'course.title_th',
            'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th',
            'course.duration_time', 'course.created_at', 'course.course_duration'
        )
        ->join('course_categories_log', 'course_categories_log.course_id', 'course.id')
        ->whereIn('course_categories_log.cate_id', $allowed_categories)
        ->where('course.started_date', '<=', Carbon::now())
        ->where('course.status', 1);

        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            $course->leftjoin('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
            $course->leftjoin('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');

            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->orWhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->orWhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->orWhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->orWhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->orWhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->orWhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }

        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }

        $course->groupby('course.id');

        $course_all = ceil((count($course->get())) / $request->limit);

        $course->offset($offset);
        $course->limit($request->limit);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');

        $courses = $course->get();

        $course_data = AppHelper::instance()->convertCourse($courses, $request->utoken);

        $response['subscription_main'] = $subscription_main;
        $response['subscriptions'] = $subscriptions;
        $response['allowed_categories'] = $allowed_categories;
        $response['course_data'] = $course_data;
        $response['total_courses'] = count($course_data);
        $response['all_page'] = $course_all;
        $response['current_page'] = $request->page;
        $response['limit'] = $request->limit;
        $response['status'] = 'success';

    } catch (\Exception $e) {
        $response['error'] = $e->getMessage();
        $response['line'] = $e->getLine();
        $response['file'] = $e->getFile();
    }

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
}

    public function getZoomFilter(Request $request)
    {
        $response = new Dummy();
        // $response['utoken'] = $request->utoken;
        $response['filter']  = json_decode($request->filter);
        $response['tag']     = json_decode($request->tag);
        $response['sort']    = $request->sort;
        $response['keyword'] = $request->keyword;

        if (! isset($request->page)) {
            $request->page = 1;
        }
        if (! isset($request->limit)) {
            $request->limit = 24;
        }
        if ($request->limit > 24) {
            $request->limit = 24;
        }
        $offset = (intval($request->page) - 1) * intval($request->limit);

        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            //$mybrowser = get_browser(null, true);
            $ip = getenv('HTTP_CLIENT_IP') ?:
            getenv('HTTP_X_FORWARDED_FOR') ?:
            getenv('HTTP_X_FORWARDED') ?:
            getenv('HTTP_FORWARDED_FOR') ?:
            getenv('HTTP_FORWARDED') ?:
            getenv('REMOTE_ADDR');
            $stat_log       = new StatLog();
            $stat_log->type = 1;
            //$stat_log->browser=$mybrowser['browser'];
            $stat_log->ip      = $ip;
            $stat_log->page    = 'zoom';
            $stat_log->keyword = $request->keyword;
            if (isset($request->utoken)) {
                $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $stat_log->user_id = $user->id;
                }
            }
            $stat_log->save();

            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
            $course->join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }
        if (isset($request->tag) && $request->is_tag == 1) {
            $val_tag = json_decode($request->tag);
            if (count($val_tag) > 0) {
                $course->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
                $course->where(function ($course) use ($val_tag) {
                    foreach ($val_tag as $key => $value) {
                        if ($key == 0) {
                            $course->where('course_tag_log.tag_id', $value->id);
                        } else {
                            $course->Orwhere('course_tag_log.tag_id', $value->id);
                        }
                    }
                });
            }
        }
        $check_content_type = 0;
        if (isset($request->filter)) {
            $val_filter = json_decode($request->filter);
            if (count($val_filter) > 0) {
                foreach ($val_filter as $key => $value) {
                    if ($value->category_key == 'department') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_department_log', 'course_department_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'disease') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_disease_log', 'course_disease_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'chula_center') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_chula_center_log', 'course_chula_center_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'speaker') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'gender') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_gender_log', 'course_gender_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'learner') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_learner_log', 'course_learner_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'organ_system') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_organ_log', 'course_organ_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'sub_categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_sub_categories_log', 'course_sub_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'is_certificate') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('cert_dynamic', 'cert_dynamic.course_id', 'course.id');
                            $course->where('cert_dynamic.status', 1);
                        }
                    }
                }
                $course->where(function ($course) use ($val_filter, $check_content_type) {
                    $keywhere = 0;
                    foreach ($val_filter as $key => $value) {
                        if ($value->category_key == 'department') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_department_log.department', $value_sub);
                                } else {
                                    $course->Orwhere('course_department_log.department', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'disease') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_disease_log.disease', $value_sub);
                                } else {
                                    $course->Orwhere('course_disease_log.disease', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'chula_center') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_chula_center_log.chula_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_chula_center_log.chula_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'speaker') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_speaker_log.speaker', $value_sub);
                                } else {
                                    $course->Orwhere('course_speaker_log.speaker', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'gender') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_gender_log.gender_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_gender_log.gender_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'level') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.level', $value_sub);
                                } else {
                                    $course->Orwhere('course.level', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'price') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->whereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->whereBetween('course.price', [5001, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->orWhereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->orWhereBetween('course.price', [5001, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'learner') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_learner_log.learner', $value_sub);
                                } else {
                                    $course->Orwhere('course_learner_log.learner', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'organ_system') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_organ_log.organ_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_organ_log.organ_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'promotion') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->where('course.is_promotion', 1);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhere('course.is_promotion', 1);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'year') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.year', $value_sub);
                                } else {
                                    $course->Orwhere('course.year', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'content_type') {
                            $check_content_type++;
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.trailer_media', $value_sub);
                                } else {
                                    $course->Orwhere('course.trailer_media', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'duration') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'sub_categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_sub_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_sub_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'cme_score') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.receive_point', '>', 0);
                                } else {
                                    $course->Orwhere('course.receive_point', '>', 0);
                                }
                                $keywhere++;
                            }
                        }
                    }
                });
            }
        }
        $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration');
        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }
        $course->groupby('course.id');
        if ($check_content_type == 0) {
            $course->where(function ($course) {
                $course->where('course.trailer_media', 2);
            });
        }
        $course_all = ceil((count($course->get())) / $request->limit);
        $course->offset($offset);
        $course->limit($request->limit);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');
        $course = $course->get();

        $course_data = [];

        if ((! isset($request->keyword) || $request->keyword == null || $request->keyword == '' || $request->keyword == 'null')
            && (! isset($request->tag) || (isset($request->tag) && count(json_decode($request->tag)) == 0) || $request->is_tag == 2)
            && (! isset($request->filter) || (isset($request->filter) && count(json_decode($request->filter)) == 0))
            && (! isset($request->sort) || $request->sort == null || $request->sort == '' || $request->sort == 'null')) {
            if (isset($request->utoken)) {
                $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $relate = Course::join('course_learner_log', 'course_learner_log.course_id', 'course.id')
                        ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->where('course_learner_log.learner', $user->user_type)
                        ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
                    $relate->where(function ($relate) {
                        $relate->where('course.end_date', '>=', Carbon::now());
                        $relate->orWhere('course.end_date', null);
                    });

                    $relate->where(function ($relate) {
                        $relate->where('course.trailer_media', 2);
                    });
                    $relate->limit(8);
                    $relate->orderby('course.started_date', 'desc');
                    $relate                      = $relate->get();
                    $course_dummy                = new Dummy();
                    $course_dummy['group_title'] = 'เหมาะกับคุณ';
                    $course_dummy['data']        = AppHelper::instance()->convertCourse($relate, $request->utoken);
                    if (count($course_dummy['data']) > 0) {
                        array_push($course_data, $course_dummy);
                    }
                }
            }
            $popular = Course::select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration')
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
            $popular->where(function ($popular) {
                $popular->where('course.end_date', '>=', Carbon::now());
                $popular->orWhere('course.end_date', null);
            });

            $popular->where(function ($popular) {
                $popular->where('course.trailer_media', 2);
            });
            $popular->orderby('course.rate', 'desc');
            $popular->limit(8);
            $popular->orderby('course.started_date', 'desc');
            $popular                     = $popular->get();
            $course_dummy                = new Dummy();
            $course_dummy['group_title'] = 'ยอดนิยม';
            $course_dummy['data']        = AppHelper::instance()->convertCourse($popular, $request->utoken);
            if (count($course_dummy['data']) > 0) {
                array_push($course_data, $course_dummy);
            }

            $new = Course::select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration')
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
            $new->where(function ($new) {
                $new->where('course.end_date', '>=', Carbon::now());
                $new->orWhere('course.end_date', null);
            });

            $new->where(function ($new) {
                $new->where('course.trailer_media', 2);
            });
            $new->orderby('course.started_date', 'desc');
            $new->limit(8);
            $new                         = $new->get();
            $course_dummy                = new Dummy();
            $course_dummy['group_title'] = 'มาใหม่';
            $course_dummy['data']        = AppHelper::instance()->convertCourse($new, $request->utoken);
            if (count($course_dummy['data']) > 0) {
                array_push($course_data, $course_dummy);
            }
        }

        $course_dummy                = new Dummy();
        $course_dummy['group_title'] = 'คอร์สทั้งหมด';
        $course_dummy['data']        = AppHelper::instance()->convertCourse($course, $request->utoken);
        if (count($course_dummy['data']) > 0) {
            if ($request->page > 1) {
                $course_data = [];
            }
            array_push($course_data, $course_dummy);
        }

        $response['course_data'] = $course_data;
        $response['all_page']    = $course_all;

        $course_id = [];
        foreach ($course_data as $value) {
            foreach ($value['data'] as $value_data) {
                array_push($course_id, $value_data->id);
            }
        }
        $data_tag = CourseTagLog::join('tag', 'tag.id', 'course_tag_log.tag_id')->select('tag.id', 'tag.title')->whereIn('course_tag_log.course_id', $course_id)->limit(10)->get();

        if ($request->is_tag == 1) {
            $response['tag_data'] = json_decode($request->tag);
        } else {
            $response['tag_data'] = $data_tag;
        }
        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $response['userPlaylist'] = UserPlaylist::select('id as key', 'id as value', 'title as text')->where('user_id', $user->id)->where('status', 1)->get();
            } else {
                $response['userPlaylist'] = [];
            }
        } else {
            $response['userPlaylist'] = [];
        }
        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function getSeminarFilter(Request $request)
    {
        $response = new Dummy();
        // $response['utoken'] = $request->utoken;
        $response['filter']  = json_decode($request->filter);
        $response['tag']     = json_decode($request->tag);
        $response['sort']    = $request->sort;
        $response['keyword'] = $request->keyword;

        if (! isset($request->page)) {
            $request->page = 1;
        }
        if (! isset($request->limit)) {
            $request->limit = 24;
        }
        if ($request->limit > 24) {
            $request->limit = 24;
        }
        $offset = (intval($request->page) - 1) * intval($request->limit);

        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            //$mybrowser = get_browser(null, true);
            $ip = getenv('HTTP_CLIENT_IP') ?:
            getenv('HTTP_X_FORWARDED_FOR') ?:
            getenv('HTTP_X_FORWARDED') ?:
            getenv('HTTP_FORWARDED_FOR') ?:
            getenv('HTTP_FORWARDED') ?:
            getenv('REMOTE_ADDR');
            $stat_log       = new StatLog();
            $stat_log->type = 1;
            //$stat_log->browser=$mybrowser['browser'];
            $stat_log->ip      = $ip;
            $stat_log->page    = 'seminar';
            $stat_log->keyword = $request->keyword;
            if (isset($request->utoken)) {
                $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $stat_log->user_id = $user->id;
                }
            }
            $stat_log->save();

            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
            $course->join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }
        if (isset($request->tag) && $request->is_tag == 1) {
            $val_tag = json_decode($request->tag);
            if (count($val_tag) > 0) {
                $course->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
                $course->where(function ($course) use ($val_tag) {
                    foreach ($val_tag as $key => $value) {
                        if ($key == 0) {
                            $course->where('course_tag_log.tag_id', $value->id);
                        } else {
                            $course->Orwhere('course_tag_log.tag_id', $value->id);
                        }
                    }
                });
            }
        }
        $check_content_type = 0;
        if (isset($request->filter)) {
            $val_filter = json_decode($request->filter);
            if (count($val_filter) > 0) {
                foreach ($val_filter as $key => $value) {
                    if ($value->category_key == 'department') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_department_log', 'course_department_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'disease') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_disease_log', 'course_disease_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'chula_center') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_chula_center_log', 'course_chula_center_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'speaker') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'gender') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_gender_log', 'course_gender_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'learner') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_learner_log', 'course_learner_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'organ_system') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_organ_log', 'course_organ_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'sub_categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_sub_categories_log', 'course_sub_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'is_certificate') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('cert_dynamic', 'cert_dynamic.course_id', 'course.id');
                            $course->where('cert_dynamic.status', 1);
                        }
                    }
                }
                $course->where(function ($course) use ($val_filter, $check_content_type) {
                    $keywhere = 0;
                    foreach ($val_filter as $key => $value) {
                        if ($value->category_key == 'department') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_department_log.department', $value_sub);
                                } else {
                                    $course->Orwhere('course_department_log.department', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'disease') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_disease_log.disease', $value_sub);
                                } else {
                                    $course->Orwhere('course_disease_log.disease', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'chula_center') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_chula_center_log.chula_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_chula_center_log.chula_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'speaker') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_speaker_log.speaker', $value_sub);
                                } else {
                                    $course->Orwhere('course_speaker_log.speaker', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'gender') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_gender_log.gender_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_gender_log.gender_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'level') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.level', $value_sub);
                                } else {
                                    $course->Orwhere('course.level', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'price') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->whereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->whereBetween('course.price', [5001, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->orWhereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->orWhereBetween('course.price', [5001, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'learner') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_learner_log.learner', $value_sub);
                                } else {
                                    $course->Orwhere('course_learner_log.learner', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'organ_system') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_organ_log.organ_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_organ_log.organ_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'promotion') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->where('course.is_promotion', 1);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhere('course.is_promotion', 1);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'year') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.year', $value_sub);
                                } else {
                                    $course->Orwhere('course.year', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'content_type') {
                            $check_content_type++;
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.trailer_media', $value_sub);
                                } else {
                                    $course->Orwhere('course.trailer_media', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'duration') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'sub_categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_sub_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_sub_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'cme_score') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.receive_point', '>', 0);
                                } else {
                                    $course->Orwhere('course.receive_point', '>', 0);
                                }
                                $keywhere++;
                            }
                        }
                    }
                });
            }
        }
        $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration');
        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }
        $course->groupby('course.id');
        if ($check_content_type == 0) {
            $course->where(function ($course) {
                $course->where('course.trailer_media', 4);
            });
        }
        $course_all = ceil((count($course->get())) / $request->limit);
        $course->offset($offset);
        $course->limit($request->limit);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');
        $course = $course->get();

        $course_data = [];

        if ((! isset($request->keyword) || $request->keyword == null || $request->keyword == '' || $request->keyword == 'null')
            && (! isset($request->tag) || (isset($request->tag) && count(json_decode($request->tag)) == 0) || $request->is_tag == 2)
            && (! isset($request->filter) || (isset($request->filter) && count(json_decode($request->filter)) == 0))
            && (! isset($request->sort) || $request->sort == null || $request->sort == '' || $request->sort == 'null')) {
            if (isset($request->utoken)) {
                $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $relate = Course::join('course_learner_log', 'course_learner_log.course_id', 'course.id')
                        ->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration')
                        ->where('course_learner_log.learner', $user->user_type)
                        ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
                    $relate->where(function ($relate) {
                        $relate->where('course.end_date', '>=', Carbon::now());
                        $relate->orWhere('course.end_date', null);
                    });

                    $relate->where(function ($relate) {
                        $relate->where('course.trailer_media', 4);
                    });
                    $relate->limit(8);
                    $relate->orderby('course.started_date', 'desc');
                    $relate                      = $relate->get();
                    $course_dummy                = new Dummy();
                    $course_dummy['group_title'] = 'เหมาะกับคุณ';
                    $course_dummy['data']        = AppHelper::instance()->convertCourse($relate, $request->utoken);
                    if (count($course_dummy['data']) > 0) {
                        array_push($course_data, $course_dummy);
                    }
                }
            }
            $popular = Course::select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration')
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
            $popular->where(function ($popular) {
                $popular->where('course.end_date', '>=', Carbon::now());
                $popular->orWhere('course.end_date', null);
            });

            $popular->where(function ($popular) {
                $popular->where('course.trailer_media', 4);
            });
            $popular->orderby('course.rate', 'desc');
            $popular->limit(8);
            $popular->orderby('course.started_date', 'desc');
            $popular                     = $popular->get();
            $course_dummy                = new Dummy();
            $course_dummy['group_title'] = 'ยอดนิยม';
            $course_dummy['data']        = AppHelper::instance()->convertCourse($popular, $request->utoken);
            if (count($course_dummy['data']) > 0) {
                array_push($course_data, $course_dummy);
            }

            $new = Course::select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration')
                ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
            $new->where(function ($new) {
                $new->where('course.end_date', '>=', Carbon::now());
                $new->orWhere('course.end_date', null);
            });

            $new->where(function ($new) {
                $new->where('course.trailer_media', 4);
            });
            $new->orderby('course.started_date', 'desc');
            $new->limit(8);
            $new                         = $new->get();
            $course_dummy                = new Dummy();
            $course_dummy['group_title'] = 'มาใหม่';
            $course_dummy['data']        = AppHelper::instance()->convertCourse($new, $request->utoken);
            if (count($course_dummy['data']) > 0) {
                array_push($course_data, $course_dummy);
            }
        }

        $course_dummy                = new Dummy();
        $course_dummy['group_title'] = 'คอร์สทั้งหมด';
        $course_dummy['data']        = AppHelper::instance()->convertCourse($course, $request->utoken);
        if (count($course_dummy['data']) > 0) {
            if ($request->page > 1) {
                $course_data = [];
            }
            array_push($course_data, $course_dummy);
        }

        $response['course_data'] = $course_data;
        $response['all_page']    = $course_all;

        $course_id = [];
        foreach ($course_data as $value) {
            foreach ($value['data'] as $value_data) {
                array_push($course_id, $value_data->id);
            }
        }
        $data_tag = CourseTagLog::join('tag', 'tag.id', 'course_tag_log.tag_id')->select('tag.id', 'tag.title')->whereIn('course_tag_log.course_id', $course_id)->limit(10)->get();

        if ($request->is_tag == 1) {
            $response['tag_data'] = json_decode($request->tag);
        } else {
            $response['tag_data'] = $data_tag;
        }
        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $response['userPlaylist'] = UserPlaylist::select('id as key', 'id as value', 'title as text')->where('user_id', $user->id)->where('status', 1)->get();
            } else {
                $response['userPlaylist'] = [];
            }
        } else {
            $response['userPlaylist'] = [];
        }
        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function getKnowledgeFilter(Request $request)
    {
        $response = new Dummy();
        // $response['utoken'] = $request->utoken;
        $response['filter']  = json_decode($request->filter);
        $response['tag']     = json_decode($request->tag);
        $response['sort']    = $request->sort;
        $response['keyword'] = $request->keyword;

        $course_data = [];

        //คอร์สออนไลน์
        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            //$mybrowser = get_browser(null, true);
            $ip = getenv('HTTP_CLIENT_IP') ?:
            getenv('HTTP_X_FORWARDED_FOR') ?:
            getenv('HTTP_X_FORWARDED') ?:
            getenv('HTTP_FORWARDED_FOR') ?:
            getenv('HTTP_FORWARDED') ?:
            getenv('REMOTE_ADDR');
            $stat_log       = new StatLog();
            $stat_log->type = 1;
            //$stat_log->browser=$mybrowser['browser'];
            $stat_log->ip      = $ip;
            $stat_log->page    = 'knowledge';
            $stat_log->keyword = $request->keyword;
            if (isset($request->utoken)) {
                $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
                if ($user) {
                    $stat_log->user_id = $user->id;
                }
            }
            $stat_log->save();

            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
            $course->join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }
        if (isset($request->tag) && $request->is_tag == 1) {
            $val_tag = json_decode($request->tag);
            if (count($val_tag) > 0) {
                $course->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
                $course->where(function ($course) use ($val_tag) {
                    foreach ($val_tag as $key => $value) {
                        if ($key == 0) {
                            $course->where('course_tag_log.tag_id', $value->id);
                        } else {
                            $course->Orwhere('course_tag_log.tag_id', $value->id);
                        }
                    }
                });
            }
        }
        $check_content_type = 0;
        if (isset($request->filter)) {
            $val_filter = json_decode($request->filter);
            if (count($val_filter) > 0) {
                foreach ($val_filter as $key => $value) {
                    if ($value->category_key == 'department') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_department_log', 'course_department_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'disease') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_disease_log', 'course_disease_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'chula_center') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_chula_center_log', 'course_chula_center_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'speaker') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'gender') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_gender_log', 'course_gender_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'learner') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_learner_log', 'course_learner_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'organ_system') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_organ_log', 'course_organ_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'sub_categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_sub_categories_log', 'course_sub_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'is_certificate') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('cert_dynamic', 'cert_dynamic.course_id', 'course.id');
                            $course->where('cert_dynamic.status', 1);
                        }
                    }
                }
                $course->where(function ($course) use ($val_filter, $check_content_type) {
                    $keywhere = 0;
                    foreach ($val_filter as $key => $value) {
                        if ($value->category_key == 'department') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_department_log.department', $value_sub);
                                } else {
                                    $course->Orwhere('course_department_log.department', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'disease') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_disease_log.disease', $value_sub);
                                } else {
                                    $course->Orwhere('course_disease_log.disease', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'chula_center') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_chula_center_log.chula_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_chula_center_log.chula_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'speaker') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_speaker_log.speaker', $value_sub);
                                } else {
                                    $course->Orwhere('course_speaker_log.speaker', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'gender') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_gender_log.gender_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_gender_log.gender_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'level') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.level', $value_sub);
                                } else {
                                    $course->Orwhere('course.level', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'price') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->whereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->whereBetween('course.price', [5001, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->orWhereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->orWhereBetween('course.price', [5001, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'learner') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_learner_log.learner', $value_sub);
                                } else {
                                    $course->Orwhere('course_learner_log.learner', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'organ_system') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_organ_log.organ_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_organ_log.organ_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'promotion') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->where('course.is_promotion', 1);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhere('course.is_promotion', 1);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'year') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.year', $value_sub);
                                } else {
                                    $course->Orwhere('course.year', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'content_type') {
                            $check_content_type++;
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.trailer_media', $value_sub);
                                } else {
                                    $course->Orwhere('course.trailer_media', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'duration') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'sub_categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_sub_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_sub_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'cme_score') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.receive_point', '>', 0);
                                } else {
                                    $course->Orwhere('course.receive_point', '>', 0);
                                }
                                $keywhere++;
                            }
                        }
                    }
                });
            }
        }
        $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration');
        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }
        $course->groupby('course.id');
        if (! isset($request->page_1)) {
            $request->page_1 = 1;
        }
        if (! isset($request->limit_1)) {
            $request->limit_1 = 24;
        }
        if ($request->limit_1 > 24) {
            $request->limit_1 = 24;
        }
        $offset_1 = (intval($request->page_1) - 1) * intval($request->limit_1);
        if ($check_content_type == 0) {
            $course->where(function ($course) {
                $course->where('course.trailer_media', 1);
                $course->Orwhere('course.trailer_media', 3);
            });
        }
        $course_all = ceil((count($course->get())) / $request->limit_1);
        $course->offset($offset_1);
        $course->limit($request->limit_1);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');
        $course                      = $course->get();
        $response['all_page_1']      = $course_all;
        $course_dummy                = new Dummy();
        $course_dummy['group_title'] = 'คอร์สออนไลน์';
        $course_dummy['data']        = AppHelper::instance()->convertCourse($course, $request->utoken);
        if (count($course) > 0) {
            array_push($course_data, $course_dummy);
        }
        //คอร์สออนไลน์

        //คอร์สสอนสด
        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
            $course->join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }
        if (isset($request->tag) && $request->is_tag == 1) {
            $val_tag = json_decode($request->tag);
            if (count($val_tag) > 0) {
                $course->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
                $course->where(function ($course) use ($val_tag) {
                    foreach ($val_tag as $key => $value) {
                        if ($key == 0) {
                            $course->where('course_tag_log.tag_id', $value->id);
                        } else {
                            $course->Orwhere('course_tag_log.tag_id', $value->id);
                        }
                    }
                });
            }
        }
        $check_content_type = 0;
        if (isset($request->filter)) {
            $val_filter = json_decode($request->filter);
            if (count($val_filter) > 0) {
                foreach ($val_filter as $key => $value) {
                    if ($value->category_key == 'department') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_department_log', 'course_department_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'disease') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_disease_log', 'course_disease_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'chula_center') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_chula_center_log', 'course_chula_center_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'speaker') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'gender') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_gender_log', 'course_gender_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'learner') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_learner_log', 'course_learner_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'organ_system') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_organ_log', 'course_organ_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'sub_categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_sub_categories_log', 'course_sub_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'is_certificate') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('cert_dynamic', 'cert_dynamic.course_id', 'course.id');
                            $course->where('cert_dynamic.status', 1);
                        }
                    }
                }
                $course->where(function ($course) use ($val_filter, $check_content_type) {
                    $keywhere = 0;
                    foreach ($val_filter as $key => $value) {
                        if ($value->category_key == 'department') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_department_log.department', $value_sub);
                                } else {
                                    $course->Orwhere('course_department_log.department', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'disease') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_disease_log.disease', $value_sub);
                                } else {
                                    $course->Orwhere('course_disease_log.disease', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'chula_center') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_chula_center_log.chula_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_chula_center_log.chula_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'speaker') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_speaker_log.speaker', $value_sub);
                                } else {
                                    $course->Orwhere('course_speaker_log.speaker', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'gender') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_gender_log.gender_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_gender_log.gender_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'level') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.level', $value_sub);
                                } else {
                                    $course->Orwhere('course.level', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'price') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->whereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->whereBetween('course.price', [5001, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->orWhereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->orWhereBetween('course.price', [5001, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'learner') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_learner_log.learner', $value_sub);
                                } else {
                                    $course->Orwhere('course_learner_log.learner', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'organ_system') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_organ_log.organ_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_organ_log.organ_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'promotion') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->where('course.is_promotion', 1);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhere('course.is_promotion', 1);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'year') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.year', $value_sub);
                                } else {
                                    $course->Orwhere('course.year', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'content_type') {
                            $check_content_type++;
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.trailer_media', $value_sub);
                                } else {
                                    $course->Orwhere('course.trailer_media', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'duration') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'sub_categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_sub_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_sub_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'cme_score') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.receive_point', '>', 0);
                                } else {
                                    $course->Orwhere('course.receive_point', '>', 0);
                                }
                                $keywhere++;
                            }
                        }
                    }
                });
            }
        }
        $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration');
        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }
        $course->groupby('course.id');
        if (! isset($request->page_2)) {
            $request->page_2 = 1;
        }
        if (! isset($request->limit_2)) {
            $request->limit_2 = 24;
        }
        if ($request->limit_2 > 24) {
            $request->limit_2 = 24;
        }
        $offset_2 = (intval($request->page_2) - 1) * intval($request->limit_2);
        if ($check_content_type == 0) {
            $course->where(function ($course) {
                $course->where('course.trailer_media', 2);
            });
        }
        $course_all = ceil((count($course->get())) / $request->limit_2);
        $course->offset($offset_2);
        $course->limit($request->limit_2);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');
        $course                      = $course->get();
        $response['all_page_2']      = $course_all;
        $course_dummy                = new Dummy();
        $course_dummy['group_title'] = 'คอร์สสอนสด';
        $course_dummy['data']        = AppHelper::instance()->convertCourse($course, $request->utoken);
        if (count($course) > 0) {
            array_push($course_data, $course_dummy);
        }
        //คอร์สสอนสด

        //คอร์สประชุมวิชาการ
        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
            $course->join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }
        if (isset($request->tag) && $request->is_tag == 1) {
            $val_tag = json_decode($request->tag);
            if (count($val_tag) > 0) {
                $course->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
                $course->where(function ($course) use ($val_tag) {
                    foreach ($val_tag as $key => $value) {
                        if ($key == 0) {
                            $course->where('course_tag_log.tag_id', $value->id);
                        } else {
                            $course->Orwhere('course_tag_log.tag_id', $value->id);
                        }
                    }
                });
            }
        }
        $check_content_type = 0;
        if (isset($request->filter)) {
            $val_filter = json_decode($request->filter);
            if (count($val_filter) > 0) {
                foreach ($val_filter as $key => $value) {
                    if ($value->category_key == 'department') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_department_log', 'course_department_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'disease') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_disease_log', 'course_disease_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'chula_center') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_chula_center_log', 'course_chula_center_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'speaker') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'gender') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_gender_log', 'course_gender_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'learner') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_learner_log', 'course_learner_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'organ_system') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_organ_log', 'course_organ_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'sub_categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_sub_categories_log', 'course_sub_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'is_certificate') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('cert_dynamic', 'cert_dynamic.course_id', 'course.id');
                            $course->where('cert_dynamic.status', 1);
                        }
                    }
                }
                $course->where(function ($course) use ($val_filter, $check_content_type) {
                    $keywhere = 0;
                    foreach ($val_filter as $key => $value) {
                        if ($value->category_key == 'department') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_department_log.department', $value_sub);
                                } else {
                                    $course->Orwhere('course_department_log.department', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'disease') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_disease_log.disease', $value_sub);
                                } else {
                                    $course->Orwhere('course_disease_log.disease', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'chula_center') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_chula_center_log.chula_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_chula_center_log.chula_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'speaker') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_speaker_log.speaker', $value_sub);
                                } else {
                                    $course->Orwhere('course_speaker_log.speaker', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'gender') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_gender_log.gender_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_gender_log.gender_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'level') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.level', $value_sub);
                                } else {
                                    $course->Orwhere('course.level', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'price') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->whereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->whereBetween('course.price', [5001, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->orWhereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->orWhereBetween('course.price', [5001, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'learner') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_learner_log.learner', $value_sub);
                                } else {
                                    $course->Orwhere('course_learner_log.learner', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'organ_system') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_organ_log.organ_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_organ_log.organ_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'promotion') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->where('course.is_promotion', 1);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhere('course.is_promotion', 1);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'year') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.year', $value_sub);
                                } else {
                                    $course->Orwhere('course.year', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'content_type') {
                            $check_content_type++;
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.trailer_media', $value_sub);
                                } else {
                                    $course->Orwhere('course.trailer_media', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'duration') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'sub_categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_sub_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_sub_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'cme_score') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.receive_point', '>', 0);
                                } else {
                                    $course->Orwhere('course.receive_point', '>', 0);
                                }
                                $keywhere++;
                            }
                        }
                    }
                });
            }
        }
        $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration');
        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }
        $course->groupby('course.id');
        if (! isset($request->page_3)) {
            $request->page_3 = 1;
        }
        if (! isset($request->limit_3)) {
            $request->limit_3 = 24;
        }
        if ($request->limit_3 > 24) {
            $request->limit_3 = 24;
        }
        $offset_3 = (intval($request->page_3) - 1) * intval($request->limit_3);
        if ($check_content_type == 0) {
            $course->where(function ($course) {
                $course->where('course.trailer_media', 4);
            });
        }
        $course_all = ceil((count($course->get())) / $request->limit_3);
        $course->offset($offset_3);
        $course->limit($request->limit_3);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');
        $course                      = $course->get();
        $response['all_page_3']      = $course_all;
        $course_dummy                = new Dummy();
        $course_dummy['group_title'] = 'คอร์สประชุมวิชาการ';
        $course_dummy['data']        = AppHelper::instance()->convertCourse($course, $request->utoken);
        if (count($course) > 0) {
            array_push($course_data, $course_dummy);
        }
        //คอร์สประชุมวิชาการ

        //Infographic
        $course = Course::where('course.started_date', '<=', Carbon::now())->where('course.status', 1);
        $course->where(function ($course) {
            $course->where('course.end_date', '>=', Carbon::now());
            $course->orWhere('course.end_date', null);
        });

        if (isset($request->keyword) && $request->keyword != null && $request->keyword != '' && $request->keyword != 'null') {
            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
            $course->join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker');
            $val_keyword = $request->keyword;
            $course->where(function ($course) use ($val_keyword) {
                $course->where(DB::raw('lower(course.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.subtitle_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(course.key_search)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_th)'), 'LIKE', '%' . strtolower($val_keyword) . '%')
                    ->Orwhere(DB::raw('lower(categories_speaker.title_en)'), 'LIKE', '%' . strtolower($val_keyword) . '%');
            });
        }
        if (isset($request->tag) && $request->is_tag == 1) {
            $val_tag = json_decode($request->tag);
            if (count($val_tag) > 0) {
                $course->join('course_tag_log', 'course_tag_log.course_id', 'course.id');
                $course->where(function ($course) use ($val_tag) {
                    foreach ($val_tag as $key => $value) {
                        if ($key == 0) {
                            $course->where('course_tag_log.tag_id', $value->id);
                        } else {
                            $course->Orwhere('course_tag_log.tag_id', $value->id);
                        }
                    }
                });
            }
        }
        $check_content_type = 0;
        if (isset($request->filter)) {
            $val_filter = json_decode($request->filter);
            if (count($val_filter) > 0) {
                foreach ($val_filter as $key => $value) {
                    if ($value->category_key == 'department') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_department_log', 'course_department_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'disease') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_disease_log', 'course_disease_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'chula_center') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_chula_center_log', 'course_chula_center_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_categories_log', 'course_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'speaker') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'gender') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_gender_log', 'course_gender_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'learner') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_learner_log', 'course_learner_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'organ_system') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_organ_log', 'course_organ_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'sub_categories') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('course_sub_categories_log', 'course_sub_categories_log.course_id', 'course.id');
                        }
                    } else if ($value->category_key == 'is_certificate') {
                        $val_cate = $value->cate;
                        if (count($val_cate) > 0) {
                            $course->join('cert_dynamic', 'cert_dynamic.course_id', 'course.id');
                            $course->where('cert_dynamic.status', 1);
                        }
                    }
                }
                $course->where(function ($course) use ($val_filter, $check_content_type) {
                    $keywhere = 0;
                    foreach ($val_filter as $key => $value) {
                        if ($value->category_key == 'department') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_department_log.department', $value_sub);
                                } else {
                                    $course->Orwhere('course_department_log.department', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'disease') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_disease_log.disease', $value_sub);
                                } else {
                                    $course->Orwhere('course_disease_log.disease', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'chula_center') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_chula_center_log.chula_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_chula_center_log.chula_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'speaker') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_speaker_log.speaker', $value_sub);
                                } else {
                                    $course->Orwhere('course_speaker_log.speaker', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'gender') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_gender_log.gender_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_gender_log.gender_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'level') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.level', $value_sub);
                                } else {
                                    $course->Orwhere('course.level', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'price') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->whereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->whereBetween('course.price', [5001, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.price', [0, 0]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.price', [100, 500]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.price', [501, 1000]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.price', [1001, 3000]);
                                    } else if ($value_sub == 5) {
                                        $course->orWhereBetween('course.price', [3001, 5000]);
                                    } else if ($value_sub == 6) {
                                        $course->orWhereBetween('course.price', [5001, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'learner') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_learner_log.learner', $value_sub);
                                } else {
                                    $course->Orwhere('course_learner_log.learner', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'organ_system') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_organ_log.organ_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_organ_log.organ_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'promotion') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->where('course.is_promotion', 1);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhere('course.is_promotion', 1);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'year') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.year', $value_sub);
                                } else {
                                    $course->Orwhere('course.year', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'content_type') {
                            $check_content_type++;
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.trailer_media', $value_sub);
                                } else {
                                    $course->Orwhere('course.trailer_media', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'duration') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    if ($value_sub == 1) {
                                        $course->whereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->whereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->whereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->whereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                } else {
                                    if ($value_sub == 1) {
                                        $course->orWhereBetween('course.course_duration', [0, 1800]);
                                    } else if ($value_sub == 2) {
                                        $course->orWhereBetween('course.course_duration', [1801, 3600]);
                                    } else if ($value_sub == 3) {
                                        $course->orWhereBetween('course.course_duration', [3601, 7200]);
                                    } else if ($value_sub == 4) {
                                        $course->orWhereBetween('course.course_duration', [7201, 9999999]);
                                    }
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'sub_categories') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course_sub_categories_log.cate_id', $value_sub);
                                } else {
                                    $course->Orwhere('course_sub_categories_log.cate_id', $value_sub);
                                }
                                $keywhere++;
                            }
                        } else if ($value->category_key == 'cme_score') {
                            $val_cate = $value->cate;
                            foreach ($val_cate as $key_sub => $value_sub) {
                                if ($keywhere == 0) {
                                    $course->where('course.receive_point', '>', 0);
                                } else {
                                    $course->Orwhere('course.receive_point', '>', 0);
                                }
                                $keywhere++;
                            }
                        }
                    }
                });
            }
        }
        $course->select('course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set', 'course.started_date', 'course.rate', 'course.started_learning', 'course.is_hot', 'course.is_new', 'course.receive_point', 'course.title_th', 'course.topic_type', 'course.id', 'course.slug', 'course.image_th', 'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end', 'course.pro_period', 'course.price', 'course.subtitle_th', 'course.is_free', 'course.duration_time', 'course.created_at', 'course.course_duration');
        if (isset($request->sort) && $request->sort != null && $request->sort != '' && $request->sort != 'null') {
            $val_sort = $request->sort;
            if ($val_sort == 'date') {
                $course->orderby('course.started_date', 'desc');
            } else if ($val_sort == 'price') {
                $course->orderby('course.price', 'asc');
            } else if ($val_sort == 'a_to_z') {
                $course->orderby('course.title_th', 'asc');
            } else if ($val_sort == 'popular') {
                $course->orderby('course.rate', 'desc');
            }
        }
        $course->groupby('course.id');
        if (! isset($request->page_4)) {
            $request->page_4 = 1;
        }
        if (! isset($request->limit_4)) {
            $request->limit_4 = 24;
        }
        if ($request->limit_4 > 24) {
            $request->limit_4 = 24;
        }
        $offset_4 = (intval($request->page_4) - 1) * intval($request->limit_4);
        if ($check_content_type == 0) {
            $course->where(function ($course) {
                $course->where('course.trailer_media', 5);
            });
        }
        $course_all = ceil((count($course->get())) / $request->limit_4);
        $course->offset($offset_4);
        $course->limit($request->limit_4);
        $course->orderby('course.started_date', 'desc');
        $course->orderby('course.id', 'desc');
        $course                      = $course->get();
        $response['all_page_4']      = $course_all;
        $course_dummy                = new Dummy();
        $course_dummy['group_title'] = 'Infographic';
        $course_dummy['data']        = AppHelper::instance()->convertCourse($course, $request->utoken);
        if (count($course) > 0) {
            array_push($course_data, $course_dummy);
        }
        //Infographic

        $response['course_data'] = $course_data;
        $course_id               = [];
        foreach ($course_data as $value) {
            foreach ($value['data'] as $value_data) {
                array_push($course_id, $value_data->id);
            }
        }
        $data_tag = CourseTagLog::join('tag', 'tag.id', 'course_tag_log.tag_id')->select('tag.id', 'tag.title')->whereIn('course_tag_log.course_id', $course_id)->limit(10)->get();

        if ($request->is_tag == 1) {
            $response['tag_data'] = json_decode($request->tag);
        } else {
            $response['tag_data'] = $data_tag;
        }
        if (isset($request->utoken)) {
            $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
            if ($user) {
                $response['userPlaylist'] = UserPlaylist::select('id as key', 'id as value', 'title as text')->where('user_id', $user->id)->where('status', 1)->get();
            } else {
                $response['userPlaylist'] = [];
            }
        } else {
            $response['userPlaylist'] = [];
        }
        $response['status'] = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function getCourseFilterData(Request $request)
    {
        $response = new Dummy();

        $level = CateLevel::join('course', 'course.level', 'categories_level.id')->select('categories_level.id', 'categories_level.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('international', $request->international)->where('categories_level.status', 1);
        $level->where(function ($level) {
            $level->where('course.end_date', '>=', Carbon::now());
            $level->orWhere('course.end_date', null);
        });
        $level->where(function ($level) {
            $level->where('course.trailer_media', 1);
            $level->Orwhere('course.trailer_media', 3);
        });
        $level->groupby('categories_level.id')->orderby('categories_level.position', 'asc');
        $level = $level->get();

        $job_function = CateJobFunction::join('course_job_function_log', 'course_job_function_log.job_function_id', 'categories_job_function.id')
            ->join('course', 'course.id', 'course_job_function_log.course_id')
            ->select('categories_job_function.id', 'categories_job_function.name as title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('international', $request->international)->where('categories_job_function.status', 1);
        $job_function->where(function ($job_function) {
            $job_function->where('course.end_date', '>=', Carbon::now());
            $job_function->orWhere('course.end_date', null);
        });
        $job_function->where(function ($job_function) {
            $job_function->where('course.trailer_media', 1);
            $job_function->Orwhere('course.trailer_media', 3);
        });
        $job_function->groupby('categories_job_function.id')->orderby('categories_job_function.position', 'asc');
        $job_function = $job_function->get();

        $speaker = CateSpeaker::join('course_speaker_log', 'course_speaker_log.speaker', 'categories_speaker.id')
            ->join('course', 'course.id', 'course_speaker_log.course_id')
            ->select('categories_speaker.id', 'categories_speaker.name as title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('international', $request->international)->where('categories_speaker.status', 1);
        $speaker->where(function ($speaker) {
            $speaker->where('course.end_date', '>=', Carbon::now());
            $speaker->orWhere('course.end_date', null);
        });
        $speaker->where(function ($speaker) {
            $speaker->where('course.trailer_media', 1);
            $speaker->Orwhere('course.trailer_media', 3);
        });
        $speaker->groupby('categories_speaker.id')->orderBy(DB::raw('ISNULL(categories_speaker.name), categories_speaker.name'), 'ASC');
        $speaker = $speaker->get();

        $categories = Categories::join('course_categories_log', 'course_categories_log.cate_id', 'categories.id')
            ->join('course', 'course.id', 'course_categories_log.course_id')
            ->select('categories.id', 'categories.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('international', $request->international)->where('categories.status', 1);
        $categories->where(function ($categories) {
            $categories->where('course.end_date', '>=', Carbon::now());
            $categories->orWhere('course.end_date', null);
        });
        $categories->where(function ($categories) {
            $categories->where('course.trailer_media', 1);
            $categories->Orwhere('course.trailer_media', 3);
        });
        $categories->groupby('categories.id')->orderby('categories.position', 'asc');
        $categories = $categories->get();

        $cate_list = Categories::join('course_categories_log', 'course_categories_log.cate_id', 'categories.id')
            ->join('course', 'course.id', 'course_categories_log.course_id')
            ->select('categories.id as value', 'categories.title_th as label')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('international', $request->international)->where('categories.status', 1);
        $cate_list->where(function ($cate_list) {
            $cate_list->where('course.end_date', '>=', Carbon::now());
            $cate_list->orWhere('course.end_date', null);
        });
        $cate_list->where(function ($cate_list) {
            $cate_list->where('course.trailer_media', 1);
            $cate_list->Orwhere('course.trailer_media', 3);
        });
        $cate_list->groupby('categories.id')->orderby('categories.position', 'asc');
        $cate_list = $cate_list->get();

        $response['cate_list'] = $cate_list;

        $certificate = [
            [
                "id"       => "1",
                "title_th" => "Yes",
            ],
        ];

        $duration = [
            [
                "id"       => "1",
                "title_th" => "< 30 Minutes",
            ],
            [
                "id"       => "2",
                "title_th" => "< 1 Hours",
            ],
            [
                "id"       => "3",
                "title_th" => "< 2 Hours",
            ],
            [
                "id"       => "4",
                "title_th" => "More than 2 Hours",
            ],
        ];

        $data_filter = [];
        if (count($categories) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'categories';
            $arr_dummy['category_name'] = 'by Course Group';
            // $arr_dummy['category_name'] = 'by Curriculum';
            $arr_dummy['cate'] = $categories;
            array_push($data_filter, $arr_dummy);
        }
        if (count($job_function) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'job_function';
            $arr_dummy['category_name'] = 'by Job Function';
            $arr_dummy['cate']          = $job_function;
            array_push($data_filter, $arr_dummy);
        }
        if (count($level) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'level';
            $arr_dummy['category_name'] = 'by Level';
            $arr_dummy['cate']          = $level;
            array_push($data_filter, $arr_dummy);
        }
        if (count($speaker) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'speaker';
            $arr_dummy['category_name'] = 'by Speaker';
            $arr_dummy['cate']          = $speaker;
            array_push($data_filter, $arr_dummy);
        }
        if (count($certificate) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'is_certificate';
            $arr_dummy['category_name'] = 'by Certificates';
            $arr_dummy['cate']          = $certificate;
            array_push($data_filter, $arr_dummy);
        }
        if (count($duration) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'duration';
            $arr_dummy['category_name'] = 'by Duration';
            $arr_dummy['cate']          = $duration;
            array_push($data_filter, $arr_dummy);
        }

        $response['filter_data'] = $data_filter;
        $response['status']      = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function getSeminarFilterData(Request $request)
    {
        $response = new Dummy();

        $level = CateLevel::join('course', 'course.level', 'categories_level.id')->select('categories_level.id', 'categories_level.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_level.status', 1);
        $level->where(function ($level) {
            $level->where('course.end_date', '>=', Carbon::now());
            $level->orWhere('course.end_date', null);
        });

        $level->where(function ($level) {
            $level->where('course.trailer_media', 4);
        });
        $level->groupby('categories_level.id')->orderby('categories_level.position', 'asc');
        $level = $level->get();

        // $years = CateYears::join('course','course.year','categories_year.id')->select('categories_year.id','categories_year.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_year.status',1);
        // $years->where(function ($years) {
        //   $years->where('course.end_date','>=',Carbon::now());
        //   $years->orWhere('course.end_date',null);
        // });

        // $years->where(function ($years) {
        //   $years->where('course.trailer_media',4);
        // });
        // $years->groupby('categories_year.id')->orderby('categories_year.position','asc');
        // $years = $years->get();
        $years = new Dummy();

        $department = CateDepartment::join('course_department_log', 'course_department_log.department', 'categories_department.id')
            ->join('course', 'course.id', 'course_department_log.course_id')
            ->select('categories_department.id', 'categories_department.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_department.status', 1);
        $department->where(function ($department) {
            $department->where('course.end_date', '>=', Carbon::now());
            $department->orWhere('course.end_date', null);
        });

        $department->where(function ($department) {
            $department->where('course.trailer_media', 4);
        });
        $department->groupby('categories_department.id')->orderby('categories_department.position', 'asc');
        $department = $department->get();

        // $disease = CateDisease::join('course_disease_log','course_disease_log.disease','categories_disease.id')
        // ->join('course','course.id','course_disease_log.course_id')
        // ->select('categories_disease.id','categories_disease.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_disease.status',1);
        // $disease->where(function ($disease) {
        //   $disease->where('course.end_date','>=',Carbon::now());
        //   $disease->orWhere('course.end_date',null);
        // });
        // $disease->where(function ($disease) {
        //   $disease->where('course.trailer_media',4);
        // });
        // $disease->groupby('categories_disease.id')->orderby('categories_disease.position','asc');
        // $disease = $disease->get();
        $disease = new Dummy();

        $speaker = CateSpeaker::join('course_speaker_log', 'course_speaker_log.speaker', 'categories_speaker.id')
            ->join('course', 'course.id', 'course_speaker_log.course_id')
            ->select('categories_speaker.id', 'categories_speaker.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_speaker.status', 1);
        $speaker->where(function ($speaker) {
            $speaker->where('course.end_date', '>=', Carbon::now());
            $speaker->orWhere('course.end_date', null);
        });

        $speaker->where(function ($speaker) {
            $speaker->where('course.trailer_media', 4);
        });
        $speaker->groupby('categories_speaker.id')->orderBy(DB::raw('ISNULL(categories_speaker.name), categories_speaker.name'), 'ASC');
        $speaker = $speaker->get();

        $categories = Categories::join('course_categories_log', 'course_categories_log.cate_id', 'categories.id')
            ->join('course', 'course.id', 'course_categories_log.course_id')
            ->select('categories.id', 'categories.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories.status', 1);
        $categories->where(function ($categories) {
            $categories->where('course.end_date', '>=', Carbon::now());
            $categories->orWhere('course.end_date', null);
        });

        $categories->where(function ($categories) {
            $categories->where('course.trailer_media', 4);
        });
        $categories->groupby('categories.id')->orderby('categories.position', 'asc');
        $categories = $categories->get();

        $gender = CateGender::join('course_gender_log', 'course_gender_log.gender_id', 'categories_gender.id')
            ->join('course', 'course.id', 'course_gender_log.course_id')
            ->select('categories_gender.id', 'categories_gender.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_gender.status', 1);
        $gender->where(function ($gender) {
            $gender->where('course.end_date', '>=', Carbon::now());
            $gender->orWhere('course.end_date', null);
        });

        $gender->where(function ($gender) {
            $gender->where('course.trailer_media', 4);
        });
        $gender->groupby('categories_gender.id')->orderby('categories_gender.position', 'asc');
        $gender = $gender->get();

        $learner = CateLearner::join('course_learner_log', 'course_learner_log.learner', 'categories_learner.id')
            ->join('course', 'course.id', 'course_learner_log.course_id')
            ->select('categories_learner.id', 'categories_learner.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_learner.status', 1);
        $learner->where(function ($learner) {
            $learner->where('course.end_date', '>=', Carbon::now());
            $learner->orWhere('course.end_date', null);
        });

        $learner->where(function ($learner) {
            $learner->where('course.trailer_media', 4);
        });
        $learner->groupby('categories_learner.id')->orderby('categories_learner.position', 'asc');
        $learner = $learner->get();

        // $organs = CateOrgans::join('course_organ_log','course_organ_log.organ_id','categories_organ_system.id')
        // ->join('course','course.id','course_organ_log.course_id')
        // ->select('categories_organ_system.id','categories_organ_system.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_organ_system.status',1);
        // $organs->where(function ($organs) {
        //   $organs->where('course.end_date','>=',Carbon::now());
        //   $organs->orWhere('course.end_date',null);
        // });

        // $organs->where(function ($organs) {
        //   $organs->where('course.trailer_media',4);
        // });
        // $organs->groupby('categories_organ_system.id')->orderby('categories_organ_system.position','asc');
        // $organs = $organs->get();
        $organs = new Dummy();

        $sub_categories = SubCategories::join('course_sub_categories_log', 'course_sub_categories_log.cate_id', 'sub_categories.id')
            ->join('course', 'course.id', 'course_sub_categories_log.course_id')
            ->select('sub_categories.id', 'sub_categories.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('sub_categories.status', 1);
        $sub_categories->where(function ($sub_categories) {
            $sub_categories->where('course.end_date', '>=', Carbon::now());
            $sub_categories->orWhere('course.end_date', null);
        });

        $sub_categories->where(function ($sub_categories) {
            $sub_categories->where('course.trailer_media', 4);
        });
        $sub_categories->groupby('sub_categories.id')->orderby('sub_categories.position', 'asc');
        $sub_categories = $sub_categories->get();
        $cme            = [
            [
                "id"       => "1",
                "title_th" => "มี",
            ],
        ];
        $certificate = [
            [
                "id"       => "1",
                "title_th" => "มี",
            ],
        ];
        $price = [
            [
                "id"       => "1",
                "title_th" => "ฟรี",
            ],
            [
                "id"       => "2",
                "title_th" => "100-500",
            ],
            [
                "id"       => "3",
                "title_th" => "501-1,000",
            ],
            [
                "id"       => "4",
                "title_th" => "1,001-3,000",
            ],
            [
                "id"       => "5",
                "title_th" => "3,001-5,000",
            ],
            [
                "id"       => "6",
                "title_th" => "5,000 ขึ้นไป",
            ],
        ];
        $promotion = [
            [
                "id"       => "1",
                "title_th" => "ลดราคา",
            ],
        ];
        $duration = [
            [
                "id"       => "1",
                "title_th" => "< 30 นาที",
            ],
            [
                "id"       => "2",
                "title_th" => "< 1 ชั่วโมง",
            ],
            [
                "id"       => "3",
                "title_th" => "< 2 ชั่วโมง",
            ],
            [
                "id"       => "4",
                "title_th" => "2 ชั่วโมงขึ้นไป",
            ],
        ];

        $data_filter = [];
        if (count($learner) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'learner';
            $arr_dummy['category_name'] = 'กลุ่มผู้เรียน';
            $arr_dummy['cate']          = $learner;
            array_push($data_filter, $arr_dummy);
        }
        if (count($level) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'level';
            $arr_dummy['category_name'] = 'ระดับเนื้อหา';
            $arr_dummy['cate']          = $level;
            array_push($data_filter, $arr_dummy);
        }
        if (count($categories) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'categories';
            $arr_dummy['category_name'] = 'ประเภทเนื้อหา';
            $arr_dummy['cate']          = $categories;
            array_push($data_filter, $arr_dummy);
        }
        if (count($sub_categories) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'sub_categories';
            $arr_dummy['category_name'] = 'ประเภทเนื้อหา เชิงลึก';
            $arr_dummy['cate']          = $sub_categories;
            array_push($data_filter, $arr_dummy);
        }
        // if(count($disease)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'disease';
        //   $arr_dummy['category_name'] = 'กลุ่มโรค ชื่อโรค อาการ';
        //   $arr_dummy['cate'] = $disease;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($organs)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'organ_system';
        //   $arr_dummy['category_name'] = 'อวัยวะ';
        //   $arr_dummy['cate'] = $organs;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($gender)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'gender';
        //   $arr_dummy['category_name'] = 'เพศ';
        //   $arr_dummy['cate'] = $gender;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($department)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'department';
        //   $arr_dummy['category_name'] = 'ภาควิชา';
        //   $arr_dummy['cate'] = $department;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($speaker)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'speaker';
        //   $arr_dummy['category_name'] = 'ผู้สอน';
        //   $arr_dummy['cate'] = $speaker;
        //   array_push($data_filter,$arr_dummy);
        // }
        if (count($cme) > 0) {
            // $arr_dummy = new Dummy();
            // $arr_dummy['category_key'] = 'cme_score';
            // $arr_dummy['category_name'] = 'มีคะแนน CME';
            // $arr_dummy['cate'] = $cme;
            // array_push($data_filter,$arr_dummy);
        }
        if (count($certificate) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'is_certificate';
            $arr_dummy['category_name'] = 'มี Certificate';
            $arr_dummy['cate']          = $certificate;
            array_push($data_filter, $arr_dummy);
        }
        if (count($price) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'price';
            $arr_dummy['category_name'] = 'ราคา';
            $arr_dummy['cate']          = $price;
            array_push($data_filter, $arr_dummy);
        }
        if (count($promotion) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'promotion';
            $arr_dummy['category_name'] = 'โปรโมชั่น';
            $arr_dummy['cate']          = $promotion;
            array_push($data_filter, $arr_dummy);
        }
        // if(count($years)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'year';
        //   $arr_dummy['category_name'] = 'ปี';
        //   $arr_dummy['cate'] = $years;
        //   array_push($data_filter,$arr_dummy);
        // }
        if (count($duration) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'duration';
            $arr_dummy['category_name'] = 'ความยาว';
            $arr_dummy['cate']          = $duration;
            array_push($data_filter, $arr_dummy);
        }

        $response['filter_data'] = $data_filter;
        $response['status']      = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function getZoomFilterData(Request $request)
    {
        $response = new Dummy();

        $level = CateLevel::join('course', 'course.level', 'categories_level.id')->select('categories_level.id', 'categories_level.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_level.status', 1);
        $level->where(function ($level) {
            $level->where('course.end_date', '>=', Carbon::now());
            $level->orWhere('course.end_date', null);
        });

        $level->where(function ($level) {
            $level->where('course.trailer_media', 2);
        });
        $level->groupby('categories_level.id')->orderby('categories_level.position', 'asc');
        $level = $level->get();

        // $years = CateYears::join('course','course.year','categories_year.id')->select('categories_year.id','categories_year.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_year.status',1);
        // $years->where(function ($years) {
        //   $years->where('course.end_date','>=',Carbon::now());
        //   $years->orWhere('course.end_date',null);
        // });

        // $years->where(function ($years) {
        //   $years->where('course.trailer_media',2);
        // });
        // $years->groupby('categories_year.id')->orderby('categories_year.position','asc');
        // $years = $years->get();
        $years = new Dummy();

        $department = CateDepartment::join('course_department_log', 'course_department_log.department', 'categories_department.id')
            ->join('course', 'course.id', 'course_department_log.course_id')
            ->select('categories_department.id', 'categories_department.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_department.status', 1);
        $department->where(function ($department) {
            $department->where('course.end_date', '>=', Carbon::now());
            $department->orWhere('course.end_date', null);
        });

        $department->where(function ($department) {
            $department->where('course.trailer_media', 2);
        });
        $department->groupby('categories_department.id')->orderby('categories_department.position', 'asc');
        $department = $department->get();

        // $disease = CateDisease::join('course_disease_log','course_disease_log.disease','categories_disease.id')
        // ->join('course','course.id','course_disease_log.course_id')
        // ->select('categories_disease.id','categories_disease.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_disease.status',1);
        // $disease->where(function ($disease) {
        //   $disease->where('course.end_date','>=',Carbon::now());
        //   $disease->orWhere('course.end_date',null);
        // });

        // $disease->where(function ($disease) {
        //   $disease->where('course.trailer_media',2);
        // });
        // $disease->groupby('categories_disease.id')->orderby('categories_disease.position','asc');
        // $disease = $disease->get();
        $disease = new Dummy();

        $speaker = CateSpeaker::join('course_speaker_log', 'course_speaker_log.speaker', 'categories_speaker.id')
            ->join('course', 'course.id', 'course_speaker_log.course_id')
            ->select('categories_speaker.id', 'categories_speaker.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_speaker.status', 1);
        $speaker->where(function ($speaker) {
            $speaker->where('course.end_date', '>=', Carbon::now());
            $speaker->orWhere('course.end_date', null);
        });

        $speaker->where(function ($speaker) {
            $speaker->where('course.trailer_media', 2);
        });
        $speaker->groupby('categories_speaker.id')->orderBy(DB::raw('ISNULL(categories_speaker.name), categories_speaker.name'), 'ASC');
        $speaker = $speaker->get();

        $categories = Categories::join('course_categories_log', 'course_categories_log.cate_id', 'categories.id')
            ->join('course', 'course.id', 'course_categories_log.course_id')
            ->select('categories.id', 'categories.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories.status', 1);
        $categories->where(function ($categories) {
            $categories->where('course.end_date', '>=', Carbon::now());
            $categories->orWhere('course.end_date', null);
        });

        $categories->where(function ($categories) {
            $categories->where('course.trailer_media', 2);
        });
        $categories->groupby('categories.id')->orderby('categories.position', 'asc');
        $categories = $categories->get();

        $gender = CateGender::join('course_gender_log', 'course_gender_log.gender_id', 'categories_gender.id')
            ->join('course', 'course.id', 'course_gender_log.course_id')
            ->select('categories_gender.id', 'categories_gender.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_gender.status', 1);
        $gender->where(function ($gender) {
            $gender->where('course.end_date', '>=', Carbon::now());
            $gender->orWhere('course.end_date', null);
        });

        $gender->where(function ($gender) {
            $gender->where('course.trailer_media', 2);
        });
        $gender->groupby('categories_gender.id')->orderby('categories_gender.position', 'asc');
        $gender = $gender->get();

        $learner = CateLearner::join('course_learner_log', 'course_learner_log.learner', 'categories_learner.id')
            ->join('course', 'course.id', 'course_learner_log.course_id')
            ->select('categories_learner.id', 'categories_learner.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_learner.status', 1);
        $learner->where(function ($learner) {
            $learner->where('course.end_date', '>=', Carbon::now());
            $learner->orWhere('course.end_date', null);
        });

        $learner->where(function ($learner) {
            $learner->where('course.trailer_media', 2);
        });
        $learner->groupby('categories_learner.id')->orderby('categories_learner.position', 'asc');
        $learner = $learner->get();

        // $organs = CateOrgans::join('course_organ_log','course_organ_log.organ_id','categories_organ_system.id')
        // ->join('course','course.id','course_organ_log.course_id')
        // ->select('categories_organ_system.id','categories_organ_system.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_organ_system.status',1);
        // $organs->where(function ($organs) {
        //   $organs->where('course.end_date','>=',Carbon::now());
        //   $organs->orWhere('course.end_date',null);
        // });

        // $organs->where(function ($organs) {
        //   $organs->where('course.trailer_media',2);
        // });
        // $organs->groupby('categories_organ_system.id')->orderby('categories_organ_system.position','asc');
        // $organs = $organs->get();

        $sub_categories = SubCategories::join('course_sub_categories_log', 'course_sub_categories_log.cate_id', 'sub_categories.id')
            ->join('course', 'course.id', 'course_sub_categories_log.course_id')
            ->select('sub_categories.id', 'sub_categories.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('sub_categories.status', 1);
        $sub_categories->where(function ($sub_categories) {
            $sub_categories->where('course.end_date', '>=', Carbon::now());
            $sub_categories->orWhere('course.end_date', null);
        });

        $sub_categories->where(function ($sub_categories) {
            $sub_categories->where('course.trailer_media', 2);
        });
        $sub_categories->groupby('sub_categories.id')->orderby('sub_categories.position', 'asc');
        $sub_categories = $sub_categories->get();
        $cme            = [
            [
                "id"       => "1",
                "title_th" => "มี",
            ],
        ];
        $certificate = [
            [
                "id"       => "1",
                "title_th" => "มี",
            ],
        ];
        $price = [
            [
                "id"       => "1",
                "title_th" => "ฟรี",
            ],
            [
                "id"       => "2",
                "title_th" => "100-500",
            ],
            [
                "id"       => "3",
                "title_th" => "501-1,000",
            ],
            [
                "id"       => "4",
                "title_th" => "1,001-3,000",
            ],
            [
                "id"       => "5",
                "title_th" => "3,001-5,000",
            ],
            [
                "id"       => "6",
                "title_th" => "5,000 ขึ้นไป",
            ],
        ];
        $promotion = [
            [
                "id"       => "1",
                "title_th" => "ลดราคา",
            ],
        ];
        $duration = [
            [
                "id"       => "1",
                "title_th" => "< 30 นาที",
            ],
            [
                "id"       => "2",
                "title_th" => "< 1 ชั่วโมง",
            ],
            [
                "id"       => "3",
                "title_th" => "< 2 ชั่วโมง",
            ],
            [
                "id"       => "4",
                "title_th" => "2 ชั่วโมงขึ้นไป",
            ],
        ];

        $data_filter = [];
        if (count($learner) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'learner';
            $arr_dummy['category_name'] = 'กลุ่มผู้เรียน';
            $arr_dummy['cate']          = $learner;
            array_push($data_filter, $arr_dummy);
        }
        if (count($level) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'level';
            $arr_dummy['category_name'] = 'ระดับเนื้อหา';
            $arr_dummy['cate']          = $level;
            array_push($data_filter, $arr_dummy);
        }
        if (count($categories) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'categories';
            $arr_dummy['category_name'] = 'ประเภทเนื้อหา';
            $arr_dummy['cate']          = $categories;
            array_push($data_filter, $arr_dummy);
        }
        if (count($sub_categories) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'sub_categories';
            $arr_dummy['category_name'] = 'ประเภทเนื้อหา เชิงลึก';
            $arr_dummy['cate']          = $sub_categories;
            array_push($data_filter, $arr_dummy);
        }
        // if(count($disease)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'disease';
        //   $arr_dummy['category_name'] = 'กลุ่มโรค ชื่อโรค อาการ';
        //   $arr_dummy['cate'] = $disease;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($organs)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'organ_system';
        //   $arr_dummy['category_name'] = 'อวัยวะ';
        //   $arr_dummy['cate'] = $organs;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($gender)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'gender';
        //   $arr_dummy['category_name'] = 'เพศ';
        //   $arr_dummy['cate'] = $gender;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($department)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'department';
        //   $arr_dummy['category_name'] = 'ภาควิชา';
        //   $arr_dummy['cate'] = $department;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($speaker)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'speaker';
        //   $arr_dummy['category_name'] = 'ผู้สอน';
        //   $arr_dummy['cate'] = $speaker;
        //   array_push($data_filter,$arr_dummy);
        // }
        if (count($cme) > 0) {
            // $arr_dummy = new Dummy();
            // $arr_dummy['category_key'] = 'cme_score';
            // $arr_dummy['category_name'] = 'มีคะแนน CME';
            // $arr_dummy['cate'] = $cme;
            // array_push($data_filter,$arr_dummy);
        }
        if (count($certificate) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'is_certificate';
            $arr_dummy['category_name'] = 'มี Certificate';
            $arr_dummy['cate']          = $certificate;
            array_push($data_filter, $arr_dummy);
        }
        if (count($price) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'price';
            $arr_dummy['category_name'] = 'ราคา';
            $arr_dummy['cate']          = $price;
            array_push($data_filter, $arr_dummy);
        }
        if (count($promotion) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'promotion';
            $arr_dummy['category_name'] = 'โปรโมชั่น';
            $arr_dummy['cate']          = $promotion;
            array_push($data_filter, $arr_dummy);
        }
        // if(count($years)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'year';
        //   $arr_dummy['category_name'] = 'ปี';
        //   $arr_dummy['cate'] = $years;
        //   array_push($data_filter,$arr_dummy);
        // }
        if (count($duration) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'duration';
            $arr_dummy['category_name'] = 'ความยาว';
            $arr_dummy['cate']          = $duration;
            array_push($data_filter, $arr_dummy);
        }

        $response['filter_data'] = $data_filter;
        $response['status']      = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function getKnowledgeFilterData(Request $request)
    {
        $response = new Dummy();

        $level = CateLevel::join('course', 'course.level', 'categories_level.id')->select('categories_level.id', 'categories_level.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_level.status', 1);
        $level->where(function ($level) {
            $level->where('course.end_date', '>=', Carbon::now());
            $level->orWhere('course.end_date', null);
        });

        $level->groupby('categories_level.id')->orderby('categories_level.position', 'asc');
        $level = $level->get();

        // $years = CateYears::join('course','course.year','categories_year.id')->select('categories_year.id','categories_year.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_year.status',1);
        // $years->where(function ($years) {
        //   $years->where('course.end_date','>=',Carbon::now());
        //   $years->orWhere('course.end_date',null);
        // });

        // $years->groupby('categories_year.id')->orderby('categories_year.position','asc');
        // $years = $years->get();
        $years = new Dummy();

        $department = CateDepartment::join('course_department_log', 'course_department_log.department', 'categories_department.id')
            ->join('course', 'course.id', 'course_department_log.course_id')
            ->select('categories_department.id', 'categories_department.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_department.status', 1);
        $department->where(function ($department) {
            $department->where('course.end_date', '>=', Carbon::now());
            $department->orWhere('course.end_date', null);
        });

        $department->groupby('categories_department.id')->orderby('categories_department.position', 'asc');
        $department = $department->get();

        // $disease = CateDisease::join('course_disease_log','course_disease_log.disease','categories_disease.id')
        // ->join('course','course.id','course_disease_log.course_id')
        // ->select('categories_disease.id','categories_disease.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_disease.status',1);
        // $disease->where(function ($disease) {
        //   $disease->where('course.end_date','>=',Carbon::now());
        //   $disease->orWhere('course.end_date',null);
        // });

        // $disease->groupby('categories_disease.id')->orderby('categories_disease.position','asc');
        // $disease = $disease->get();
        $disease = new Dummy();

        $speaker = CateSpeaker::join('course_speaker_log', 'course_speaker_log.speaker', 'categories_speaker.id')
            ->join('course', 'course.id', 'course_speaker_log.course_id')
            ->select('categories_speaker.id', 'categories_speaker.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_speaker.status', 1);
        $speaker->where(function ($speaker) {
            $speaker->where('course.end_date', '>=', Carbon::now());
            $speaker->orWhere('course.end_date', null);
        });

        $speaker->groupby('categories_speaker.id')->orderBy(DB::raw('ISNULL(categories_speaker.name), categories_speaker.name'), 'ASC');
        $speaker = $speaker->get();

        $categories = Categories::join('course_categories_log', 'course_categories_log.cate_id', 'categories.id')
            ->join('course', 'course.id', 'course_categories_log.course_id')
            ->select('categories.id', 'categories.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories.status', 1);
        $categories->where(function ($categories) {
            $categories->where('course.end_date', '>=', Carbon::now());
            $categories->orWhere('course.end_date', null);
        });

        $categories->groupby('categories.id')->orderby('categories.position', 'asc');
        $categories = $categories->get();

        $gender = CateGender::join('course_gender_log', 'course_gender_log.gender_id', 'categories_gender.id')
            ->join('course', 'course.id', 'course_gender_log.course_id')
            ->select('categories_gender.id', 'categories_gender.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_gender.status', 1);
        $gender->where(function ($gender) {
            $gender->where('course.end_date', '>=', Carbon::now());
            $gender->orWhere('course.end_date', null);
        });

        $gender->groupby('categories_gender.id')->orderby('categories_gender.position', 'asc');
        $gender = $gender->get();

        $learner = CateLearner::join('course_learner_log', 'course_learner_log.learner', 'categories_learner.id')
            ->join('course', 'course.id', 'course_learner_log.course_id')
            ->select('categories_learner.id', 'categories_learner.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('categories_learner.status', 1);
        $learner->where(function ($learner) {
            $learner->where('course.end_date', '>=', Carbon::now());
            $learner->orWhere('course.end_date', null);
        });

        $learner->groupby('categories_learner.id')->orderby('categories_learner.position', 'asc');
        $learner = $learner->get();

        // $organs = CateOrgans::join('course_organ_log','course_organ_log.organ_id','categories_organ_system.id')
        // ->join('course','course.id','course_organ_log.course_id')
        // ->select('categories_organ_system.id','categories_organ_system.title_th')
        // ->where('course.started_date','<=',Carbon::now())->where('course.status', 1)->where('categories_organ_system.status',1);
        // $organs->where(function ($organs) {
        //   $organs->where('course.end_date','>=',Carbon::now());
        //   $organs->orWhere('course.end_date',null);
        // });

        // $organs->groupby('categories_organ_system.id')->orderby('categories_organ_system.position','asc');
        // $organs = $organs->get();
        $organs = new Dummy();

        $sub_categories = SubCategories::join('course_sub_categories_log', 'course_sub_categories_log.cate_id', 'sub_categories.id')
            ->join('course', 'course.id', 'course_sub_categories_log.course_id')
            ->select('sub_categories.id', 'sub_categories.title_th')
            ->where('course.started_date', '<=', Carbon::now())->where('course.status', 1)->where('sub_categories.status', 1);
        $sub_categories->where(function ($sub_categories) {
            $sub_categories->where('course.end_date', '>=', Carbon::now());
            $sub_categories->orWhere('course.end_date', null);
        });

        $sub_categories->groupby('sub_categories.id')->orderby('sub_categories.position', 'asc');
        $sub_categories = $sub_categories->get();
        $cme            = [
            [
                "id"       => "1",
                "title_th" => "มี",
            ],
        ];
        $certificate = [
            [
                "id"       => "1",
                "title_th" => "มี",
            ],
        ];
        $price = [
            [
                "id"       => "1",
                "title_th" => "ฟรี",
            ],
            [
                "id"       => "2",
                "title_th" => "100-500",
            ],
            [
                "id"       => "3",
                "title_th" => "501-1,000",
            ],
            [
                "id"       => "4",
                "title_th" => "1,001-3,000",
            ],
            [
                "id"       => "5",
                "title_th" => "3,001-5,000",
            ],
            [
                "id"       => "6",
                "title_th" => "5,000 ขึ้นไป",
            ],
        ];
        $promotion = [
            [
                "id"       => "1",
                "title_th" => "ลดราคา",
            ],
        ];
        $type = [
            [
                "id"       => "1",
                "title_th" => "VDO",
            ],
            [
                "id"       => "3",
                "title_th" => "Audio",
            ],
        ];
        $duration = [
            [
                "id"       => "1",
                "title_th" => "< 30 นาที",
            ],
            [
                "id"       => "2",
                "title_th" => "< 1 ชั่วโมง",
            ],
            [
                "id"       => "3",
                "title_th" => "< 2 ชั่วโมง",
            ],
            [
                "id"       => "4",
                "title_th" => "2 ชั่วโมงขึ้นไป",
            ],
        ];
        $data_filter = [];
        if (count($learner) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'learner';
            $arr_dummy['category_name'] = 'กลุ่มผู้เรียน';
            $arr_dummy['cate']          = $learner;
            array_push($data_filter, $arr_dummy);
        }
        if (count($level) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'level';
            $arr_dummy['category_name'] = 'ระดับเนื้อหา';
            $arr_dummy['cate']          = $level;
            array_push($data_filter, $arr_dummy);
        }
        if (count($categories) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'categories';
            $arr_dummy['category_name'] = 'ประเภทเนื้อหา';
            $arr_dummy['cate']          = $categories;
            array_push($data_filter, $arr_dummy);
        }
        if (count($sub_categories) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'sub_categories';
            $arr_dummy['category_name'] = 'ประเภทเนื้อหา เชิงลึก';
            $arr_dummy['cate']          = $sub_categories;
            array_push($data_filter, $arr_dummy);
        }
        // if(count($disease)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'disease';
        //   $arr_dummy['category_name'] = 'กลุ่มโรค ชื่อโรค อาการ';
        //   $arr_dummy['cate'] = $disease;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($organs)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'organ_system';
        //   $arr_dummy['category_name'] = 'อวัยวะ';
        //   $arr_dummy['cate'] = $organs;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($gender)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'gender';
        //   $arr_dummy['category_name'] = 'เพศ';
        //   $arr_dummy['cate'] = $gender;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($department)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'department';
        //   $arr_dummy['category_name'] = 'ภาควิชา';
        //   $arr_dummy['cate'] = $department;
        //   array_push($data_filter,$arr_dummy);
        // }
        // if(count($speaker)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'speaker';
        //   $arr_dummy['category_name'] = 'ผู้สอน';
        //   $arr_dummy['cate'] = $speaker;
        //   array_push($data_filter,$arr_dummy);
        // }
        if (count($cme) > 0) {
            // $arr_dummy = new Dummy();
            // $arr_dummy['category_key'] = 'cme_score';
            // $arr_dummy['category_name'] = 'มีคะแนน CME';
            // $arr_dummy['cate'] = $cme;
            // array_push($data_filter,$arr_dummy);
        }
        if (count($certificate) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'is_certificate';
            $arr_dummy['category_name'] = 'มี Certificate';
            $arr_dummy['cate']          = $certificate;
            array_push($data_filter, $arr_dummy);
        }
        if (count($price) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'price';
            $arr_dummy['category_name'] = 'ราคา';
            $arr_dummy['cate']          = $price;
            array_push($data_filter, $arr_dummy);
        }
        if (count($promotion) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'promotion';
            $arr_dummy['category_name'] = 'โปรโมชั่น';
            $arr_dummy['cate']          = $promotion;
            array_push($data_filter, $arr_dummy);
        }
        // if(count($years)>0){
        //   $arr_dummy = new Dummy();
        //   $arr_dummy['category_key'] = 'year';
        //   $arr_dummy['category_name'] = 'ปี';
        //   $arr_dummy['cate'] = $years;
        //   array_push($data_filter,$arr_dummy);
        // }
        if (count($type) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'content_type';
            $arr_dummy['category_name'] = 'ลักษณะเนื้อหา';
            $arr_dummy['cate']          = $type;
            array_push($data_filter, $arr_dummy);
        }
        if (count($duration) > 0) {
            $arr_dummy                  = new Dummy();
            $arr_dummy['category_key']  = 'duration';
            $arr_dummy['category_name'] = 'ความยาว';
            $arr_dummy['cate']          = $duration;
            array_push($data_filter, $arr_dummy);
        }

        $response['filter_data'] = $data_filter;
        $response['status']      = 'success';
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
