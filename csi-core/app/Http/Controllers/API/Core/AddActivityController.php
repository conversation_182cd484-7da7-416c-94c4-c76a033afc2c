<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use App\Services\BillingHistoryService;
use App\Models\Core\UserBillingHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Article;
use App\Models\Core\Course;
use App\Models\Core\CourseCheckerLog;
use App\Models\Core\CourseGroup;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\CourseRate;
use App\Models\Core\CourseView;
use App\Models\Core\District;
use App\Models\Core\DynamicKey;
use App\Models\Core\Email;
use App\Models\Core\EmailDynamic;
use App\Models\Core\LessonView;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\Province;
use App\Models\Core\SubDistrict;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\UserGiftLog;
use App\Models\Core\UserGraduateLog;
use App\Models\Core\UserHistory;
use App\Models\Core\Users;
use App\Models\Core\UsersCartLog;
use App\Models\Core\UsersCommentLikeLog;
use App\Models\Core\UsersCommentLog;
use App\Models\Core\UsersExamPoint;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\UsersLearningLog;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\Core\UsersPointLog;
use App\Models\Core\VolumnBy;
use App\Models\Core\VolumnByUser;
use App\Models\Core\CommunityLikeLog;
use App\Models\Core\CommunityRepliedLike;
use App\Models\Core\UserCommunityLog;
use App\Models\Core\UserCommunityRepliedLog;
use App\Models\Core\UserLearnedTime;
use App\Models\User;
use App\Service\PeakService;
use App\Service\LineService;
use App\Models\Core\Bank;
use App\Models\Core\CheckoutField;
use App\Models\Core\SubscriptionMain;
use App\Models\Core\CheckoutFieldSubmission;

class AddActivityController extends Controller
{
  protected $billingService;

  public function __construct(BillingHistoryService $billingService)
  {
    $this->billingService = $billingService;
  }

  public function cancelOrder(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->order_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $order = UsersOrderLog::where('user_id', $user->id)->where('id', $request->order_id)->first();
        if ($order) {
          $order->status = 3;
          $order->save();
          $response['status'] = 'success';
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function addFavourite(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->course_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $favorite = UsersFavoriteLog::where('user_id', $user->id)->where('course_id', $request->course_id)->first();
        if ($favorite) {
          $del_fav = UsersFavoriteLog::find($favorite->id);
          $del_fav->delete();
          $response['action'] = 'delete';
          $response['status'] = 'success';
        } else {
          $new_fav = new UsersFavoriteLog();
          $new_fav->user_id = $user->id;
          $new_fav->course_id = $request->course_id;
          $new_fav->favorite_type = 1;
          $new_fav->save();
          $response['action'] = 'add';
          $response['status'] = 'success';
        }
      } else {
        $response['action'] = '';
        $response['status'] = 'false';
      }
    } else {
      $response['action'] = '';
      $response['status'] = 'false';
    }



    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addCommunityReply(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->feed_id) && (isset($request->text) || isset($request->image))) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $feed = UserCommunityLog::where('id', $request->feed_id)->where('status', 1)->first();
        if ($feed) {
          $reply = new UserCommunityRepliedLog();
          $reply->user_id = $user->id;
          $reply->feed_id = $feed->id;
          $reply->comment = isset($request->text) ? AppHelper::instance()->cleanInput($request->text) : null;
          if (isset($request->image)) {
            $reply->image = AppHelper::instance()->saveImageBase64($request->image, 'upload/file');
          }
          $reply->save();
          $response['status'] = 'success';
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addCommunityPost(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->course_id) && (isset($request->text) || isset($request->image))) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $course = Course::where('id', $request->course_id)->first();
        if ($course) {
          $post = new UserCommunityLog();
          $post->user_id = $user->id;
          $post->course_id = $course->id;
          $post->feed = isset($request->text) ? AppHelper::instance()->cleanInput($request->text) : null;
          if (isset($request->image)) {
            $post->image = AppHelper::instance()->saveImageBase64($request->image, 'upload/file');
          }
          $post->status = 1;
          $post->save();
          $response['status'] = 'success';
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addCommunityReplyLike(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->feed_id) && isset($request->replied_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $like = CommunityRepliedLike::where('user_id', $user->id)->where('feed_id', $request->feed_id)->where('replied_id', $request->replied_id)->first();
        if ($like) {
          $like->delete();
          $response['status'] = 'success';
        } else {
          $like = new CommunityRepliedLike();
          $like->user_id = $user->id;
          $like->feed_id = $request->feed_id;
          $like->replied_id = $request->replied_id;
          $like->save();
          $response['status'] = 'success';
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addCommunityLike(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->feed_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $like = CommunityLikeLog::where('user_id', $user->id)->where('feed_id', $request->feed_id)->first();
        if ($like) {
          $like->delete();
          $response['status'] = 'success';
        } else {
          $like = new CommunityLikeLog();
          $like->user_id = $user->id;
          $like->feed_id = $request->feed_id;
          $like->save();
          $response['status'] = 'success';
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function addLike(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->comment_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $like = UsersCommentLikeLog::where('user_id', $user->id)->where('comment_id', $request->comment_id)->first();
        if ($like) {
          $del_like = UsersCommentLikeLog::find($like->id);
          $del_like->delete();
          $response['action'] = 'delete';
          $response['status'] = 'success';
        } else {
          $new_like = new UsersCommentLikeLog();
          $new_like->user_id = $user->id;
          $new_like->comment_id = $request->comment_id;
          $new_like->save();
          $response['action'] = 'add';
          $response['status'] = 'success';
        }
      } else {
        $response['action'] = '';
        $response['status'] = 'false';
      }
    } else {
      $response['action'] = '';
      $response['status'] = 'false';
    }



    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function addSubscription(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->code)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $check_sub = Subscription::where('code', $request->code)->where('type', 2)->where('status', 1)->first();
        if ($check_sub) {
          $count_sub = SubscriptionLog::where('user_id', $user->id)->where('subscription_id', $check_sub->id)->count();
          if ($check_sub->limit == null || $count_sub < $check_sub->limit) {
            $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $user->id)->where('subscription_id', $check_sub->id)->orderby('expired', 'desc')->first();
            $subscription_log = new SubscriptionLog();
            $subscription_log->user_id = $user->id;
            $subscription_log->subscription_id = $check_sub->id;
            if ($last_sub) {
              $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($check_sub->period);
            } else {
              $subscription_log->expired = Carbon::now()->addDays($check_sub->period);
            }
            $subscription_log->save();
            $response['status'] = 'success';
          } else {
            $response['status'] = 'limit';
          }
        }
      }
    }

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function addComment(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->course_id) && isset($request->comment) && isset($request->rate)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $course = Course::where('id', $request->course_id)->first();
        if ($course) {
          $graduate = UserGraduateLog::where('user_id', $user->id)->where('course_id', $course->id)->first();
          if ($graduate) {
            $check = UsersCommentLog::where('user_id', $user->id)->where('course_id', $course->id)->first();
            if (!$check) {
              $add_comment = new UsersCommentLog();
              $add_comment->user_id = $user->id;
              $add_comment->course_id = $request->course_id;
              $add_comment->comment = AppHelper::instance()->cleanInputBr($request->comment);
              $add_comment->rate = $request->rate;
              $add_comment->status = 1;
              $add_comment->save();

              $course_rate = UsersCommentLog::where('course_id', $course->id)->avg('rate');
              $course_rating = UsersCommentLog::where('course_id', $course->id)->count();
              $course->rating = $course_rating;
              $course->rate = $course_rate;
              $course->save();

              $response['rate'] = $course_rate;
              $response['rating'] = $course_rating;

              $response['status'] = 'success';
            }
          }
        }


        // $response['comment'] = $add_comment->comment;
        // $email_dynamic = EmailDynamic::where('id',9)->where('status',1)->first();
        // $course_data = Course::where('id',$add_comment->course_id)->first();
        // if($email_dynamic && $course_data){
        //   $course_email = CourseCheckerLog::join('admin','admin.id','course_checker_log.admin_id')->select('admin.username as email')->where('admin.status',1)
        //   ->where('course_checker_log.course_id',$course_data->id)->get();
        //   $email_arr = array();
        //   foreach($course_email as $val){
        //     array_push($email_arr,$val->email);
        //   }
        //   if(count($email_arr)>0){
        //     $replace_detail = $email_dynamic->details;
        //     $replace_detail = str_replace("{{name}}",$user->name,$replace_detail);
        //     $replace_detail = str_replace("{{lastname}}",$user->lastname,$replace_detail);
        //     $replace_detail = str_replace("{{title}}",$course_data->title_th,$replace_detail);
        //     $link_env = env('APP_URL').'/csisocietyadmin/course/'.$course_data->id.'/edit';
        //     $replace_detail = str_replace("{{link}}",'<a href="'.$link_env.'" target="_blank">'.$link_env.'</a>',$replace_detail);
        //     $obj = new Dummy();
        //     $obj['subject'] = $email_dynamic->subject;
        //     $obj['code'] = $replace_detail;
        //     $obj['email'] = $email_arr;
        //     $obj['cc'] = [];
        //     $obj['bcc'] = [];
        //     AppHelper::instance()->mailTemplate($obj);
        //   }
        // }
      }
    }

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function addCourseStamp(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';
    if (isset($request->utoken) && isset($request->course_id) && isset($request->lesson_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $learning = UsersLearningLog::where('user_id', $user->id)->where('course_id', $request->course_id)
          ->where('lesson_id', $request->lesson_id)->first();
        $learned_time = UserLearnedTime::where('user_id', $user->id)->where('course_id', $request->course_id)->first();
        if (!$learned_time) {
          $learned_time = new UserLearnedTime();
          $learned_time->user_id = $user->id;
          $learned_time->course_id = $request->course_id;
          $learned_time->seconds = 1;
          $learned_time->save();
        } else {
          $learned_time->seconds = $learned_time->seconds + 1;
          $learned_time->save();
        }
        if (!$learning) {
          $learning = new UsersLearningLog();
          $learning->user_id = $user->id;
          $learning->course_id = $request->course_id;
          $learning->lesson_id = $request->lesson_id;
          $response['action'] = 'add';
        } else {
          $response['action'] = 'update';
        }
        if (isset($request->watching_time)) {
          $learning->watching_time = AppHelper::instance()->ConvertSecTotime($request->watching_time);
        }
        $learning->save();
        $response['status'] = 'success';
        $response['course_percent'] = 0;
        $response['learned_limit'] = 0;

        $count_lesson = CourseLessonLog::where('status', 1)->where('course_id', $request->course_id)->count();
        $count_learn_log = UsersLearningLog::join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
          ->where('course_lesson.status', 1)
          ->where('course_lesson.course_id', $request->course_id)
          ->where('user_learning_log.user_id', $user->id)
          ->where('user_learning_log.course_id', $request->course_id)->get();
        $count_learn = 0;
        foreach ($count_learn_log as $val_l) {
          $count_learn += $val_l->watch_percent;
        }
        if ($count_learn != 0 && $count_lesson != 0) {
          $response['course_percent'] = number_format($count_learn / $count_lesson);
        }
        $learned_time = UserLearnedTime::where('user_id', $user->id)->where('course_id', $request->course_id)->first();
        if ($learned_time) {
          $response['learned_limit'] = $learned_time->seconds;
        }

        $ep = CourseLessonLog::where('id', $request->lesson_id)->first();
        if ($ep && $ep->price != 0) {
          $lesson_view = LessonView::where('user_id', $user->id)->where('course_id', $ep->course_id)->where('lesson_id', $ep->id)->whereDate('created_at', Carbon::today())->first();
          if (!$lesson_view) {
            $lesson_view = new LessonView();
            $lesson_view->user_id = $user->id;
            $lesson_view->course_id = $ep->course_id;
            $lesson_view->lesson_id = $ep->id;
            $lesson_view->save();
          }
        }
        if (isset($request->watching_time) && $ep && $ep->duration != null && $ep->duration != '' && $ep->duration != 'null') {
          $file_name = 'data_' . $user->id . '_' . $request->course_id . '_' . $request->lesson_id . '.json';
          if (Storage::disk('watching')->exists($file_name)) {
            $json = Storage::disk('watching')->get($file_name);
            $data = json_decode($json, true);
          } else {
            $data = array();
          }
          $block_id = ceil($request->watching_time / 9);
          if (!in_array($block_id, $data)) {
            array_push($data, $block_id);
          }
          Storage::disk('watching')->put($file_name, json_encode($data));

          $ep_all_block = ceil(AppHelper::instance()->hoursToSecods($ep->duration) / 9);
          $ep_watched_block = count($data);
          $ep_percent = $ep_watched_block / $ep_all_block * 100;
          if ($ep_percent > 100) {
            $ep_percent = 100;
          }

          $learning->log_file = asset('upload/watching/' . $file_name);
          if ($learning->watch_percent == null || ($learning->watch_percent != null && $learning->watch_percent < $ep_percent)) {
            $learning->watch_percent = $ep_percent;
          }
          if ($learning->watch_percent > 80) {
            $learning->status = 2;
          }
          $learning->save();

          if ($learning->status == 2) {
            $count_lesson = CourseLessonLog::where('is_teaser', '!=', 0)->where('status', 1)->where('course_id', $request->course_id)->count();
            $count_learn = UsersLearningLog::join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')->where('course_lesson.is_teaser', '!=', 0)
              ->where('course_lesson.status', 1)
              ->where('course_lesson.course_id', $request->course_id)
              ->where('user_learning_log.user_id', $user->id)
              ->where('user_learning_log.course_id', $request->course_id)->where('user_learning_log.status', 2)->count();
            $count_exam = count(UsersExamPoint::join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
              ->join('course_exam', 'course_exam.lesson_id', 'course_lesson.id')
              ->where('course_exam.course_id', $request->course_id)->where('course_exam.status', 1)->where('course_lesson.status', 1)
              ->where('user_exam_point.user_id', $user->id)->where('user_exam_point.course_id', $request->course_id)
              ->where('user_exam_point.status', 1)->groupby('user_exam_point.lesson_id')->get());
            $count_exam_all = count(CourseLessonLog::join('course_exam', 'course_exam.lesson_id', 'course_lesson.id')->where('course_exam.course_id', $request->course_id)->groupby('course_exam.lesson_id')->where('course_exam.status', 1)->where('course_lesson.status', 1)->get());
            if (($count_learn == $count_lesson) && ($count_exam == $count_exam_all) && $count_learn != 0 && $count_lesson != 0) {
              $graduate = UserGraduateLog::where('user_id', $user->id)->where('course_id', $request->course_id)->first();
              if (!$graduate) {
                $graduate = new UserGraduateLog();
                $graduate->user_id = $user->id;
                $graduate->course_id = $request->course_id;
                $graduate->save();
                AppHelper::instance()->addCertificateLog($graduate);
                $course_point = Course::where('id', $request->course_id)->first();
                $user_point = User::where('id', $user->id)->first();
                if ($course_point && $user_point) {
                  if ($course_point->receive_point > 0) {
                    $check_get_point = UsersPointLog::where('user_id', $user_point->id)->where('point_type', 1)->where('course_id', $course_point->id)->first();
                    if (!$check_get_point) {
                      $point_log = new UsersPointLog();
                      $point_log->user_id = $user_point->id;
                      $point_log->point_type = 1;
                      $point_log->course_id = $course_point->id;
                      $point_log->point = $course_point->receive_point;
                      $point_log->save();

                      $user_point->point = $user_point->point + $course_point->receive_point;
                      $user_point->save();
                      $email_dynamic = EmailDynamic::where('id', 6)->where('status', 1)->first();
                      if ($email_dynamic) {
                        $replace_detail = $email_dynamic->details;
                        $replace_detail = str_replace("{{name}}", $user_point->name, $replace_detail);
                        $replace_detail = str_replace("{{lastname}}", $user_point->lastname, $replace_detail);
                        $replace_detail = str_replace("{{title}}", $course_point->title_th, $replace_detail);
                        $replace_detail = str_replace("{{point}}", number_format($course_point->receive_point), $replace_detail);
                        $obj = new Dummy();
                        $obj['subject'] = $email_dynamic->subject;
                        $obj['code'] = $replace_detail;
                        $obj['email'] = $user_point->email;
                        $obj['cc'] = [];
                        $obj['bcc'] = [];
                        AppHelper::instance()->mailTemplate($obj);
                      }

                      $noti_log = new NotiAutoLog();
                      $noti_log->user_id = $user_point->id;
                      $noti_log->image = '/icontest.png';;
                      $noti_log->title = 'ยินดีด้วย! คุณได้รับคะแนน : ' . number_format($course_point->receive_point) . ' คะแนน';
                      $noti_log->description = 'จากการเรียน : ' . $course_point->title_th;
                      $noti_log->link = '';
                      $noti_log->save();
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function skipEp(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';
    if (isset($request->utoken) && isset($request->course_id) && isset($request->lesson_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $learning = UsersLearningLog::where('user_id', $user->id)->where('course_id', $request->course_id)
          ->where('lesson_id', $request->lesson_id)->where('status', 1)->first();
        if ($learning) {
          $learning->status = 2;
          $learning->save();
          $response['status'] = 'success';

          $count_lesson = CourseLessonLog::where('is_teaser', '!=', 0)->where('status', 1)->where('course_id', $request->course_id)->count();
          $count_learn = UsersLearningLog::join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')->where('course_lesson.is_teaser', '!=', 0)
            ->where('course_lesson.status', 1)
            ->where('course_lesson.course_id', $request->course_id)
            ->where('user_learning_log.user_id', $user->id)
            ->where('user_learning_log.course_id', $request->course_id)->where('user_learning_log.status', 2)->count();
          $count_exam = count(UsersExamPoint::join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
            ->join('course_exam', 'course_exam.lesson_id', 'course_lesson.id')
            ->where('course_exam.course_id', $request->course_id)->where('course_exam.status', 1)->where('course_lesson.status', 1)
            ->where('user_exam_point.user_id', $user->id)->where('user_exam_point.course_id', $request->course_id)
            ->where('user_exam_point.status', 1)->groupby('user_exam_point.lesson_id')->get());
          $count_exam_all = count(CourseLessonLog::join('course_exam', 'course_exam.lesson_id', 'course_lesson.id')->where('course_exam.course_id', $request->course_id)->groupby('course_exam.lesson_id')->where('course_exam.status', 1)->where('course_lesson.status', 1)->get());
          if (($count_learn == $count_lesson) && ($count_exam == $count_exam_all) && $count_learn != 0 && $count_lesson != 0) {
            $graduate = UserGraduateLog::where('user_id', $user->id)->where('course_id', $request->course_id)->first();
            if (!$graduate) {
              $graduate = new UserGraduateLog();
              $graduate->user_id = $user->id;
              $graduate->course_id = $request->course_id;
              $graduate->save();
              AppHelper::instance()->addCertificateLog($graduate);
              $course_point = Course::where('id', $request->course_id)->first();
              $user_point = User::where('id', $user->id)->first();
              if ($course_point && $user_point) {
                if ($course_point->receive_point > 0) {
                  $check_get_point = UsersPointLog::where('user_id', $user_point->id)->where('point_type', 1)->where('course_id', $course_point->id)->first();
                  if (!$check_get_point) {
                    $point_log = new UsersPointLog();
                    $point_log->user_id = $user_point->id;
                    $point_log->point_type = 1;
                    $point_log->course_id = $course_point->id;
                    $point_log->point = $course_point->receive_point;
                    $point_log->save();

                    $user_point->point = $user_point->point + $course_point->receive_point;
                    $user_point->save();
                    $email_dynamic = EmailDynamic::where('id', 6)->where('status', 1)->first();
                    if ($email_dynamic) {
                      $replace_detail = $email_dynamic->details;
                      $replace_detail = str_replace("{{name}}", $user_point->name, $replace_detail);
                      $replace_detail = str_replace("{{lastname}}", $user_point->lastname, $replace_detail);
                      $replace_detail = str_replace("{{title}}", $course_point->title_th, $replace_detail);
                      $replace_detail = str_replace("{{point}}", number_format($course_point->receive_point), $replace_detail);
                      $obj = new Dummy();
                      $obj['subject'] = $email_dynamic->subject;
                      $obj['code'] = $replace_detail;
                      $obj['email'] = $user_point->email;
                      $obj['cc'] = [];
                      $obj['bcc'] = [];
                      AppHelper::instance()->mailTemplate($obj);
                    }

                    $noti_log = new NotiAutoLog();
                    $noti_log->user_id = $user_point->id;
                    $noti_log->image = '/icontest.png';
                    $noti_log->title = 'ยินดีด้วย! คุณได้รับคะแนน : ' . number_format($course_point->receive_point) . ' คะแนน';
                    $noti_log->description = 'จากการเรียน : ' . $course_point->title_th;
                    $noti_log->link = '';
                    $noti_log->save();
                  }
                }
              }
            }
          }
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function getDynamicKey(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    $dynamic = DynamicKey::select('key', 'value')->where('status', 1)->get();

    $course = Course::where('course.status', 1)->where('course.started_date', '<=', Carbon::now());
    $course->where(function ($course) {
      $course->where('course.end_date', '>=', Carbon::now());
      $course->orWhere('course.end_date', null);
    });

    $course->where(function ($course) {
      $course->where('course.trailer_media', 1);
      $course->Orwhere('course.trailer_media', 3);
    });
    $course = $course->count();

    $seminar = Course::where('course.trailer_media', 4)->where('course.status', 1)->where('course.started_date', '<=', Carbon::now());
    $seminar->where(function ($seminar) {
      $seminar->where('course.end_date', '>=', Carbon::now());
      $seminar->orWhere('course.end_date', null);
    });

    // if($user_type!=''){
    //   $seminar->join('group_code','group_code.group_code','course.group_code');
    //   $seminar->where('group_code.'.$user_type,'!=',3);
    //   $seminar->where('group_code.status',1);
    // }

    $seminar = $seminar->count();

    // $seminar = StaticSeminar::where('status',1)->count();

    $zoom = Course::where('course.trailer_media', 2)->where('course.status', 1)->where('course.started_date', '<=', Carbon::now());
    $zoom->where(function ($zoom) {
      $zoom->where('course.end_date', '>=', Carbon::now());
      $zoom->orWhere('course.end_date', null);
    });

    // if($user_type!=''){
    //   $zoom->join('group_code','group_code.group_code','course.group_code');
    //   $zoom->where('group_code.'.$user_type,'!=',3);
    //   $zoom->where('group_code.status',1);
    // }

    $zoom = $zoom->count();

    $knowledge = Course::where('course.status', 1)->where('course.started_date', '<=', Carbon::now());
    $knowledge->where(function ($knowledge) {
      $knowledge->where('course.end_date', '>=', Carbon::now());
      $knowledge->orWhere('course.end_date', null);
    });

    // if($user_type!=''){
    //   $knowledge->join('group_code','group_code.group_code','course.group_code');
    //   $knowledge->where('group_code.'.$user_type,'!=',3);
    //   $knowledge->where('group_code.status',1);
    // }

    $knowledge = $knowledge->count();

    $article = Article::where('status', 1)->count();

    if ($course > 0) {
      $response['is_course'] = true;
    } else {
      $response['is_course'] = false;
    }
    if ($seminar > 0) {
      $response['is_seminar'] = true;
    } else {
      $response['is_seminar'] = false;
    }
    if ($zoom > 0) {
      $response['is_zoom'] = true;
    } else {
      $response['is_zoom'] = false;
    }
    if ($knowledge > 0) {
      $response['is_knowledge'] = true;
    } else {
      $response['is_knowledge'] = false;
    }
    if ($article > 0) {
      $response['is_article'] = true;
    } else {
      $response['is_article'] = false;
    }

    $response['dynamicKey'] = new Dummy();
    foreach ($dynamic as $val) {
      $response['dynamicKey'][$val->key] = $val->value;
    }

    $response['status'] = 'success';

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function addOrder(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (
      isset($request->utoken) && isset($request->cart_data)
      && isset($request->payment) && isset($request->receipt) && isset($request->receipt_type) && isset($request->address_type)
      && isset($request->tax)
    ) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      $bank = Bank::mainActive()->first();
      if ($user) {

        // บันทึก Billing History สำหรับ Auto-fill ครั้งถัดไป
        $this->billingService->saveBillingHistory($user->id, $request);

        if ($request->address_type == 1) {
          $new_order = new UsersOrderLog();
          if (isset($request->utm_source) && $request->utm_source != null && $request->utm_source != '' && $request->utm_source != 'null') {
            $new_order->utm_source = $request->utm_source;
          }
          $new_order->is_free = 2;
          $new_order->bank_id = $bank ? $bank->id : null;
          $new_order->receive_email = $request->email;
          $new_order->tel = $request->tel;
          $new_order->address_type = $request->address_type;
          $new_order->iden_no = $request->iden_no;
          $new_order->user_id = $user->id;
          $new_order->name = $request->name;
          $new_order->lastname = $request->lastname;
          $new_order->address = $request->address;
          $subdistrict = SubDistrict::where('id', $request->subdistrict)->first();
          if ($subdistrict) {
            $new_order->subdistrict = $subdistrict->name;
          }
          $district = District::where('id', $request->district)->first();
          if ($district) {
            $new_order->district = $district->name;
          }
          $province = Province::where('id', $request->province)->first();
          if ($province) {
            $new_order->province = $province->name;
          }
          $new_order->postcode = $request->postcode;
          $new_order->receipt_type = $request->receipt_type;
          $new_order->receipt = $request->receipt;
          $new_order->tax = $request->tax;
          $new_order->payment_type = $request->payment;
          $new_order->progress_report = $request->progress_report;

          // เพิ่มเงื่อนไขเช็คการชำระด้วยบัตรเครดิต
          if ($request->payment == 2) { // payment_type = 2 คือบัตรเครดิต
            $new_order->credit_payment_type = $request->credit_payment_type; // 'full' หรือ 'installment'
          }

          $order_total = 0;
          $this_year = Carbon::now('Asia/Bangkok')->format('Y');
          $this_month = Carbon::now('Asia/Bangkok')->format('m');
          $count_order = UsersOrderLog::whereYear('created_at', '=', $this_year)->whereMonth('created_at', '=', $this_month)->orderby('order_no', 'desc')->first();
          if ($count_order && $count_order->order_no != null && $count_order->order_no != '') {
            $new_order->order_no = intval($count_order->order_no) + 1;
          } else {
            $new_order->order_no = Carbon::now('Asia/Bangkok')->format('Ym') . sprintf('%05d', 1);
          }
          $new_order->save();

          $cart_data = json_decode($request->cart_data);
          $discount_web = 0;
          foreach ($cart_data->data as $key => $val) {
            $new_list = new UsersOrderList();
            if ($val->type == 'course') {
              $new_list->course_id = $val->course_id;
            } else if ($val->type == 'group') {
              $new_list->group_id = $val->group_id;
              $new_list->type = 2;
            } else if ($val->type == 'subscription') {
              $new_list->subscription_id = $val->subscription_id;
              $new_list->type = 3;
            }
            $new_list->order_id = $new_order->id;
            $new_list->price = $val->price;
            $new_list->discount_code = $val->discount_code;
            $new_list->discount_value = $val->discount_price ?? 0;
            $new_list->code_id = $val->code_id;
            $new_list->total_price = $val->total;
            $new_list->save();
            $order_total += $new_list->total_price;
            if ($val->web_price != null && $val->web_price != '' && $val->web_price != 0 && $val->web_price != 'null' && $val->web_price > $discount_web) {
              $discount_web = $val->web_price;
              $new_order->web_price = $val->web_price;
              $new_order->web_code = $val->web_code;
              $new_order->web_code_id = $val->web_code_id;
            }
            $cart_del = UsersCartLog::where('id', $val->id)->first();
            if ($cart_del) {
              $cart_del->delete();
            }
          }
          // Handle checkout field submissions and calculate additional pricing
          $checkoutFields = null;
          $checkoutFieldsPrice = 0;

          if (isset($request->checkout_fields)) {
            if (is_array($request->checkout_fields)) {
              $checkoutFields = $request->checkout_fields;
            } else if (is_string($request->checkout_fields)) {
              $checkoutFields = json_decode($request->checkout_fields, true);
            }
          }

          if ($checkoutFields && is_array($checkoutFields)) {
            foreach ($checkoutFields as $fieldSubmission) {
              if (isset($fieldSubmission['field_id']) && isset($fieldSubmission['field_value'])) {
                try {
                  // Get the checkout field to calculate pricing
                  $checkoutField = CheckoutField::find($fieldSubmission['field_id']);

                  // Calculate additional price if field has pricing
                  if ($checkoutField && $checkoutField->has_pricing) {
                    $fieldValue = $fieldSubmission['field_value'];

                    if ($checkoutField->field_type === 'checkbox') {
                      // Handle multiple selections (checkbox)
                      $values = is_array($fieldValue) ? $fieldValue : explode(',', $fieldValue);
                      $checkoutFieldsPrice += $checkoutField->calculateTotalPrice($values);
                    } else {
                      // Handle single selection (select, radio)
                      $checkoutFieldsPrice += $checkoutField->getOptionPrice($fieldValue);
                    }
                  }

                  // Save the submission
                  CheckoutFieldSubmission::create([
                    'checkout_field_id' => $fieldSubmission['field_id'],
                    'user_id' => $user->id,
                    'order_id' => $new_order->id,
                    'course_id' => isset($fieldSubmission['course_id']) ? $fieldSubmission['course_id'] : null,
                    'subscription_id' => isset($fieldSubmission['subscription_id']) ? $fieldSubmission['subscription_id'] : null,
                    'field_value' => $fieldSubmission['field_value'],
                    'submitted_at' => now()
                  ]);
                } catch (\Exception $e) {
                  // Log error but continue processing other fields
                  error_log('Failed to create checkout field submission: ' . $e->getMessage());
                }
              }
            }
          }

          // Check if any items in the order require VAT calculation
          $requiresVat = false;
          $orderLists = UsersOrderList::where('order_id', $new_order->id)->get();

          foreach ($orderLists as $orderItem) {
            if ($orderItem->type == 1 && $orderItem->course_id) {
              // Course
              $course = Course::find($orderItem->course_id);
              if ($course && $course->requires_vat) {
                $requiresVat = true;
                break;
              }
            } elseif ($orderItem->type == 3 && $orderItem->subscription_id) {
              // Subscription - only check regular subscription (not subscription_main)
              $subscription = Subscription::find($orderItem->subscription_id);
              if ($subscription && $subscription->requires_vat) {
                $requiresVat = true;
                break;
              }
            }
          }

          // Calculate tax based on tax_type and VAT requirement
          $taxType = isset($request->tax_type) ? $request->tax_type : 2; // Default to individual (VAT only)
          // Note: $order_total already includes checkout fields price and VAT calculation from cart
          $basePrice = $order_total - $discount_web;
          $preVat = 0;
          $vatAmount = 0;
          $taxAmount = 0;
          $finalPrice = $basePrice;

          if ($requiresVat) {
            // Cart sends total that includes VAT calculation, but we need to recalculate
            // because checkout fields pricing is added here
            $preVat = $basePrice * 100 / 107; // Extract base price without VAT from cart total
            $checkoutFieldsPriceBeforeVat = $checkoutFieldsPrice; // Checkout fields price is before VAT

            // Recalculate properly: base price + checkout fields, then add VAT
            $totalBeforeVat = $preVat + $checkoutFieldsPriceBeforeVat;
            $vatAmount = $totalBeforeVat * 7 / 100;
            $finalPrice = $totalBeforeVat + $vatAmount;
            $preVat = $totalBeforeVat;

            if ($taxType == 1) {
              // Corporate: additional 3% withholding tax
              $taxAmount = $totalBeforeVat * 3 / 100;
              $finalPrice = $finalPrice - $taxAmount; // Subtract withholding tax from final total
            } else {
              // Individual: VAT only
              $taxAmount = 0;
            }
          }

          // Store the calculated values
          $new_order->total_price = $finalPrice;
          $new_order->checkout_fields_price = $checkoutFieldsPrice;
          $new_order->tax_type = $taxType;
          $new_order->vat_amount = $vatAmount;
          $new_order->tax_amount = $taxAmount;
          $new_order->price_before_tax = $preVat;

          if (isset($request->gift) && isset($request->gift_email) && $request->gift == 1) {
            $new_order->buy_type = 2;
            $new_order->receive_email = $request->gift_email;
            if (isset($request->gift_message) && $request->gift_message != 'null') {
              $new_order->receive_message = $request->gift_message;
            }
          }
          if ($new_order->total_price == 0) {
            $new_order->created_receipt_date = Carbon::now('Asia/Bangkok');
            $new_order->receipt_path = env('APP_URL') . '/receipt/' . SimpleEnDeHelper::instance()->encryptString($new_order->id);
            $new_order->status = 2;
            $new_order->save();
            if (isset($request->gift) && isset($request->gift_email) && $request->gift == 1) {
              $gift_log = new UserGiftLog();
              $gift_log->user_id = $user->id;
              $gift_log->order_id = $new_order->id;
              $gift_log->receive_email = $request->gift_email;
              $gift_log->save();

              $email_dynamic = EmailDynamic::where('id', 10)->where('status', 1)->first();
              if ($email_dynamic) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{sender}}", $new_order->name . ' ' . $new_order->lastname, $replace_detail);
                if (isset($new_order->receive_message) && $new_order->receive_message != null && $new_order->receive_message != '' && $new_order->receive_message != 'null') {
                  $replace_detail = str_replace("{{message}}", 'ข้อความ : ' . $new_order->receive_message, $replace_detail);
                } else {
                  $replace_detail = str_replace("{{message}}", '', $replace_detail);
                }
                $order_gift = UsersOrderList::where('user_order_list.order_id', $new_order->id)->get();
                foreach ($order_gift as $key => $value) {
                  $order_gift[$key]['title_th'] = null;
                  $order_gift[$key]['subtitle_th'] = null;
                  $order_gift[$key]['image_th'] = null;
                  $order_gift[$key]['course_link'] = null;
                  if ($value->type == 1) {
                    $course_log = Course::where('id', $value->course_id)->first();
                    if ($course_log) {
                      $order_gift[$key]['title_th'] = $course_log->title_th;
                      $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                      $order_gift[$key]['image_th'] = $course_log->image_th;
                      $order_gift[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
                    }
                  } else if ($value->type == 2) {
                    $course_log = CourseGroup::where('id', $value->group_id)->first();
                    if ($course_log) {
                      $order_gift[$key]['title_th'] = $course_log->title;
                      $order_gift[$key]['subtitle_th'] = $course_log->details;
                      $order_gift[$key]['image_th'] = $course_log->thumb;
                    }
                  } else if ($value->type == 3) {
                    $course_log = Subscription::where('id', $value->subscription_id)->first();
                    if ($course_log) {
                      $order_gift[$key]['title_th'] = $course_log->title;
                      $order_gift[$key]['subtitle_th'] = $course_log->details;
                      $order_gift[$key]['image_th'] = $course_log->thumb;
                    }
                  }
                }
                $order_replace = '';
                foreach ($order_gift as $value) {
                  if ($value->type == 1) {
                    $order_replace .=
                      '<tr>
                          <td style="width:30px;"></td>
                          <td style="width: 140px">
                              <a href="' . $value->course_link . '">
                                <img src="' . $value->image_th . '" style="width: 140px;">
                              </a>
                          </td>
                          <td style="padding: 10px;">
                            <a href="' . $value->course_link . '">
                              <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                            </a>
                          </td>
                      </tr>';
                  } else {
                    $order_replace .=
                      '<tr>
                          <td style="width:30px;"></td>
                          <td style="width: 140px">
                              <img src="' . $value->image_th . '" style="width: 140px;">
                          </td>
                          <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                          ' . $value->subtitle_th . '
                          </td>
                      </tr>';
                  }
                }
                $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
                $replace_detail = str_replace("{{link}}", "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id), $replace_detail);
                $obj = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code'] = $replace_detail;
                $obj['email'] = $new_order->receive_email;
                $obj['cc'] = [];
                $obj['bcc'] = [];
                AppHelper::instance()->mailTemplate($obj);
              }
              $receive_user = User::where('email', $new_order->receive_email)->first();
              if ($receive_user) {
                $noti_log = new NotiAutoLog();
                $noti_log->user_id = $receive_user->id;
                $noti_log->image = '/images/dashboard/crown-img.png';
                $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
                $noti_log->description = 'จากคุณ' . $new_order->name . ' ' . $new_order->lastname;
                $noti_log->link = "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id);
                $noti_log->save();
              }
            } else {
              $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
                ->select(
                  'user_order_log.id',
                  'user_order_log.receive_email',
                  'user_order_log.receive_message',
                  'user_order_log.user_id',
                  'user_order_log.buy_type',
                  'user_order_log.created_receipt_date',
                  'user_order_list.type',
                  'user_order_list.course_id',
                  'user_order_list.group_id',
                  'user_order_list.subscription_id',
                  'user_order_list.id as list_id'
                )
                ->where('user_order_log.status', 2)->where('user_order_log.id', $new_order->id)->get();
              foreach ($order_history_arr as $order_history) {
                if ($order_history && $order_history->buy_type == 1) {
                  if ($order_history->type == 1) {
                    $course = Course::where('id', $order_history->course_id)->first();
                    if ($course) {
                      $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $order_history->course_id)
                        ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                      if (!$check) {
                        $history_log = new UserHistory();
                        if ($course->trailer_media == 2) {
                          $history_log->zoom_join_url = $course->zoom_join_url;
                          $response['zoom_join_url'] = $history_log->zoom_join_url;
                        }
                        $history_log->get_type = 2;
                        $history_log->order_list_id = $order_history->list_id;
                        $history_log->user_id = $order_history->user_id;
                        $history_log->course_id = $order_history->course_id;
                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                        $history_log->save();
                        $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                        if ($del_limit) {
                          $del_limit->delete();
                        }
                        $response['status'] = 'success';
                      }
                    }
                  } else if ($order_history->type == 2) {
                    $group = CourseGroup::where('id', $order_history->group_id)->first();
                    if ($group) {
                      $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                      foreach ($course as $val_course) {
                        $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                          ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                        if (!$check) {
                          $history_log = new UserHistory();
                          if ($val_course->trailer_media == 2) {
                            $history_log->zoom_join_url = $val_course->zoom_join_url;
                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                          }
                          $history_log->get_type = 2;
                          $history_log->order_list_id = $order_history->list_id;
                          $history_log->user_id = $order_history->user_id;
                          $history_log->course_id = $val_course->id;
                          $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                          $history_log->save();
                          $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                          if ($del_limit) {
                            $del_limit->delete();
                          }
                          $response['status'] = 'success';
                        }
                      }
                    }
                  } else if ($order_history->type == 3) {
                    $subscription = Subscription::where('id', $order_history->subscription_id)->first();
                    if ($subscription) {
                      $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                      $subscription_log = new SubscriptionLog();
                      $subscription_log->order_list_id = $order_history->list_id;
                      $subscription_log->user_id = $order_history->user_id;
                      $subscription_log->subscription_id = $subscription->id;
                      if ($last_sub) {
                        $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                      } else {
                        $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                      }
                      $subscription_log->save();
                    }
                  }
                }
              }
            }
          } else {
            $new_order->status = 1;
            $new_order->save();
          }

          $response['order_id_encrypt'] = SimpleEnDeHelper::instance()->encryptString($new_order->id);
          $response['status'] = 'success';
        } else {
          $new_order = new UsersOrderLog();
          if (isset($request->utm_source) && $request->utm_source != null && $request->utm_source != '' && $request->utm_source != 'null') {
            $new_order->utm_source = $request->utm_source;
          }
          $new_order->is_free = 2;
          $new_order->bank_id = $bank ? $bank->id : null;
          $new_order->receive_email = $request->email;
          $new_order->tel = $request->tel;
          $new_order->address_type = $request->address_type;
          $new_order->company_type = $request->company_type;
          $new_order->company_branch = $request->company_branch;
          $new_order->iden_no = $request->iden_no;
          $new_order->user_id = $user->id;
          $new_order->name = $request->name;
          $new_order->lastname = $request->lastname;
          $new_order->address = $request->address;
          $subdistrict = SubDistrict::where('id', $request->subdistrict)->first();
          if ($subdistrict) {
            $new_order->subdistrict = $subdistrict->name;
          }
          $district = District::where('id', $request->district)->first();
          if ($district) {
            $new_order->district = $district->name;
          }
          $province = Province::where('id', $request->province)->first();
          if ($province) {
            $new_order->province = $province->name;
          }
          $new_order->postcode = $request->postcode;
          $new_order->receipt_type = $request->receipt_type;
          $new_order->receipt = $request->receipt;
          $new_order->tax = $request->tax;
          $new_order->payment_type = $request->payment;
          $new_order->progress_report = $request->progress_report;

          // เพิ่มเงื่อนไขเช็คการชำระด้วยบัตรเครดิต
          if ($request->payment == 2) { // payment_type = 2 คือบัตรเครดิต
            $new_order->credit_payment_type = $request->credit_payment_type; // 'full' หรือ 'installment'
          }

          $order_total = 0;
          $this_year = Carbon::now('Asia/Bangkok')->format('Y');
          $this_month = Carbon::now('Asia/Bangkok')->format('m');
          $count_order = UsersOrderLog::whereYear('created_at', '=', $this_year)->whereMonth('created_at', '=', $this_month)->orderby('order_no', 'desc')->first();
          if ($count_order && $count_order->order_no != null && $count_order->order_no != '') {
            $new_order->order_no = intval($count_order->order_no) + 1;
          } else {
            $new_order->order_no = Carbon::now('Asia/Bangkok')->format('Ym') . sprintf('%05d', 1);
          }
          $new_order->save();

          $cart_data = json_decode($request->cart_data);
          $discount_web = 0;
          foreach ($cart_data->data as $key => $val) {
            $new_list = new UsersOrderList();
            if ($val->type == 'course') {
              $new_list->course_id = $val->course_id;
            } else if ($val->type == 'group') {
              $new_list->group_id = $val->group_id;
              $new_list->type = 2;
            } else if ($val->type == 'subscription') {
              $new_list->subscription_id = $val->subscription_id;
              $new_list->type = 3;
            }
            $new_list->order_id = $new_order->id;
            $new_list->price = $val->price;
            $new_list->discount_code = $val->discount_code;
            $new_list->discount_value = $val->discount_price ?? 0;
            $new_list->code_id = $val->code_id;
            $new_list->total_price = $val->total;
            $new_list->save();
            $order_total += $new_list->total_price;
            if ($val->web_price != null && $val->web_price != '' && $val->web_price != 0 && $val->web_price != 'null' && $val->web_price > $discount_web) {
              $discount_web = $val->web_price;
              $new_order->web_price = $val->web_price;
              $new_order->web_code = $val->web_code;
              $new_order->web_code_id = $val->web_code_id;
            }
            $cart_del = UsersCartLog::where('id', $val->id)->first();
            if ($cart_del) {
              $cart_del->delete();
            }
          }
          // Handle checkout field submissions and calculate additional pricing
          $checkoutFields = null;
          $checkoutFieldsPrice = 0;

          if (isset($request->checkout_fields)) {
            if (is_array($request->checkout_fields)) {
              $checkoutFields = $request->checkout_fields;
            } else if (is_string($request->checkout_fields)) {
              $checkoutFields = json_decode($request->checkout_fields, true);
            }
          }

          if ($checkoutFields && is_array($checkoutFields)) {
            foreach ($checkoutFields as $fieldSubmission) {
              if (isset($fieldSubmission['field_id']) && isset($fieldSubmission['field_value'])) {
                try {
                  // Get the checkout field to calculate pricing
                  $checkoutField = CheckoutField::find($fieldSubmission['field_id']);

                  // Calculate additional price if field has pricing
                  if ($checkoutField && $checkoutField->has_pricing) {
                    $fieldValue = $fieldSubmission['field_value'];

                    if ($checkoutField->field_type === 'checkbox') {
                      // Handle multiple selections (checkbox)
                      $values = is_array($fieldValue) ? $fieldValue : explode(',', $fieldValue);
                      $checkoutFieldsPrice += $checkoutField->calculateTotalPrice($values);
                    } else {
                      // Handle single selection (select, radio)
                      $checkoutFieldsPrice += $checkoutField->getOptionPrice($fieldValue);
                    }
                  }

                  // Save the submission
                  CheckoutFieldSubmission::create([
                    'checkout_field_id' => $fieldSubmission['field_id'],
                    'user_id' => $user->id,
                    'order_id' => $new_order->id,
                    'course_id' => isset($fieldSubmission['course_id']) ? $fieldSubmission['course_id'] : null,
                    'subscription_id' => isset($fieldSubmission['subscription_id']) ? $fieldSubmission['subscription_id'] : null,
                    'field_value' => $fieldSubmission['field_value'],
                    'submitted_at' => now()
                  ]);
                } catch (\Exception $e) {
                  // Log error but continue processing other fields
                  error_log('Failed to create checkout field submission: ' . $e->getMessage());
                }
              }
            }
          }

          // Check if any items in the order require VAT calculation
          $requiresVat = false;
          $orderLists = UsersOrderList::where('order_id', $new_order->id)->get();

          foreach ($orderLists as $orderItem) {
            if ($orderItem->type == 1 && $orderItem->course_id) {
              // Course
              $course = Course::find($orderItem->course_id);
              if ($course && $course->requires_vat) {
                $requiresVat = true;
                break;
              }
            } elseif ($orderItem->type == 3 && $orderItem->subscription_id) {
              // Subscription - only check regular subscription (not subscription_main)
              $subscription = Subscription::find($orderItem->subscription_id);
              if ($subscription && $subscription->requires_vat) {
                $requiresVat = true;
                break;
              }
            }
          }

          // Calculate tax based on tax_type and VAT requirement
          $taxType = isset($request->tax_type) ? $request->tax_type : 2; // Default to individual (VAT only)
          // Note: $order_total already includes checkout fields price and VAT calculation from cart
          $basePrice = $order_total - $discount_web;
          $preVat = 0;
          $vatAmount = 0;
          $taxAmount = 0;
          $finalPrice = $basePrice;

          if ($requiresVat) {
            // Cart sends total that includes VAT calculation, but we need to recalculate
            // because checkout fields pricing is added here
            $preVat = $basePrice * 100 / 107; // Extract base price without VAT from cart total
            $checkoutFieldsPriceBeforeVat = $checkoutFieldsPrice; // Checkout fields price is before VAT

            // Recalculate properly: base price + checkout fields, then add VAT
            $totalBeforeVat = $preVat + $checkoutFieldsPriceBeforeVat;
            $vatAmount = $totalBeforeVat * 7 / 100;
            $finalPrice = $totalBeforeVat + $vatAmount;
            $preVat = $totalBeforeVat;

            if ($taxType == 1) {
              // Corporate: additional 3% withholding tax
              $taxAmount = $totalBeforeVat * 3 / 100;
              $finalPrice = $finalPrice - $taxAmount; // Subtract withholding tax from final total
            } else {
              // Individual: VAT only
              $taxAmount = 0;
            }
          }

          // Store the calculated values
          $new_order->total_price = $finalPrice;
          $new_order->checkout_fields_price = $checkoutFieldsPrice;
          $new_order->tax_type = $taxType;
          $new_order->vat_amount = $vatAmount;
          $new_order->tax_amount = $taxAmount;
          $new_order->price_before_tax = $preVat;

          if (isset($request->gift) && isset($request->gift_email) && $request->gift == 1) {
            $new_order->buy_type = 2;
            $new_order->receive_email = $request->gift_email;
            if (isset($request->gift_message) && $request->gift_message != 'null') {
              $new_order->receive_message = $request->gift_message;
            }
          }
          if ($new_order->total_price == 0) {
            $new_order->created_receipt_date = Carbon::now('Asia/Bangkok');
            $new_order->receipt_path = env('APP_URL') . '/receipt/' . SimpleEnDeHelper::instance()->encryptString($new_order->id);
            $new_order->status = 2;
            $new_order->save();
            if (isset($request->gift) && isset($request->gift_email) && $request->gift == 1) {
              $gift_log = new UserGiftLog();
              $gift_log->user_id = $user->id;
              $gift_log->order_id = $new_order->id;
              $gift_log->receive_email = $request->gift_email;
              $gift_log->save();

              $email_dynamic = EmailDynamic::where('id', 10)->where('status', 1)->first();
              if ($email_dynamic) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{sender}}", $new_order->name . ' ' . $new_order->lastname, $replace_detail);
                if (isset($new_order->receive_message) && $new_order->receive_message != null && $new_order->receive_message != '' && $new_order->receive_message != 'null') {
                  $replace_detail = str_replace("{{message}}", 'ข้อความ : ' . $new_order->receive_message, $replace_detail);
                } else {
                  $replace_detail = str_replace("{{message}}", '', $replace_detail);
                }
                $order_gift = UsersOrderList::where('user_order_list.order_id', $new_order->id)->get();
                foreach ($order_gift as $key => $value) {
                  $order_gift[$key]['title_th'] = null;
                  $order_gift[$key]['subtitle_th'] = null;
                  $order_gift[$key]['image_th'] = null;
                  $order_gift[$key]['course_link'] = null;
                  if ($value->type == 1) {
                    $course_log = Course::where('id', $value->course_id)->first();
                    if ($course_log) {
                      $order_gift[$key]['title_th'] = $course_log->title_th;
                      $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                      $order_gift[$key]['image_th'] = $course_log->image_th;
                      $order_gift[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
                    }
                  } else if ($value->type == 2) {
                    $course_log = CourseGroup::where('id', $value->group_id)->first();
                    if ($course_log) {
                      $order_gift[$key]['title_th'] = $course_log->title;
                      $order_gift[$key]['subtitle_th'] = $course_log->details;
                      $order_gift[$key]['image_th'] = $course_log->thumb;
                    }
                  } else if ($value->type == 3) {
                    $course_log = Subscription::where('id', $value->subscription_id)->first();
                    if ($course_log) {
                      $order_gift[$key]['title_th'] = $course_log->title;
                      $order_gift[$key]['subtitle_th'] = $course_log->details;
                      $order_gift[$key]['image_th'] = $course_log->thumb;
                    }
                  }
                }
                $order_replace = '';
                foreach ($order_gift as $value) {
                  if ($value->type == 1) {
                    $order_replace .=
                      '<tr>
                          <td style="width:30px;"></td>
                          <td style="width: 140px">
                              <a href="' . $value->course_link . '">
                                <img src="' . $value->image_th . '" style="width: 140px;">
                              </a>
                          </td>
                          <td style="padding: 10px;">
                            <a href="' . $value->course_link . '">
                              <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                            </a>
                          </td>
                      </tr>';
                  } else {
                    $order_replace .=
                      '<tr>
                          <td style="width:30px;"></td>
                          <td style="width: 140px">
                              <img src="' . $value->image_th . '" style="width: 140px;">
                          </td>
                          <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                          ' . $value->subtitle_th . '
                          </td>
                      </tr>';
                  }
                }
                $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
                $replace_detail = str_replace("{{link}}", "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id), $replace_detail);
                $obj = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code'] = $replace_detail;
                $obj['email'] = $new_order->receive_email;
                $obj['cc'] = [];
                $obj['bcc'] = [];
                AppHelper::instance()->mailTemplate($obj);
              }
              $receive_user = User::where('email', $new_order->receive_email)->first();
              if ($receive_user) {
                $noti_log = new NotiAutoLog();
                $noti_log->user_id = $receive_user->id;
                $noti_log->image = '/images/dashboard/crown-img.png';
                $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
                $noti_log->description = 'จากคุณ' . $new_order->name . ' ' . $new_order->lastname;
                $noti_log->link = "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id);
                $noti_log->save();
              }
            } else {
              $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
                ->select(
                  'user_order_log.id',
                  'user_order_log.receive_email',
                  'user_order_log.receive_message',
                  'user_order_log.user_id',
                  'user_order_log.buy_type',
                  'user_order_log.created_receipt_date',
                  'user_order_list.type',
                  'user_order_list.course_id',
                  'user_order_list.group_id',
                  'user_order_list.subscription_id',
                  'user_order_list.id as list_id'
                )
                ->where('user_order_log.status', 2)->where('user_order_log.id', $new_order->id)->get();
              foreach ($order_history_arr as $order_history) {
                if ($order_history && $order_history->buy_type == 1) {
                  if ($order_history->type == 1) {
                    $course = Course::where('id', $order_history->course_id)->first();
                    if ($course) {
                      $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $order_history->course_id)
                        ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                      if (!$check) {
                        $history_log = new UserHistory();
                        if ($course->trailer_media == 2) {
                          $history_log->zoom_join_url = $course->zoom_join_url;
                          $response['zoom_join_url'] = $history_log->zoom_join_url;
                        }
                        $history_log->get_type = 2;
                        $history_log->order_list_id = $order_history->list_id;
                        $history_log->user_id = $order_history->user_id;
                        $history_log->course_id = $order_history->course_id;
                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                        $history_log->save();
                        $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                        if ($del_limit) {
                          $del_limit->delete();
                        }
                        $response['status'] = 'success';
                      }
                    }
                  } else if ($order_history->type == 2) {
                    $group = CourseGroup::where('id', $order_history->group_id)->first();
                    if ($group) {
                      $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                      foreach ($course as $val_course) {
                        $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                          ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                        if (!$check) {
                          $history_log = new UserHistory();
                          if ($val_course->trailer_media == 2) {
                            $history_log->zoom_join_url = $val_course->zoom_join_url;
                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                          }
                          $history_log->get_type = 2;
                          $history_log->order_list_id = $order_history->list_id;
                          $history_log->user_id = $order_history->user_id;
                          $history_log->course_id = $val_course->id;
                          $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                          $history_log->save();
                          $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                          if ($del_limit) {
                            $del_limit->delete();
                          }
                          $response['status'] = 'success';
                        }
                      }
                    }
                  } else if ($order_history->type == 3) {
                    $subscription = Subscription::where('id', $order_history->subscription_id)->first();
                    if ($subscription) {
                      $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                      $subscription_log = new SubscriptionLog();
                      $subscription_log->order_list_id = $order_history->list_id;
                      $subscription_log->user_id = $order_history->user_id;
                      $subscription_log->subscription_id = $subscription->id;
                      if ($last_sub) {
                        $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                      } else {
                        $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                      }
                      $subscription_log->save();
                    }
                  }
                }
              }
            }
          } else {
            $new_order->status = 1;
            $new_order->save();
          }

          $response['order_id_encrypt'] = SimpleEnDeHelper::instance()->encryptString($new_order->id);
          $response['status'] = 'success';
        }

        $new_order->cus_name = isset($request->customer_name) ? AppHelper::instance()->cleanInput($request->customer_name) : null;
        $new_order->cus_lastname = isset($request->customer_lastname) ? AppHelper::instance()->cleanInput($request->customer_lastname) : null;
        $new_order->cus_email = isset($request->customer_email) ? AppHelper::instance()->cleanInput($request->customer_email) : null;
        $new_order->cus_mobile = isset($request->customer_mobile) ? AppHelper::instance()->cleanInput($request->customer_mobile) : null;
        $new_order->doc_address = isset($request->customer_address) ? AppHelper::instance()->cleanInput($request->customer_address) : null;
        if ($new_order->doc_address != null) {
          $user->address = $new_order->doc_address;
        }
        $new_order->doc_postcode = isset($request->customer_postcode) ? AppHelper::instance()->cleanInput($request->customer_postcode) : null;
        if ($new_order->doc_postcode != null) {
          $user->postcode = $new_order->doc_postcode;
        }
        if (isset($request->customer_province)) {
          $province = Province::where('id', $request->customer_province)->first();
          if ($province) {
            $new_order->doc_province = $province->name;
            $user->province = $province->id;
            $user->province_name = $province->name;
          }
        }
        if (isset($request->customer_district)) {
          $district = District::where('id', $request->customer_district)->first();
          if ($district) {
            $new_order->doc_district = $district->name;
            $user->district = $district->id;
            $user->district_name = $district->name;
          }
        }
        if (isset($request->customer_subdistrict)) {
          $subdistrict = SubDistrict::where('id', $request->customer_subdistrict)->first();
          if ($subdistrict) {
            $new_order->doc_subdistrict = $subdistrict->name;
            $user->subdistrict = $subdistrict->id;
            $user->subdistrict_name = $subdistrict->name;
          }
        }
        $new_order->save();
        $user->save();

        if ($new_order->doc_address != null) {
        }

        $order_list = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->select('user_order_list.*', 'user_order_log.web_price as discount_web')
          ->where('user_order_list.order_id', $new_order->id)->get();

        $order_total = 0;
        $discount_web = 0;
        foreach ($order_list as $key => $value) {
          $order_list[$key]['title_th'] = null;
          $order_list[$key]['subtitle_th'] = null;
          $order_list[$key]['image_th'] = null;
          $order_list[$key]['course_link'] = null;
          if ($value->type == 1) {
            $course_log = Course::where('id', $value->course_id)->first();
            if ($course_log) {
              $order_list[$key]['title_th'] = $course_log->title_th;
              $order_list[$key]['subtitle_th'] = $course_log->subtitle_th;
              $order_list[$key]['image_th'] = $course_log->image_th;
              $order_list[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
            }
          } else if ($value->type == 2) {
            $course_log = CourseGroup::where('id', $value->group_id)->first();
            if ($course_log) {
              $order_list[$key]['title_th'] = $course_log->title;
              $order_list[$key]['subtitle_th'] = $course_log->details;
              $order_list[$key]['image_th'] = $course_log->thumb;
            }
          } else if ($value->type == 3) {
            $course_log = Subscription::where('id', $value->subscription_id)->first();
            if ($course_log) {
              $order_list[$key]['title_th'] = $course_log->title;
              $order_list[$key]['subtitle_th'] = $course_log->details;
              $order_list[$key]['image_th'] = $course_log->thumb;
            }
          }
          if ($value->discount_web != null && $value->discount_web != '' && $value->discount_web != 0 && $value->discount_web != 'null' && $value->discount_web > $discount_web) {
            $discount_web = $value->discount_web;
          }
          $order_total += $value->total_price;
          $order_list[$key]['total_price'] = number_format($value->total_price, 2);
        }
        $order_total -= $discount_web;

        $email_dynamic = EmailDynamic::where('id', 3)->where('status', 1)->first();
        if ($email_dynamic) {
          $replace_detail = $email_dynamic->details;
          $replace_detail = str_replace("{{name}}", $user->name, $replace_detail);
          $replace_detail = str_replace("{{lastname}}", $user->lastname, $replace_detail);
          $replace_detail = str_replace("{{order_no}}", $new_order->order_no, $replace_detail);
          $replace_detail = str_replace("{{order_date}}", $new_order->created_at, $replace_detail);
          if ($discount_web == 0) {
            $replace_detail = str_replace("{{discount_web}}", '', $replace_detail);
          } else {
            $replace_detail = str_replace("{{discount_web}}", 'ส่วนลดคูปองเงินสด : -' . number_format($discount_web, 2) . ' THB', $replace_detail);
          }
          $replace_detail = str_replace("{{link}}", '<a href="https://csisociety.com/dashboard/history" target="_blank">https://csisociety.com/dashboard/history</a>', $replace_detail);
          $replace_detail = str_replace("{{order_total}}", number_format($order_total, 2), $replace_detail);
          $order_replace = '';
          foreach ($order_list as $value) {
            if ($value->type == 1) {
              $order_replace .=
                '<tr>
                    <td style="width:30px;"></td>
                    <td style="width: 140px">
                      <a href="' . $value->course_link . '">
                        <img src="' . $value->image_th . '" style="width: 140px;">
                      </a>
                    </td>
                    <td style="padding: 10px;">
                      <a href="' . $value->course_link . '">
                        <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                      </a>
                    </td>
                    <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                    <td style="width:30px;"></td>
                </tr>';
            } else {
              $order_replace .=
                '<tr>
                    <td style="width:30px;"></td>
                    <td style="width: 140px">
                        <img src="' . $value->image_th . '" style="width: 140px;">
                    </td>
                    <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                    ' . $value->subtitle_th . '
                    </td>
                    <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                    <td style="width:30px;"></td>
                </tr>';
            }
          }
          $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
          $obj = new Dummy();
          $obj['subject'] = $email_dynamic->subject;
          $obj['code'] = $replace_detail;
          $obj['email'] = $user->email;
          $obj['cc'] = [];
          $obj['bcc'] = [];
          AppHelper::instance()->mailTemplate($obj);
        }

        $noti_log = new NotiAutoLog();
        $noti_log->user_id = $user->id;
        $noti_log->image = '/assets/images/qr.png';
        $noti_log->title = 'ระบบได้รับคำสั่งซื้อของท่านแล้ว';
        $noti_log->description = 'Order : ' . $new_order->order_no;
        $noti_log->link = '/dashboard/history';
        $noti_log->save();

        $response['order_no'] = $new_order->order_no;
        $response['payment_order_id'] = $new_order->id;
        $response['order_total'] = $order_total;
        $response['payment_return'] = url('/api/2c2p/complete/' . $new_order->id);
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addOrderFree(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $new_order = new UsersOrderLog();
        $new_order->user_id = $user->id;
        $new_order->name = $user->name;
        $new_order->lastname = $user->lastname;
        $new_order->address = $user->address;
        $new_order->subdistrict = $user->subdistrict_name;
        $new_order->district = $user->district_name;
        $new_order->province = $user->province_name;
        $new_order->postcode = $user->postcode;
        $new_order->receipt_type = 1;
        $new_order->receipt = 2;
        $new_order->tax = 2;
        $new_order->payment_type = 3;
        $this_year = Carbon::now('Asia/Bangkok')->format('Y');
        $this_month = Carbon::now('Asia/Bangkok')->format('m');
        $count_order = UsersOrderLog::whereYear('created_at', '=', $this_year)->whereMonth('created_at', '=', $this_month)->orderby('order_no', 'desc')->first();
        if ($count_order && $count_order->order_no != null && $count_order->order_no != '') {
          $new_order->order_no = intval($count_order->order_no) + 1;
        } else {
          $new_order->order_no = Carbon::now('Asia/Bangkok')->format('Ym') . sprintf('%05d', 1);
        }
        $new_order->total_price = 0;
        $new_order->created_receipt_date = Carbon::now('Asia/Bangkok');
        $new_order->receipt_path = env('APP_URL') . '/receipt/' . SimpleEnDeHelper::instance()->encryptString($new_order->id);
        $new_order->status = 2;

        $new_list = new UsersOrderList();

        $new_list->price = 0;
        $new_list->course_id = $request->course_id;
        $new_list->total_price = 0;

        $course_check = Course::join('course_lesson', 'course_lesson.course_id', 'course.id')
          ->select('course.*', 'course_lesson.zoom_id')
          ->where('course.id', $request->course_id)->where('course.status', 1)
          ->where('course.started_date', '<=', Carbon::now());
        $course_check->where(function ($course_check) {
          $course_check->where('course.end_date', '>=', Carbon::now());
          $course_check->orWhere('course.end_date', null);
        });

        $course_check = $course_check->first();
        if ($course_check && $course_check->trailer_media == 2) {
          $assign_zoom = AppHelper::instance()->assignZoom($course_check->zoom_id, $user->email, $user->name, $user->lastname);
          if ($assign_zoom->status == 'approve') {
            $new_order->save();
            $new_list->registrant_id = $assign_zoom->registrant_id;
            $new_list->order_id = $new_order->id;
            $new_list->save();
          } else {
            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
          }
        } else {
          $new_order->save();
          $new_list->order_id = $new_order->id;
          $new_list->save();
        }

        $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->select(
            'user_order_log.id',
            'user_order_log.receive_email',
            'user_order_log.receive_message',
            'user_order_log.user_id',
            'user_order_log.buy_type',
            'user_order_log.created_receipt_date',
            'user_order_list.type',
            'user_order_list.course_id',
            'user_order_list.group_id',
            'user_order_list.subscription_id',
            'user_order_list.id as list_id'
          )
          ->where('user_order_log.status', 2)->where('user_order_log.id', $new_order->id)->get();
        foreach ($order_history_arr as $order_history) {
          if ($order_history && $order_history->buy_type == 1) {
            if ($order_history->type == 1) {
              $course = Course::where('id', $order_history->course_id)->first();
              if ($course) {
                $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $order_history->course_id)
                  ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                if (!$check) {
                  $history_log = new UserHistory();
                  if ($course->trailer_media == 2) {
                    $history_log->zoom_join_url = $course->zoom_join_url;
                    $response['zoom_join_url'] = $history_log->zoom_join_url;
                  }
                  $check_volume = VolumnBy::join('volumn_user_log', 'volumn_user_log.company_id', 'volumn_by.id')
                    ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_by.id')
                    ->select('volumn_by.id')
                    ->where('volumn_by.status', 1)->where('volumn_user_log.user_id', $order_history->user_id)
                    ->where('volumn_course_log.course_id', $order_history->course_id)->where('volumn_course_log.status', 1)->first();
                  if ($check_volume) {
                    $history_log->get_type = 6;
                    $history_log->company_id = $check_volume->id;
                  } else {
                    $history_log->get_type = 1;
                  }
                  $history_log->order_list_id = $order_history->list_id;
                  $history_log->user_id = $order_history->user_id;
                  $history_log->course_id = $order_history->course_id;
                  $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                  $history_log->save();
                  $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                  if ($del_limit) {
                    $del_limit->delete();
                  }
                  $response['status'] = 'success';
                }
              }
            } else if ($order_history->type == 2) {
              $group = CourseGroup::where('id', $order_history->group_id)->first();
              if ($group) {
                $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                foreach ($course as $val_course) {
                  $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                    ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                  if (!$check) {
                    $history_log = new UserHistory();
                    if ($val_course->trailer_media == 2) {
                      $history_log->zoom_join_url = $val_course->zoom_join_url;
                      $response['zoom_join_url'] = $history_log->zoom_join_url;
                    }
                    $check_volume = VolumnBy::join('volumn_user_log', 'volumn_user_log.company_id', 'volumn_by.id')
                      ->join('volumn_course_log', 'volumn_course_log.company_id', 'volumn_by.id')
                      ->select('volumn_by.id')
                      ->where('volumn_by.status', 1)->where('volumn_user_log.user_id', $order_history->user_id)
                      ->where('volumn_course_log.course_id', $val_course->id)->where('volumn_course_log.status', 1)->first();
                    if ($check_volume) {
                      $history_log->get_type = 6;
                      $history_log->company_id = $check_volume->id;
                    } else {
                      $history_log->get_type = 1;
                    }
                    $history_log->order_list_id = $order_history->list_id;
                    $history_log->user_id = $order_history->user_id;
                    $history_log->course_id = $val_course->id;
                    $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                    $history_log->save();
                    $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                    if ($del_limit) {
                      $del_limit->delete();
                    }
                    $response['status'] = 'success';
                  }
                }
              }
            } else if ($order_history->type == 3) {
              $subscription = Subscription::where('id', $order_history->subscription_id)->first();
              if ($subscription) {
                $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                $subscription_log = new SubscriptionLog();
                $subscription_log->order_list_id = $order_history->list_id;
                $subscription_log->user_id = $order_history->user_id;
                $subscription_log->subscription_id = $subscription->id;
                if ($last_sub) {
                  $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                } else {
                  $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                }
                $subscription_log->save();
              }
            }
          }
        }


        $order_list = UsersOrderList::join('course', 'course.id', 'user_order_list.course_id')
          ->select('user_order_list.total_price', 'course.title_th', 'course.subtitle_th', 'course.image_th')
          ->where('user_order_list.order_id', $new_order->id)->get();

        $order_total = 0;
        foreach ($order_list as $key => $value) {
          $order_total += $value->total_price;
          $order_list[$key]['total_price'] = number_format($value->total_price, 2);
        }
        //Course ID*** 
        // $response['order_list'] = $order_list;

        $response['order_id_encrypt'] = SimpleEnDeHelper::instance()->encryptString($new_order->id);
        $response['status'] = 'success';

        // $email_dynamic = EmailDynamic::where('id',2)->where('status',1)->first();
        // if($email_dynamic){
        //   $replace_detail = $email_dynamic->details;
        //   $replace_detail = str_replace("{{name}}",$user->name,$replace_detail);
        //   $replace_detail = str_replace("{{lastname}}",$user->lastname,$replace_detail);
        //   $replace_detail = str_replace("{{order_no}}",$new_order->order_no,$replace_detail);
        //   $replace_detail = str_replace("{{order_date}}",$new_order->created_at,$replace_detail);
        //   $replace_detail = str_replace("{{link}}",'<a href="'.env('APP_NEXT_URL').'/profile/history" target="_blank">'.env('APP_NEXT_URL').'/profile/history</a>',$replace_detail);
        //   if($new_order->payment_type==1){
        //     $replace_detail = str_replace("{{payment_channel}}",'QR Code',$replace_detail);
        //   }else if($new_order->payment_type==2){
        //     $replace_detail = str_replace("{{payment_channel}}",'Credit/Debit',$replace_detail);
        //   }else{
        //     $replace_detail = str_replace("{{payment_channel}}",'โอนเงิน',$replace_detail);
        //   }
        //   $replace_detail = str_replace("{{order_total}}",number_format($order_total,2),$replace_detail);
        //   $order_replace = '';
        //   foreach($order_list as $value){
        //     $order_replace.=
        //     '<tr>
        //         <td style="width:30px;"></td>
        //         <td style="width: 140px">
        //             <img src="'.$value->image_th.'" style="width: 140px;">
        //         </td>
        //         <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
        //         '.$value->subtitle_th.'
        //         </td>
        //         <td style="width: 100px;text-align: right;"><b>'.$value->total_price.' THB</b></td>
        //         <td style="width:30px;"></td>
        //     </tr>';
        //   }
        //   $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
        //   $obj = new Dummy();
        //   $obj['subject'] = $email_dynamic->subject;
        //   $obj['code'] = $replace_detail;
        //   $obj['email'] = $user->email;
        //   $obj['cc'] = [];
        //   $obj['bcc'] = [];
        //   AppHelper::instance()->mailTemplate($obj);
        // }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addOrderVip(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $new_order = new UsersOrderLog();
        $new_order->user_id = $user->id;
        $new_order->name = $user->name;
        $new_order->lastname = $user->lastname;
        $new_order->address = $user->address;
        $new_order->subdistrict = $user->subdistrict_name;
        $new_order->district = $user->district_name;
        $new_order->province = $user->province_name;
        $new_order->postcode = $user->postcode;
        $new_order->receipt_type = 1;
        $new_order->receipt = 2;
        $new_order->tax = 2;
        $new_order->payment_type = 3;
        $this_year = Carbon::now('Asia/Bangkok')->format('Y');
        $this_month = Carbon::now('Asia/Bangkok')->format('m');
        $count_order = UsersOrderLog::whereYear('created_at', '=', $this_year)->whereMonth('created_at', '=', $this_month)->orderby('order_no', 'desc')->first();
        if ($count_order && $count_order->order_no != null && $count_order->order_no != '') {
          $new_order->order_no = intval($count_order->order_no) + 1;
        } else {
          $new_order->order_no = Carbon::now('Asia/Bangkok')->format('Ym') . sprintf('%05d', 1);
        }
        $new_order->total_price = 0;
        $new_order->created_receipt_date = Carbon::now('Asia/Bangkok');
        $new_order->receipt_path = env('APP_URL') . '/receipt/' . SimpleEnDeHelper::instance()->encryptString($new_order->id);
        $new_order->status = 2;

        $new_list = new UsersOrderList();

        $new_list->price = 0;
        $new_list->course_id = $request->course_id;
        $new_list->total_price = 0;

        $course_check = Course::join('course_lesson', 'course_lesson.course_id', 'course.id')
          ->select('course.*', 'course_lesson.zoom_id')
          ->where('course.id', $request->course_id)->where('course.status', 1)
          ->where('course.started_date', '<=', Carbon::now());
        $course_check->where(function ($course_check) {
          $course_check->where('course.end_date', '>=', Carbon::now());
          $course_check->orWhere('course.end_date', null);
        });

        $course_check = $course_check->first();
        if ($course_check && $course_check->trailer_media == 2) {
          $assign_zoom = AppHelper::instance()->assignZoom($course_check->zoom_id, $user->email, $user->name, $user->lastname);
          if ($assign_zoom->status == 'approve') {
            $new_order->save();
            $new_list->registrant_id = $assign_zoom->registrant_id;
            $new_list->order_id = $new_order->id;
            $new_list->save();
          } else {
            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
          }
        } else {
          $new_order->save();
          $new_list->order_id = $new_order->id;
          $new_list->save();
        }

        $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->select(
            'user_order_log.id',
            'user_order_log.receive_email',
            'user_order_log.receive_message',
            'user_order_log.user_id',
            'user_order_log.buy_type',
            'user_order_log.created_receipt_date',
            'user_order_list.type',
            'user_order_list.course_id',
            'user_order_list.group_id',
            'user_order_list.subscription_id',
            'user_order_list.id as list_id'
          )
          ->where('user_order_log.status', 2)->where('user_order_log.id', $new_order->id)->get();
        foreach ($order_history_arr as $order_history) {
          if ($order_history && $order_history->buy_type == 1) {
            if ($order_history->type == 1) {
              $course = Course::where('id', $order_history->course_id)->first();
              if ($course) {
                $check_sub = AppHelper::instance()->courseSubscription($course, $user);
                if ($check_sub[0] && $check_sub[1] != '') {
                  $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $order_history->course_id)
                    ->where('expired', $check_sub[1])->first();
                  if (!$check) {
                    $history_log = new UserHistory();
                    if ($course->trailer_media == 2) {
                      $history_log->zoom_join_url = $course->zoom_join_url;
                      $response['zoom_join_url'] = $history_log->zoom_join_url;
                    }
                    $history_log->get_type = 5;
                    $history_log->order_list_id = $order_history->list_id;
                    $history_log->type = 2;
                    $history_log->user_id = $order_history->user_id;
                    $history_log->course_id = $order_history->course_id;
                    $history_log->expired = $check_sub[1];
                    $history_log->save();
                    $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                    if ($del_limit) {
                      $del_limit->delete();
                    }
                    $response['status'] = 'success';
                  }
                }
              }
            } else if ($order_history->type == 2) {
              $group = CourseGroup::where('id', $order_history->group_id)->first();
              if ($group) {
                $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                foreach ($course as $val_course) {
                  $check_sub = AppHelper::instance()->courseSubscription($val_course, $user);
                  if ($check_sub[0] && $check_sub[1] != '') {
                    $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                      ->where('expired', $check_sub[1])->first();
                    if (!$check) {
                      $history_log = new UserHistory();
                      if ($val_course->trailer_media == 2) {
                        $history_log->zoom_join_url = $val_course->zoom_join_url;
                        $response['zoom_join_url'] = $history_log->zoom_join_url;
                      }
                      $history_log->get_type = 5;
                      $history_log->order_list_id = $order_history->list_id;
                      $history_log->type = 2;
                      $history_log->user_id = $order_history->user_id;
                      $history_log->course_id = $val_course->id;
                      $history_log->expired = $check_sub[1];
                      $history_log->save();
                      $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                      if ($del_limit) {
                        $del_limit->delete();
                      }
                      $response['status'] = 'success';
                    }
                  }
                }
              }
            } else if ($order_history->type == 3) {
              $subscription = Subscription::where('id', $order_history->subscription_id)->first();
              if ($subscription) {
                $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                $subscription_log = new SubscriptionLog();
                $subscription_log->order_list_id = $order_history->list_id;
                $subscription_log->user_id = $order_history->user_id;
                $subscription_log->subscription_id = $subscription->id;
                if ($last_sub) {
                  $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                } else {
                  $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                }
                $subscription_log->save();
              }
            }
          }
        }


        $order_list = UsersOrderList::join('course', 'course.id', 'user_order_list.course_id')
          ->select('user_order_list.total_price', 'course.title_th', 'course.subtitle_th', 'course.image_th')
          ->where('user_order_list.order_id', $new_order->id)->get();

        $order_total = 0;
        foreach ($order_list as $key => $value) {
          $order_total += $value->total_price;
          $order_list[$key]['total_price'] = number_format($value->total_price, 2);
        }
        //Course ID*** 
        // $response['order_list'] = $order_list;

        $response['order_id_encrypt'] = SimpleEnDeHelper::instance()->encryptString($new_order->id);
        $response['status'] = 'success';

        // $email_dynamic = EmailDynamic::where('id',2)->where('status',1)->first();
        // if($email_dynamic){
        //   $replace_detail = $email_dynamic->details;
        //   $replace_detail = str_replace("{{name}}",$user->name,$replace_detail);
        //   $replace_detail = str_replace("{{lastname}}",$user->lastname,$replace_detail);
        //   $replace_detail = str_replace("{{order_no}}",$new_order->order_no,$replace_detail);
        //   $replace_detail = str_replace("{{order_date}}",$new_order->created_at,$replace_detail);
        //   $replace_detail = str_replace("{{link}}",'<a href="'.env('APP_NEXT_URL').'/profile/history" target="_blank">'.env('APP_NEXT_URL').'/profile/history</a>',$replace_detail);
        //   if($new_order->payment_type==1){
        //     $replace_detail = str_replace("{{payment_channel}}",'QR Code',$replace_detail);
        //   }else if($new_order->payment_type==2){
        //     $replace_detail = str_replace("{{payment_channel}}",'Credit/Debit',$replace_detail);
        //   }else{
        //     $replace_detail = str_replace("{{payment_channel}}",'โอนเงิน',$replace_detail);
        //   }
        //   $replace_detail = str_replace("{{order_total}}",number_format($order_total,2),$replace_detail);
        //   $order_replace = '';
        //   foreach($order_list as $value){
        //     $order_replace.=
        //     '<tr>
        //         <td style="width:30px;"></td>
        //         <td style="width: 140px">
        //             <img src="'.$value->image_th.'" style="width: 140px;">
        //         </td>
        //         <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
        //         '.$value->subtitle_th.'
        //         </td>
        //         <td style="width: 100px;text-align: right;"><b>'.$value->total_price.' THB</b></td>
        //         <td style="width:30px;"></td>
        //     </tr>';
        //   }
        //   $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
        //   $obj = new Dummy();
        //   $obj['subject'] = $email_dynamic->subject;
        //   $obj['code'] = $replace_detail;
        //   $obj['email'] = $user->email;
        //   $obj['cc'] = [];
        //   $obj['bcc'] = [];
        //   AppHelper::instance()->mailTemplate($obj);
        // }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addOrderVolume(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $new_order = new UsersOrderLog();
        $new_order->user_id = $user->id;
        $new_order->name = $user->name;
        $new_order->lastname = $user->lastname;
        $new_order->address = $user->address;
        $new_order->subdistrict = $user->subdistrict_name;
        $new_order->district = $user->district_name;
        $new_order->province = $user->province_name;
        $new_order->postcode = $user->postcode;
        $new_order->receipt_type = 1;
        $new_order->receipt = 2;
        $new_order->tax = 2;
        $new_order->payment_type = 3;
        $this_year = Carbon::now('Asia/Bangkok')->format('Y');
        $this_month = Carbon::now('Asia/Bangkok')->format('m');
        $count_order = UsersOrderLog::whereYear('created_at', '=', $this_year)->whereMonth('created_at', '=', $this_month)->orderby('order_no', 'desc')->first();
        if ($count_order && $count_order->order_no != null && $count_order->order_no != '') {
          $new_order->order_no = intval($count_order->order_no) + 1;
        } else {
          $new_order->order_no = Carbon::now('Asia/Bangkok')->format('Ym') . sprintf('%05d', 1);
        }
        $new_order->total_price = 0;
        $new_order->created_receipt_date = Carbon::now('Asia/Bangkok');
        $new_order->receipt_path = env('APP_URL') . '/receipt/' . SimpleEnDeHelper::instance()->encryptString($new_order->id);
        $new_order->status = 2;

        $new_list = new UsersOrderList();

        $new_list->price = 0;
        $new_list->course_id = $request->course_id;
        $new_list->total_price = 0;

        $course_check = Course::join('course_lesson', 'course_lesson.course_id', 'course.id')
          ->select('course.*', 'course_lesson.zoom_id')
          ->where('course.id', $request->course_id)->where('course.status', 1)
          ->where('course.started_date', '<=', Carbon::now());
        $course_check->where(function ($course_check) {
          $course_check->where('course.end_date', '>=', Carbon::now());
          $course_check->orWhere('course.end_date', null);
        });


        $course_check = $course_check->first();
        if ($course_check && $course_check->trailer_media == 2) {
          $assign_zoom = AppHelper::instance()->assignZoom($course_check->zoom_id, $user->email, $user->name, $user->lastname);
          if ($assign_zoom->status == 'approve') {
            $new_order->save();
            $new_list->registrant_id = $assign_zoom->registrant_id;
            $new_list->order_id = $new_order->id;
            $new_list->save();
          } else {
            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
          }
        } else {
          $new_order->save();
          $new_list->order_id = $new_order->id;
          $new_list->save();
        }

        $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->select(
            'user_order_log.id',
            'user_order_log.receive_email',
            'user_order_log.receive_message',
            'user_order_log.user_id',
            'user_order_log.buy_type',
            'user_order_log.created_receipt_date',
            'user_order_list.type',
            'user_order_list.course_id',
            'user_order_list.group_id',
            'user_order_list.subscription_id',
            'user_order_list.id as list_id'
          )
          ->where('user_order_log.status', 2)->where('user_order_log.id', $new_order->id)->get();
        foreach ($order_history_arr as $order_history) {
          if ($order_history && $order_history->buy_type == 1) {
            if ($order_history->type == 1) {
              $course = Course::where('id', $order_history->course_id)->first();
              if ($course) {
                $check_sub = AppHelper::instance()->checkVolume($course, $user);
                if ($check_sub[0] && $check_sub[1] != '') {
                  $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $course->id)
                    ->where('expired', $check_sub[1])->first();
                  if (!$check) {
                    $history_log = new UserHistory();
                    if ($course->trailer_media == 2) {
                      $history_log->zoom_join_url = $course->zoom_join_url;
                      $response['zoom_join_url'] = $history_log->zoom_join_url;
                    }
                    $history_log->get_type = 6;
                    $history_log->company_id = $check_sub[2];
                    $history_log->company_lot_id = $check_sub[3];
                    $history_log->order_list_id = $order_history->list_id;
                    $history_log->user_id = $order_history->user_id;
                    $history_log->course_id = $course->id;
                    $history_log->expired = $check_sub[1];
                    $history_log->save();
                    $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                    if ($del_limit) {
                      $del_limit->delete();
                    }
                    if ($user->email != null && $user->email != '') {
                      $assign_lot = VolumnByUser::where('company_id', $check_sub[2])->where('lot_id', $check_sub[3]);
                      $assign_lot->where(function ($assign_lot) use ($user) {
                        if ($user->email != null && $user->email != '') {
                          $assign_lot->where('email', $user->email);
                        }
                      });
                      $assign_lot = $assign_lot->first();
                      if (!$assign_lot) {
                        if ($user->email != null && $user->email != '') {
                          $assign_lot = new VolumnByUser();
                          $assign_lot->company_id = $check_sub[2];
                          $assign_lot->lot_id = $check_sub[3];
                          $assign_lot->email = $user->email;
                          $assign_lot->save();
                        }
                      }
                    }

                    $response['status'] = 'success';
                  }
                }
              }
            } else if ($order_history->type == 2) {
              $group = CourseGroup::where('id', $order_history->group_id)->first();
              if ($group) {
                $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                foreach ($course as $val_course) {
                  $check_sub = AppHelper::instance()->checkVolume($val_course, $user);
                  if ($check_sub[0] && $check_sub[1] != '') {
                    $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                      ->where('expired', $check_sub[1])->first();
                    if (!$check) {
                      $history_log = new UserHistory();
                      if ($val_course->trailer_media == 2) {
                        $history_log->zoom_join_url = $val_course->zoom_join_url;
                        $response['zoom_join_url'] = $history_log->zoom_join_url;
                      }
                      $history_log->get_type = 6;
                      $history_log->company_id = $check_sub[2];
                      $history_log->company_lot_id = $check_sub[3];
                      $history_log->order_list_id = $order_history->list_id;
                      $history_log->user_id = $order_history->user_id;
                      $history_log->course_id = $val_course->id;
                      $history_log->expired = $check_sub[1];
                      $history_log->save();
                      $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                      if ($del_limit) {
                        $del_limit->delete();
                      }
                      if ($user->email != null && $user->email != '') {
                        $assign_lot = VolumnByUser::where('company_id', $check_sub[2])->where('lot_id', $check_sub[3]);
                        $assign_lot->where(function ($assign_lot) use ($user) {
                          if ($user->email != null && $user->email != '') {
                            $assign_lot->where('email', $user->email);
                          }
                        });
                        $assign_lot = $assign_lot->first();
                        if (!$assign_lot) {
                          if ($user->email != null && $user->email != '') {
                            $assign_lot = new VolumnByUser();
                            $assign_lot->company_id = $check_sub[2];
                            $assign_lot->lot_id = $check_sub[3];
                            $assign_lot->email = $user->email;
                            $assign_lot->save();
                          }
                        }
                      }

                      $response['status'] = 'success';
                    }
                  }
                }
              }
            } else if ($order_history->type == 3) {
              $subscription = Subscription::where('id', $order_history->subscription_id)->first();
              if ($subscription) {
                $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                $subscription_log = new SubscriptionLog();
                $subscription_log->order_list_id = $order_history->list_id;
                $subscription_log->user_id = $order_history->user_id;
                $subscription_log->subscription_id = $subscription->id;
                if ($last_sub) {
                  $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                } else {
                  $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                }
                $subscription_log->save();
              }
            }
          }
        }


        $order_list = UsersOrderList::join('course', 'course.id', 'user_order_list.course_id')
          ->select('user_order_list.total_price', 'course.title_th', 'course.subtitle_th', 'course.image_th')
          ->where('user_order_list.order_id', $new_order->id)->get();

        $order_total = 0;
        foreach ($order_list as $key => $value) {
          $order_total += $value->total_price;
          $order_list[$key]['total_price'] = number_format($value->total_price, 2);
        }
        //Course ID*** 
        // $response['order_list'] = $order_list;

        $response['order_id_encrypt'] = SimpleEnDeHelper::instance()->encryptString($new_order->id);
        $response['status'] = 'success';

        // $email_dynamic = EmailDynamic::where('id',2)->where('status',1)->first();
        // if($email_dynamic){
        //   $replace_detail = $email_dynamic->details;
        //   $replace_detail = str_replace("{{name}}",$user->name,$replace_detail);
        //   $replace_detail = str_replace("{{lastname}}",$user->lastname,$replace_detail);
        //   $replace_detail = str_replace("{{order_no}}",$new_order->order_no,$replace_detail);
        //   $replace_detail = str_replace("{{order_date}}",$new_order->created_at,$replace_detail);
        //   $replace_detail = str_replace("{{link}}",'<a href="'.env('APP_NEXT_URL').'/profile/history" target="_blank">'.env('APP_NEXT_URL').'/profile/history</a>',$replace_detail);
        //   if($new_order->payment_type==1){
        //     $replace_detail = str_replace("{{payment_channel}}",'QR Code',$replace_detail);
        //   }else if($new_order->payment_type==2){
        //     $replace_detail = str_replace("{{payment_channel}}",'Credit/Debit',$replace_detail);
        //   }else{
        //     $replace_detail = str_replace("{{payment_channel}}",'โอนเงิน',$replace_detail);
        //   }
        //   $replace_detail = str_replace("{{order_total}}",number_format($order_total,2),$replace_detail);
        //   $order_replace = '';
        //   foreach($order_list as $value){
        //     $order_replace.=
        //     '<tr>
        //         <td style="width:30px;"></td>
        //         <td style="width: 140px">
        //             <img src="'.$value->image_th.'" style="width: 140px;">
        //         </td>
        //         <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
        //         '.$value->subtitle_th.'
        //         </td>
        //         <td style="width: 100px;text-align: right;"><b>'.$value->total_price.' THB</b></td>
        //         <td style="width:30px;"></td>
        //     </tr>';
        //   }
        //   $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
        //   $obj = new Dummy();
        //   $obj['subject'] = $email_dynamic->subject;
        //   $obj['code'] = $replace_detail;
        //   $obj['email'] = $user->email;
        //   $obj['cc'] = [];
        //   $obj['bcc'] = [];
        //   AppHelper::instance()->mailTemplate($obj);
        // }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function updateOrder(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->data)) {
      $data = json_decode($request->data);
      if (
        isset($data->by_bank) && isset($data->order_no) && isset($data->t_date)
        && isset($data->to_bank) && (isset($data->image_slip) || isset($data->image_slips))
      ) {
        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user) {
          $order = UsersOrderLog::where('user_id', $user->id)->where('order_no', $data->order_no)->where('payment_type', 3)->where('status', 1)->first();
          if ($order) {
            $order->amount = isset($data->amount) ? AppHelper::instance()->cleanInput($data->amount) : '';
            $order->by_bank = isset($data->by_bank) ? AppHelper::instance()->cleanInput($data->by_bank) : '';
            
            if (isset($data->image_slips) && is_array($data->image_slips)) {
              $cleanedImages = [];
              foreach ($data->image_slips as $image) {
                if (!empty($image)) {
                  $cleanedImages[] = AppHelper::instance()->cleanInput($image);
                }
              }
              $order->image_slip = json_encode($cleanedImages);
            } elseif (isset($data->image_slip)) {
              $order->image_slip = AppHelper::instance()->cleanInput($data->image_slip);
            }
            
            $order->remark = isset($data->remark) ? AppHelper::instance()->cleanInput($data->remark) : '';
            $order->t_date = isset($data->t_date) ? AppHelper::instance()->cleanInput($data->t_date) : '';
            $order->to_bank = isset($data->to_bank) ? AppHelper::instance()->cleanInput($data->to_bank) : '';
            $order->status = 2;
            $order->save();

            if (env('PEAK_ENABLE', false)) {
              // Handle Peak contact creation/update
              $this->handlePeakContact($user, $order);
            }

            $response['status'] = 'success';

            if ($order->buy_type == 1) {
              $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
                ->select(
                  'user_order_log.id',
                  'user_order_log.receive_email',
                  'user_order_log.receive_message',
                  'user_order_log.user_id',
                  'user_order_log.buy_type',
                  'user_order_log.created_receipt_date',
                  'user_order_list.type',
                  'user_order_list.course_id',
                  'user_order_list.group_id',
                  'user_order_list.subscription_id',
                  'user_order_list.id as list_id'
                )
                ->where('user_order_log.status', 2)->where('user_order_log.id', $order->id)->get();
              foreach ($order_history_arr as $order_history) {
                if ($order_history && $order_history->buy_type == 1) {
                  if ($order_history->type == 1) {
                    $course = Course::where('id', $order_history->course_id)->first();
                    if ($course) {
                      $history_log = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $order_history->course_id)
                        ->first();
                      if (!$history_log) {
                        $history_log = new UserHistory();
                      }
                      if ($course->trailer_media == 2) {
                        $history_log->zoom_join_url = $course->zoom_join_url;
                        $response['zoom_join_url'] = $history_log->zoom_join_url;
                      }
                      $history_log->get_type = 2;
                      $history_log->order_list_id = $order_history->list_id;
                      $history_log->user_id = $order_history->user_id;
                      $history_log->course_id = $order_history->course_id;
                      $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                      $history_log->save();
                      $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                      if ($del_limit) {
                        $del_limit->delete();
                      }
                      $response['status'] = 'success';
                    }
                  } else if ($order_history->type == 2) {
                    $group = CourseGroup::where('id', $order_history->group_id)->first();
                    if ($group) {
                      $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                      foreach ($course as $val_course) {
                        $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                          ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                        if (!$check) {
                          $history_log = new UserHistory();
                          if ($val_course->trailer_media == 2) {
                            $history_log->zoom_join_url = $val_course->zoom_join_url;
                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                          }
                          $history_log->get_type = 2;
                          $history_log->order_list_id = $order_history->list_id;
                          $history_log->user_id = $order_history->user_id;
                          $history_log->course_id = $val_course->id;
                          $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                          $history_log->save();
                          $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                          if ($del_limit) {
                            $del_limit->delete();
                          }
                          $response['status'] = 'success';
                        }
                      }
                    }
                  } else if ($order_history->type == 3) {
                    $subscription = Subscription::where('id', $order_history->subscription_id)->first();
                    if ($subscription) {
                      $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                      $subscription_log = new SubscriptionLog();
                      $subscription_log->order_list_id = $order_history->list_id;
                      $subscription_log->user_id = $order_history->user_id;
                      $subscription_log->subscription_id = $subscription->id;
                      if ($last_sub) {
                        $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                      } else {
                        $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                      }
                      $subscription_log->save();
                    }
                  }
                }
              }
            }
            if ($order->status == 2) {
              $email = Users::select('id', 'name', 'lastname', 'email')->where('id', $order->user_id)->first();
              $order_list = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
                ->select('user_order_list.*', 'user_order_log.web_price as discount_web')
                ->where('user_order_list.order_id', $order->id)->get();

              $order_total = 0;
              $discount_web = 0;
              foreach ($order_list as $key => $value) {
                $order_list[$key]['title_th'] = null;
                $order_list[$key]['subtitle_th'] = null;
                $order_list[$key]['image_th'] = null;
                $order_list[$key]['course_link'] = null;
                if ($value->type == 1) {
                  $course_log = Course::where('id', $value->course_id)->first();
                  if ($course_log) {
                    $order_list[$key]['title_th'] = $course_log->title_th;
                    $order_list[$key]['subtitle_th'] = $course_log->subtitle_th;
                    $order_list[$key]['image_th'] = $course_log->image_th;
                    $order_list[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
                  }
                } else if ($value->type == 2) {
                  $course_log = CourseGroup::where('id', $value->group_id)->first();
                  if ($course_log) {
                    $order_list[$key]['title_th'] = $course_log->title;
                    $order_list[$key]['subtitle_th'] = $course_log->details;
                    $order_list[$key]['image_th'] = $course_log->thumb;
                  }
                }
                if ($value->discount_web != null && $value->discount_web != '' && $value->discount_web != 0 && $value->discount_web != 'null' && $value->discount_web > $discount_web) {
                  $discount_web = $value->discount_web;
                }
                $order_total += $value->total_price;
                $order_list[$key]['total_price'] = number_format($value->total_price, 2);
              }

              $order_total -= $discount_web;

              if ($email) {
                $email_dynamic = EmailDynamic::where('id', 2)->where('status', 1)->first();
                if ($email_dynamic) {
                  $replace_detail = $email_dynamic->details;
                  $replace_detail = str_replace("{{name}}", $email->name, $replace_detail);
                  $replace_detail = str_replace("{{lastname}}", $email->lastname, $replace_detail);
                  $replace_detail = str_replace("{{order_no}}", $order->order_no, $replace_detail);
                  $replace_detail = str_replace("{{order_date}}", $order->created_at, $replace_detail);
                  if ($discount_web == 0) {
                    $replace_detail = str_replace("{{discount_web}}", '', $replace_detail);
                  } else {
                    $replace_detail = str_replace("{{discount_web}}", 'ส่วนลดคูปองเงินสด : -' . number_format($discount_web, 2) . ' THB', $replace_detail);
                  }
                  $replace_detail = str_replace("{{link}}", '<a href="https://csisociety.com/dashboard/history" target="_blank">https://csisociety.com/dashboard/history</a>', $replace_detail);
                  if ($order->payment_type == 1) {
                    $replace_detail = str_replace("{{payment_channel}}", 'QR Code', $replace_detail);
                  } else if ($order->payment_type == 2) {
                    $replace_detail = str_replace("{{payment_channel}}", 'Credit/Debit', $replace_detail);
                  } else {
                    $replace_detail = str_replace("{{payment_channel}}", 'โอนเงิน', $replace_detail);
                  }
                  $replace_detail = str_replace("{{order_total}}", number_format($order_total, 2), $replace_detail);
                  $order_replace = '';
                  foreach ($order_list as $value) {
                    if ($value->type == 1) {
                      $order_replace .=
                        '<tr>
                      <td style="width:30px;"></td>
                      <td style="width: 140px">
                        <a href="' . $value->course_link . '">
                          <img src="' . $value->image_th . '" style="width: 140px;">
                        </a>
                      </td>
                      <td style="padding: 10px;">
                        <a href="' . $value->course_link . '">
                          <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                        </a>
                      </td>
                      <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                      <td style="width:30px;"></td>
                  </tr>';
                    } else {
                      $order_replace .=
                        '<tr>
                      <td style="width:30px;"></td>
                      <td style="width: 140px">
                          <img src="' . $value->image_th . '" style="width: 140px;">
                      </td>
                      <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                      ' . $value->subtitle_th . '
                      </td>
                      <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                      <td style="width:30px;"></td>
                  </tr>';
                    }
                  }
                  $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
                  $obj = new Dummy();
                  $obj['subject'] = $email_dynamic->subject;
                  $obj['code'] = $replace_detail;
                  $obj['email'] = $email->email;
                  $obj['cc'] = [];
                  $obj['bcc'] = [];
                  AppHelper::instance()->mailTemplate($obj);
                }
              }
            }

            $email = Email::where('role', 1)->first();

            if ($email) {
              if ($email->email != null && $email->email != '') {
                $main = explode(',', $email->email);
              } else {
                $main = [];
              }
              if ($email->cc != null && $email->cc != '') {
                $cc = explode(',', $email->cc);
              } else {
                $cc = [];
              }
              if ($email->bcc != null && $email->bcc != '') {
                $bcc = explode(',', $email->bcc);
              } else {
                $bcc = [];
              }
              $email_dynamic = EmailDynamic::where('id', 1)->where('status', 1)->first();
              if ($email_dynamic) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $user->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $user->lastname, $replace_detail);
                $replace_detail = str_replace("{{order_no}}", $order->order_no, $replace_detail);
                $replace_detail = str_replace("{{order_date}}", $order->updated_at, $replace_detail);
                $link_env = env('APP_URL') . '/csisocietyadmin/order/' . $order->id . '/edit';
                $replace_detail = str_replace("{{link}}", '<a href="' . $link_env . '" target="_blank">' . $link_env . '</a>', $replace_detail);
                $obj = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code'] = $replace_detail;
                $obj['email'] = $main;
                $obj['cc'] = $cc;
                $obj['bcc'] = $bcc;
                AppHelper::instance()->mailTemplate($obj);
              }
            }
          }
          if (env('PEAK_ENABLE', false)) {
            $this->createPeakInvoiceAndReceipt($order);
          }
          // Send Line OA notification
          if (env('LINE_ENABLE', false)) {
            try {
              $lineService = new LineService();
              $body_line = "ข้อมูลการแจ้งชำระเงิน\n";
              $body_line .= "เลขที่สั่งซื้อ: " . $order->order_no . "\n";
              $body_line .= "ชื่อ-สกุล: " . $user->name . ' ' . $user->lastname . "\n";
              $body_line .= "เบอร์โทร: " . $user->mobile ?? '' . "\n";
              $body_line .= "อีเมล: " . $user->email . "\n";
              $body_line .= "หมายเหตุ: " . ($order->remark ?? '') . "\n";
              $body_line .= "สลิป: " . ($order->image_slip ?? '');
              $lineService->sendMessage(env('LINE_CHANNEL_ACCESS_TOKEN'), env('LINE_USER_ID'), $body_line);
            } catch (\Exception $e) {
              // Log error but don't fail the order update
              // error_log('Line notification failed: ' . $e->getMessage());
            }
          }
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  private function handlePeakContact($user, $order)
  {
    try {
      $peakService = new PeakService();

      // เตรียมข้อมูลสำหรับสร้าง/อัพเดต contact
      $contactData = [
        'name' => $user->name . ' ' . $user->lastname,
        'type' => 'Customer',
        'address' => $order->address ?? '',
        'tax_number' => $user->tax_number ?? '',
        'sub_district' => $order->subdistrict ?? '',
        'district' => $order->district ?? '',
        'province' => $order->province ?? '',
        'post_code' => $order->postcode ?? '',
        'email' => $user->email,
        'first_name' => $user->name,
        'last_name' => $user->lastname,
        'phone' => $user->tel ?? '',
        'branch_code' => '00000'
      ];

      // ตรวจสอบว่ามี peak_contact_id หรือไม่
      $contactId = $user->peak_contact_id;
      if ($contactId) {
        // มี peak_contact_id แล้ว ให้อัพเดต contact ทุกกรณี
        $updateResult = $peakService->updateContact($contactId, $contactData);
        if ($updateResult['status']) {
          $user->peak_last_sync = Carbon::now();
          $user->save();
        }
      } else {
        // ยังไม่มี peak_contact_id ให้สร้าง contact ใหม่
        $contactResult = $peakService->createContact($contactData);
        if ($contactResult['status']) {
          $contactId = $contactResult['id'];
          $user->peak_contact_id = $contactId;
          $user->peak_sync_status = 2;
          $user->peak_last_sync = Carbon::now();
          $user->save();
        }
      }
    } catch (\Exception $e) {
      // Log error but don't fail the order update
      error_log('Peak contact handling failed: ' . $e->getMessage());
    }
  }

  private function createPeakInvoiceAndReceipt($order)
  {
    try {
      $peakService = new PeakService();

      // ดึงข้อมูล order list
      $orderList = UsersOrderList::where('order_id', $order->id)->first();
      if (!$orderList) {
        return;
      }

      // ดึงข้อมูล user
      $user = Users::find($order->user_id);
      if (!$user) {
        return;
      }

      // ดึงข้อมูล product ตาม type ของ order
      $product = null;
      $productCode = null;
      $productName = '';
      $productPrice = $orderList->price;

      if ($orderList->type == 1) {
        // Course
        $product = Course::find($orderList->course_id);
        if ($product) {
          $productCode = $product->peak_product_code;
          $productName = $product->title_th;
        }
      } elseif ($orderList->type == 3) {
        // Subscription
        $product = Subscription::find($orderList->subscription_id);
        if ($product) {
          $productCode = $product->peak_product_code;
          $productName = $product->title;
        }
      }

      if (!$product) {
        return;
      }

      // เตรียมข้อมูลสำหรับสร้าง contact
      $contactData = [
        'name' => $order->name . ' ' . $order->lastname,
        'type' => 'Customer',
        'address' => $order->address ?? '',
        'tax_number' => $order->iden_no ?? '',
        'sub_district' => $order->subdistrict ?? '',
        'district' => $order->district ?? '',
        'province' => $order->province ?? '',
        'post_code' => $order->postcode ?? '',
        'email' => $order->receive_email ?? $user->email,
        'first_name' => $order->name,
        'last_name' => $order->lastname,
        'phone' => $order->tel ?? '',
        'branch_code' => '00000'
      ];

      // สร้าง contact (หรือใช้ที่มีอยู่)
      $contactId = $user->peak_contact_id;
      if (!$contactId) {
        $contactResult = $peakService->createContact($contactData);
        if ($contactResult['status']) {
          $contactId = $contactResult['id'];
          $user->peak_contact_id = $contactId;
          $user->peak_sync_status = 2;
          $user->peak_last_sync = Carbon::now();
          $user->save();
        } else {
          return;
        }
      } else {
        // Update existing contact with latest order information
        $updateResult = $peakService->updateContact($contactId, $contactData);
        if ($updateResult['status']) {
          $user->peak_last_sync = Carbon::now();
          $user->save();
        }
      }

      // สร้าง product (หรือใช้ที่มีอยู่)
      if (!$productCode) {
        $productData = [
          'name' => $productName,
          'price' => $productPrice,
          'description' => $product->subtitle_th ?? $product->details ?? $productName
        ];

        $productResult = $peakService->createProduct($productData);
        if ($productResult['status']) {
          $productCode = $productResult['code'];
          $product->peak_product_id = $productResult['id'];
          $product->peak_product_code = $productCode;
          $product->peak_sync_status = 2;
          $product->peak_last_sync = Carbon::now();
          $product->save();
        } else {
          return;
        }
      }

      // Check if VAT is required based on course/subscription setting
      $requiresVat = false;
      if ($orderList->type == 1 && $orderList->course_id) {
        // Course
        $course = Course::find($orderList->course_id);
        $requiresVat = $course && $course->requires_vat;
      } elseif ($orderList->type == 3 && $orderList->subscription_id) {
        // Subscription
        $subscription = Subscription::find($orderList->subscription_id);
        $requiresVat = $subscription && $subscription->requires_vat;
      }

      // Use pre-calculated values from order instead of recalculating
      $subtotalBeforeVat = $order->price_before_tax ?? $orderList->total_price;
      $checkoutFieldsPrice = $order->checkout_fields_price ?? 0;
      $vatAmount = $requiresVat ? ($order->vat_amount ?? 0) : 0;
      $totalWithVat = $order->total_price;

      // เตรียมข้อมูลสำหรับสร้าง invoice
      $invoiceData = [
        'contact_id' => $contactId,
        'product_code' => $productCode,
        'quantity' => 1,
        'subtotal' => $subtotalBeforeVat,
        'additional_services_price' => $checkoutFieldsPrice,
        'vat_amount' => $vatAmount,
        'total_with_vat' => $totalWithVat,
        'discount' => $orderList->discount_value ?? 0,
        'created_at' => $order->created_at,
        'is_tax_invoice' => $requiresVat ? 1 : 0
      ];

      // สร้าง invoice
      $invoiceResult = $peakService->createInvoice($invoiceData);
      if ($invoiceResult['status']) {
        $order->peak_invoice_id = $invoiceResult['id'];
        $order->peak_invoice_link = $invoiceResult['invoices_link'] ?? null;

        // เตรียมข้อมูลสำหรับสร้าง receipt
        $receiptData = [
          'contact_id' => $contactId,
          'product_code' => $productCode,
          'quantity' => 1,
          'subtotal' => $subtotalBeforeVat,
          'additional_services_price' => $checkoutFieldsPrice,
          'vat_amount' => $vatAmount,
          'total_with_vat' => $totalWithVat,
          'tax_status' => $requiresVat ? 1 : 0
        ];

        // สร้าง receipt
        $receiptResult = $peakService->createReceipt($receiptData);
        if ($receiptResult['status']) {
          $order->peak_receipt_id = $receiptResult['id'];
          $order->peak_receipt_link = $receiptResult['receipt_link'] ?? null;
          $order->peak_sync_status = 2;
          $order->peak_last_sync = Carbon::now();
        } else {
          $order->peak_sync_status = 3;
          $order->peak_error_message = $receiptResult['message'];
        }
      } else {
        $order->peak_sync_status = 3;
        $order->peak_error_message = $invoiceResult['message'];
      }

      $order->save();
    } catch (\Exception $e) {
      $order->peak_sync_status = 3;
      $order->peak_error_message = $e->getMessage();
      $order->peak_last_sync = Carbon::now();
      $order->save();
    }
  }

  /**
   * Get checkout fields for a specific course
   */
  public function getCheckoutFieldsByCourse($courseId)
  {
    try {
      $course = Course::find($courseId);
      if (!$course) {
        return response()->json([
          'status' => 'error',
          'message' => 'Course not found'
        ], 404);
      }

      $fields = $course->checkoutFields()
        ->active()
        ->orderBy('position')
        ->get()
        ->map(function ($field) {
          return [
            'id' => $field->id,
            'field_name' => $field->field_name,
            'field_label' => $field->field_label,
            'field_type' => $field->field_type,
            'field_options' => $field->field_options,
            'has_pricing' => $field->has_pricing,
            'validation_rules' => $field->validation_rules,
            'placeholder' => $field->placeholder,
            'help_text' => $field->help_text,
            'is_required' => $field->is_required,
            'position' => $field->pivot->position ?? 0
          ];
        });

      return response()->json([
        'status' => 'success',
        'data' => [
          'course_id' => $course->id,
          'course_title' => $course->title_th,
          'fields' => $fields
        ]
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'status' => 'error',
        'message' => 'Failed to retrieve checkout fields'
      ], 500);
    }
  }

  /**
   * Get checkout fields for a specific subscription
   */
  public function getCheckoutFieldsBySubscription($subscriptionId)
  {
    try {
      // Try SubscriptionMain first, then fall back to Subscription
      $subscription = SubscriptionMain::find($subscriptionId);
      $isSubscriptionMain = true;

      if (!$subscription) {
        $subscription = Subscription::find($subscriptionId);
        $isSubscriptionMain = false;
      }

      if (!$subscription) {
        return response()->json([
          'status' => 'error',
          'message' => 'Subscription not found'
        ], 404);
      }

      $fields = $subscription->checkoutFields()
        ->active()
        ->orderBy('position')
        ->get()
        ->map(function ($field) {
          return [
            'id' => $field->id,
            'field_name' => $field->field_name,
            'field_label' => $field->field_label,
            'field_type' => $field->field_type,
            'field_options' => $field->field_options,
            'has_pricing' => $field->has_pricing,
            'validation_rules' => $field->validation_rules,
            'placeholder' => $field->placeholder,
            'help_text' => $field->help_text,
            'is_required' => $field->is_required,
            'position' => $field->pivot->position ?? 0
          ];
        });

      return response()->json([
        'status' => 'success',
        'data' => [
          'subscription_id' => $subscription->id,
          'subscription_title' => $subscription->title ?? $subscription->s_title,
          'subscription_type' => $isSubscriptionMain ? 'main' : 'regular',
          'fields' => $fields
        ]
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'status' => 'error',
        'message' => 'Failed to retrieve checkout fields'
      ], 500);
    }
  }

  /**
   * Calculate total additional price from checkout fields
   */
  public function calculateCheckoutPrice(Request $request)
  {
    try {
      $checkoutFields = $request->input('checkout_fields', []);
      $totalAdditionalPrice = 0;

      // Parse checkout fields if it's a JSON string
      if (is_string($checkoutFields)) {
        $checkoutFields = json_decode($checkoutFields, true);
      }

      if (!is_array($checkoutFields)) {
        return response()->json([
          'status' => 'error',
          'message' => 'Invalid checkout fields format'
        ], 400);
      }

      foreach ($checkoutFields as $fieldData) {
        if (!isset($fieldData['field_id']) || !isset($fieldData['field_value'])) {
          continue;
        }

        $field = CheckoutField::find($fieldData['field_id']);
        if (!$field || !$field->has_pricing) {
          continue;
        }

        $fieldValue = $fieldData['field_value'];

        if ($field->field_type === 'checkbox') {
          // Handle multiple selections
          $values = is_array($fieldValue) ? $fieldValue : explode(',', $fieldValue);
          $totalAdditionalPrice += $field->calculateTotalPrice($values);
        } else {
          // Handle single selection
          $totalAdditionalPrice += $field->getOptionPrice($fieldValue);
        }
      }

      // Calculate with VAT 7%
      $vatAmount = $totalAdditionalPrice * 0.07;
      $totalWithVat = $totalAdditionalPrice + $vatAmount;

      return response()->json([
        'status' => 'success',
        'data' => [
          'additional_price' => $totalAdditionalPrice,
          'vat_amount' => $vatAmount,
          'total_with_vat' => $totalWithVat,
          'additional_price_formatted' => number_format($totalAdditionalPrice, 2),
          'vat_amount_formatted' => number_format($vatAmount, 2),
          'total_with_vat_formatted' => number_format($totalWithVat, 2)
        ]
      ]);
    } catch (\Exception $e) {
      return response()->json([
        'status' => 'error',
        'message' => 'Failed to calculate checkout price'
      ], 500);
    }
  }

  public function addCourseRate(Request $request)
  {
    $response = new Dummy();
    $response['utoken'] = $request->utoken;
    $response['course_id'] = $request->course_id;
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->course_id) && isset($request->rate)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $dup = CourseRate::where('user_id', $user->id)->where('course_id', $request->course_id)->first();
        if ($dup) {
          $dup->rate = $request->rate;
          $dup->save();
        } else {
          $new = new CourseRate();
          $new->user_id = $user->id;
          $new->course_id = $request->course_id;
          $new->rate = $request->rate;
          $new->save();
        }
        $course_rate = CourseRate::where('course_id', $request->course_id)->avg('rate');
        $course_rating = CourseRate::where('course_id', $request->course_id)->count();
        $course = Course::where('id', $request->course_id)->first();
        if ($course) {
          $course->rating = $course_rating;
          $course->rate = $course_rate;
          $course->save();
        }
        $response['rate'] = $course_rate;
        $response['rating'] = $course_rating;
        $response['status'] = 'success';
      } else {
        $response['status'] = 'false';
      }
    } else {
      $response['status'] = 'false';
    }

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function addCourseView(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';
    if (isset($request->course_id)) {
      $model = new CourseView();
      $model->course_id = $request->course_id;
      if (isset($request->utoken)) {
        $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if ($user) {
          $model->user_id = $user->id;
        }
      }
      $model->save();
      $response['status'] = 'success';
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function kpayOrder(Request $request)
  {
    $response = new Dummy();
    $response['utoken'] = $request->utoken;
    $response['order_id'] = $request->order_id;
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->order_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $order = UsersOrderLog::where('id', SimpleEnDeHelper::instance()->decryptString($request->order_id))->first();
        if ($order) {
          $response['order'] = $order;
          $curl = curl_init();

          curl_setopt_array($curl, array(
            CURLOPT_URL => env('PAYMENT_SDK') . '/qr/v2/order?amount=' . $order->total_price . '&currency=THB&description=Lrk&source_type=qr&reference_order=' . $order->order_no . '_' . AppHelper::instance()->generateRandomString(6),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
              'Content-Type: application/json',
              'x-api-key: ' . env('PAYMENT_SKEY')
            ),
          ));

          $resp = curl_exec($curl);
          $resp = json_decode($resp, TRUE);

          curl_close($curl);
          // echo $response;
          $response['response'] = $resp;
          if ($resp['object'] == 'order' && $resp['status'] == 'success') {
            $response['status'] = 'success';
            $order->qr_id = $resp['id'];
          }
          $order->updated_at = Carbon::now();
          $order->save();
          $response['qr_id'] = $order->qr_id;
          $response['status'] = 'success';
        }
      }
    }

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
  public function kpayOrderStamp(Request $request)
  {
    $response = new Dummy();
    $response['utoken'] = $request->utoken;
    $response['order_id'] = $request->order_id;
    $response['status'] = 'false';

    if (isset($request->utoken) && isset($request->order_id)) {
      $user = Users::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
      if ($user) {
        $order = UsersOrderLog::where('id', SimpleEnDeHelper::instance()->decryptString($request->order_id))->first();
        if ($order) {
          $order->updated_at = Carbon::now();
          $order->save();
          $response['status'] = 'success';
        }
      }
    }

    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
}
