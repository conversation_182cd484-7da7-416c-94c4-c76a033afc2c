<?php

namespace App\Http\Controllers\API\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Ya<PERSON>ra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Input;
use Intervention\Image\ImageManagerStatic as Image;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Assessment;
use App\Models\Core\AssessmentChoice;
use App\Models\Core\AssessmentLog;
use App\Models\Core\Course;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\CourseExamLog;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\EmailDynamic;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\UserExamImage;
use App\Models\Core\UserGraduateLog;
use App\Models\Core\Users;
use App\Models\Core\UsersExamLog;
use App\Models\Core\UsersExamPoint;
use App\Models\Core\UsersLearningLog;
use App\Models\Core\UsersPointLog;
use App\Models\User;
use PhpOffice\PhpSpreadsheet\Calculation\Financial\Coupons;

class QuizController extends Controller
{

    public function __construct()
    {

    }

    public function getCourseRank(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['course_id'] = $request->key;
      $response['utoken'] = $request->utoken;
      if(isset($request->utoken)&&isset($request->key)){
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $count_fail = 0;
          $count_pass = 0;
          $count_not = 0;
          $count_do = 0;
          $get_all = CourseLessonLog::join('course_exam','course_exam.lesson_id','course_lesson.id')->select('course_lesson.*')->where('course_exam.course_id',$request->key)->groupby('course_exam.lesson_id')->where('course_exam.status',1)->where('course_lesson.status',1)->get();
          foreach($get_all as $value){
            $check_do = UsersExamPoint::where('user_id',$user->id)->where('lesson_id',$value->id)->where('course_id',$request->key)->first();
            if($check_do){
              $count_do++;
              $check_pass = UsersExamPoint::where('user_id',$user->id)->where('lesson_id',$value->id)->where('course_id',$request->key)->where('status',1)->first();
              if($check_pass){
                $count_pass++;
              }else{
                $count_fail++;
              }
            }else{
              $count_not++;
            }
          }
          $data = new Dummy();
          $data['count_all'] = count($get_all);
          $data['count_fail'] = $count_fail;
          $data['count_pass'] = $count_pass;
          $data['count_not'] = $count_not;
          $data['count_do'] = $count_do;

          $max_point = UsersExamPoint::join('course_exam','course_exam.id','user_exam_point.exam_id')
          ->join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
          ->select('user_exam_point.*',DB::raw('SUM(user_exam_point.max_point) as point'))
          ->where('course_exam.status',1)
          ->where('course_lesson.status',1)
          ->where('user_exam_point.course_id',$request->key)
          ->groupby('user_exam_point.user_id')
          ->orderby('point','desc')->first();
          if($max_point){
            $data['max_point'] = $max_point->point;
          }else{
            $data['max_point'] = 0;
          }
          $min_point = UsersExamPoint::join('course_exam','course_exam.id','user_exam_point.exam_id')
          ->join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
          ->select('user_exam_point.*',DB::raw('SUM(user_exam_point.max_point) as point'))
          ->where('course_exam.status',1)
          ->where('course_lesson.status',1)
          ->where('user_exam_point.course_id',$request->key)
          ->groupby('user_exam_point.user_id')
          ->orderby('point','asc')->first();
          if($min_point){
            $data['min_point'] = $min_point->point;
          }else{
            $data['min_point'] = 0;
          }
          $my_point = UsersExamPoint::join('course_exam','course_exam.id','user_exam_point.exam_id')
          ->join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
          ->select('user_exam_point.*',DB::raw('SUM(user_exam_point.max_point) as point'))
          ->where('course_exam.status',1)
          ->where('course_lesson.status',1)
          ->where('user_exam_point.course_id',$request->key)
          ->where('user_exam_point.user_id',$user->id)
          ->groupby('user_exam_point.user_id')
          ->orderby('point','desc')->first();
          if($my_point){
            $data['my_point'] = $my_point->point;
          }else{
            $data['my_point'] = 0;
          }
          
          $data['my_log'] = UsersExamPoint::join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
          ->join('course_exam','course_exam.id','user_exam_point.exam_id')->where('course_exam.status',1)->where('course_lesson.status',1)
          ->select('user_exam_point.*','course_lesson.title_th')
          ->where('user_exam_point.course_id',$request->key)
          ->where('user_exam_point.user_id',$user->id)->orderby('user_exam_point.id','asc')
          ->get();

          $top_ten = UsersExamPoint::join('user','user.id','user_exam_point.user_id')
          ->join('course_exam','course_exam.id','user_exam_point.exam_id')
          ->join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
          ->where('course_exam.status',1)
          ->where('course_lesson.status',1)
          ->select('user_exam_point.*','user.name','user.lastname',DB::raw('SUM(user_exam_point.max_point) as point'))
          ->where('user_exam_point.course_id',$request->key)->orderby('point','desc')->groupby('user.id')->limit(10)->get();

          // $check_me = 0;
          // foreach($top_ten as $key=>$value){
          //   if($value->user_id==$user->id){
          //     $top_ten[$key]['is_me'] = true;
          //     $check_me++;
          //   }else{
          //     $top_ten[$key]['is_me'] = false;
          //   }
          //   $top_ten[$key]['position'] = $key+1;
          //   $top_ten[$key]['date'] = AppHelper::instance()->convertDateSlash($value->created_at,'en');
          // }
          // $top_ten = json_decode(json_encode($top_ten), true);
          // if($check_me == 0){
            $top_ten = array();
            $top_all = UsersExamPoint::join('user','user.id','user_exam_point.user_id')
            ->join('course_exam','course_exam.id','user_exam_point.exam_id')
            ->join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
            ->where('course_exam.status',1)
            ->where('course_lesson.status',1)
            ->select('user_exam_point.*','user.name','user.lastname',DB::raw('SUM(user_exam_point.max_point) as point'))
            ->where('user_exam_point.course_id',$request->key)->orderby('user_exam_point.point','desc')->groupby('user.id')->get();
            foreach($top_all as $key=>$value){
              if($value->user_id==$user->id){
                $top_dummy = new Dummy();
                $top_dummy=$value;
                $top_dummy['is_me']=true;
                $top_dummy['position']=$key+1;
                array_push($top_ten,$top_dummy);
                break;
              }
            }
          // }
          $data['top_ten'] = $top_ten;

          $response['data'] = $data;
          $response['status'] = 'success';
        }
      }
      

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function getCourseQuiz(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['course_id'] = $request->key;
      $response['utoken'] = $request->utoken;

      if($request->utoken){
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $lesson = CourseLessonLog::where('course_id', $request->key)->select('id', 'title_th as title','exam_time')->get();
          foreach($lesson as $key_lesson=>$val_lesson){
            $first = UsersExamPoint::where('user_id', $user->id)
            ->where('course_id', $request->key)->where('lesson_id', $val_lesson->id)->count();
            if($first == 0){
              $lesson[$key_lesson]['first_time'] = true;
            }else{
              $lesson[$key_lesson]['first_time'] = false;
            }
            $check = UsersExamPoint::join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
            ->where('user_exam_point.user_id', $user->id)
            ->where('user_exam_point.course_id', $request->key)->where('user_exam_point.lesson_id', $val_lesson->id)->where('user_exam_point.check_status', 2)->first();
            if($check){
              $lesson[$key_lesson]['check'] = true;
            }else{
              $lesson[$key_lesson]['check'] = false;
            }
            $check_learning = UsersLearningLog::where('course_id', $request->key)->where('lesson_id', $val_lesson->id)
            ->where('status', 2)->where('user_learning_log.user_id', $user->id)->first();
            
            $limit = UsersExamPoint::where('user_id', $user->id)->where('course_id', $request->key)
            ->where('lesson_id', $val_lesson->id)->count();

            if($check_learning){
              if($val_lesson->exam_time==0){
                $lesson[$key_lesson]['allowed'] = true;
              }else{
                if($limit >= $val_lesson->exam_time){
                  $lesson[$key_lesson]['allowed'] = false;
                }else{
                  $lesson[$key_lesson]['allowed'] = true;
                }
              }
            }else{
              $lesson[$key_lesson]['allowed'] = false;
            }
            $point = UsersExamPoint::where('user_id', $user->id)->where('course_id', $request->key)
            ->where('lesson_id', $val_lesson->id)->orderBy('point', 'desc')->first();
            if($point){
              $lesson[$key_lesson]['last_point'] = $point->point;
              $lesson[$key_lesson]['remark'] = $point->remark;
            }else{
              $lesson[$key_lesson]['last_point'] = 0;
              $lesson[$key_lesson]['remark'] = '';
            }
            $question = CourseExamLog::where('status',1)->where('course_id', $request->key)->where('lesson_id', $val_lesson->id)
            ->select('id', 'question_th as question', 'exam_type as type', 'answer_type')->get();
            foreach($question as $key_question=>$value_question){
              if($value_question->type == 1){
                $question[$key_question]['type'] = 'choice';
              }else if($value_question->type == 2){
                $question[$key_question]['type'] = 'text';
              }else if($value_question->type == 3){
                $question[$key_question]['type'] = 'image';
              }else{
                $question[$key_question]['type'] = 'file';
              }
              $answer = CourseExamAnswer::where('course_id', $request->key)->where('lesson_id', $val_lesson->id)->where('exam_id', $value_question->id)
              ->select('id', 'answer_th as answer')->get();
              $question[$key_question]['answer'] = $answer;
            }
            $lesson[$key_lesson]['question'] = $question;
          }
          $response['data'] = $lesson;
          $response['status'] = 'success';
        }else{
          $lesson = CourseLessonLog::where('course_id', $request->key)->select('id', 'title_th as title')->get();
          foreach($lesson as $key_lesson=>$value_lesson){
            $lesson[$key_lesson]['allowed'] = false;
            $lesson[$key_lesson]['first_time'] = true;
            $lesson[$key_lesson]['check'] = false;
            $lesson[$key_lesson]['last_point'] = 0;
            $lesson[$key_lesson]['remark'] = '';
            $lesson[$key_lesson]['question'] = new Dummy();
          }
          $response['data'] = $lesson;
          $response['status'] = 'success';
        }
      }else{
        $lesson = CourseLessonLog::where('course_id', $request->key)->select('id', 'title_th as title')->get();
        foreach($lesson as $key_lesson=>$value_lesson){
          $lesson[$key_lesson]['allowed'] = false;
          $lesson[$key_lesson]['first_time'] = true;
          $lesson[$key_lesson]['check'] = false;
          $lesson[$key_lesson]['last_point'] = 0;
          $lesson[$key_lesson]['remark'] = '';
          $lesson[$key_lesson]['question'] = new Dummy();
        }
        $response['data'] = $lesson;
        $response['status'] = 'success';
      }

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function getCourseVdo(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['course_allow'] = false;
      $response['course_free'] = false;
      $response['course_id'] = null;

      $course = Course::select('course.*')->where('course.id', $request->key)->where('course.started_date','<=',Carbon::now())->where('course.status', '!=' , 2);
      $course->where(function ($course) {
        $course->where('course.end_date','>=',Carbon::now());
        $course->orWhere('course.end_date',null);
      });

      $course = $course->first();
      
      if($course){
        $response['course_id'] = $course->id;
        if($course->is_promotion==1 && $course->pro_started <= Carbon::now() && ($course->pro_end >= Carbon::now()||$course->pro_period==2)){
          if($course->pro_price==0){
            $response['course_free'] = true;
          }
        }else{
          if($course->price==0){
            $response['course_free'] = true;
          }
        }
        if (isset($request->utoken)) {
          $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
          if ($user){
              $check_sub = AppHelper::instance()->courseSubscription($course,$user);
              if($check_sub[0]&&$check_sub[1]!=''){
                $response['course_free'] = true;
              }
              $array_check = AppHelper::instance()->checkCourseAllow($course,$user);
              $response['course_allow'] = $array_check[0];
              $video = CourseLessonLog::select('shortcut','id', 'link as vdo','link_2 as vdo_2','link_3 as vdo_3', 'type', 'title_th as title', 'image_th as thumb', 'duration', 'price','is_teaser', 'status','is_skip','skip_intro','skip_sec', 'point as lesson_point','lesson_key','exam_time')
              ->where('course_id',$course->id)->where('status', 1)->orderBy('position', 'asc')->get();
              
              foreach($video as $key=>$value){

                $video[$key]['lock'] = AppHelper::instance()->checkVideoAllow($video,$value,$key,$user,$course);

                $learning = UsersLearningLog::where('course_id',$course->id)->where('lesson_id',$value->id)->where('user_id',$user->id)->first();
                if(!$learning){
                  $video[$key]['description'] = "";
                  $video[$key]['learning'] = false;
                  $video[$key]['watching'] = 0;
                  $video[$key]['duration'] = AppHelper::instance()->hoursToSecods($value->duration);
                }else{
                  if($learning->status == 1){
                    $video[$key]['description'] = "";
                    $video[$key]['learning'] = false;
                    $video[$key]['watching'] = AppHelper::instance()->hoursToSecods($learning->watching_time);
                    $video[$key]['duration'] = AppHelper::instance()->hoursToSecods($value->duration);
                  }else{
                    $video[$key]['description'] = "";
                    $video[$key]['learning'] = true;
                    $video[$key]['watching'] = AppHelper::instance()->hoursToSecods($learning->watching_time);
                    $video[$key]['duration'] = AppHelper::instance()->hoursToSecods($value->duration);
                  }
                }
                if($value->price!=0){
                  $video[$key]['quiz']=new Dummy();
                  $video[$key]['quiz']['exam_time'] = $value->exam_time;
                  $video[$key]['quiz']['id'] = $value->id;
                  $video[$key]['quiz']['title'] = $value->title;
                  $first = UsersExamPoint::where('user_id', $user->id)
                  ->where('course_id', $request->key)->where('lesson_id', $value->id)->count();
                  if($first == 0){
                    $video[$key]['quiz']['first_time'] = true;
                  }else{
                    $video[$key]['quiz']['first_time'] = false;
                  }
                  $check = UsersExamPoint::join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
                  ->where('user_exam_point.user_id', $user->id)
                  ->where('user_exam_point.course_id', $request->key)->where('user_exam_point.lesson_id', $value->id)->where('user_exam_point.check_status', 2)->first();
                  if($check){
                    $video[$key]['quiz']['check'] = true;
                  }else{
                    $video[$key]['quiz']['check'] = false;
                  }
                  $check_learning = UsersLearningLog::where('course_id', $request->key)->where('lesson_id', $value->id)
                  ->where('status', 2)->where('user_learning_log.user_id', $user->id)->first();
                  
                  $limit = UsersExamPoint::where('user_id', $user->id)->where('course_id', $request->key)
                  ->where('lesson_id', $value->id)->count();

                  if($value->shortcut==2){
                    if($check_learning){
                      if($value->exam_time==0){
                        $video[$key]['quiz']['allowed'] = true;
                      }else{
                        if($limit >= $value->exam_time){
                          $video[$key]['quiz']['allowed'] = false;
                        }else{
                          $video[$key]['quiz']['allowed'] = true;
                        }
                      }
                    }else{
                      $video[$key]['quiz']['allowed'] = false;
                    }
                  }else{
                    $video[$key]['quiz']['allowed'] = true;
                  }
                  
                  $point = UsersExamPoint::where('user_id', $user->id)->where('course_id', $request->key)
                  ->where('lesson_id', $value->id)->orderBy('point', 'desc')->first();
                  if($point){
                    $video[$key]['quiz']['last_point'] = $point->point;
                    $video[$key]['quiz']['remark'] = $point->remark;
                  }else{
                    $video[$key]['quiz']['last_point'] = 0;
                    $video[$key]['quiz']['remark'] = '';
                  }
                  $question = CourseExamLog::where('status',1)->where('course_id', $request->key)->where('lesson_id', $value->id)
                  ->select('id', 'question_th as question', 'exam_type as type', 'answer_type')->get();
                  foreach($question as $key_question=>$value_question){
                    if($value_question->type == 1){
                      $question[$key_question]['type'] = 'choice';
                    }else if($value_question->type == 2){
                      $question[$key_question]['type'] = 'text';
                    }else if($value_question->type == 3){
                      $question[$key_question]['type'] = 'image';
                    }else{
                      $question[$key_question]['type'] = 'file';
                    }
                    $answer = CourseExamAnswer::where('course_id', $request->key)->where('lesson_id', $value->id)->where('exam_id', $value_question->id)
                    ->select('id', 'answer_th as answer')->get();
                    $question[$key_question]['answer'] = $answer;
                  }
                  $video[$key]['quiz']['question'] = $question;
                }else{
                  $video[$key]['quiz']=new Dummy();
                  $video[$key]['quiz']['exam_time'] = 0;
                  $video[$key]['quiz']['id'] = null;
                  $video[$key]['quiz']['title'] = '';
                  $video[$key]['quiz']['allowed'] = false;
                  $video[$key]['quiz']['first_time'] = true;
                  $video[$key]['quiz']['check'] = false;
                  $video[$key]['quiz']['last_point'] = 0;
                  $video[$key]['quiz']['remark'] = '';
                  $video[$key]['quiz']['question'] = new Dummy();
                }
              }
          }else{
            $video = CourseLessonLog::select('id', 'link as vdo','link_2 as vdo_2','link_3 as vdo_3', 'type', 'title_th as title', 'image_th as thumb', 'duration', 'price','is_teaser', 'status','is_skip','skip_intro','skip_sec', 'point as lesson_point','lesson_key')
            ->where('course_id',$course->id)->where('status', 1)->orderBy('position', 'asc')->get();
            foreach($video as $key=>$value){
              if($value->price != 0){
                $video[$key]['lock'] = true;
              }else{
                $video[$key]['lock'] = false;
              }
              $video[$key]['description'] = "";
              $video[$key]['learning'] = false;
              $video[$key]['watching'] = 0;
              $video[$key]['duration'] = AppHelper::instance()->hoursToSecods($value->duration);

              $video[$key]['quiz']=new Dummy();
              $video[$key]['quiz']['exam_time'] = 0;
              $video[$key]['quiz']['id'] = null;
              $video[$key]['quiz']['title'] = '';
              $video[$key]['quiz']['allowed'] = false;
              $video[$key]['quiz']['first_time'] = true;
              $video[$key]['quiz']['check'] = false;
              $video[$key]['quiz']['last_point'] = 0;
              $video[$key]['quiz']['remark'] = '';
              $video[$key]['quiz']['question'] = new Dummy();
            }
          }
        }else{
          $video = CourseLessonLog::select('id', 'link as vdo','link_2 as vdo_2','link_3 as vdo_3', 'type', 'title_th as title', 'image_th as thumb', 'duration', 'price','is_teaser', 'status','is_skip','skip_intro','skip_sec', 'point as lesson_point','lesson_key')
          ->where('course_id',$course->id)->where('status', 1)->orderBy('position', 'asc')->get();
          foreach($video as $key=>$value){
            if($value->price != 0){
              $video[$key]['lock'] = true;
            }else{
              $video[$key]['lock'] = false;
            }
            $video[$key]['description'] = "";
            $video[$key]['learning'] = false;
            $video[$key]['watching'] = 0;
            $video[$key]['duration'] = AppHelper::instance()->hoursToSecods($value->duration);

            $video[$key]['quiz']=new Dummy();
            $video[$key]['quiz']['exam_time'] = 0;
            $video[$key]['quiz']['id'] = null;
            $video[$key]['quiz']['title'] = '';
            $video[$key]['quiz']['allowed'] = false;
            $video[$key]['quiz']['first_time'] = true;
            $video[$key]['quiz']['check'] = false;
            $video[$key]['quiz']['last_point'] = 0;
            $video[$key]['quiz']['remark'] = '';
            $video[$key]['quiz']['question'] = new Dummy();
          }
        }

        foreach($video as $key=>$value){
          if($value->duration!=null&&$value->duration!=''&&$value->duration!=0){
            if($value->duration>=$value->watching){
              $video[$key]['played_percent'] = $value->watching / $value->duration * 100;
            }else{
              $video[$key]['played_percent'] = 100;
            }
          }else{
            $video[$key]['played_percent'] = 0;
          }
          $link_arr = array();
          if($value->vdo!=null&&$value->vdo!=''){
            array_push($link_arr,$value->vdo);
          }
          if($value->vdo_2!=null&&$value->vdo_2!=''){
            array_push($link_arr,$value->vdo_2);
          }
          if($value->vdo_3!=null&&$value->vdo_3!=''){
            array_push($link_arr,$value->vdo_3);
          }
          if(count($link_arr)>0){
            $random_keys=array_rand($link_arr,1);
            $video[$key]['vdo'] = $link_arr[$random_keys];
          }
        }
        $response['data'] = $video;
        $response['status'] = 'success';
      }else{
      $response['status'] = 'false';
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public function addQuiz(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      $response['point'] = 0;
      // $response['utoken'] = $request->utoken;
      // $response['answer_data'] = json_decode($request->answer_data);

      if(isset($request->utoken)&&isset($request->course_id)){
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $choice = 0;
          $text = 0;
          $file = 0;
          $image = 0;
          $lesson_id = '';
          $exam_id = '';
          $round = '';
          $total = 0;
          $ans_data = json_decode($request->answer_data);
          foreach($ans_data as $key=>$value){
            if($value->question_type == 'choice'){
              $choice++;
              $exam_log = UsersExamLog::where('user_id',$user->id)->where('course_id',$request->course_id)
              ->where('lesson_id',$value->lesson_id)->where('exam_id',$value->question_id)->count();
              $add = new UsersExamLog();
              $add->user_id = $user->id;
              $add->round = $exam_log+1;
              $add->course_id = $request->course_id;
              $add->lesson_id = $value->lesson_id;
              $add->exam_id = $value->question_id;
              $add->answer = $value->answer;
              $check = CourseExamAnswer::join('course_exam', 'course_exam.id', 'course_exam_answer.exam_id')
              ->where('course_exam_answer.course_id', $add->course_id)->where('course_exam_answer.lesson_id', $add->lesson_id)
              ->where('course_exam_answer.exam_id', $add->exam_id)->where('course_exam_answer.id', $add->answer)->where('is_right', 1)
              ->select('course_exam_answer.is_right', 'course_exam.point')->first();
              if($check){
                $add->is_right = 1;
                $add->point = $check->point;
                $total += $check->point;
                $add->save();
              }else{
                $add->is_right = 2;
                $add->point = 0;
                $add->save();
              }
              $lesson_id = $add->lesson_id;
              $exam_id = $add->exam_id;
              $round = $add->round;
            }else if($value->question_type == 'text'){
              $text++;
              $exam_log = UsersExamLog::where('user_id',$user->id)->where('course_id',$request->course_id)
              ->where('lesson_id',$value->lesson_id)->where('exam_id',$value->question_id)->count();
              $add = new UsersExamLog();
              $add->user_id = $user->id;
              $add->round = $exam_log+1;
              $add->course_id = $request->course_id;
              $add->lesson_id = $value->lesson_id;
              $add->exam_id = $value->question_id;
              $add->answer_text = $value->answer;
              $add->save();
              $lesson_id = $add->lesson_id;
              $exam_id = $add->exam_id;
              $round = $add->round;
            }else if($value->question_type == 'file'){
              $file++;
              $exam_log = UsersExamLog::where('user_id',$user->id)->where('course_id',$request->course_id)
              ->where('lesson_id',$value->lesson_id)->where('exam_id',$value->question_id)->count();
              $add = new UsersExamLog();
              $add->user_id = $user->id;
              $add->round = $exam_log+1;
              $add->course_id = $request->course_id;
              $add->lesson_id = $value->lesson_id;
              $add->exam_id = $value->question_id;
              $add->file = $value->answer;
              $add->save();
              $lesson_id = $add->lesson_id;
              $exam_id = $add->exam_id;
              $round = $add->round;
            }else{
              $image++;
              $exam_log = UsersExamLog::where('user_id',$user->id)->where('course_id',$request->course_id)
              ->where('lesson_id',$value->lesson_id)->where('exam_id',$value->question_id)->count();
              $add = new UsersExamLog();
              $add->user_id = $user->id;
              $add->round = $exam_log+1;
              $add->course_id = $request->course_id;
              $add->lesson_id = $value->lesson_id;
              $add->exam_id = $value->question_id;
              $add->save();
              $lesson_id = $add->lesson_id;
              $exam_id = $add->exam_id;
              $round = $add->round;
              foreach($value->log as $key_img=>$value_img){
                $log_img = new UserExamImage();
                $log_img->exam_log_id = $add->id;
                $log_img->image = $value_img->link;
                $log_img->save();
              }
            }
          }
          if($lesson_id != '' && $round != ''){
            if($text == 0 && $file == 0 && $image == 0){
              $point = new UsersExamPoint();
              $point->round = $round;
              $point->user_id = $user->id;
              $point->course_id = $request->course_id;
              $point->lesson_id = $lesson_id;
              $point->exam_id = $exam_id;
              $point->point = $total;
              $check_pass = CourseLessonLog::where('id',$lesson_id)->first();
              if($check_pass){
                if($check_pass->point<=$total){
                  $point->status = 1;
                }else{
                  $point->status = 2;
                }
              }else{
                $point->status = 2;
              }
              $point->check_status = 1;
              $point->save();
              $max_point = UsersExamPoint::where('user_id',$user->id)->where('course_id',$request->course_id)->where('lesson_id',$lesson_id)->orderby('point','desc')->first();
              if($max_point){
                $point->max_point = $max_point->point;
                $point->save();
              }
              $point_zero = UsersExamPoint::where('id','!=',$point->id)->where('user_id',$user->id)->where('course_id',$request->course_id)->where('lesson_id',$lesson_id)->get();
              foreach($point_zero as $val){
                $val->max_point=0;
                $val->save();
              }
              if($point->status==1){
                $response['status'] = 'pass';
              }else{
                $response['status'] = 'fail';
              }
              $response['point'] = $total;
            }else{
              $point = new UsersExamPoint();
              $point->round = $round;
              $point->user_id = $user->id;
              $point->course_id = $request->course_id;
              $point->lesson_id = $lesson_id;
              $point->exam_id = $exam_id;
              $point->point = null;
              $point->status = 2;
              $point->check_status = 2;
              $point->save();
              $response['status'] = 'check';
            }
            $count_lesson = CourseLessonLog::where('is_teaser','!=',0)->where('status',1)->where('course_id',$request->course_id)->count();
            $count_learn = UsersLearningLog::join('course_lesson','course_lesson.id','user_learning_log.lesson_id')->where('course_lesson.is_teaser','!=',0)
            ->where('course_lesson.status',1)
            ->where('course_lesson.course_id',$request->course_id)
            ->where('user_learning_log.user_id', $user->id)
            ->where('user_learning_log.course_id', $request->course_id)->where('user_learning_log.status',2)->count();
            $count_exam = count(UsersExamPoint::join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
            ->join('course_exam','course_exam.lesson_id','course_lesson.id')
            ->where('course_exam.course_id', $request->course_id)->where('course_exam.status',1)->where('course_lesson.status',1)
            ->where('user_exam_point.user_id', $user->id)->where('user_exam_point.course_id', $request->course_id)
            ->where('user_exam_point.status',1)->groupby('user_exam_point.lesson_id')->get());
            $count_exam_all = count(CourseLessonLog::join('course_exam','course_exam.lesson_id','course_lesson.id')->where('course_exam.course_id', $request->course_id)->groupby('course_exam.lesson_id')->where('course_exam.status',1)->where('course_lesson.status',1)->get());
            if(($count_learn==$count_lesson)&&($count_exam==$count_exam_all)&&$count_learn!=0&&$count_lesson!=0){
              $graduate = UserGraduateLog::where('user_id',$user->id)->where('course_id',$request->course_id)->first();
              if(!$graduate){
                $graduate = new UserGraduateLog();
                $graduate->user_id = $user->id;
                $graduate->course_id = $request->course_id;
                $graduate->save();
                AppHelper::instance()->addCertificateLog($graduate);
                $course_point = Course::where('id',$request->course_id)->first();
                $user_point = User::where('id',$user->id)->first();
                if($course_point&&$user_point){
                  if($course_point->receive_point > 0){
                    $check_get_point = UsersPointLog::where('user_id',$user_point->id)->where('point_type',1)->where('course_id',$course_point->id)->first();
                    if(!$check_get_point){
                      $point_log = new UsersPointLog();
                      $point_log->user_id = $user_point->id;
                      $point_log->point_type = 1;
                      $point_log->course_id = $course_point->id;
                      $point_log->point = $course_point->receive_point;
                      $point_log->save();

                      $user_point->point = $user_point->point+$course_point->receive_point;
                      $user_point->save();
                      $email_dynamic = EmailDynamic::where('id',6)->where('status',1)->first();
                      if($email_dynamic){
                        $replace_detail = $email_dynamic->details;
                        $replace_detail = str_replace("{{name}}",$user_point->name,$replace_detail);
                        $replace_detail = str_replace("{{lastname}}",$user_point->lastname,$replace_detail);
                        $replace_detail = str_replace("{{title}}",$course_point->title_th,$replace_detail);
                        $replace_detail = str_replace("{{point}}",number_format($course_point->receive_point),$replace_detail);
                        $obj = new Dummy();
                        $obj['subject'] = $email_dynamic->subject;
                        $obj['code'] = $replace_detail;
                        $obj['email'] = $user_point->email;
                        $obj['cc'] = [];
                        $obj['bcc'] = [];
                        AppHelper::instance()->mailTemplate($obj);
                      }

                      $noti_log = new NotiAutoLog();
                      $noti_log->user_id = $user_point->id;
                      $noti_log->image = '/icontest.png';
                      $noti_log->title = 'ยินดีด้วย! คุณได้รับคะแนน : '.number_format($course_point->receive_point).' คะแนน';
                      $noti_log->description = 'จากการเรียน : '.$course_point->title_th;
                      $noti_log->link = '';
                      $noti_log->save();
                    }
                  }
                }
              }
            }
            $lesson_data = CourseLessonLog::where('id',$lesson_id)->first();
            if($lesson_data){
              $email_dynamic = EmailDynamic::where('id',4)->where('status',1)->first();
              if($email_dynamic){
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}",$user->name,$replace_detail);
                $replace_detail = str_replace("{{lastname}}",$user->lastname,$replace_detail);
                $replace_detail = str_replace("{{title}}",$lesson_data->title_th,$replace_detail);
                $replace_detail = str_replace("{{date}}",$point->created_at,$replace_detail);
                $link_env = env('APP_URL').'/csisocietyadmin/exam/'.$point->course_id.'/'.$point->lesson_id.'/'.$user->id.'/'.$point->round.'/edit';
                $replace_detail = str_replace("{{link}}",'<a href="'.$link_env.'" target="_blank">'.$link_env.'</a>',$replace_detail);
                $obj = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code'] = $replace_detail;
                $obj['email'] = $lesson_data->email;
                $obj['cc'] = [];
                $obj['bcc'] = [];
                AppHelper::instance()->mailTemplate($obj);
              }
            }
          }
        }
      }


      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function check(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      if(isset($request->data)){
        $data = json_decode($request->data);
        $remark = $request->remark;
        $point_total = 0;
        $course_id = '';
        $lesson_id = '';
        $user_id = '';
        $round = '';
        foreach($data as $key=>$value){
          $exam_log = UsersExamLog::where('id',$value->log_id)->where('course_id',$value->course_id)->where('lesson_id',$value->lesson_id)
          ->where('user_id',$value->user_id)->where('round',$value->round)->first();
          if($exam_log){
            $course_id = $value->course_id;
            $lesson_id = $value->lesson_id;
            $user_id = $value->user_id;
            $round = $value->round;
            $exam_log->point = isset($value->value) ? AppHelper::instance()->cleanInput($value->value) : 0;
            
            $exam_log->save();
          }
        }
        
        if($course_id!=''&&$lesson_id!=''&&$user_id!=''&&$round!=''){
          $all_log = UsersExamLog::where('course_id',$course_id)->where('lesson_id',$lesson_id)
          ->where('user_id',$user_id)->where('round',$round)->get();
          foreach($all_log as $val){
            $point_total += $val->point;
          }
          $exam_point = UsersExamPoint::where('round',$round)->where('course_id',$course_id)->where('lesson_id',$lesson_id)
          ->where('user_id',$user_id)->first();
          if($exam_point){
            $exam_point->point = $point_total;
            $check_pass = CourseLessonLog::where('id',$lesson_id)->first();
            if($check_pass){
              if($check_pass->point<=$point_total){
                $exam_point->status = 1;
              }else{
                $exam_point->status = 2;
              }
            }else{
              $exam_point->status = 2;
            }
            $exam_point->check_status = 1;
            $exam_point->remark = isset($remark) ? AppHelper::instance()->cleanInputBr($remark) : null;
            $exam_point->save();
            $max_point = UsersExamPoint::where('user_id',$user_id)->where('course_id',$course_id)->where('lesson_id',$lesson_id)->orderby('point','desc')->first();
            if($max_point){
              $exam_point->max_point = $max_point->point;
              $exam_point->save();
            }
            $point_zero = UsersExamPoint::where('id','!=',$exam_point->id)->where('user_id',$user_id)->where('course_id',$course_id)->where('lesson_id',$lesson_id)->get();
            foreach($point_zero as $val){
              $val->max_point=0;
              $val->save();
            }
          }
          $count_lesson = CourseLessonLog::where('is_teaser','!=',0)->where('status',1)->where('course_id',$course_id)->count();
          $count_learn = UsersLearningLog::join('course_lesson','course_lesson.id','user_learning_log.lesson_id')->where('course_lesson.is_teaser','!=',0)
          ->where('course_lesson.status',1)
          ->where('course_lesson.course_id',$course_id)
          ->where('user_learning_log.user_id', $user_id)
          ->where('user_learning_log.course_id', $course_id)->where('user_learning_log.status',2)->count();
          $count_exam = count(UsersExamPoint::join('course_lesson','course_lesson.id','user_exam_point.lesson_id')
          ->join('course_exam','course_exam.lesson_id','course_lesson.id')
          ->where('course_exam.course_id', $course_id)->where('course_exam.status',1)->where('course_lesson.status',1)
          ->where('user_exam_point.user_id', $user_id)->where('user_exam_point.course_id', $course_id)
          ->where('user_exam_point.status',1)->groupby('user_exam_point.lesson_id')->get());
          $count_exam_all = count(CourseLessonLog::join('course_exam','course_exam.lesson_id','course_lesson.id')->where('course_exam.course_id', $course_id)->groupby('course_exam.lesson_id')->where('course_exam.status',1)->where('course_lesson.status',1)->get());
          if(($count_learn==$count_lesson)&&($count_exam==$count_exam_all)&&$count_learn!=0&&$count_lesson!=0){
            $graduate = UserGraduateLog::where('user_id',$user_id)->where('course_id',$course_id)->first();
            if(!$graduate){
              $graduate = new UserGraduateLog();
              $graduate->user_id = $user_id;
              $graduate->course_id = $course_id;
              $graduate->save();
              AppHelper::instance()->addCertificateLog($graduate);
              $course_point = Course::where('id',$course_id)->first();
              $user_point = User::where('id',$user_id)->first();
              if($course_point&&$user_point){
                if($course_point->receive_point > 0){
                  $check_get_point = UsersPointLog::where('user_id',$user_point->id)->where('point_type',1)->where('course_id',$course_point->id)->first();
                  if(!$check_get_point){
                    $point_log = new UsersPointLog();
                    $point_log->user_id = $user_point->id;
                    $point_log->point_type = 1;
                    $point_log->course_id = $course_point->id;
                    $point_log->point = $course_point->receive_point;
                    $point_log->save();

                    $user_point->point = $user_point->point+$course_point->receive_point;
                    $user_point->save();
                    $email_dynamic = EmailDynamic::where('id',6)->where('status',1)->first();
                    if($email_dynamic){
                      $replace_detail = $email_dynamic->details;
                      $replace_detail = str_replace("{{name}}",$user_point->name,$replace_detail);
                      $replace_detail = str_replace("{{lastname}}",$user_point->lastname,$replace_detail);
                      $replace_detail = str_replace("{{title}}",$course_point->title_th,$replace_detail);
                      $replace_detail = str_replace("{{point}}",number_format($course_point->receive_point),$replace_detail);
                      $obj = new Dummy();
                      $obj['subject'] = $email_dynamic->subject;
                      $obj['code'] = $replace_detail;
                      $obj['email'] = $user_point->email;
                      $obj['cc'] = [];
                      $obj['bcc'] = [];
                      AppHelper::instance()->mailTemplate($obj);
                    }

                    $noti_log = new NotiAutoLog();
                    $noti_log->user_id = $user_point->id;
                    $noti_log->image = '/icontest.png';
                    $noti_log->title = 'ยินดีด้วย! คุณได้รับคะแนน : '.number_format($course_point->receive_point).' คะแนน';
                    $noti_log->description = 'จากการเรียน : '.$course_point->title_th;
                    $noti_log->link = '';
                    $noti_log->save();
                  }
                }
              }
            }
          }
          $user = User::where('id',$user_id)->first();
          $lesson_data = CourseLessonLog::where('id',$lesson_id)->first();
          $course = Course::where('id',$course_id)->first();
          if($lesson_data && $user && $exam_point && $course){
            $email_dynamic = EmailDynamic::where('id',5)->where('status',1)->first();
            if($email_dynamic){
              $replace_detail = $email_dynamic->details;
              $replace_detail = str_replace("{{name}}",$user->name,$replace_detail);
              $replace_detail = str_replace("{{lastname}}",$user->lastname,$replace_detail);
              $replace_detail = str_replace("{{title}}",$lesson_data->title_th,$replace_detail);
              $replace_detail = str_replace("{{date}}",$exam_point->updated_at,$replace_detail);
              $link_env = env('APP_NEXT_URL').'/course/'.$course->slug;
              $replace_detail = str_replace("{{link}}",'<a href="'.$link_env.'" target="_blank">'.$link_env.'</a>',$replace_detail);
              $obj = new Dummy();
              $obj['subject'] = $email_dynamic->subject;
              $obj['code'] = $replace_detail;
              $obj['email'] = $user->email;
              $obj['cc'] = [];
              $obj['bcc'] = [];
              AppHelper::instance()->mailTemplate($obj);
            }

            $noti_log = new NotiAutoLog();
            $noti_log->user_id = $user->id;
            $noti_log->image = '/icontest.png';
            $noti_log->title = 'ระบบได้ตรวจสอบผลสอบของคุณแล้ว';
            $noti_log->description = 'ตรวจสอบผลสอบได้ที่นี่';
            $noti_log->link = env('APP_NEXT_URL').'/course/'.$course->slug;
            $noti_log->save();
          }
        }
        $response['status'] = 'success';
        $response['data'] = $data;
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function getAssessment(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'success';
      $assessment = Assessment::where('status',1)->orderby('position','asc')->get();
      foreach($assessment as $key=>$value){
        $assessment[$key]['choice'] = AssessmentChoice::where('status',1)->where('assessment_id',$value->id)->orderby('position','asc')->get();
      }
      $response['data'] = $assessment;

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    public function addAssessment(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      if(isset($request->utoken)&&isset($request->course_id)&&isset($request->answer_data)){
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($request->utoken))->first();
        if($user){
          $course = Course::where('id',$request->course_id)->first();
          if($course){
            $check = AssessmentLog::where('user_id',$user->id)->where('course_id',$course->id)->first();
            if(!$check){
              $ans_data = json_decode($request->answer_data);
              foreach($ans_data as $key=>$value){
                $add = new AssessmentLog();
                $add->assessment_id = $value->question_id;
                $add->user_id = $user->id;
                $add->course_id = $course->id;
                $add->type = $value->question_type;
                if($value->question_type == 1){
                  $add->choice = $value->answer;
                }else if($value->question_type == 3){
                  $point = AssessmentChoice::where('id',$value->answer)->first();
                  if($point){
                    $add->point = $point->point;
                  }
                  $add->choice = $value->answer;
                }else{
                  $add->input = $value->answer;
                }
                $add->save();
              }
              $response['status'] = 'success';
            }
          }
        }
      }

      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
}
