<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Helpers\AppHelper;
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{


    
    use AuthenticatesUsers{}

    protected $username;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // $this->middleware('guest:admin')->except('logout');
        // $this->middleware('guest:admin')->except('logout_corp');
        // $this->username = 'username';
    }

    public function username()
    {
        return $this->username;
    }
    
    public function logout(Request $request)
    {
      Auth::guard('admin')->logout();
      Auth::guard('user')->logout();
      Session::flush();
      return redirect(route('admin'));
    }

    public function showLoginForm()
    {
        return view('admin.auth.login', ['url' => 'admin']);
    }

    protected function validatorRecatcha(array $data)
    {
        return Validator::make($data, [
            'g-recaptcha-response' => 'recaptcha',
        ]);
    }

    public function login(Request $request)
    {
        if(!$this->validatorRecatcha($request->all())->fails()){
            $this->validate($request, [
                'username'   => 'required',
                'password' => 'required|min:4'
            ]);

            //  if (Auth::guard('admin')->attempt(['username' => $request->username, 'password' => $request->password], $request->get('remember'))) {
            //      return redirect()->intended(route('admin'));
            //  }

            $admin = Admin::where('username', $request->username)
            ->where('status', '1')
            // ->where('level', '!=', 55)
            ->first();
            if($admin){
                if(password_verify($request->password,$admin->password)){
                    Auth::guard('admin')->loginUsingId($admin->id,true);
                    return redirect()->intended(route('admin'));
                }
            }

            return back()->withInput($request->only('username', 'remember'))->withErrors(['username' => 'ไม่สามารถเข้าสู่ระบบได้']);
        }
    }
    
    public function logout_corp(Request $request)
    {
      Auth::guard('admin')->logout();
      Auth::guard('user')->logout();
      Session::flush();
      return redirect(route('corp'));
    }

    public function showLoginForm_corp()
    {
        return view('admin.auth.login_corp', ['url' => 'corp']);
    }

    protected function validatorRecatcha_corp(array $data)
    {
        return Validator::make($data, [
            'g-recaptcha-response' => 'recaptcha',
        ]);
    }

    public function login_corp(Request $request)
    {
        // echo 'login_corp';
        if(!$this->validatorRecatcha($request->all())->fails()){
            $this->validate($request, [
                'username'   => 'required',
                'password' => 'required|min:4'
            ]);

            $admin = Admin::where('username', $request->username)
            ->where('company_id', '!=', null)
            ->where('status', '1')
            ->first();
            if($admin){
                if(password_verify($request->password,$admin->password)){
                    Auth::guard('admin')->loginUsingId($admin->id,true);
                    return redirect('/Corporation');
                }
            }

            return back()->withInput($request->only('username', 'remember'))->withErrors(['username' => 'ไม่สามารถเข้าสู่ระบบได้']);
        }
    }
}
