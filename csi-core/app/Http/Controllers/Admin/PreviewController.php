<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\Admin;

use App\Helpers\AppHelper;
use App\Helpers\InputHelper;

use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;


use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema as Schema;
use Illuminate\Database\Migrations\Migration;

use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;

use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\Dummy;

class PreviewController extends Controller
{

    public function __construct()
    {

    }

    public  function  index(Request $request){
      return view('Admin/page_builder');
    }

}
