<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\SimpleEnDeHelper;

use App\Helpers\AppHelper;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\DynamicCert;
use App\Models\Core\DynamicCertGroup;
use App\Models\Core\GlobalCert;
use App\Models\Core\ReportDownloadCertLog;
use App\Models\Core\UsersCertLog;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\User;
use PDF;

class CertificateController extends Controller
{

    public function __construct()
    {

    }
    public  function  certificate(Request $request){
      $decrypt = explode('_type_',SimpleEnDeHelper::instance()->decryptString($request->key));
      $lang = 'th';
      if(isset($request->lang)){
        if($request->lang=='en'){
          $lang = 'en';
        }
      }
      if(count($decrypt)!=5){
        $pdf = PDF::loadView('pdf.certificate');
        return $pdf->stream();
      }
      $user = User::where('id',$decrypt[2])->first();
      if(!$user || ($decrypt[1]!='course'&&$decrypt[1]!='group'&&$decrypt[1]!='global')){
        $pdf = PDF::loadView('pdf.certificate');
        return $pdf->stream();
      }
      if($decrypt[1]=='global'){
        $course_data = Course::where('id',$decrypt[4])->first();
      }
      if($decrypt[1]=='course'){
        $certificate = DynamicCert::where('id',$decrypt[0])->first();
      }else if($decrypt[1]=='group'){
        $certificate = DynamicCertGroup::where('id',$decrypt[0])->first();
      }else{
        $certificate = GlobalCert::where('id',$decrypt[0])->first();
      }
      if(!$certificate){
        $pdf = PDF::loadView('pdf.certificate');
        return $pdf->stream();
      }

      $data = new Dummy();
      if($lang=='th'){
        if($user->cert_name_th!=null&&$user->cert_name_th!=''&&$user->cert_name_th!='null'){
          $data['user'] = $user->cert_name_th; 
        }else{
          $data['user'] = $user->name.' '.$user->lastname; 
        }

        if($decrypt[1]=='course'){
          $data['name'] = $certificate->couse_name; 
        }else if($decrypt[1]=='group'){
          $data['name'] = $certificate->series_name; 
        }else{
          if($course_data){
            $data['name'] = $course_data->title_th; 
          }else{
            $data['name'] = '';
          }
        }
      }else{
        if($user->cert_name_en!=null&&$user->cert_name_en!=''&&$user->cert_name_en!='null'){
          $data['user'] = $user->cert_name_en; 
        }else{
          $data['user'] = $user->name.' '.$user->lastname; 
        }

        if($decrypt[1]=='course'){
          if($certificate->course_name_en!=null&&$certificate->course_name_en!=''&&$certificate->course_name_en!='null'){
            $data['name'] = $certificate->course_name_en;
          }else{
            $data['name'] = $certificate->couse_name;
          }
        }else if($decrypt[1]=='group'){
          if($certificate->series_name_en!=null&&$certificate->series_name_en!=''&&$certificate->series_name_en!='null'){
            $data['name'] = $certificate->series_name_en;
          }else{
            $data['name'] = $certificate->series_name;
          }
        }else{
          if($course_data){
            if($course_data->title_en!=null&&$course_data->title_en!=''&&$course_data->title_en!='null'){
              $data['name'] = $course_data->title_en;
            }else{
              $data['name'] = $course_data->title_th;
            }
          }else{
            $data['name'] = '';
          }
        }
      }
      $data['cme'] = 0;
      if($decrypt[1]=='course'){
        $cme_check = Course::where('id',$certificate->course_id)->first();
        if($cme_check&&$cme_check->is_cme==1&&$cme_check->receive_point!=0){
          $data['cme'] = $cme_check->receive_point;
        }
      }else if($decrypt[1]=='global'){
        if($course_data&&$course_data->is_cme==1&&$course_data->receive_point!=0){
          $data['cme'] = $course_data->receive_point;
        }
      }
      if($lang=='th'){
        $data['date'] = AppHelper::instance()->DateThaiFull($decrypt[3]);
      }else{
        $data['date'] = AppHelper::instance()->DateEngFull($decrypt[3]);
      }
      $data['bg'] = $certificate->bg_color; 
      $data['bg_en'] = $certificate->bg_color_en; 
      $data['director'] = $certificate->course_director; 
      $data['director_en'] = $certificate->course_director_en; 
      $data['signature'] = $certificate->signature; 
      $data['signature_en'] = $certificate->signature_en; 
      $data['type'] = $certificate->type; 
      $data['director_2'] = $certificate->course_director_2; 
      $data['director_2_en'] = $certificate->course_director_2_en; 
      $data['signature_2'] = $certificate->signature_2; 
      $data['signature_2_en'] = $certificate->signature_2_en; 
      $data['director_3'] = $certificate->course_director_3; 
      $data['director_3_en'] = $certificate->course_director_3_en; 
      $data['signature_3'] = $certificate->signature_3; 
      $data['signature_3_en'] = $certificate->signature_3_en; 
      $data['director_position'] = $certificate->director_position; 
      $data['director_position_en'] = $certificate->director_position_en; 
      $data['director_position_2'] = $certificate->director_position_2; 
      $data['director_position_2_en'] = $certificate->director_position_2_en; 
      $data['director_position_3'] = $certificate->director_position_3; 
      $data['director_position_3_en'] = $certificate->director_position_3_en; 
      $data['lang'] = $lang;

      if($decrypt[1]=='course'){
        $log_id = UsersCertLog::where('user_id',$user->id)->where('cert_id',$certificate->id)->first();
      }else if($decrypt[1]=='group'){
        $log_id = UsersCertLog::where('user_id',$user->id)->where('group_cert_id',$certificate->id)->first();
      }else{
        $log_id = UsersCertLog::where('user_id',$user->id)->where('global_cert_id',$certificate->id)->first();
      }
      
      if($log_id){
        $stat = new ReportDownloadCertLog();
        $stat->user_id = $user->id;
        $stat->cert_id = $log_id->id;
        $stat->save();
      }

      // return view('pdf/certificate',  compact('data'));
      $pdf = PDF::loadView('pdf.certificate', compact('data'))->setPaper('a4', 'landscape');
      return $pdf->stream();
    }
}

