<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\SimpleEnDeHelper;

use App\Helpers\AppHelper;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\CourseGroup;
use App\Models\Core\DynamicReceipt;
use App\Models\Core\Subscription;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use PDF;

class ReceiptController extends Controller
{

    public function __construct()
    {

    }
    public  function  receipt(Request $request){

      $order = UsersOrderLog::join('user','user.id','user_order_log.user_id')->select('user_order_log.*','user.email as user_email','user.id as user_id')
      ->where('user_order_log.id',SimpleEnDeHelper::instance()->decryptString($request->key))
      ->where('user_order_log.status',2)->first();
      $dynamic_receipt = DynamicReceipt::first();
      if(!$order||!$dynamic_receipt){
        $pdf = PDF::loadView('pdf.order_payment');
        return $pdf->stream();
      }
      $order['discount'] = 0;
      $order['total'] = 0;
      $order['discount_web'] = 0;
      $order_list = UsersOrderList::join('user_order_log','user_order_log.id','user_order_list.order_id')
          ->select('user_order_list.*','user_order_log.web_price as discount_web')
          ->where('user_order_list.order_id', $order->id)->get();
      foreach($order_list as $key=>$value){
        $order_list[$key]['course_name'] = '';
        if($value->type==1){
          $course = Course::where('id',$value->course_id)->first();
          if($course){
            $order_list[$key]['course_name'] = 'ค่าบริการการใช้แพลตฟอร์มออนไลน์ ชื่อบทเรียน : '.$course->title_th;
          }
        }else if($value->type==2){
          $course = CourseGroup::where('id',$value->group_id)->first();
          if($course){
            $order_list[$key]['course_name'] = 'ค่าบริการการใช้แพลตฟอร์มออนไลน์ ชื่อบทเรียน : '.$course->title;
          }
        }else if($value->type==3){
          $course = Subscription::where('id',$value->subscription_id)->first();
          if($course){
            $order_list[$key]['course_name'] = 'ค่าบริการการใช้แพลตฟอร์มออนไลน์ ชื่อบทเรียน : '.$course->title;
          }
        }
        
        $order_list[$key]['price_comma'] = number_format($value->price,2);
        if($value->discount_web!=null&&$value->discount_web!=''&&$value->discount_web!=0&&$value->discount_web!='null'&&$value->discount_web>$order['discount_web']){
          $order['discount_web'] = $value->discount_web;
        }
        $order['discount'] += $value->discount_value;
        $order['total'] += $value->price-$value->discount_value;
      }
      $order['pre_vat'] = $order['total']*100/107;
      $order['vat'] = $order['pre_vat']*7/100;
      $order['pre_vat_comma'] = number_format($order['pre_vat'],2);
      $order['vat_comma'] = number_format($order['vat'],2);
      $order['list_data'] = $order_list;
      $order['dummy'] = 4 - count($order_list);
      if($order['dummy']<0){
        $order['dummy'] = 0;
      }
      if($order->created_receipt_date!=null&&$order->created_receipt_date!=''){
        $order['create_date'] = AppHelper::instance()->DateEngSlash($order->created_receipt_date);
      }else{
        $order['create_date'] = AppHelper::instance()->DateEngSlash($order->created_at);
      }
      $order['discount_comma'] = number_format($order['discount'],2);
      if($order->tax==1){
        $order['total'] = $order['total']-($order['pre_vat']*3/100);
        $order['total_tax'] = number_format($order['pre_vat']*3/100,2);
      }else{
        $order['total_tax'] = 0;
      }
      $order['company_logo'] = $dynamic_receipt->logo;
      $order['company_name'] = $dynamic_receipt->name;
      $order['company_address'] = $dynamic_receipt->address;
      $order['company_tax'] = $dynamic_receipt->tax_no;
      $order['company_email'] = $dynamic_receipt->email;
      $order['company_tel'] = $dynamic_receipt->tel;
      $order['company_website'] = $dynamic_receipt->website;
      $order['company_approve'] = $dynamic_receipt->approve_signature;
      $order['company_approve_name'] = $dynamic_receipt->approve_name;
      $order['company_approve_position'] = $dynamic_receipt->approve_position;
      $order['company_receiver'] = $dynamic_receipt->receiver_signature;
      $order['company_receiver_name'] = $dynamic_receipt->receiver_name;
      $order['company_receiver_position'] = $dynamic_receipt->receiver_position;
      $order['company_remark'] = $dynamic_receipt->remark;

      $order['total'] -= $order['discount_web'];
      $order['total_comma'] = number_format($order['total'],2);
      $order['total_text'] = AppHelper::instance()->bahtText($order['total']);
      $order['discount_web_comma'] = number_format($order['discount_web'],2);
      // return view('pdf/order_payment',  compact('order'));

      // $stat = new ReportDownloadReceiptLog();
      // $stat->user_id = $order->user_id;
      // $stat->order_id = $order->id;
      // $stat->save();

      $pdf = PDF::loadView('pdf.order_payment', compact('order'));

      return $pdf->stream();
    }
}

