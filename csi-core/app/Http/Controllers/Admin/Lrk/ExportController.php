<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;

use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

use App\Exports\TempExport;

class ExportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }


    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='temp_export') {
                $data = DB::table('temps')
                ->join('types', 'types.id', '=', 'temps.type_id')
                ->join('admin', 'admin.id', '=', 'temps.admin_id')
                ->select('temps.*', 'admin.name as admin_name', 'types.name as type_name')
                ->orderBy('id', 'desc')
                ->get();

                foreach ($data as $e) {
                    $ex_array[]=array(
                  'Text'=>$e->text,
                  'HTML'=>$e->html,
                  'Content type'=>$e->type_name,
                  'Created by'=>$e->admin_name,
              );
                }
            } elseif ($request->name=='tag_export') {
                $data = DB::table('tag')
                ->select('tag.*')
                ->get();

                foreach ($data as $e) {
                    $ex_array[]=array(
                  'id'=>$e->id,
                  'name'=>$e->name
              );
                }
            }



            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    // Excel::create($request->filename, function ($excel) use ($ex_array) {
                    //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
                    //         $sheet->fromArray($ex_array);
                    //     });
                    // })->download('xlsx');
                    $export = new TempExport($ex_array);
                    // AppHelper::instance()->consoleLog($ex_array);

                    return Excel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }


    public function filter(Request $request)
    {
        if ($request->name=='temp_filter') {
            $data = DB::table('temps');
            $data->join('types', 'types.id', '=', 'temps.type_id');
            $data->join('admin', 'admin.id', '=', 'temps.admin_id');
            $data->select('temps.*', 'admin.name as admin_name', 'types.name as type_name');

            if (isset($request->text)) {
                $data->where('temps.text', 'like', '%'.$request->text.'%');
            }
            if (isset($request->status)) {
                $data->where('temps.status', '=', $request->status);
            }
            if (isset($request->type_id)) {
                $data->where('temps.type_id', '=', $request->type_id);
            }
            if (isset($request->created_at)) {
                $data->whereDate('temps.created_at', '=', Carbon::createFromFormat('d-m-Y', $request->created_at)->toDateString());
            }
            if (isset($request->created_at_start) && isset($request->created_at_to)) {
                $from = Carbon::createFromFormat('d-m-Y', $request->created_at_start)->toDateString();
                $to = Carbon::createFromFormat('d-m-Y', $request->created_at_to)->addDays(1)->toDateString();
                $data->whereBetween('temps.created_at', [$from, $to]);
            }

            $data->orderBy('temps.id', 'desc');
            $data = $data->get();

            foreach ($data as $e) {
                $ex_array[]=array(
                'Text'=>$e->text,
                'HTML'=>$e->html,
                'Content type'=>$e->type_name,
                'Created by'=>$e->admin_name,
                'Created at'=>$e->created_at,
            );
            }
        }


        // ไม่ต้องแก้ตรงนี้ก็ได้
        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
        if (isset($ex_array)) {
            if ($export_type=='excel') {
                // Excel::create($request->filename, function ($excel) use ($ex_array) {
                //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
                //         $sheet->fromArray($ex_array);
                //     });
                // })->download('xlsx');
                $export = new TempExport($ex_array);
                // AppHelper::instance()->consoleLog($ex_array);

                return Excel::download($export, $request->filename.'.xlsx');
            } else {
                $headers = array(
                "Content-Encoding" => 'UTF-8',
                "Content-type" => "text/csv; charset=UTF-8",
                "Content-Disposition" => "attachment; ".$request->filename.".csv",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            );
                echo "\xEF\xBB\xBF";

                $keys = array_keys($ex_array[0]);
                $callback = function () use ($ex_array,$keys) {
                    $fp= fopen('php://output', 'w');
                    fputcsv($fp, $keys);
                    foreach ($ex_array as $fields) {
                        fputcsv($fp, $fields);
                    }
                    fclose($fp);
                };

                return response()->stream($callback, 200, $headers);
            }
        }

    }
}
