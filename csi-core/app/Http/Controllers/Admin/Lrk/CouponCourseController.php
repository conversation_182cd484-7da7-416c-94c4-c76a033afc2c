<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CouponCourse;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class CouponCourseController extends Controller
{
    public $prefix = 'coupon_course';
    public $project_url = 'coupon_course';
    public $project_layout = 'admin.Lrk.coupon_course';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      $model = CouponCourse::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      $model = CouponCourse::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['coupon_course_log.status', $value['search']['value']]);
          }else if($value['data']=='title_th'){
            array_push($where_search, ['course.title_th', $value['search']['value']]);
          }else{
            array_push($where_search, ['coupon_course_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('coupon_course_log')
      ->join('course', 'course.id', 'coupon_course_log.course_id')
      ->select('coupon_course_log.*', 'course.title_th', 'course.image_th')
      ->where('coupon_id',$request->coupon_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('coupon_course_log')
      ->join('course', 'course.id', 'coupon_course_log.course_id')
      ->where('coupon_id',$request->coupon_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem1('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem1('.$obj->id.',\''.route('admin').'/coupon_course/delete\',\'datatable_gal\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new CouponCourse; 

      $model->coupon_id = $request->coupon_id;
      $model->course_id = isset($request->gallery_modal_course) ? AppHelper::instance()->cleanInput($request->gallery_modal_course) : null;

      $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';

      $model->save();
      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = CouponCourse::find($request->id);

      if($model){
  
        $model->course_id = isset($request->gallery_modal_course) ? AppHelper::instance()->cleanInput($request->gallery_modal_course) : null;
  
        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';
        $model->save();

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = CouponCourse::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }
}
