<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\Datatables\Datatables;
use App\Models\Dummy;
use App\Repositories\Interfaces\SubscriptionLogRepositoryInterface;

class SubscriptionLogController extends Controller
{
    public $prefix = 'subscription_log';
    public $project_url = 'subscription_log';
    public $project_layout = 'admin.Lrk.subscription_log';
    
    private $subscriptionLogRepository;

    public function __construct(SubscriptionLogRepositoryInterface $subscriptionLogRepository)
    {
        $this->middleware('auth:admin');
        $this->subscriptionLogRepository = $subscriptionLogRepository;
    }

    public function index(Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '44') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $obj = [
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout,
        ];

        return view($this->project_layout . '.index', compact('obj'));
    }

    public function dblist(DataTables $datatables, Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '44') {
            return new Dummy();
        }

        $draw = $request->get('draw');
        $start = (int) $request->get('start', 0);
        $length = (int) $request->get('length', 10);

        // Build filters
        $filters = $this->buildFilters($request->get('columns', []));

        // Get order parameters
        $index = $request->get('order')[0]['column'] ?? 0;
        $orderby = $request->get('columns')[$index]['data'] ?? 'subscription_log.id';
        $order_sort = $request->get('order')[0]['dir'] ?? 'desc';

        // Get data from repository
        $data_query = $this->subscriptionLogRepository->getSubscriptionLogsForDatatable(
            $filters,
            $orderby,
            $order_sort,
            $start,
            $length
        );

        // Get total count
        $count_total = $this->subscriptionLogRepository->countFilteredSubscriptionLogs($filters);

        // Transform data for DataTables
        $formatted_data = [];
        foreach ($data_query as $row) {
            $obj = new Dummy();
            $obj['email'] = $row['email'];
            $obj['u_name'] = $row['u_name'];
            $obj['u_lastname'] = $row['u_lastname'];
            $obj['mobile'] = $row['mobile'];
            $obj['package'] = $row['package'];
            $obj['start_date'] = $row['start_date'];
            $obj['end_date'] = $row['end_date'];
            $obj['period_days'] = $row['period_days'];
            $obj['left_days'] = $row['left_days'];
            $formatted_data[] = $obj;
        }

        $data = $datatables->of($formatted_data)
            ->with([
                'data' => $formatted_data,
                'draw' => $draw,
                'recordsTotal' => $count_total,
                'recordsFiltered' => $count_total,
            ])
            ->make(true);

        return $data;
    }

    /**
     * Build filters from request columns
     * 
     * @param array $columns
     * @return array
     */
    private function buildFilters(array $columns): array
    {
        $filters = [];
        
        foreach ($columns as $value) {
            if (!empty($value['search']['value'])) {
                $col = $value['data'];
                $val = $value['search']['value'];
                
                if (in_array($col, ['email', 'mobile'])) {
                    $filters[] = ['user.' . $col, 'LIKE', '%' . $val . '%'];
                } elseif ($col === 'u_name') {
                    $filters[] = ['user.name', 'LIKE', '%' . $val . '%'];
                } elseif ($col === 'u_lastname') {
                    $filters[] = ['user.lastname', 'LIKE', '%' . $val . '%'];
                } elseif ($col === 'package') {
                    $filters[] = ['subscription.title', 'LIKE', '%' . $val . '%'];
                }
            }
        }
        
        return $filters;
    }
} 