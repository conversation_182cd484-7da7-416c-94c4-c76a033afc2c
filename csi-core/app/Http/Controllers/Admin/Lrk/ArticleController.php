<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\Article;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\ArticleCateLog;
use App\Models\Core\ArticleLog;
use App\Models\Core\ArticleSubCateLog;
use App\Models\Core\ArticleTagLog;
use App\Models\Core\Tags;
use App\Models\Core\UsersActivityLog;
use App\Models\Core\UsersFavoriteLog;
use Maatwebsite\Excel\Facades\Excel;


class ArticleController extends Controller
{
    public $prefix = 'article';
    public $project_url = 'article';
    public $project_layout = 'admin.Lrk.article';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Article();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = Article::find($id);
      $article_id = $model->id;
      if($model){
        $obj = array(
          'type'=>'view',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.view', compact('model','obj','article_id'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = Article::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['article.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['article.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['article.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['article.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('article')
      ->select('article.*')
      ->where('is_article', 1)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('article')
      ->where('is_article', 1)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $count_activity = UsersActivityLog::where('activity_type', 3)->where('article_id', $value->id)->count();
        if($count_activity){
          $obj['count_activity'] = $count_activity. ' ราย';
        }else{
          $obj['count_activity'] = '0 ราย';
        }

        $count_favorite = UsersFavoriteLog::where('favorite_type', 3)->where('article_id', $value->id)->count();
        if($count_favorite){
          $obj['count_favorite'] = $count_favorite. ' ราย';
        }else{
          $obj['count_favorite'] = '0 ราย';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Article;

      $slug_clean = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug = Article::where('article.slug','=', $slug_clean)->count();

      if($slug==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : AppHelper::instance()->cleanInputSlug($request->title_th);
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }

      $model->is_article = 1;
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = isset($request->title_en) ? AppHelper::instance()->cleanInput($request->title_en) : null;
      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null;
      $model->subtitle_en = isset($request->subtitle_en) ? AppHelper::instance()->cleanInput($request->subtitle_en) : null;
      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;
      $model->details_en = isset($request->details_en) ? AppHelper::instance()->convertDomHTML($request->details_en) : null;

      if ($request->hasFile('image')) {
        $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/article');
      }

      if ($request->hasFile('banner')) {
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/article');
      }

      $model->meta_title = isset($request->meta_title) ? AppHelper::instance()->cleanInput($request->meta_title) : $model->title_th;
      $model->meta_description = isset($request->meta_description) ? AppHelper::instance()->cleanInput($request->meta_description) : $model->title_th;
      $model->meta_keyword = isset($request->meta_keyword) ? AppHelper::instance()->cleanInput($request->meta_keyword) : $model->title_th;

      if ($request->hasFile('meta_image')) {
        $model->meta_image = AppHelper::instance()->saveImage($request->file('meta_image'),'/upload/article');
      }

      $count = Article::where('is_article', 1)->count();
      $model->position = $count + 1;

      $model->status = $request->status;
  
      //เช็ค Tags
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tags_val = $request->tag;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      //เช็ค Tags

      $model->save();
  
      if(is_array($request->tag)){ //ถ้า tags มีค่า
        foreach($request->tag as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new ArticleTagLog();
          $log->article_id = $model->id;
          $log->tag_id = $value;
          $log->save();
        }
      }

      return redirect(route('admin').'/article/'.$model->id.'/edit/')->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = Article::find($id);

      //เช็ค Tags
      $old_tags = explode(',', $model->tag);
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tag_val = $request->tag;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      if(is_array($request->tag)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->tag))>0 || count(array_diff($request->tag, $old_tags))>0){
          $tags = ArticleTagLog::where('article_id', $model->id);
          $tags->delete();

          foreach($request->tag as $value) {
            $log = new ArticleTagLog();
            $log->article_id = $model->id;
            $log->tag_id = $value;
            $log->save();
          }
        }
      }

      $model->is_article = 1;
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = isset($request->title_en) ? AppHelper::instance()->cleanInput($request->title_en) : null;
      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null;
      $model->subtitle_en = isset($request->subtitle_en) ? AppHelper::instance()->cleanInput($request->subtitle_en) : null;
      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;
      $model->details_en = isset($request->details_en) ? AppHelper::instance()->convertDomHTML($request->details_en) : null;

      if ($request->hasFile('image')) {
        if($model->image!=null&&$model->image!=''){
          AppHelper::instance()->removeImage($model->image);
        }
        $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/article');
      }else{
        if(empty($request->source_image)){
          if($model->image!=null&&$model->image!=''){
            AppHelper::instance()->removeImage($model->image);
          }
          $model->image = null;
        }
      }

      if ($request->hasFile('banner')) {
        if($model->banner!=null&&$model->banner!=''){
          AppHelper::instance()->removeImage($model->banner);
        }
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/article');
      }else{
        if(empty($request->source_banner)){
          if($model->banner!=null&&$model->banner!=''){
            AppHelper::instance()->removeImage($model->banner);
          }
          $model->banner = null;
        }
      }

      $slug_clean_th = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug_th = Article::where('article.slug','=', $slug_clean_th)->where('article.id','!=', $id)->count();
      if($slug_th==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : '';
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }

      $model->meta_title = isset($request->meta_title) ? AppHelper::instance()->cleanInput($request->meta_title) : $model->meta_title;
      $model->meta_description = isset($request->meta_description) ? AppHelper::instance()->cleanInput($request->meta_description) : $model->meta_description;
      $model->meta_keyword = isset($request->meta_keyword) ? AppHelper::instance()->cleanInput($request->meta_keyword) : $model->meta_keyword;

      if ($request->hasFile('meta_image')) {
        if($model->meta_image!=null&&$model->meta_image!=''){
          AppHelper::instance()->removeImage($model->meta_image);
        }
        $model->meta_image = AppHelper::instance()->saveImage($request->file('meta_image'),'/upload/article');
      }else{
        if(empty($request->source_meta_image)){
          if($model->meta_image!=null&&$model->meta_image!=''){
            AppHelper::instance()->removeImage($model->meta_image);
          }
          $model->meta_image = null;
        }
      }

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function position(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = Article::where('is_article',1)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = Article::where('is_article',1)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return response()->json();
      }

      $model = Article::find($request->id);

      $log = ArticleLog::where('article_id', $model->id)->get();
      foreach($log as $key_log => $val_log){
        $val_log->delete();
      }

      $tag = ArticleTagLog::where('article_id', $model->id)->get();
      foreach($tag as $key_tag => $val_tag){
        $val_tag->delete();
      }

      Article::where('is_article',1)->where('position','>',$model->position)
      ->update(['position' => DB::raw('position - 1')]);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return response()->json();
      }
      $model = Article::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
