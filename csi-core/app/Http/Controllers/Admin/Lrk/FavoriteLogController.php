<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\UsersFavoriteLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class FavoriteLogController extends Controller
{
    public $prefix = 'favorite_log';
    public $project_url = 'favorite_log';
    public $project_layout = 'admin.Lrk.favorite_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      return response()->json();
    }

    public function status(Request $request)
    {
      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_favorite_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_favorite_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_favorite_log.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', $value['search']['value']]);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', $value['search']['value']]);
          }else{
            array_push($where_search, ['user_favorite_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_favorite_log')
      ->join('user', 'user.id', 'user_favorite_log.user_id')
      ->select('user_favorite_log.*', 'user.name as u_name', 'user.lastname as u_lastname')
      ->where('user_favorite_log.favorite_type', 1)
      ->where('user_favorite_log.course_id', $request->course_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_favorite_log')
      ->join('user', 'user.id', 'user_favorite_log.user_id')
      ->where('user_favorite_log.favorite_type', 1)
      ->where('user_favorite_log.course_id', $request->course_id)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      return json_decode($response, true);

    }
}
