<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\Users;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CateDepartment;
use App\Models\Core\CateInteresting;
use App\Models\Core\EmailDynamic;
use App\Models\Core\UserHistory;
use App\Models\Core\UserInterestingLog;
use App\Models\Core\UsersCartLog;
use App\Models\Core\UsersCertLog;
use App\Models\Core\UsersCommentLikeLog;
use App\Models\Core\UsersCommentLog;
use App\Models\Core\UsersCommentRepliedLog;
use App\Models\Core\UsersCommentReplyLikeLog;
use App\Models\Core\UsersExamLog;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\UsersLearningLog;
use App\Models\Core\UsersLoginLog;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\Core\UsersPointLog;
use App\Models\Core\UsersReceiptLog;
use App\Models\Core\UsersResetPass;
use App\Models\Core\UsersResetPassLog;
use App\Models\Core\UserVerify;
use App\Models\Core\UsersExamPoint;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;
use Maatwebsite\Lrk\Facades\Excel;


class UsersController extends Controller
{
  public $prefix = 'users';
  public $project_url = 'users';
  public $project_layout = 'admin.Lrk.users';

  public function __construct()
  {
    $this->middleware('auth:admin');
  }

  public function index(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }

    if (isset($request->from)) {
      $from = Carbon::parse($request->from)->format('Y-m-d H:i:s');
      $from_date = $request->from;
    } else {
      $from = '';
      $from_date = '';
    }
    if (isset($request->to)) {
      $to = Carbon::parse($request->to)->addDays(1)->format('Y-m-d H:i:s');
      $to_date = $request->to;
    } else {
      $to = '';
      $to_date = '';
    }

    $user_non_start = Users::whereDate('user.created_at', '>=', $from)
      ->count();

    $user_non_end = Users::whereDate('user.created_at', '<=', $to)
      ->count();

    $user_non_start_end = Users::count();

    $user_start_end = Users::whereBetween('user.created_at', [$from, $to])
      ->count();

    $obj = array(
      'started_date' => $from,
      'end_date' => $to,
      'prefix' => $this->prefix,
      'project_url' => $this->project_url,
      'project_layout' => $this->project_layout
    );

    return view($this->project_layout . '.index', compact('obj', 'user_non_start', 'user_non_end', 'user_non_start_end', 'user_start_end', 'from_date', 'to_date'));
  }

  public function create()
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = new Users();
    $old = session()->getOldInput();
    if ($old) {
      foreach ($old as $key => $value) {
        $model[$key] = $value;
      }
    }
    $obj = array(
      'type' => 'create',
      'route' => route($this->prefix . '.store'),
      'prefix' => $this->prefix,
      'project_url' => $this->project_url,
      'project_layout' => $this->project_layout
    );
    return view($this->project_layout . '.form', compact('model', 'obj'));
  }

  public function show($id)
  {
    return redirect(route('admin') . '/' . $this->project_url);
  }

  public function edit($id)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = Users::find($id);
    if ($model) {
      $obj = array(
        'type' => 'edit',
        'route' => route($this->prefix . '.update', $model->id),
        'prefix' => $this->prefix,
        'project_url' => $this->project_url,
        'project_layout' => $this->project_layout
      );
      return view($this->project_layout . '.form', compact('model', 'obj'));
    } else {
      return redirect(route('admin') . '/' . $this->project_url);
    }
  }

  public function dblist(DataTables $datatables, Request $request)
  {

    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return new Dummy();
    }
    $draw = $request->get('draw');
    $start = $request->get('start');
    $length = $request->get('length');

    //custom search
    $where_search = array();
    foreach ($request->get('columns') as $key => $value) {
      if (!empty($value['search']['value'])) {
        if ($value['data'] == 'status') {
          array_push($where_search, ['user.status', $value['search']['value']]);
        } else if ($value['data'] == 'position') {
          array_push($where_search, ['user.position', $value['search']['value']]);
        } else if ($value['data'] == 'id') {
          array_push($where_search, ['user.id', $value['search']['value']]);
        } else {
          array_push($where_search, ['user.' . $value['data'], 'LIKE', '%' . $value['search']['value'] . '%']);
        }
      }
    }

    $index = $request->get('order')[0]['column'];
    $orderby = $request->get('columns')[$index]['data'];
    $order_sort = $request->get('order')[0]['dir'];

    $query = DB::table('user')
      ->select(
        'user.id',
        'user.g_id',
        'user.e_id',
        'user.prefix',
        'user.name',
        'user.lastname',
        'user.nickname',
        'user.position',
        'user.location_code',
        'user.email',
        'user.mobile',
        'user.school',
        'user.know_ch',
        'user.created_at',
        'user.status',
        'user.is_confirm',
        'user.short_name',
        'user.class',
        'user.major',
      )
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

    $count_total = DB::table('user')
      ->where($where_search)
      ->count();

    $data_query = array();
    foreach ($query as $key => $value) {
      $obj = new Dummy();
      foreach ($value as $key_data => $data) {
        $obj[$key_data] = $data;
      }

      $obj['action'] = '
          <div class="btn-group">
            <a href="' . $this->prefix . '/' . $obj->id . '/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
            <a href="javascript:void(0)" onclick="deleteDatatableItem(' . $obj->id . ',\'' . $this->prefix . '/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
          </div>';
      array_push($data_query, $obj);
    }

    $data = $datatables->of($data_query)
      ->with([
        'data' => $data_query,
        'draw' => $draw,
        "recordsTotal" => $count_total,
        "recordsFiltered" => $count_total,
      ])
      ->make(true);
    return $data;
  }

  public function store(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = new User();

    $user_email = User::where('email', $request->email)->first();
    if (!$user_email) {
      $model->prefix = isset($request->prefix) ? $request->prefix : 99;
      $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
      $model->lastname = isset($request->lastname) ? AppHelper::instance()->cleanInput($request->lastname) : null;
      $model->email = isset($request->email) ? AppHelper::instance()->cleanInput($request->email) : null;
      $model->mobile = isset($request->mobile) ? AppHelper::instance()->cleanInput($request->mobile) : null;
      $model->school = isset($request->school) ? AppHelper::instance()->cleanInput($request->school) : null;
      $model->know_ch = isset($request->know_ch) ? AppHelper::instance()->cleanInput($request->know_ch) : null;
      $model->nickname = isset($request->nickname) ? AppHelper::instance()->cleanInput($request->nickname) : null;
      $model->class = isset($request->class) ? AppHelper::instance()->cleanInput($request->class) : null;
      $model->major = isset($request->major) ? AppHelper::instance()->cleanInput($request->major) : null;
      $model->is_confirm = isset($request->is_confirm) ? $request->is_confirm : 1;

      $model->username = $model->email;

      if (Auth::user()->level == '99' && Auth::user()->level != '44') {
        $model->password = isset($request->password) ? Hash::make($request->password) : null;
      }

      $model->status = isset($request->status) ? $request->status : 1;

      $model->save();

      if ($model->is_confirm == 1) {
        $email_dynamic = EmailDynamic::where('id', 8)->where('status', 1)->first();
        if ($email_dynamic) {
          $replace_detail = $email_dynamic->details;
          $replace_detail = str_replace("{{name}}", $model->name, $replace_detail);
          $replace_detail = str_replace("{{lastname}}", $model->lastname, $replace_detail);
          $replace_detail = str_replace("{{updated_at}}", $model->updated_at, $replace_detail);
          $replace_detail = str_replace("{{link}}", 'https://csisociety.com/login', $replace_detail);
          $obj = new Dummy();
          $obj['subject'] = $email_dynamic->subject;
          $obj['code'] = $replace_detail;
          $obj['email'] = $model->email;
          $obj['cc'] = [];
          $obj['bcc'] = [];
          AppHelper::instance()->mailTemplate($obj);
        }
      }

      return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been update');
    } else {
      return redirect(route('admin') . '/' . $this->project_url)->with('errors', 'Duplicateed User Email');
    }
  }

  public function update(Request $request, $id)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = Users::find($id);
    $user_confirm = $model->is_confirm;
    if ($model) {
      $user_email = User::where('email', $request->email)->where('id', '!=', $id)->first();

      if (!$user_email) {

        $model->prefix = isset($request->prefix) ? $request->prefix : 99;
        $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
        $model->lastname = isset($request->lastname) ? AppHelper::instance()->cleanInput($request->lastname) : null;
        $model->email = isset($request->email) ? AppHelper::instance()->cleanInput($request->email) : null;
        $model->mobile = isset($request->mobile) ? AppHelper::instance()->cleanInput($request->mobile) : null;
        $model->school = isset($request->school) ? AppHelper::instance()->cleanInput($request->school) : null;
        $model->know_ch = isset($request->know_ch) ? AppHelper::instance()->cleanInput($request->know_ch) : null;
        $model->nickname = isset($request->nickname) ? AppHelper::instance()->cleanInput($request->nickname) : null;
        $model->class = isset($request->class) ? AppHelper::instance()->cleanInput($request->class) : null;
        $model->major = isset($request->major) ? AppHelper::instance()->cleanInput($request->major) : null;

        $model->is_confirm = isset($request->is_confirm) ? $request->is_confirm : 1;

        $model->username = $model->email;

        if (Auth::user()->level == '99' && Auth::user()->level != '44') {
          $model->password = isset($request->password) ? Hash::make($request->password) : $model->password;
        }

        $model->status = isset($request->status) ? $request->status : 1;

        $model->save();

        if ($model->is_confirm == 1 && $user_confirm != 1) {
          $email_dynamic = EmailDynamic::where('id', 8)->where('status', 1)->first();
          if ($email_dynamic) {
            $replace_detail = $email_dynamic->details;
            $replace_detail = str_replace("{{name}}", $model->name, $replace_detail);
            $replace_detail = str_replace("{{lastname}}", $model->lastname, $replace_detail);
            $replace_detail = str_replace("{{updated_at}}", $model->updated_at, $replace_detail);
            $replace_detail = str_replace("{{link}}", 'https://csisociety.com/login', $replace_detail);
            $obj = new Dummy();
            $obj['subject'] = $email_dynamic->subject;
            $obj['code'] = $replace_detail;
            $obj['email'] = $model->email;
            $obj['cc'] = [];
            $obj['bcc'] = [];
            AppHelper::instance()->mailTemplate($obj);
          }
        }

        return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been update');
      } else {
        return redirect(route('admin') . '/' . $this->project_url)->with('errors', 'Duplicateed User Email');
      }
    } else {
      return redirect(route('admin') . '/' . $this->project_url)->with('errors', 'Data has not been found');
    }
  }

  public function destroy(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return response()->json();
    }

    $model = Users::find($request->id);

    $cert = UsersCertLog::where('user_id', $model->id)->get();
    foreach ($cert as $key_cert => $val_cert) {
      $val_cert->delete();
    }

    $comment = UsersCommentLog::where('user_id', $model->id)->get();
    foreach ($comment as $key_comment => $val_comment) {
      $val_comment->delete();
    }

    $comment_reply = UsersCommentRepliedLog::where('user_id', $model->id)->get();
    foreach ($comment_reply as $key_comment_reply => $val_comment_reply) {
      $val_comment_reply->delete();
    }

    $comment_reply_like = UsersCommentReplyLikeLog::where('user_id', $model->id)->get();
    foreach ($comment_reply_like as $key_comment_reply_like => $val_comment_reply_like) {
      $val_comment_reply_like->delete();
    }

    $comment_like = UsersCommentLikeLog::where('user_id', $model->id)->get();
    foreach ($comment_like as $key_comment_like => $val_comment_like) {
      $val_comment_like->delete();
    }

    $exam = UsersExamLog::where('user_id', $model->id)->get();
    foreach ($exam as $key_exam => $val_exam) {
      $point = UsersExamPoint::where('exam_id', $val_exam->id)->get();
      foreach ($point as $key_point => $val_point) {
        $val_point->delete();
      }
      $val_exam->delete();
    }

    $favorite = UsersFavoriteLog::where('user_id', $model->id)->get();
    foreach ($favorite as $key_favorite => $val_favorite) {
      $val_favorite->delete();
    }

    $learning = UsersLearningLog::where('user_id', $model->id)->get();
    foreach ($learning as $key_learning => $val_learning) {
      $val_learning->delete();
    }

    $interesting = UserInterestingLog::where('user_id', $model->id)->get();
    foreach ($interesting as $key_int => $val_int) {
      $val_int->delete();
    }

    $login = UsersLoginLog::where('user_id', $model->id)->get();
    foreach ($login as $key_login => $val_login) {
      $val_login->delete();
    }

    $order = UsersOrderLog::where('user_id', $model->id)->get();
    foreach ($order as $key_order => $val_order) {
      $list = UsersOrderList::where('order_id', $val_order->id)->get();
      foreach ($list as $key_list => $val_list) {
        $val_list->delete();
      }
      $val_order->delete();
    }

    $point = UsersPointLog::where('user_id', $model->id)->get();
    foreach ($point as $key_point => $val_point) {
      $val_point->delete();
    }

    $receipt = UsersReceiptLog::where('user_id', $model->id)->get();
    foreach ($receipt as $key_receipt => $val_receipt) {
      $val_receipt->delete();
    }

    $cart = UsersCartLog::where('user_id', $model->id)->get();
    foreach ($cart as $key_cart => $val_cart) {
      $val_cart->delete();
    }

    $verify = UserVerify::where('user_id', $model->id)->get();
    foreach ($verify as $key_verify => $val_verify) {
      $val_verify->delete();
    }

    $reply_like = UsersCommentReplyLikeLog::where('user_id', $model->id)->get();
    foreach ($reply_like as $key_reply_like => $val_reply_like) {
      $val_reply_like->delete();
    }

    $reset_pass = UsersResetPassLog::where('user_id', $model->id)->get();
    foreach ($reset_pass as $key_reset_pass => $val_reset_pass) {
      $val_reset_pass->delete();
    }

    $model->delete();
    return response()->json();
  }

  public function status(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return response()->json();
    }
    $model = Users::find($request->id);

    if ($model->status == '1') {
      $model->status = '2';
    } else {
      $model->status = '1';
    }

    $model->save();

    return response()->json();
  }

  public function confirm(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return response()->json();
    }
    $model = Users::find($request->id);

    if ($model->is_confirm == '1') {
      $model->is_confirm = '2';
    } else {
      $model->is_confirm = '1';
    }

    $model->save();

    return response()->json();
  }

  public function filter(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    } else {
      if ($request->name == 'user_filter') {
        $data = DB::table('user');
        $data->select('user.*');
        $data->orderBy('user.created_at', 'desc');

        if (isset($request->created_at_start) && isset($request->created_at_to)) {
          $from = Carbon::parse($request->created_at_start)->toDateString();
          $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
          $data->whereBetween('user.created_at', [$from, $to]);
        }

        $data = $data->get();

        $ex_array[] = array(

          'ชื่อ' => 'ชื่อ',
          'นามสกุล' => 'นามสกุล',
          'ชื่อเล่น' => 'ชื่อเล่น',
          'อีเมล์' => 'อีเมล์',
          'เบอร์โทรศัพท์' => 'เบอร์โทรศัพท์',
          // 'โรงเรียน' => 'โรงเรียน',
          // 'ชั้นเรียน' => 'ชั้นเรียน',
          // 'สายการเรียน' => 'สายการเรียน',
          // 'รู้จักพี่โอมจากทางไหน' => 'รู้จักพี่โอมจากทางไหน',
          'วันที่เก็บข้อมูล' => 'วันที่เก็บข้อมูล',
        );

        foreach ($data as $key => $e) {

          if ($e->status == 1) {
            $e->status = 'เปิดใช้งาน';
          } else if ($e->status == 2) {
            $e->status = 'ปิดใช้งาน';
          }

          if ($e->prefix == 99) {
            $e->prefix = 'ไม่ระบุ';
          } else if ($e->prefix == 1) {
            $e->prefix = 'Mr';
          } else if ($e->prefix == 2) {
            $e->prefix = 'Miss';
          } else if ($e->prefix == 3) {
            $e->prefix = 'Mrs';
          }


          $ex_array[] = array(

            'ชื่อ' => $e->name,
            'นามสกุล' => $e->lastname,
            'ชื่อเล่น' => $e->nickname,
            'อีเมล์' => $e->email,
            'เบอร์โทรศัพท์' => $e->mobile,
            // 'โรงเรียน' => $e->school,
            // 'ชั้นเรียน' => $e->class,
            // 'สายการเรียน' => $e->major,
            // 'รู้จักพี่โอมจากทางไหน' => $e->know_ch,
            'วันที่เก็บข้อมูล' => $e->created_at,
          );
        }
      }

      $export_type = isset($request->export_type) ? $request->export_type : 'excel';
      if (isset($ex_array)) {
        if ($export_type == 'excel') {
          // Excel::create($request->filename, function ($excel) use ($ex_array) {
          //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
          //         $sheet->fromArray($ex_array);
          //     });
          // })->download('xlsx');
          $export = new TempExport($ex_array);
          // AppHelper::instance()->consoleLog($ex_array);

          return FacadesExcel::download($export, $request->filename . '.xlsx');
        } else {
          $headers = array(
            "Content-Encoding" => 'UTF-8',
            "Content-type" => "text/csv; charset=UTF-8",
            "Content-Disposition" => "attachment; " . $request->filename . ".csv",
            "Pragma" => "no-cache",
            "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
            "Expires" => "0"
          );
          echo "\xEF\xBB\xBF";

          $keys = array_keys($ex_array[0]);
          $callback = function () use ($ex_array, $keys) {
            $fp = fopen('php://output', 'w');
            fputcsv($fp, $keys);
            foreach ($ex_array as $fields) {
              fputcsv($fp, $fields);
            }
            fclose($fp);
          };

          return response()->stream($callback, 200, $headers);
        }
      }
    }
  }

  public function learn_filter(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    } else {
      if ($request->name == 'users_learn_filter') {
        $data = DB::table('user')
          ->join('user_learning_log', 'user_learning_log.user_id', 'user.id')
          ->join('course', 'course.id', 'user_learning_log.course_id')
          ->select(
            'user.*',
            'course.title_th as course_name',
            'course.course_duration',
            DB::raw('SUM(TIME_TO_SEC(user_learning_log.watching_time)) as learn_duration')
          )
          ->orderBy('user.created_at', 'desc');

        if (isset($request->created_at_start) && isset($request->created_at_to)) {
          $from = Carbon::parse($request->created_at_start)->toDateString();
          $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
          $data->whereBetween('user.created_at', [$from, $to]);
        }
        $data->groupby('user_learning_log.user_id');
        $data->groupby('user_learning_log.course_id');
        $data = $data->get();

        $ex_array[] = array(
          'Record ID' => 'Record ID',
          'Date' => 'Date',
          'Name' => 'Name',
          'Surname' => 'Surname',
          'Nickname' => 'Nickname',
          'Email' => 'Email',
          'School' => 'School',
          'Class' => 'Class',
          'Study Plans' => 'Study Plans',
          'Learn Course (Course Name)' => 'Learn Course (Course Name)',
          'Learning Durations (Mins)' => 'Learning Durations (Mins)',
          'Total Durations (Mins)' => 'Total Durations (Mins)',
          'Learning Progress' => 'Learning Progress'
        );

        foreach ($data as $key => $e) {

          $learned_percent = strval(number_format(0, 2)) . '%';
          if ($e->course_duration != null && $e->course_duration != '' && $e->course_duration != 0) {
            $learned_percent = strval(number_format($e->learn_duration * 100 / $e->course_duration)) . '%';
          }

          $ex_array[] = array(
            'Record ID' => $e->id,
            'Date' => $e->created_at,
            'Name' => $e->name,
            'Surname' => $e->lastname,
            'Nickname' => $e->nickname,
            'Email' => $e->email,
            'School' => $e->school,
            'Class' => $e->class,
            'Study Plans' => $e->major,
            'Learn Course (Course Name)' => $e->course_name,
            'Learning Durations (Mins)' => round($e->learn_duration / 60),
            'Total Durations (Mins)' => round($e->course_duration / 60),
            'Learning Progress' => $learned_percent
          );
        }
      }

      $export_type = isset($request->export_type) ? $request->export_type : 'excel';
      if (isset($ex_array)) {
        if ($export_type == 'excel') {
          // Excel::create($request->filename, function ($excel) use ($ex_array) {
          //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
          //         $sheet->fromArray($ex_array);
          //     });
          // })->download('xlsx');
          $export = new TempExport($ex_array);
          // AppHelper::instance()->consoleLog($ex_array);

          return FacadesExcel::download($export, $request->filename . '.xlsx');
        } else {
          $headers = array(
            "Content-Encoding" => 'UTF-8',
            "Content-type" => "text/csv; charset=UTF-8",
            "Content-Disposition" => "attachment; " . $request->filename . ".csv",
            "Pragma" => "no-cache",
            "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
            "Expires" => "0"
          );
          echo "\xEF\xBB\xBF";

          $keys = array_keys($ex_array[0]);
          $callback = function () use ($ex_array, $keys) {
            $fp = fopen('php://output', 'w');
            fputcsv($fp, $keys);
            foreach ($ex_array as $fields) {
              fputcsv($fp, $fields);
            }
            fclose($fp);
          };

          return response()->stream($callback, 200, $headers);
        }
      }
    }
  }

  public function dblist_date(DataTables $datatables, Request $request)
  {

    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return new Dummy();
    }

    if ($request->start_date != '0') {
      $start_date = Carbon::parse($request->start_date)->format('Y-m-d H:i:s');
    } else {
      $start_date = '';
    }
    if ($request->end_date != '0') {
      $end_date = Carbon::parse($request->end_date)->addDays(1)->format('Y-m-d H:i:s');
    } else {
      $end_date = '';
    }
    $draw = $request->get('draw');
    $start = $request->get('start');
    $length = $request->get('length');

    //custom search
    $where_search = array();
    foreach ($request->get('columns') as $key => $value) {
      if (!empty($value['search']['value'])) {
        array_push($where_search, [$value['data'], 'LIKE', '%' . $value['search']['value'] . '%']);
      }
    }

    $index = $request->get('order')[0]['column'];
    $orderby = $request->get('columns')[$index]['data'];
    $order_sort = $request->get('order')[0]['dir'];

    if ($start_date != '' && $end_date == '') {
      $query = DB::table('user')
        ->select(
          'user.id',
          'user.g_id',
          'user.e_id',
          'user.prefix',
          'user.name',
          'user.lastname',
          'user.nickname',
          'user.position',
          'user.location_code',
          'user.email',
          'user.mobile',
          'user.school',
          'user.know_ch',
          'user.created_at',
          'user.status',
          'user.is_confirm',
          'user.short_name',
        )
        ->whereDate('user.created_at', '>=', $start_date)
        ->get();
    }
    if ($start_date == '' && $end_date != '') {
      $query = DB::table('user')
        ->select(
          'user.id',
          'user.g_id',
          'user.e_id',
          'user.prefix',
          'user.name',
          'user.lastname',
          'user.nickname',
          'user.position',
          'user.location_code',
          'user.email',
          'user.mobile',
          'user.school',
          'user.know_ch',
          'user.created_at',
          'user.status',
          'user.is_confirm',
          'user.short_name',
        )
        ->whereDate('user.created_at', '<=', $end_date)
        ->get();
    }
    if ($start_date == '' && $end_date == '') {
      $query = DB::table('user')
        ->select(
          'user.id',
          'user.g_id',
          'user.e_id',
          'user.prefix',
          'user.name',
          'user.lastname',
          'user.nickname',
          'user.position',
          'user.location_code',
          'user.email',
          'user.mobile',
          'user.school',
          'user.know_ch',
          'user.created_at',
          'user.status',
          'user.is_confirm',
          'user.short_name',
        )
        ->get();
    }
    if ($start_date != '' && $end_date != '') {
      $query = DB::table('user')
        ->select(
          'user.id',
          'user.g_id',
          'user.e_id',
          'user.prefix',
          'user.name',
          'user.lastname',
          'user.nickname',
          'user.position',
          'user.location_code',
          'user.email',
          'user.mobile',
          'user.school',
          'user.know_ch',
          'user.created_at',
          'user.status',
          'user.is_confirm',
          'user.short_name',
        )
        ->whereBetween('user.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

      $count_total = DB::table('user')
        ->whereBetween('user.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->count();
    }

    $data_query = array();
    foreach ($query as $key => $value) {
      $obj = new Dummy();
      foreach ($value as $key_data => $data) {
        $obj[$key_data] = $data;
      }

      $obj['action'] = '
          <div class="btn-group">
            <a href="' . $this->prefix . '/' . $obj->id . '/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
            <a href="javascript:void(0)" onclick="deleteDatatableItem(' . $obj->id . ',\'' . $this->prefix . '/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
          </div>';
      array_push($data_query, $obj);
    }

    $data = $datatables->of($data_query)
      ->with([
        'data' => $data_query,
        'draw' => $draw,
        "recordsTotal" => $count_total,
        "recordsFiltered" => $count_total,
      ])
      ->make(true);
    return $data;
  }

  public function import(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $response = [
      'store' => 0,
      'update' => 0,
      'updated_emails' => [],
    ];

    if (isset($request->excel)) {
      $excels = json_decode($request->excel);
      if (count($excels) > 0) {
        foreach ($excels as &$excel) {
          $type = 'store'; // ค่าเริ่มต้นเป็น store

          $email = $excel->user_email ?? null;
          $username = $excel->user_login ?? null;

          if (!$email && $username) {
            $email = $username;
          }
          if (!$username && $email) {
            $username = $email;
          }

          // ตรวจสอบว่า username ซ้ำในระบบหรือไม่
          $existingUser = Users::where('username', $username)->first();

          // ถ้า username ซ้ำ, สร้าง username ใหม่โดยการเพิ่มตัวเลขหรือข้อความเข้าไป
          if ($existingUser) {
            $originalUsername = $username; // เก็บ username เดิม
            $counter = 1;
            // สร้าง username ใหม่โดยเพิ่มเลขที่ท้าย username
            do {
              $username = $originalUsername . '_' . $counter;
              $counter++;
            } while (Users::where('username', $username)->exists());
          }

          // ค้นหาผู้ใช้จาก email ถ้า username ไม่ซ้ำ
          $result = Users::where('email', $email)
            ->orWhere('username', $username)
            ->first();

          if ($result) {
            $model_excel = Users::find($result->id);
            $type = 'update';  // ถ้าเจอ, ให้ทำการอัปเดต
          } else {
            $model_excel = new Users();
            $type = 'store';  // ผู้ใช้ไม่พบ, สร้างใหม่
          }

          // กำหนดค่าต่างๆ ให้กับโมเดล
          $model_excel->name = $excel->first_name ?? null;
          $model_excel->lastname = $excel->last_name ?? null;
          $model_excel->nickname = $excel->nickname ?? null;
          $model_excel->email = $email;
          $model_excel->mobile = $excel->billing_phone ?? null;
          $model_excel->username = $username;
          $model_excel->user_id_bundit = $excel->ID_BunditOrg ?? null;
          $model_excel->user_id_online_bundit = $excel->ID_OnlineBundit ?? null;
          $model_excel->status = 1;
          $model_excel->created_at = $excel->user_registered ?? null;
          $model_excel->updated_at = $excel->user_registered ?? null;
          $model_excel->save();

          $response[$type]++;

          if ($type === 'update' && $email) {
            $response['updated_emails'][] = $email;
          }
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }

  public function import_history(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '44') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $response = [
      'store' => 0,
      'update' => 0,
    ];

    if (isset($request->excel)) {
      $excels = json_decode($request->excel);
      if (count($excels) > 0) {
        foreach ($excels as &$excel) {
          $type = 'store'; // ค่าเริ่มต้นเป็น store

          // ตรวจสอบว่า username ซ้ำในระบบหรือไม่
          if (isset($excel->user)) {
            $user = Users::where('id', $excel->user)->first();

            // ถ้า username ซ้ำ, สร้าง username ใหม่โดยการเพิ่มตัวเลขหรือข้อความเข้าไป
            if ($user) {
              $model_excel = new UserHistory();
              $model_excel->user_id = $user->id ?? null;
              $model_excel->course_id = $excel->course ?? null;
              $model_excel->status = 1;
              $model_excel->save();
              $response[$type]++;
            }
          }
        }
      }
    }
    return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'], JSON_UNESCAPED_UNICODE);
  }
}
