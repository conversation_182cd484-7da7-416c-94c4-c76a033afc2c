<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CourseExamAnswer;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseExamLog;
use App\Models\VNE\Project;
use Maatwebsite\Excel\Facades\Excel;


class CourseExamAnswerController extends Controller
{
    public $prefix = 'exam_answer';
    public $project_url = 'exam_answer';
    public $project_layout = 'admin.Lrk.exam_answer';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      
      $exam_id = $request->exam_id;

      $course_id = CourseExamLog::join('course_exam_answer', 'course_exam.id', 'course_exam_answer.exam_id')
                    ->where('course_exam.id', $exam_id)->select('course_exam.course_id')->first();
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','exam_id'));
    }

    public function create(Request $request)
    {
        $exam_id = $request->exam_id;

        $course_id = CourseExamLog::where('course_exam.id', $exam_id)->select('course_exam.course_id')->first();

        $model = new CourseExamAnswer();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> url('csisocietyadmin/exam_answer/'.$exam_id.'/create'),
          'btn'=>'Add',
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj','course_id','exam_id'));
    }

    public function show(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        $exam_id = $request->exam_id;
        $model = CourseExamAnswer::find($request->id);
        $course_id = CourseExamLog::where('course_exam.id', $exam_id)->select('course_exam.course_id')->first();

        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>'/csisocietyadmin/exam_answer/'.$request->exam_id.'/edit/'.$request->id,
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj','exam_id','course_id'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='answer_th'){
            array_push($where_search, ['course_exam_answer.answer_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='answer_en'){
            array_push($where_search, ['course_exam_answer.answer_en', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_exam_answer.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('course_exam_answer')
      ->select('course_exam_answer.*')
      ->where('course_exam_answer.exam_id', $request->exam_id)
      ->where($where_search) //
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_exam_answer')
      ->where('course_exam_answer.exam_id', $request->exam_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="/csisocietyadmin/exam_answer/'.$request->exam_id.'/edit/'.$obj->id.'" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        $obj['text_area'] = AppHelper::instance()->limitCharUTF8($obj['text_area'],30);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

        $model = new CourseExamAnswer;
        $model->exam_id = $request->exam_id;
        $model->answer_th = isset($request->answer_th) ? AppHelper::instance()->cleanInput($request->answer_th) : null;
        $model->answer_en = isset($request->answer_en) ? AppHelper::instance()->cleanInput($request->answer_en) : null;
        $model->is_right = isset($request->is_right) ? AppHelper::instance()->cleanInput($request->is_right) : 1;

        $count = CourseExamAnswer::where('exam_id', $request->exam_id)->count();
        $model->position = $count + 1;

        $model->status = $request->status;
        $model->save();

        return redirect(route('admin').'/exam_answer/'.$model->exam_id)->with('success', 'Data has been update');
    }

    public function update(Request $request)
    {

        $model = CourseExamAnswer::find($request->id);
        if($model){

          $model->exam_id = $request->exam_id;
          $model->answer_th = isset($request->answer_th) ? AppHelper::instance()->cleanInput($request->answer_th) : '';
          $model->answer_en = isset($request->answer_en) ? AppHelper::instance()->cleanInput($request->answer_en) : '';
          $model->is_right = isset($request->is_right) ? AppHelper::instance()->cleanInput($request->is_right) : 1;

          $model->status = $request->status;
          $model->save();

          return redirect(route('admin').'/exam_answer/'.$request->exam_id)->with('success', 'Data has been update');
        }
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CourseExamAnswer::where('exam_id',$request->exam_id)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CourseExamAnswer::where('exam_id',$request->exam_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {
        $model = CourseExamAnswer::find($request->id);
        CourseExamAnswer::where('exam_id', $model->exam_id)->where('position','>',$model->position)
          ->update(['position' => DB::raw('position - 1')]);
        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = CourseExamAnswer::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        // $model->timestamps = false;
        $model->save();

        return response()->json();
    }

  }
