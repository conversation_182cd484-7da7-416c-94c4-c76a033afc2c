<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CourseGroup;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\GroupLog;
use Maatwebsite\Excel\Facades\Excel;


class GroupFreeController extends Controller
{
    public $prefix = 'group_free';
    public $project_url = 'group_free';
    public $project_layout = 'admin.Lrk.group_free';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
        $model = new CourseGroup();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> route($this->prefix.'.store'),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
        $model = CourseGroup::find($id);
        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>route($this->prefix.'.update',$model->id),
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course_group.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['course_group.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['course_group.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_group.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('course_group')
      ->select('course_group.*')
      ->where('group_cert', 1)
      ->where('is_free', 1)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_group')
      ->where('group_cert', 1)
      ->where('is_free', 1)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      $model = new CourseGroup;
      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->details = isset($request->details) ? AppHelper::instance()->cleanInputBr($request->details) : null;

      if ($request->hasFile('banner')) {
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/group');
      }

      $model->group_cert = 1;
      $model->is_free = 1;
      $model->price = 0;

      $model->course = is_array($request->course) ? implode(",",$request->course) : '';


      $count = CourseGroup::where('is_free', 1)->where('group_cert',1)->count();
      $model->position = $count + 1;

      $model->status = $request->status;
      $model->save();

      if(is_array($request->course)){ 
        foreach($request->course as $value) { 
          $log = new GroupLog();
          $log->group_id = $model->id;
          $log->course_id = $value;
          $log->save();
        }
      }

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

        $model = CourseGroup::find($id);
        $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
        $model->details = isset($request->details) ? AppHelper::instance()->cleanInputBr($request->details) : null;


        $old_tags = explode(',', $model->course); 
        $model->course = is_array($request->course) ? implode(",",$request->course) : ''; 
        if(is_array($request->course)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          $diff_tags = array_diff($old_tags, $request->course); 

          if(count(array_diff($old_tags, $request->course))>0 || count(array_diff($request->course, $old_tags))>0){
            $log = GroupLog::where('group_id', $model->id);
            $log->delete();

            foreach($request->course as $value) { 
              $log = new GroupLog();
              $log->group_id = $model->id;
              $log->course_id = $value;
              $log->save();
            }
          }
        }

        if ($request->hasFile('banner')) {
          if($model->banner!=null&&$model->banner!=''){
            AppHelper::instance()->removeImage($model->banner);
          }
          $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/group');
        }else{
          if(empty($request->source_banner)){
            if($model->banner!=null&&$model->banner!=''){
              AppHelper::instance()->removeImage($model->banner);
            }
            $model->banner = null;
          }
        }

        $model->group_cert = 1;
        $model->is_free = 1;
        $model->price = 0;

        $model->status = $request->status;
        $model->save();

        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CourseGroup::where('is_free',1)->where('group_cert',1)
          ->where('id',$data[0])
          ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CourseGroup::where('is_free', 1)->where('group_cert',1)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {

        $model = CourseGroup::find($request->id);

        $log = GroupLog::where('group_id', $model->id)->get();
        foreach($log as $key=>$val){
          $val->delete();
        }
      
        CourseGroup::where('is_free', 1)->where('group_cert', 1)->where('position','>',$model->position)
        ->update(['position' => DB::raw('position - 1')]);

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = CourseGroup::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

}
