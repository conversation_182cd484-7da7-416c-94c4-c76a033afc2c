<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\BannerHome;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class BannerHomeController extends Controller
{
    public $prefix = 'banner_home';
    public $project_url = 'banner_home';
    public $project_layout = 'admin.Lrk.banner_home';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
        $model = new BannerHome();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> route($this->prefix.'.store'),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
        $model = BannerHome::find($id);
        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>route($this->prefix.'.update',$model->id),
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['home_banner.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['home_banner.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['home_banner.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['home_banner.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('home_banner')
      ->select('home_banner.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('home_banner')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      $model = new BannerHome;
      $model->page = isset($request->page) ? $request->page : 1;
      $model->type = isset($request->type) ? $request->type : 1;
      if($model->type == 1){
        $model->cta = isset($request->cta) ? AppHelper::instance()->cleanInput($request->cta) : null;
        $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;
      }else{
        $model->cta = null;
        $model->link = null;
      }
      $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
      $model->title = isset($request->title) ? $request->title : null;
      $model->subtitle = isset($request->subtitle) ? $request->subtitle : null;
      $model->object_position = isset($request->object_position) ? $request->object_position : 1;

      if ($request->hasFile('image_th_desktop')) {
        $model->image_th_desktop = AppHelper::instance()->saveImage($request->file('image_th_desktop'),'/upload/home_banner');
      }

      if ($request->hasFile('image_th_mobile')) {
        $model->image_th_mobile = AppHelper::instance()->saveImage($request->file('image_th_mobile'),'/upload/home_banner');
      }

      if ($request->hasFile('image_en_desktop')) {
        $model->image_en_desktop = AppHelper::instance()->saveImage($request->file('image_en_desktop'),'/upload/home_banner');
      }

      if ($request->hasFile('image_en_mobile')) {
        $model->image_en_mobile = AppHelper::instance()->saveImage($request->file('image_en_mobile'),'/upload/home_banner');
      }

      $count = BannerHome::all()->count();
      $model->position = $count + 1;

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

        $model = BannerHome::find($id);
        if($model){
          $model->page = isset($request->page) ? $request->page : 1;
          $model->type = isset($request->type) ? $request->type : null;
          $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
          if($model->type == 1){
            $model->cta = isset($request->cta) ? AppHelper::instance()->cleanInput($request->cta) : null;
            $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;
          }else{
            $model->cta = null;
            $model->link = null;
          }
  
          $model->title = isset($request->title) ? $request->title : null;
          $model->subtitle = isset($request->subtitle) ? $request->subtitle : null;
          $model->object_position = isset($request->object_position) ? $request->object_position : 1;
  
          if ($request->hasFile('image_th_desktop')) {
            if($model->image_th_desktop!=null&&$model->image_th_desktop!=''){
              AppHelper::instance()->removeImage($model->image_th_desktop);
            }
            $model->image_th_desktop = AppHelper::instance()->saveImage($request->file('image_th_desktop'),'/upload/home_banner');
          }else{
            if(empty($request->source_image_th_desktop)){
              if($model->image_th_desktop!=null&&$model->image_th_desktop!=''){
                AppHelper::instance()->removeImage($model->image_th_desktop);
              }
              $model->image_th_desktop = null;
            }
          }
  
          if ($request->hasFile('image_th_mobile')) {
            if($model->image_th_mobile!=null&&$model->image_th_mobile!=''){
              AppHelper::instance()->removeImage($model->image_th_mobile);
            }
            $model->image_th_mobile = AppHelper::instance()->saveImage($request->file('image_th_mobile'),'/upload/home_banner');
          }else{
            if(empty($request->source_image_th_mobile)){
              if($model->image_th_mobile!=null&&$model->image_th_mobile!=''){
                AppHelper::instance()->removeImage($model->image_th_mobile);
              }
              $model->image_th_mobile = null;
            }
          }
  
          if ($request->hasFile('image_en_desktop')) {
            if($model->image_en_desktop!=null&&$model->image_en_desktop!=''){
              AppHelper::instance()->removeImage($model->image_en_desktop);
            }
            $model->image_en_desktop = AppHelper::instance()->saveImage($request->file('image_en_desktop'),'/upload/home_banner');
          }else{
            if(empty($request->source_image_en_desktop)){
              if($model->image_en_desktop!=null&&$model->image_en_desktop!=''){
                AppHelper::instance()->removeImage($model->image_en_desktop);
              }
              $model->image_en_desktop = null;
            }
          }
  
          if ($request->hasFile('image_en_mobile')) {
            if($model->image_en_mobile!=null&&$model->image_en_mobile!=''){
              AppHelper::instance()->removeImage($model->image_en_mobile);
            }
            $model->image_en_mobile = AppHelper::instance()->saveImage($request->file('image_en_mobile'),'/upload/home_banner');
          }else{
            if(empty($request->source_image_en_mobile)){
              if($model->image_en_mobile!=null&&$model->image_en_mobile!=''){
                AppHelper::instance()->removeImage($model->image_en_mobile);
              }
              $model->image_en_mobile = null;
            }
          }
  
          $model->status = $request->status;
          $model->save();
  
          return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
        }else{
          return redirect(route('admin').'/'.$this->project_url)->with('error', 'Data has been invalided');
        }
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = BannerHome::find($data[0]);
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = BannerHome::count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {

        $model = BannerHome::find($request->id);
        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = BannerHome::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

}
