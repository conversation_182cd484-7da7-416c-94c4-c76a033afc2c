<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use App\Services\CompanyVisitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\Datatables\Datatables;

class CompanyVisitController extends Controller
{
    protected $companyVisitService;
    public $prefix = 'company_visit';
    public $project_url = 'company_visit';
    public $project_layout = 'admin.Lrk.company_visit';

    public function __construct(CompanyVisitService $companyVisitService)
    {
        $this->middleware('auth:admin');
        $this->companyVisitService = $companyVisitService;
    }

    public function index()
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $company_visit_all = $this->companyVisitService->getCount();
        $company_visit_open = $this->companyVisitService->getAll()->where('status', 1)->count();
        $company_visit_closed = $this->companyVisitService->getAll()->where('status', 2)->count();

        $obj = array(
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.index', compact(
            'obj', 
            'company_visit_all', 
            'company_visit_open', 
            'company_visit_closed'
        ));
    }

    public function create()
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $data = $this->companyVisitService->prepareDataForCreate();
        $model = $data['model'];

        $obj = array(
            'type' => 'create',
            'route' => route('company_visit.store'),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', compact('model', 'obj'));
    }

    public function store(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $companyVisit = $this->companyVisitService->createCompanyVisit($request->all());

            if ($companyVisit) {
                return response()->json(['success' => true, 'message' => 'สร้างข้อมูลสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function show($id)
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $model = $this->companyVisitService->findById($id);

        if (!$model) {
            return redirect()->route('admin.company_visit')->with('error', 'ไม่พบข้อมูล');
        }

        $obj = array(
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.view', compact('model', 'obj'));
    }

    public function edit($id)
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $model = $this->companyVisitService->findById($id);

        if (!$model) {
            return redirect()->route('admin.company_visit')->with('error', 'ไม่พบข้อมูล');
        }

        $obj = array(
            'type' => 'edit',
            'route' => route('company_visit.update', $id),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', compact('model', 'obj'));
    }

    public function update(Request $request, $id)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $companyVisit = $this->companyVisitService->updateCompanyVisit($id, $request->all());

            if ($companyVisit) {
                return response()->json(['success' => true, 'message' => 'อัปเดตข้อมูลสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function destroy(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $id = $request->input('id');
            $result = $this->companyVisitService->deleteCompanyVisit($id);

            if ($result) {
                return response()->json(['success' => true, 'message' => 'ลบข้อมูลสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function status(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $id = $request->input('id');
            $result = $this->companyVisitService->toggleStatus($id);

            if ($result) {
                return response()->json(['success' => true, 'message' => 'อัปเดตสถานะสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถอัปเดตสถานะได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function position(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $id = $request->input('id');
            $position = $request->input('position');
            $result = $this->companyVisitService->updatePosition($id, $position);

            if ($result) {
                return response()->json(['success' => true, 'message' => 'อัปเดตลำดับสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถอัปเดตลำดับได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
        $model = $this->companyVisitService->getAll();

        return $datatables->of($model)
            ->addIndexColumn()
            ->addColumn('action', function ($model) {
                $editUrl = route('admin') . '/' . $this->prefix . '/' . $model->id . '/edit';

                return '
            <div class="btn-group">
                <a href="' . $editUrl . '" class="btn btn-sm btn-info" title="แก้ไข">
                    <i class="fa fa-edit"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteData(' . $model->id . ', \'/csisocietyadmin/' . $this->prefix . '/delete\', \'datatable_company_visit\')" class="btn btn-sm btn-danger" title="ลบ">
                    <i class="fa fa-times"></i>
                </a>
            </div>';
            })
            ->addColumn('status', function ($model) {
                return $model->status;
            })
            ->addColumn('status_label', function ($model) {
                return $model->status == 1 ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
            })
            ->addColumn('category_display', function ($model) {
                return 'Company Visit';
            })
            ->rawColumns(['action'])
            ->make(true);
    }
}
