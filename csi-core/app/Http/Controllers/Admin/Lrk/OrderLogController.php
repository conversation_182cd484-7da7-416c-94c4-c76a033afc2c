<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\UsersOrderLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Users;
use Maatwebsite\Excel\Facades\Excel;


class OrderLogController extends Controller
{
    public $prefix = 'order_log';
    public $project_url = 'order_log';
    public $project_layout = 'admin.Lrk.order_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      return response()->json();
    }

    public function status(Request $request)
    {
      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_order_list.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_order_list.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_order_list.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='order_no'){
            array_push($where_search, ['user_order_log.order_no', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='email'){
            array_push($where_search, ['user.email', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='mobile'){
            array_push($where_search, ['user.mobile', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_order_list.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_order_list')
      ->join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
      ->join('user', 'user.id', 'user_order_log.user_id')
      ->select('user_order_list.*', 'user.name as u_name', 'user.lastname as u_lastname', 'user_order_log.order_no', 'user.email', 'user.mobile')
      ->where('user_order_list.course_id', $request->course_id)
      ->where('user_order_log.status', 2)
      ->where('user_order_log.payment_type', 3)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_order_list')
      ->join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
      ->join('user', 'user.id', 'user_order_log.user_id')
      ->where('user_order_list.course_id', $request->course_id)
      ->where('user_order_log.status', 2)
      ->where('user_order_log.payment_type', 3)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {

      $response = new Dummy();
      $response['status'] = 'false';

      $model = UsersOrderLog::find($request->id);

      if($model){
  
        $model->status = isset($request->gallery_modal_status_2) ? $request->gallery_modal_status_2 : '';

        $model->save();

        if($model->status == 2 && $model->payment_type == 2){
          $user_detail = Users::where('id', $model->user_id)->first();
          if($user_detail){
            $obj = new Dummy();
            $obj['subject'] = 'ขอบคุณสำหรับการสั่งซื้อของคุณ';
            $obj['title'] = 'สวัสดี คุณ '.$user_detail->name.' '.$user_detail->lastname;
            $obj['message'] = 'รายการคำสั่งซื้อของท่าน เมื่อวันที่ '.$model->created_at.' ได้รับการอนุมัติแล้ว';
            $obj['email'] = $user_detail->email;
            AppHelper::instance()->mailTemplate($obj);
          }
        }

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = UsersOrderLog::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }
}
