<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class StatController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function stat(Request $request)
    {
      $response = (object) [
        'label'=>'',
        'data'=>''
      ];

      if (isset($request->name)) {

        if ($request->name=='temp_chart_status') {
            $active = DB::table('temps')
              ->where('status','1')
              ->count();
            $inactive = DB::table('temps')
              ->where('status','0')
              ->count();

            $response->label = array('Active','Inactvie');
            $response->data = array($active,$inactive);
        }
        else if($request->name=='temp_chart_image'){
          $notnull = DB::table('temps')
            ->whereNotNull('image')
            ->count();
          $null = DB::table('temps')
            ->whereNull('image')
            ->count();

          $response->label = array('Has Image','Null');
          $response->data = array($notnull,$null);
        }


      }

      return response()->json($response);
      // return $response;
    }

    public function export(Request $request){
      $label = explode( ',', $request->label );
      $data = explode( ',', $request->data );
      $arr = array();
      for($i=0;$i<count($data);$i++){
        $arr[$label[$i]] = $data[$i];
      }
      $ex_array[] = $arr;

      $export_type = isset($request->export_type) ? $request->export_type : 'csv';
      $export_name = isset($request->filename) ? $request->filename : 'excel';
      if ($export_type=='excel') {
          Excel::create($export_name, function ($excel) use ($ex_array) {
              $excel->sheet('Data', function ($sheet) use ($ex_array) {
                  $sheet->fromArray($ex_array);
              });
          })->download('xlsx');
      } else {
          $headers = array(
            "Content-Encoding" => 'UTF-8',
            "Content-type" => "text/csv; charset=UTF-8",
            "Content-Disposition" => "attachment; ".$export_name.".csv",
            "Pragma" => "no-cache",
            "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
            "Expires" => "0"
          );
          // echo "\xEF\xBB\xBF";
          $keys = array_keys($ex_array[0]);
          $callback = function () use ($ex_array,$keys) {
              $fp= fopen('php://output', 'w');
              fputcsv($fp, $keys);
              foreach ($ex_array as $fields) {
                  fputcsv($fp, $fields);
              }
              fclose($fp);
          };
          return response()->stream($callback, 200, $headers);
      }
    }


}
