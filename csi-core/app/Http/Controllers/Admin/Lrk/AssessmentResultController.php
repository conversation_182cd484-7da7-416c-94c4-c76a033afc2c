<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\AssessmentLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Assessment;
use App\Models\Core\AssessmentChoice;
use Maatwebsite\Excel\Facades\Excel;


class AssessmentResultController extends Controller
{
    public $prefix = 'assessment_result';
    public $project_url = 'assessment_result';
    public $project_layout = 'admin.Lrk.assessment_result';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new AssessmentLog();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id, Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = AssessmentLog::find($id);
      if($model){
        $assessment = AssessmentLog::join('assessment', 'assessment.id','assessment_log.assessment_id')
        ->leftjoin('assessment_choice', 'assessment_choice.id','assessment_log.choice')
        ->where('user_id', $model->user_id)
        ->where('course_id', $model->course_id)
        ->get();

        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($assessment);
        return view($this->project_layout.'.form', compact('model','obj','assessment'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['assessment_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['assessment_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['assessment_log.id', $value['search']['value']]);
          }else if($value['data']=='u_id'){
            array_push($where_search, ['user.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_email'){
            array_push($where_search, ['user.email', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='c_id'){
            array_push($where_search, ['course.id', $value['search']['value']]);
          }else if($value['data']=='c_name'){
            array_push($where_search, ['course.title_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['assessment_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('assessment_log')
      ->join('user', 'user.id', 'assessment_log.user_id')
      ->join('course', 'course.id', 'assessment_log.course_id')
      ->select('assessment_log.*', 'user.id as u_id', 'user.name as u_name', 'user.lastname as u_lastname', 'user.email as u_email', 'course.id as c_id', 'course.title_th as c_name')
      ->groupBy('user_id')
      ->groupBy('course_id')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = count(DB::table('assessment_log')
      ->join('user', 'user.id', 'assessment_log.user_id')
      ->join('course', 'course.id', 'assessment_log.course_id')
      ->groupBy('user_id')
      ->groupBy('course_id')
      ->where($where_search)
      ->pluck('assessment_log.user_id')->toArray());

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $score = AssessmentLog::join('assessment_choice', 'assessment_choice.id', 'assessment_log.choice')
        ->where('assessment_log.user_id', $value->u_id)->where('assessment_log.course_id', $value->c_id)->sum('assessment_choice.point');
        if($score != 0){
          $obj['score'] = $score;
        }else{
          $obj['score'] = 0;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-eye"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
    }

    public function update(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
    }
}
