<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\VolumnBy;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Models\Admin;
use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\UserHistory;
use App\Models\Core\VolumnByCourse;
use App\Models\Core\VolumnByLot;
use App\Models\Core\VolumnByUser;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel;


class VolumnByController extends Controller
{
    public $prefix = 'volumn_by';
    public $project_url = 'volumn_by';
    public $project_layout = 'admin.Lrk.volumn_by';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new VolumnBy();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = VolumnBy::find($id);
      $article_id = $model->id;
      if($model){
        $obj = array(
          'type'=>'view',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.view', compact('model','obj','article_id'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = VolumnBy::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['volumn_by.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['volumn_by.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['volumn_by.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['volumn_by.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('volumn_by')
      ->select('volumn_by.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('volumn_by')
      ->where($where_search)
      ->count();


      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        if(Auth::user()->level == '99'){
          $obj['action'] = '
          <div class="btn-group">
            <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
            <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
          </div>';
        }else{
          $obj['action'] = '
          <div class="btn-group">
            <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          </div>';
        }
        
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new VolumnBy;

      $admin_username = Admin::where('username', $request->username)->first();
      if(!$admin_username){

        $slug_clean = AppHelper::instance()->cleanInputSlug($request->slug);
        $slug = VolumnBy::where('slug','=', $slug_clean)->count();

        if($slug==0){
          $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : AppHelper::instance()->cleanInputSlug($request->title_th);
        }else{
          $characters = '0123456789';
          $charactersLength = strlen($characters);
          $ramdomNumber = '';
          for ($i = 0; $i < 4; $i++) {
              $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
          }
          $slug_plus = $ramdomNumber;
          $slug_text = $request->slug.''.$slug_plus;
          $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
        }

        $model->company_name = isset($request->company_name) ? AppHelper::instance()->cleanInput($request->company_name) : null;
        $model->company_title = isset($request->company_title) ? AppHelper::instance()->cleanInput($request->company_title) : null;
        $model->company_subtitle = isset($request->company_subtitle) ? AppHelper::instance()->cleanInputBr($request->company_subtitle) : null;
        $model->domain = isset($request->domain) ? AppHelper::instance()->cleanInput($request->domain) : null;
        
        if ($request->hasFile('company_image')) {
          $model->company_image = AppHelper::instance()->saveImage($request->file('company_image'),'/upload/company');
        }
  
        $model->status = $request->status;
        
        $model->save();

        $admin = new Admin();
        $admin->name = $request->admin_name;
        $admin->username = $request->username;
        $admin->password = isset($request->password) ? Hash::make($request->password) : null;
        $admin->level = 55;
        $admin->company_id = $model->id;
        $admin->save();

        return redirect(route('admin').'/volumn_by/'.$model->id.'/edit/')->with('success', 'Data has been update');
        
      }else{
        return redirect(route('admin').'/'.$this->project_url)->with('errors', 'Username Cannot be used');
      }
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = VolumnBy::find($id);
      if($model){

        $slug_clean_th = AppHelper::instance()->cleanInputSlug($request->slug);
        $slug_th = VolumnBy::where('slug','=', $slug_clean_th)->where('id','!=', $id)->count();
        if($slug_th==0){
          $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : '';
        }else{
          $characters = '0123456789';
          $charactersLength = strlen($characters);
          $ramdomNumber = '';
          for ($i = 0; $i < 4; $i++) {
              $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
          }
          $slug_plus = $ramdomNumber;
          $slug_text = $request->slug.''.$slug_plus;
          $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
        }

        $model->company_name = isset($request->company_name) ? AppHelper::instance()->cleanInput($request->company_name) : null;
        $model->company_title = isset($request->company_title) ? AppHelper::instance()->cleanInput($request->company_title) : null;
        $model->company_subtitle = isset($request->company_subtitle) ? AppHelper::instance()->cleanInputBr($request->company_subtitle) : null;
        $model->domain = isset($request->domain) ? AppHelper::instance()->cleanInput($request->domain) : null;
        
        if ($request->hasFile('company_image')) {
          if($model->company_image!=null&&$model->company_image!=''){
            AppHelper::instance()->removeImage($model->company_image);
          }
          $model->company_image = AppHelper::instance()->saveImage($request->file('company_image'),'/upload/company');
        }else{
          if(empty($request->source_company_image)){
            if($model->company_image!=null&&$model->company_image!=''){
              AppHelper::instance()->removeImage($model->company_image);
            }
            $model->company_image = null;
          }
        }
        
        $model->status = $request->status;
  
        $model->save();
  
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been updated');
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = VolumnBy::find($request->id);
      if($model){

        $volumn_lot = VolumnByLot::where('company_id', $model->id)->get();
        foreach($volumn_lot as $key_lot=>$val_lot){
          $course = VolumnByCourse::where('volumn_id',$val_lot->id)->get();
          foreach($course as $key_course=>$val_course){
            $val_course->delete();
          }
          $user = VolumnByUser::where('company_id', $val_lot->company_id)
          ->where('lot_id', $val_lot->id)
          ->get();
          foreach($user as $key_user=>$val_user){
            $user = User::where(function ($user) use ($val_user) {
              $user->where('internal_email',$val_user->email);
              $user->orWhere('email',$val_user->email);
              });
            $user = $user->first();

            if($user){
                $history = UserHistory::where('user_id',$user->id)
                ->where('get_type',6)
                ->where('company_id', $val_user->company_id)
                ->where('company_lot_id', $val_user->lot_id)
                ->where('expired','>=',Carbon::now())
                ->get();
                foreach($history as $val_his){
                  $val_his->expired = Carbon::now()->subdays(1);
                  $val_his->save();
                }
            }
            $val_user->delete();
          }
          $val_lot->delete();
        }

        $model->delete();
      }

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = VolumnBy::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
