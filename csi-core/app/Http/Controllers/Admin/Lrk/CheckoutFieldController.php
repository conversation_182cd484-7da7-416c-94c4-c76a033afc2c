<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Core\CheckoutField;
use Yajra\DataTables\Facades\DataTables;

class CheckoutFieldController extends Controller
{
    public $prefix = 'checkout_field';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
        $obj = [
            'prefix' => $this->prefix,
            'title' => 'Checkout Fields'
        ];
        return view('admin.Lrk.checkout_field.index', compact('obj'));
    }

    public function dblist(Request $request)
    {
        $query = CheckoutField::select(['id', 'field_name', 'field_label', 'field_type', 'is_required', 'status']);

        return DataTables::of($query)
            ->addColumn('type_badge', function ($row) {
                $badges = [
                    'text' => 'primary',
                    'email' => 'info', 
                    'number' => 'success',
                    'select' => 'warning',
                    'checkbox' => 'secondary',
                    'radio' => 'secondary',
                    'textarea' => 'dark',
                    'file' => 'danger'
                ];
                $class = $badges[$row->field_type] ?? 'light';
                return '<span class="badge badge-' . $class . '">' . $row->field_type . '</span>';
            })
            ->addColumn('status_badge', function ($row) {
                return $row->status 
                    ? '<span class="badge badge-success">Active</span>'
                    : '<span class="badge badge-danger">Inactive</span>';
            })
            ->addColumn('required_badge', function ($row) {
                return $row->is_required 
                    ? '<span class="badge badge-warning">Required</span>'
                    : '<span class="badge badge-light">Optional</span>';
            })
            ->addColumn('actions', function ($row) {
                $actions = '<div class="btn-group">';
                $actions .= '<a href="' . route('checkout_fields.edit', $row->id) . '" class="btn btn-sm btn-primary">Edit</a>';
                $actions .= '<button class="btn btn-sm btn-danger" onclick="deleteField(' . $row->id . ')">Delete</button>';
                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['type_badge', 'status_badge', 'required_badge', 'actions'])
            ->make(true);
    }

    public function create()
    {
        $obj = [
            'prefix' => $this->prefix,
            'title' => 'Create Checkout Field',
            'field' => new CheckoutField()
        ];

        return view('admin.Lrk.checkout_field.form', compact('obj'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'field_name' => 'required|string|max:255|unique:checkout_fields,field_name',
            'field_label' => 'required|string|max:255',
            'field_type' => 'required|in:text,email,number,select,checkbox,radio,textarea,file'
        ]);

        CheckoutField::create([
            'field_name' => $request->field_name,
            'field_label' => $request->field_label,
            'field_type' => $request->field_type,
            'field_options' => $request->field_options ? json_decode($request->field_options, true) : null,
            'validation_rules' => $request->validation_rules ? json_decode($request->validation_rules, true) : null,
            'placeholder' => $request->placeholder,
            'help_text' => $request->help_text,
            'is_required' => $request->has('is_required'),
            'status' => $request->has('status')
        ]);

        return redirect()->route('admin.checkout_fields')->with('success', 'Field created successfully');
    }

    public function edit($id)
    {
        $field = CheckoutField::findOrFail($id);
        $obj = [
            'prefix' => $this->prefix,
            'title' => 'Edit Checkout Field',
            'field' => $field
        ];

        return view('admin.Lrk.checkout_field.form', compact('obj', 'field'));
    }

    public function update(Request $request, $id)
    {
        $field = CheckoutField::findOrFail($id);

        $request->validate([
            'field_name' => 'required|string|max:255|unique:checkout_fields,field_name,' . $id,
            'field_label' => 'required|string|max:255',
            'field_type' => 'required|in:text,email,number,select,checkbox,radio,textarea,file'
        ]);

        $field->update([
            'field_name' => $request->field_name,
            'field_label' => $request->field_label,
            'field_type' => $request->field_type,
            'field_options' => $request->field_options ? json_decode($request->field_options, true) : null,
            'validation_rules' => $request->validation_rules ? json_decode($request->validation_rules, true) : null,
            'placeholder' => $request->placeholder,
            'help_text' => $request->help_text,
            'is_required' => $request->has('is_required'),
            'status' => $request->has('status')
        ]);

        return redirect()->route('admin.checkout_fields')->with('success', 'Field updated successfully');
    }

    public function destroy($id)
    {
        $field = CheckoutField::findOrFail($id);
        $field->delete();

        return response()->json(['success' => true]);
    }
}