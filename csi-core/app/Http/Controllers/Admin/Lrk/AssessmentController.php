<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\Assessment;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\AssessmentChoice;
use App\Models\Core\AssessmentLog;
use Maatwebsite\Excel\Facades\Excel;


class AssessmentController extends Controller
{
    public $prefix = 'assessment';
    public $project_url = 'assessment';
    public $project_layout = 'admin.Lrk.assessment';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Assessment();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = Assessment::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['assessment.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['assessment.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['assessment.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['assessment.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('assessment')
      ->select('assessment.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('assessment')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Assessment;

      $model->point_type = isset($request->point_type) ? AppHelper::instance()->cleanInput($request->point_type) : 1;

      $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : 1;
      $model->question_th = isset($request->question_th) ? AppHelper::instance()->cleanInput($request->question_th) : null;
      // $model->question_en = isset($request->question_en) ? AppHelper::instance()->cleanInput($request->question_en) : null;


      $count = Assessment::all()->count();
      $model->position = $count + 1;

      $model->status = $request->status;

      $model->save();

      if($model->type != 2){
        return redirect(route('admin').'/assessment/'.$model->id.'/edit/')->with('success', 'Data has been store');
      }else{
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = Assessment::find($id);
      
      $model->point_type = isset($request->point_type) ? AppHelper::instance()->cleanInput($request->point_type) : 1;

      // $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : 1;
      $model->question_th = isset($request->question_th) ? AppHelper::instance()->cleanInput($request->question_th) : null;
      // $model->question_en = isset($request->question_en) ? AppHelper::instance()->cleanInput($request->question_en) : null;
      
      $model->status = $request->status;
      $model->save();

      if($model->type == 2){
        $choice = AssessmentChoice::where('assessment_id', $model->id)->get();
        foreach($choice as $val_choice){
          $val_choice->delete();
        }
      }

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = Assessment::find($data[0]);
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = Assessment::count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = Assessment::find($request->id);

      $log = AssessmentLog::where('assessment_id', $model->id)->get();
      foreach($log as $val_log){
        $val_log->delete();
      }

      $choice = AssessmentChoice::where('assessment_id', $model->id)->get();
      foreach($choice as $val_choice){
        $val_choice->delete();
      }

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = Assessment::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function filter_(Request $request)
    {
        if ($request->name=='assessment_filter') {
            $data = DB::table('assessment_log');
            $data->leftjoin('course', 'course.id', '=', 'assessment_log.course_id');
            $data->leftjoin('assessment', 'assessment.id', '=', 'assessment_log.assessment_id');
            $data->leftjoin('assessment_choice', 'assessment_choice.id', '=', 'assessment_log.choice');
            $data->leftjoin('user', 'user.id', '=', 'assessment_log.user_id');
            $data->select('assessment_log.*', 'assessment.question_th', 'assessment_choice.choice_th', 'user.name as u_name', 'user.lastname as u_lastname', 'user.email as u_email'
                          , 'course.title_th as c_title');

            if (isset($request->created_at_start) && isset($request->created_at_to)) {
                $from = Carbon::createFromFormat('d-m-Y', $request->created_at_start)->toDateString();
                $to = Carbon::createFromFormat('d-m-Y', $request->created_at_to)->addDays(1)->toDateString();
                $data->whereBetween('assessment_log.created_at', [$from, $to]);
            }

            $data->orderBy('assessment_log.id', 'asc');
            $data = $data->get();

            $ex_array[]=array(
              'คอร์ส'=>'คอร์ส',
              'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
              'อีเมล์'=>'อีเมล์',
              'หัวข้อสอบถาม'=>'หัวข้อสอบถาม',
              'คำตอบ'=>'คำตอบ',
              'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
            );

            foreach ($data as $e) {

              if($e->choice == null){
                $ans = $e->input;
              }else{
                $ans = $e->choice_th;
              }

              $ex_array[]=array(
              'คอร์ส'=>$e->c_title,
              'ชื่อ - นามสกุล'=>$e->u_name.' '.$e->u_lastname,
              'อีเมล์'=>$e->u_email,
              'หัวข้อสอบถาม'=>$e->question_th,
              'คำตอบ'=>$ans,
              'วันที่เก็บข้อมูล'=>$e->created_at,
              );
            }
        }


        // ไม่ต้องแก้ตรงนี้ก็ได้
        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
        if (isset($ex_array)) {
            if ($export_type=='excel') {
                // Excel::create($request->filename, function ($excel) use ($ex_array) {
                //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
                //         $sheet->fromArray($ex_array);
                //     });
                // })->download('xlsx');
                $export = new TempExport($ex_array);
                // AppHelper::instance()->consoleLog($ex_array);

                return Excel::download($export, $request->filename.'.xlsx');
            } else {
                $headers = array(
                "Content-Encoding" => 'UTF-8',
                "Content-type" => "text/csv; charset=UTF-8",
                "Content-Disposition" => "attachment; ".$request->filename.".csv",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            );
                echo "\xEF\xBB\xBF";

                $keys = array_keys($ex_array[0]);
                $callback = function () use ($ex_array,$keys) {
                    $fp= fopen('php://output', 'w');
                    fputcsv($fp, $keys);
                    foreach ($ex_array as $fields) {
                        fputcsv($fp, $fields);
                    }
                    fclose($fp);
                };

                return response()->stream($callback, 200, $headers);
            }
        }

    }


    public function filter(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='assessment_filter') {
          $data = DB::table('assessment_log');
          $data->leftjoin('course', 'course.id', 'assessment_log.course_id');
          $data->leftjoin('assessment', 'assessment.id', 'assessment_log.assessment_id');
          $data->leftjoin('assessment_choice', 'assessment_choice.id', 'assessment_log.choice');
          $data->leftjoin('user', 'user.id', 'assessment_log.user_id');
          $data->select('assessment_log.*', 'user.name as user_name','user.lastname as user_lastname','user.email as user_email',
                  'user.mobile as user_mobile', 'assessment.question_th', 'assessment.id as a_id', 'course.title_th as c_title', 'user.id as u_id',
                  'course.id as c_id');
          $data->orderBy('assessment_log.created_at', 'asc');
          $data->groupby('assessment_log.course_id');
          $data->get();

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('assessment_log.created_at', [$from, $to]);
          }
          $data = $data->get();
                

          $assessment = Assessment::where('status', 1)->select('id', 'question_th','type')->get();

          foreach($assessment as $key=>$value){
              $assessment_choice = AssessmentChoice::where('assessment_id',$value->id)->where('status',1)->orderby('position','asc')->get();
              $assessment[$key]['assessment_choice'] = $assessment_choice;
          }

          $head = array(
            'คอร์ส'=>'คอร์ส',
            'ชื่อ'=>'ชื่อ',
            'นามสกุล'=>'นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'เบอร์โทรศัพท์'=>'เบอร์โทรศัพท์',
            'คะแนน'=>'คะแนน',
            'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
          );

          foreach($assessment as $key=>$value){
            $head[$value->question_th]=$value->question_th;
          }

          $ex_array[] = $head;

          foreach ($data as $key=>$e) {

            $score = AssessmentLog::join('assessment_choice', 'assessment_choice.id', 'assessment_log.choice')
            ->where('assessment_log.user_id', $e->u_id)->where('assessment_log.course_id', $e->c_id)->sum('assessment_choice.point');

            $data_array=array(
              'คอร์ส'=>$e->c_title,
              'ชื่อ'=>$e->user_name,
              'นามสกุล'=>$e->user_lastname,
              'อีเมล์'=>$e->user_email,
              'เบอร์โทรศัพท์'=>$e->user_mobile,
              'คะแนน'=>$score,
              'วันที่เก็บข้อมูล'=>$e->created_at,
            );

            if(isset($request->created_at_start) && isset($request->created_at_to)){
              $from = Carbon::parse($request->created_at_start)->toDateString();
              $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();

              foreach($assessment as $key_q=>$value_q){
                if($value_q->type == 1){
                  $answer_txt = '';
                  foreach($value_q->assessment_choice as $key_a=>$value_a){
                      $ans_log = AssessmentLog::where('assessment_id',$value_q->id)->where('choice',$value_a->id)->where('course_id', $e->course_id)->where('user_id',$e->user_id)
                      ->whereBetween('created_at', [$from, $to])
                      ->first();
                      if($ans_log){
                        if($answer_txt!=''){
                          $answer_txt.=',';
                        }
                        $answer_txt.=$value_a->choice_th;
                      }
                      // $answer_txt.=$value_a
                  }
                  $data_array[$value_q->question_th]=$answer_txt;
                }else if($value_q->type == 2){
                  $ans_input = AssessmentLog::where('assessment_id',$value_q->id)->where('course_id', $e->course_id)->where('user_id',$e->user_id)
                  ->whereBetween('created_at', [$from, $to])
                  ->first();
                  $data_array[$value_q->question_th]=$ans_input->input;
                }else{
                  $ans_input = AssessmentLog::join('assessment_choice', 'assessment_choice.id', 'assessment_log.choice')
                  ->where('assessment_log.assessment_id',$value_q->id)->where('assessment_log.course_id', $e->course_id)->where('assessment_log.user_id',$e->user_id)
                  ->select('assessment_choice.point as choice_point')
                  ->whereBetween('created_at', [$from, $to])
                  ->first();
                  $data_array[$value_q->question_th]=$ans_input->choice_point;
                }
              }
            }else{
              foreach($assessment as $key_q=>$value_q){
                if($value_q->type == 1){
                  $answer_txt = '';
                  foreach($value_q->assessment_choice as $key_a=>$value_a){
  
                      $ans_log = AssessmentLog::where('assessment_id',$value_q->id)->where('choice',$value_a->id)->where('course_id', $e->course_id)->where('user_id',$e->user_id)
                      ->first();
                      if($ans_log){
                        if($answer_txt!=''){
                          $answer_txt.=',';
                        }
                        $answer_txt.=$value_a->choice_th;
                      }
                      // $answer_txt.=$value_a
                  }
                  $data_array[$value_q->question_th]=$answer_txt;
                }else if($value_q->type == 2){
                  $ans_input = AssessmentLog::where('assessment_id',$value_q->id)->where('course_id', $e->course_id)->where('user_id',$e->user_id)
                  ->first();
                  $data_array[$value_q->question_th]=$ans_input->input;
                }else{
                  $ans_input = AssessmentLog::join('assessment_choice', 'assessment_choice.id', 'assessment_log.choice')
                  ->where('assessment_log.assessment_id',$value_q->id)->where('assessment_log.course_id', $e->course_id)->where('assessment_log.user_id',$e->user_id)
                  ->select('assessment_choice.point as choice_point')
                  ->first();
                  $data_array[$value_q->question_th]=$ans_input->choice_point;
                }
              }
            }
            $ex_array[] = $data_array;
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return Excel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }
}
