<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\UsersActivityLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class ArticleActivityController extends Controller
{
    public $prefix = 'ar_favorite';
    public $project_url = 'ar_favorite';
    public $project_layout = 'admin.Lrk.ar_favorite';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      return response()->json();
    }

    public function status(Request $request)
    {
      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_activity_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_activity_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_activity_log.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', $value['search']['value']]);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', $value['search']['value']]);
          }else{
            array_push($where_search, ['user_activity_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_activity_log')
      ->join('user', 'user.id', 'user_activity_log.user_id')
      ->select('user_activity_log.*', 'user.name as u_name', 'user.lastname as u_lastname')
      ->where('user_activity_log.activity_type', 3)
      ->where('user_activity_log.article_id', $request->article_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_activity_log')
      ->join('user', 'user.id', 'user_activity_log.user_id')
      ->where('user_activity_log.activity_type', 3)
      ->where('user_activity_log.article_id', $request->article_id)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column
      
      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      return json_decode($response, true);

    }

    public function export(Request $request)
    {
      $article_id = $request->article_id;
      
        if (isset($request->name)) {
            if ($request->name=='article_activity_export') {
                $data = DB::table('user_activity_log')
                ->leftjoin('user', 'user.id', 'user_activity_log.user_id')
                ->leftjoin('article', 'article.id', 'user_activity_log.article_id')
                ->select('user_activity_log.*', 'user.name as user_name','user.lastname as user_lastname','user.email as user_email',
                        'user.mobile as user_mobile', 'article.title_th as ar_title')
                ->where('user_activity_log.activity_type', 3)
                ->where('user_activity_log.article_id', $article_id)
                ->orderBy('user_activity_log.created_at', 'desc')
                ->get();

                $ex_array[] = array(
                  'ชื่อ'=>'ชื่อ',
                  'นามสกุล'=>'นามสกุล',
                  'อีเมล์'=>'อีเมล์',
                  'เบอร์โทรศัพท์'=>'เบอร์โทรศัพท์',
                  'ที่อยู่'=>'ที่อยู่',
                  'บทความ'=>'บทความ',
                  'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
                );

                foreach ($data as $key=>$e) {

                  $data_array=array(
                    'ชื่อ'=>$e->user_name,
                    'นามสกุล'=>$e->user_lastname,
                    'อีเมล์'=>$e->user_email,
                    'เบอร์โทรศัพท์'=>$e->user_mobile,
                    'บทความ'=>$e->ar_title,
                    'วันที่เก็บข้อมูล'=>$e->created_at,
                  );
                  $ex_array[] = $data_array;
                }
            }



            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    // Excel::create($request->filename, function ($excel) use ($ex_array) {
                    //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
                    //         $sheet->fromArray($ex_array);
                    //     });
                    // })->download('xlsx');
                    $export = new TempExport($ex_array);
                    // AppHelper::instance()->consoleLog($ex_array);

                    return Excel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }
}
