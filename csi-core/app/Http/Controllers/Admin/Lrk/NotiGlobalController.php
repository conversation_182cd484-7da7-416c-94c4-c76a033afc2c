<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\NotiGlobal;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Lays\BlogTagLog;
use App\Models\Lays\NotiLog;
use App\Models\Lays\SurveyAnswer;
use App\Models\Lays\SurveyLog;
use App\Models\Lays\SurveyQuestion;
use Maatwebsite\Excel\Facades\Excel;


class NotiGlobalController extends Controller
{
    public $prefix = 'noti_global';
    public $project_url = 'noti_global';
    public $project_layout = 'admin.Lrk.noti_global';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new NotiGlobal();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = NotiGlobal::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return new Dummy();
      }
      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['noti_global.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['noti_global.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['noti_global.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['noti_global.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('noti_global')
      ->select('noti_global.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('noti_global')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        $model = new NotiGlobal;
  
        $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
        $model->description = isset($request->description) ? AppHelper::instance()->cleanInput($request->description) : null;
        $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;


        $model->started_date = isset($request->started_date) ? Carbon::parse($request->started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
        $model->end_date = isset($request->end_date) ? Carbon::parse($request->end_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');

        //save image
        if ($request->hasFile('image')) {
          $model->image = AppHelper::instance()->saveImage($request->file('image'),'noti');
        }

        $model->status = $request->status;
  
        $model->save();
  
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        $model = NotiGlobal::find($id);
  
        $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
        $model->description = isset($request->description) ? AppHelper::instance()->cleanInput($request->description) : null;
        $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;

        $model->started_date = isset($request->started_date) ? Carbon::parse($request->started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
        $model->end_date = isset($request->end_date) ? Carbon::parse($request->end_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');

        //save image
        if ($request->hasFile('image')) {
          if($model->image!=null&&$model->image!=''){
            AppHelper::instance()->removeImage($model->image);
          }
          $model->image = AppHelper::instance()->saveImage($request->file('image'),'noti');
        }else{
          if(empty($request->source_image)){
            if($model->image!=null&&$model->image!=''){
              AppHelper::instance()->removeImage($model->image);
            }
            $model->image = null;
          }
        }

        $model->status = $request->status;

        $model->save();
        
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }
    }

    public function destroy(Request $request)
    {
      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return response()->json();
      }
      $model = NotiGlobal::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
