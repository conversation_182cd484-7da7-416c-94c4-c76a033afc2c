<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Core\CheckoutField;
use App\Models\Core\Course;
use App\Models\Core\SubscriptionMain;

class CheckoutFieldApiController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    // Get available fields for a course (fields not yet assigned)
    public function getAvailableFields($courseId)
    {
        $course = Course::findOrFail($courseId);
        $assignedFieldIds = $course->checkoutFields()->pluck('checkout_fields.id')->toArray();
        
        $availableFields = CheckoutField::where('status', true)
            ->whereNotIn('id', $assignedFieldIds)
            ->orderBy('field_label')
            ->get();

        return response()->json([
            'success' => true,
            'fields' => $availableFields
        ]);
    }

    // Get selected fields for a course
    public function getSelectedFields($courseId)
    {
        $course = Course::with(['checkoutFields' => function($query) {
            $query->where('checkout_fields.status', true)->orderBy('position');
        }])->findOrFail($courseId);

        return response()->json([
            'success' => true,
            'fields' => $course->checkoutFields
        ]);
    }

    // Add a field to a course
    public function addFieldToCourse(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $course = Course::findOrFail($request->course_id);
        $field = CheckoutField::findOrFail($request->field_id);

        // Check if already assigned
        if ($course->checkoutFields()->where('checkout_field_id', $request->field_id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Field already assigned to this course'
            ]);
        }

        // Get next position
        $maxPosition = $course->checkoutFields()->max('position') ?? -1;
        
        $course->checkoutFields()->attach($request->field_id, [
            'position' => $maxPosition + 1,
            'is_required_override' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field added to course successfully'
        ]);
    }

    // Remove a field from a course
    public function removeFieldFromCourse(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $course = Course::findOrFail($request->course_id);
        $course->checkoutFields()->detach($request->field_id);

        return response()->json([
            'success' => true,
            'message' => 'Field removed from course successfully'
        ]);
    }

    // Update field settings for a course
    public function updateCourseField(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id',
            'position' => 'required|integer|min:0',
            'is_required_override' => 'required|boolean'
        ]);

        $course = Course::findOrFail($request->course_id);
        
        $course->checkoutFields()->updateExistingPivot($request->field_id, [
            'position' => $request->position,
            'is_required_override' => $request->is_required_override
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field settings updated successfully'
        ]);
    }

    // Get available fields for a subscription (fields not yet assigned)
    public function getAvailableFieldsForSubscription($subscriptionId)
    {
        $subscription = SubscriptionMain::findOrFail($subscriptionId);
        $assignedFieldIds = $subscription->checkoutFields()->pluck('checkout_fields.id')->toArray();
        
        $availableFields = CheckoutField::where('status', true)
            ->whereNotIn('id', $assignedFieldIds)
            ->orderBy('field_label')
            ->get();

        return response()->json([
            'success' => true,
            'fields' => $availableFields
        ]);
    }

    // Get selected fields for a subscription
    public function getSelectedFieldsForSubscription($subscriptionId)
    {
        $subscription = SubscriptionMain::with(['checkoutFields' => function($query) {
            $query->where('checkout_fields.status', true)->orderBy('position');
        }])->findOrFail($subscriptionId);

        return response()->json([
            'success' => true,
            'fields' => $subscription->checkoutFields
        ]);
    }

    // Add a field to a subscription
    public function addFieldToSubscription(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscription_main,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $subscription = SubscriptionMain::findOrFail($request->subscription_id);
        $field = CheckoutField::findOrFail($request->field_id);

        // Check if already assigned
        if ($subscription->checkoutFields()->where('checkout_field_id', $request->field_id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Field already assigned to this subscription'
            ]);
        }

        // Get next position
        $maxPosition = $subscription->checkoutFields()->max('position') ?? -1;
        
        $subscription->checkoutFields()->attach($request->field_id, [
            'position' => $maxPosition + 1,
            'is_required_override' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field added to subscription successfully'
        ]);
    }

    // Remove a field from a subscription
    public function removeFieldFromSubscription(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscription_main,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $subscription = SubscriptionMain::findOrFail($request->subscription_id);
        $subscription->checkoutFields()->detach($request->field_id);

        return response()->json([
            'success' => true,
            'message' => 'Field removed from subscription successfully'
        ]);
    }

    // Update field settings for a subscription
    public function updateSubscriptionField(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscription_main,id',
            'field_id' => 'required|exists:checkout_fields,id',
            'position' => 'required|integer|min:0',
            'is_required_override' => 'required|boolean'
        ]);

        $subscription = SubscriptionMain::findOrFail($request->subscription_id);
        
        $subscription->checkoutFields()->updateExistingPivot($request->field_id, [
            'position' => $request->position,
            'is_required_override' => $request->is_required_override
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field settings updated successfully'
        ]);
    }

    // Methods for Subscription (non-code) table
    // Get available fields for a subscription non-code (fields not yet assigned)
    public function getAvailableFieldsForSubscriptionNonCode($subscriptionId)
    {
        $subscription = \App\Models\Core\Subscription::findOrFail($subscriptionId);
        $assignedFieldIds = $subscription->checkoutFields()->pluck('checkout_fields.id')->toArray();
        
        $availableFields = CheckoutField::where('status', true)
            ->whereNotIn('id', $assignedFieldIds)
            ->orderBy('field_label')
            ->get();

        return response()->json([
            'success' => true,
            'fields' => $availableFields
        ]);
    }

    // Get selected fields for a subscription non-code
    public function getSelectedFieldsForSubscriptionNonCode($subscriptionId)
    {
        $subscription = \App\Models\Core\Subscription::with(['checkoutFields' => function($query) {
            $query->where('checkout_fields.status', true)->orderBy('position');
        }])->findOrFail($subscriptionId);

        return response()->json([
            'success' => true,
            'fields' => $subscription->checkoutFields
        ]);
    }

    // Add a field to a subscription non-code
    public function addFieldToSubscriptionNonCode(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscription,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $subscription = \App\Models\Core\Subscription::findOrFail($request->subscription_id);
        $field = CheckoutField::findOrFail($request->field_id);

        // Check if already assigned
        if ($subscription->checkoutFields()->where('checkout_field_id', $request->field_id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Field already assigned to this subscription'
            ]);
        }

        // Get next position
        $maxPosition = $subscription->checkoutFields()->max('position') ?? -1;
        
        $subscription->checkoutFields()->attach($request->field_id, [
            'position' => $maxPosition + 1,
            'is_required_override' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field added to subscription successfully'
        ]);
    }

    // Remove a field from a subscription non-code
    public function removeFieldFromSubscriptionNonCode(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscription,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $subscription = \App\Models\Core\Subscription::findOrFail($request->subscription_id);
        $subscription->checkoutFields()->detach($request->field_id);

        return response()->json([
            'success' => true,
            'message' => 'Field removed from subscription successfully'
        ]);
    }

    // Update field settings for a subscription non-code
    public function updateSubscriptionNonCodeField(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscription,id',
            'field_id' => 'required|exists:checkout_fields,id',
            'position' => 'required|integer|min:0',
            'is_required_override' => 'required|boolean'
        ]);

        $subscription = \App\Models\Core\Subscription::findOrFail($request->subscription_id);
        
        $subscription->checkoutFields()->updateExistingPivot($request->field_id, [
            'position' => $request->position,
            'is_required_override' => $request->is_required_override
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field settings updated successfully'
        ]);
    }

    // Methods for Course Live
    // Get available fields for course live (fields not yet assigned)
    public function getAvailableFieldsForCourseLive($courseLiveId)
    {
        $courseLive = Course::findOrFail($courseLiveId);
        $assignedFieldIds = $courseLive->checkoutFields()->pluck('checkout_fields.id')->toArray();
        
        $availableFields = CheckoutField::where('status', true)
            ->whereNotIn('id', $assignedFieldIds)
            ->orderBy('field_label')
            ->get();

        return response()->json([
            'success' => true,
            'fields' => $availableFields
        ]);
    }

    // Get selected fields for course live
    public function getSelectedFieldsForCourseLive($courseLiveId)
    {
        $courseLive = Course::with(['checkoutFields' => function($query) {
            $query->where('checkout_fields.status', true)->orderBy('position');
        }])->findOrFail($courseLiveId);

        return response()->json([
            'success' => true,
            'fields' => $courseLive->checkoutFields
        ]);
    }

    // Add a field to course live
    public function addFieldToCourseLive(Request $request)
    {
        $request->validate([
            'course_live_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $courseLive = Course::findOrFail($request->course_live_id);
        $field = CheckoutField::findOrFail($request->field_id);
        
        // Get the next position
        $nextPosition = $courseLive->checkoutFields()->max('position') + 1;
        
        $courseLive->checkoutFields()->attach($request->field_id, [
            'position' => $nextPosition,
            'is_required_override' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field added to course live successfully'
        ]);
    }

    // Remove a field from course live
    public function removeFieldFromCourseLive(Request $request)
    {
        $request->validate([
            'course_live_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $courseLive = Course::findOrFail($request->course_live_id);
        $courseLive->checkoutFields()->detach($request->field_id);

        return response()->json([
            'success' => true,
            'message' => 'Field removed from course live successfully'
        ]);
    }

    // Methods for Course Event
    // Get available fields for course event (fields not yet assigned)
    public function getAvailableFieldsForCourseEvent($courseEventId)
    {
        $courseEvent = Course::findOrFail($courseEventId);
        $assignedFieldIds = $courseEvent->checkoutFields()->pluck('checkout_fields.id')->toArray();
        
        $availableFields = CheckoutField::where('status', true)
            ->whereNotIn('id', $assignedFieldIds)
            ->orderBy('field_label')
            ->get();

        return response()->json([
            'success' => true,
            'fields' => $availableFields
        ]);
    }

    // Get selected fields for course event
    public function getSelectedFieldsForCourseEvent($courseEventId)
    {
        $courseEvent = Course::with(['checkoutFields' => function($query) {
            $query->where('checkout_fields.status', true)->orderBy('position');
        }])->findOrFail($courseEventId);

        return response()->json([
            'success' => true,
            'fields' => $courseEvent->checkoutFields
        ]);
    }

    // Add a field to course event
    public function addFieldToCourseEvent(Request $request)
    {
        $request->validate([
            'course_event_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $courseEvent = Course::findOrFail($request->course_event_id);
        $field = CheckoutField::findOrFail($request->field_id);
        
        // Get the next position
        $nextPosition = $courseEvent->checkoutFields()->max('position') + 1;
        
        $courseEvent->checkoutFields()->attach($request->field_id, [
            'position' => $nextPosition,
            'is_required_override' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field added to course event successfully'
        ]);
    }

    // Remove a field from course event
    public function removeFieldFromCourseEvent(Request $request)
    {
        $request->validate([
            'course_event_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $courseEvent = Course::findOrFail($request->course_event_id);
        $courseEvent->checkoutFields()->detach($request->field_id);

        return response()->json([
            'success' => true,
            'message' => 'Field removed from course event successfully'
        ]);
    }

    // Methods for Company Visit
    // Get available fields for company visit (fields not yet assigned)
    public function getAvailableFieldsForCompanyVisit($companyVisitId)
    {
        $companyVisit = Course::findOrFail($companyVisitId);
        $assignedFieldIds = $companyVisit->checkoutFields()->pluck('checkout_fields.id')->toArray();
        
        $availableFields = CheckoutField::where('status', true)
            ->whereNotIn('id', $assignedFieldIds)
            ->orderBy('field_label')
            ->get();

        return response()->json([
            'success' => true,
            'fields' => $availableFields
        ]);
    }

    // Get selected fields for company visit
    public function getSelectedFieldsForCompanyVisit($companyVisitId)
    {
        $companyVisit = Course::with(['checkoutFields' => function($query) {
            $query->where('checkout_fields.status', true)->orderBy('position');
        }])->findOrFail($companyVisitId);

        return response()->json([
            'success' => true,
            'fields' => $companyVisit->checkoutFields
        ]);
    }

    // Add a field to company visit
    public function addFieldToCompanyVisit(Request $request)
    {
        $request->validate([
            'company_visit_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $companyVisit = Course::findOrFail($request->company_visit_id);
        $field = CheckoutField::findOrFail($request->field_id);
        
        // Get the next position
        $nextPosition = $companyVisit->checkoutFields()->max('position') + 1;
        
        $companyVisit->checkoutFields()->attach($request->field_id, [
            'position' => $nextPosition,
            'is_required_override' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field added to company visit successfully'
        ]);
    }

    // Remove a field from company visit
    public function removeFieldFromCompanyVisit(Request $request)
    {
        $request->validate([
            'company_visit_id' => 'required|exists:course,id',
            'field_id' => 'required|exists:checkout_fields,id'
        ]);

        $companyVisit = Course::findOrFail($request->company_visit_id);
        $companyVisit->checkoutFields()->detach($request->field_id);

        return response()->json([
            'success' => true,
            'message' => 'Field removed from company visit successfully'
        ]);
    }
}