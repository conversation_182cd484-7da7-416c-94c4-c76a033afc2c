<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\Subscription;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\SubscriptionExCate;
use App\Models\Core\SubscriptionExCourse;
use App\Models\Core\SubscriptionInCate;
use Maatwebsite\Excel\Facades\Excel;
use App\Service\PeakService;


class SubscriptCodeController extends Controller
{
  public $prefix = 'subscript_code';
  public $project_url = 'subscript_code';
  public $project_layout = 'admin.Lrk.subscript_code';

  public function __construct()
  {
    $this->middleware('auth:admin');
  }

  public function index()
  {
    if (Auth::user()->level != '99') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }

    $obj = array(
      'prefix' => $this->prefix,
      'project_url' => $this->project_url,
      'project_layout' => $this->project_layout
    );
    return view($this->project_layout . '.index', compact('obj'));
  }

  public function create()
  {
    if (Auth::user()->level != '99') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = new Subscription();
    $old = session()->getOldInput();
    if ($old) {
      foreach ($old as $key => $value) {
        $model[$key] = $value;
      }
    }
    $obj = array(
      'type' => 'create',
      'route' => route($this->prefix . '.store'),
      'prefix' => $this->prefix,
      'project_url' => $this->project_url,
      'project_layout' => $this->project_layout
    );
    return view($this->project_layout . '.form', compact('model', 'obj'));
  }

  public function show($id)
  {
    if (Auth::user()->level != '99') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = Subscription::find($id);
    $article_id = $model->id;
    if ($model) {
      $obj = array(
        'type' => 'view',
        'route' => route($this->prefix . '.update', $model->id),
        'prefix' => $this->prefix,
        'project_url' => $this->project_url,
        'project_layout' => $this->project_layout
      );
      AppHelper::instance()->consoleLog($model);
      return view($this->project_layout . '.view', compact('model', 'obj', 'article_id'));
    } else {
      return redirect(route('admin') . '/' . $this->project_url);
    }
  }

  public function edit($id)
  {
    if (Auth::user()->level != '99') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = Subscription::find($id);
    if ($model) {
      $obj = array(
        'type' => 'edit',
        'route' => route($this->prefix . '.update', $model->id),
        'prefix' => $this->prefix,
        'project_url' => $this->project_url,
        'project_layout' => $this->project_layout
      );
      AppHelper::instance()->consoleLog($model);
      return view($this->project_layout . '.form', compact('model', 'obj'));
    } else {
      return redirect(route('admin') . '/' . $this->project_url);
    }
  }

  public function dblist(DataTables $datatables, Request $request)
  {
    if (Auth::user()->level != '99') {
      return new Dummy();
    }

    $draw = $request->get('draw');
    $start = $request->get('start');
    $length = $request->get('length');

    //custom search
    $where_search = array();
    foreach ($request->get('columns') as $key => $value) {
      if (!empty($value['search']['value'])) {
        if ($value['data'] == 'status') {
          array_push($where_search, ['subscription.status', $value['search']['value']]);
        } else if ($value['data'] == 'position') {
          array_push($where_search, ['subscription.position', $value['search']['value']]);
        } else if ($value['data'] == 'id') {
          array_push($where_search, ['subscription.id', $value['search']['value']]);
        } else {
          array_push($where_search, ['subscription.' . $value['data'], 'LIKE', '%' . $value['search']['value'] . '%']);
        }
      }
    }

    $index = $request->get('order')[0]['column'];
    $orderby = $request->get('columns')[$index]['data'];
    $order_sort = $request->get('order')[0]['dir'];

    $query = DB::table('subscription')
      ->select('subscription.*')
      ->where('type', 2)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

    $count_total = DB::table('subscription')
      ->where('type', 2)
      ->where($where_search)
      ->count();

    $data_query = array();
    foreach ($query as $key => $value) {
      $obj = new Dummy();
      foreach ($value as $key_data => $data) {
        $obj[$key_data] = $data;
      }

      $obj['action'] = '
        <div class="btn-group">
          <a href="' . $this->prefix . '/' . $obj->id . '/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem(' . $obj->id . ',\'' . $this->prefix . '/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
      array_push($data_query, $obj);
    }

    $data = $datatables->of($data_query)
      ->with([
        'data' => $data_query,
        'draw' => $draw,
        "recordsTotal" => $count_total,
        "recordsFiltered" => $count_total,
      ])
      ->make(true);
    return $data;
  }

  public function store(Request $request)
  {
    if (Auth::user()->level != '99') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = new Subscription;

    $model->type = 2;
    $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
    $model->details = null;

    $model->left_details =  null;
    $model->right_details = null;
    $model->bottom_details = null;

    $model->banner =  null;
    $model->banner_m = null;
    $model->thumb = null;

    $model->remark = null;
    $model->period = isset($request->period) ? AppHelper::instance()->cleanInput($request->period) : null;
    $model->price = isset($request->price) ? AppHelper::instance()->cleanInput($request->price) : null;
    $model->code = isset($request->code) ? AppHelper::instance()->cleanInput($request->code) : null;
    $model->limit = isset($request->limit) ? AppHelper::instance()->cleanInput($request->limit) : null;

    $model->select_type = isset($request->select_type) ? $request->select_type : 1;

    $model->only_cate = is_array($request->only_cate) ? implode(",", $request->only_cate) : null;
    $model->excluding_course = is_array($request->excluding_course) ? implode(",", $request->excluding_course) : null;
    $model->excluding_cate = is_array($request->excluding_cate) ? implode(",", $request->excluding_cate) : null;

    $count = Subscription::where('type', 2)->count();
    $model->position = $count + 1;

    $model->status = $request->status;

    $model->save();

    if (is_array($request->excluding_cate)) { //ถ้า tags มีค่า
      foreach ($request->excluding_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
        $log = new SubscriptionExCate();
        $log->subscription_id = $model->id;
        $log->cate_id = $value;
        $log->save();
      }
    }

    if (is_array($request->excluding_course)) { //ถ้า tags มีค่า
      foreach ($request->excluding_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
        $log = new SubscriptionExCourse();
        $log->subscription_id = $model->id;
        $log->course_id = $value;
        $log->save();
      }
    }

    if (is_array($request->only_cate)) { //ถ้า tags มีค่า
      foreach ($request->only_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
        $log = new SubscriptionInCate();
        $log->subscription_id = $model->id;
        $log->cate_id = $value;
        $log->save();
      }
    }

    // เพิ่มการสร้าง product ใน Peak หลัง save subscription
    try {
      $peakService = new PeakService();

      // เตรียมข้อมูลสำหรับสร้าง product ใน Peak
      $productData = [
        'name' => $model->title,
        'price' => $model->price ?? 0,
        'description' => $model->details ?? $model->title,
        'original_id' => $model->id
      ];

      // สร้าง product ใน Peak พร้อม callback เพื่ออัปเดต subscription
      $result = $peakService->createProductWithCallback($productData, function ($peakData) use ($model) {
        $model->peak_product_id = $peakData['peak_product_id'];
        $model->peak_product_code = $peakData['peak_product_code'];
        $model->peak_sync_status = 2; // 2 = Synced
        $model->peak_last_sync = now();
        $model->save();
      });

      if (!$result['status']) {
        $model->peak_sync_status = 3; // 3 = Error
        $model->peak_last_sync = now();
        $model->peak_error_message = $result['message'];
        $model->save();
      }
    } catch (\Exception $e) {
      $model->peak_sync_status = 3; // 3 = Error
      $model->peak_last_sync = now();
      $model->peak_error_message = $e->getMessage();
      $model->save();
    }

    return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been update');
  }

  public function update(Request $request, $id)
  {
    if (Auth::user()->level != '99') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }

    $model = Subscription::find($id);

    $model->type = 2;
    $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
    $model->details = null;
    $model->remark = null;
    $model->period = isset($request->period) ? AppHelper::instance()->cleanInput($request->period) : null;
    $model->price = isset($request->price) ? AppHelper::instance()->cleanInput($request->price) : null;
    $model->code = isset($request->code) ? AppHelper::instance()->cleanInput($request->code) : null;
    $model->limit = isset($request->limit) ? AppHelper::instance()->cleanInput($request->limit) : null;

    $model->left_details =  null;
    $model->right_details = null;
    $model->bottom_details = null;

    $model->banner =  null;
    $model->banner_m = null;
    $model->thumb = null;

    $model->select_type = isset($request->select_type) ? $request->select_type : 1;

    if ($model->select_type == 2) {

      $old_tags = explode(',', $model->excluding_cate); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
      $model->excluding_cate = is_array($request->excluding_cate) ? implode(",", $request->excluding_cate) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
      if (is_array($request->excluding_cate)) {
        if (!is_array($old_tags)) {
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->excluding_cate); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

        if (count(array_diff($old_tags, $request->excluding_cate)) > 0 || count(array_diff($request->excluding_cate, $old_tags)) > 0) {
          $log = SubscriptionExCate::where('subscription_id', $model->id);
          $log->delete();

          foreach ($request->excluding_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new SubscriptionExCate();
            $log->subscription_id = $model->id;
            $log->cate_id = $value;
            $log->save();
          }
        }
      }
      $model->only_cate = null;
      $including_cate = SubscriptionInCate::where('subscription_id', $model->id)->get();
      foreach ($including_cate as $key_in_cate => $val_in_cate) {
        $val_in_cate->delete();
      }
    } else {
      $old_tags = explode(',', $model->only_cate); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
      $model->only_cate = is_array($request->only_cate) ? implode(",", $request->only_cate) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
      if (is_array($request->only_cate)) {
        if (!is_array($old_tags)) {
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->only_cate); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

        if (count(array_diff($old_tags, $request->only_cate)) > 0 || count(array_diff($request->only_cate, $old_tags)) > 0) {
          $log = SubscriptionInCate::where('subscription_id', $model->id);
          $log->delete();

          foreach ($request->only_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new SubscriptionInCate();
            $log->subscription_id = $model->id;
            $log->cate_id = $value;
            $log->save();
          }
        }
      }
      $model->excluding_cate = null;
      $excluding_cate = SubscriptionExCate::where('subscription_id', $model->id)->get();
      foreach ($excluding_cate as $key_ex_cate => $val_ex_cate) {
        $val_ex_cate->delete();
      }
    }

    $old_tags = explode(',', $model->excluding_course); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
    $model->excluding_course = is_array($request->excluding_course) ? implode(",", $request->excluding_course) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
    if (is_array($request->excluding_course)) {
      if (!is_array($old_tags)) {
        $old_tags = array();
      }

      $diff_tags = array_diff($old_tags, $request->excluding_course); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

      if (count(array_diff($old_tags, $request->excluding_course)) > 0 || count(array_diff($request->excluding_course, $old_tags)) > 0) {
        $log = SubscriptionExCourse::where('subscription_id', $model->id);
        $log->delete();

        foreach ($request->excluding_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new SubscriptionExCourse();
          $log->subscription_id = $model->id;
          $log->course_id = $value;
          $log->save();
        }
      }
    } else {
      $log = SubscriptionExCourse::where('subscription_id', $model->id);
      $log->delete();
    }

    // เพิ่มการอัปเดต product ใน Peak หากมีการเปลี่ยนแปลงข้อมูลสำคัญ
    try {
      $peakService = new PeakService();

      // ตรวจสอบว่าข้อมูลที่เกี่ยวข้องกับ Peak มีการเปลี่ยนแปลงหรือไม่
      $shouldUpdatePeak = $request->has(['title', 'price', 'details']);

      if ($shouldUpdatePeak && $model->peak_product_id) {
        $model->peak_sync_status = 1; // 1 = Not Synced (ต้อง sync ใหม่)
        $model->save();
      } elseif ($shouldUpdatePeak && !$model->peak_product_id) {
        // สร้าง product ใหม่ใน Peak ถ้ายังไม่เคยสร้าง
        $productData = [
          'name' => $model->title,
          'price' => $model->price ?? 0,
          'description' => $model->details ?? $model->title,
          'original_id' => $model->id
        ];

        $result = $peakService->createProductWithCallback($productData, function ($peakData) use ($model) {
          $model->peak_product_id = $peakData['peak_product_id'];
          $model->peak_product_code = $peakData['peak_product_code'];
          $model->peak_sync_status = 2; // 2 = Synced
          $model->peak_last_sync = now();
          $model->save();
        });

        if (!$result['status']) {
          $model->peak_sync_status = 3; // 3 = Error
          $model->peak_last_sync = now();
          $model->peak_error_message = $result['message'];
          $model->save();
        }
      }
    } catch (\Exception $e) {
      $model->peak_sync_status = 3; // 3 = Error
      $model->peak_last_sync = now();
      $model->peak_error_message = $e->getMessage();
      $model->save();
    }

    $model->status = $request->status;
    $model->save();

    return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been update');
  }

  public function position(Request $request)
  {
    if (Auth::user()->level != '99') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $lists = explode(',', $request->list);
    if (count($lists) > 0) {
      foreach ($lists as $key => $value) {
        $data = explode(':', $value);
        $model = Subscription::where('type', 2)
          ->where('id', $data[0])
          ->first();
        if ($model) {
          $page = (int)$request->page;
          $length = (int)$request->length;
          if ($request->order == 'asc') {
            $pos = ((int)$data[1] + 1) + (($page) * $length);
          } else if ($request->order == 'desc') {
            $top = 0;
            $alldata = Subscription::where('type', 2)->count();
            $top = $alldata - ($length * $page);
            $pos = ($top - ((int)$data[1]));
          }
          $model->position = $pos;
          $model->save();
        }
      }
    }
  }

  public function destroy(Request $request)
  {
    if (Auth::user()->level != '99') {
      return response()->json();
    }

    $model = Subscription::find($request->id);

    $in_cate = SubscriptionInCate::where('subscription_id', $model->id)->get();
    foreach ($in_cate as $key_in_cate => $val_in_cate) {
      $val_in_cate->delete();
    }

    $ex_cate = SubscriptionExCate::where('subscription_id', $model->id)->get();
    foreach ($ex_cate as $key_ex_cate => $val_ex_cate) {
      $val_ex_cate->delete();
    }

    $ex_course = SubscriptionExCourse::where('subscription_id', $model->id)->get();
    foreach ($ex_course as $key_ex_course => $val_ex_course) {
      $val_ex_course->delete();
    }

    Subscription::where('is_article', 1)->where('position', '>', $model->position)
      ->update(['position' => DB::raw('position - 1')]);

    $model->delete();

    return response()->json();
  }

  public function status(Request $request)
  {
    if (Auth::user()->level != '99') {
      return response()->json();
    }
    $model = Subscription::find($request->id);

    if ($model->status == '1') {
      $model->status = '2';
    } else {
      $model->status = '1';
    }

    $model->save();

    return response()->json();
  }
}
