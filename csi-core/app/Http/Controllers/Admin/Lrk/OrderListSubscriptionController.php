<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\UsersOrderList;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class OrderListSubscriptionController extends Controller
{
    public $prefix = 'order_list_subscription';
    public $project_url = 'order_list_subscription';
    public $project_layout = 'admin.Lrk.order_list_subscription';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      $order_id = $request->order_id;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','order_id'));
    }

    public function create(Request $request)
    {      
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_order_list.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_order_list.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_order_list.id', $value['search']['value']]);
          }else if($value['data']=='s_title'){
            array_push($where_search, ['subscription.title', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_order_list.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_order_list')
      ->join('subscription', 'subscription.id', 'user_order_list.subscription_id')
      ->select('user_order_list.*', 'subscription.title as s_title')
      ->where('user_order_list.order_id', $request->order_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_order_list')
      ->join('subscription', 'subscription.id', 'user_order_list.subscription_id')
      ->where('user_order_list.order_id', $request->order_id)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

    }

    public function update(Request $request)
    {

    }

  }
