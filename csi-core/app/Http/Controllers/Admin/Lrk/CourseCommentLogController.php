<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\UsersCommentLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class CourseCommentLogController extends Controller
{
    public $prefix = 'comment_log';
    public $project_url = 'comment_log';
    public $project_layout = 'admin.Lrk.comment_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      $model = UsersCommentLog::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      $model = UsersCommentLog::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='replied_comment'){
            array_push($where_search, ['user_comment_log.replied_comment', $value['search']['value']]);
          }else if($value['data']=='comment'){
            array_push($where_search, ['user_comment_log.comment', $value['search']['value']]);
          }else{
            array_push($where_search, ['user_comment_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('user_comment_log')
      ->select('user_comment_log.*')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_comment_log')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        if($obj['replied_comment'] == null){
          $obj['comment_status'] = 'ไม่มีการตอบกลับ';
        }else{
          $obj['comment_status'] = 'ตอบกลับแล้ว';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="deleteDatatableItem3('.$obj->id.',\''.route('admin').'/comment_log/delete\',\'datatable_comment_log\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        // $obj['action'] = '
        // <div class="btn-group">
        //   <a href="javascript:void(0)" onclick="viewDatatableItem3('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        //   <a href="javascript:void(0)" onclick="deleteDatatableItem3('.$obj->id.',\''.route('admin').'/comment_log/delete\',\'datatable_comment_log\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        // </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = UsersCommentLog::find($request->id);

      if($model){
  
        $model->comment = isset($request->gallery_modal_comment) ? AppHelper::instance()->cleanInput($request->gallery_modal_comment) : null;
        $model->replied_comment = isset($request->gallery_modal_replied_comment) ? AppHelper::instance()->cleanInput($request->gallery_modal_replied_comment) : null;
        $model->admin_id = Auth::user()->id;
        $model->save();

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = UsersCommentLog::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }
}
