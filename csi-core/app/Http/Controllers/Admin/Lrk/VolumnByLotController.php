<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\VolumnByLot;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CateDomain;
use App\Models\Core\UserHistory;
use App\Models\Core\VolumnBy;
use App\Models\Core\VolumnByCourse;
use App\Models\Core\VolumnByExCate;
use App\Models\Core\VolumnByInCate;
use App\Models\Core\VolumnByUser;
use App\Models\Core\VolumnDomain;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel;


class VolumnByLotController extends Controller
{
    public $prefix = 'volumn_by_lot';
    public $project_url = 'volumn_by_lot';
    public $project_layout = 'admin.Lrk.volumn_by_lot';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {

        $model = VolumnByLot::find($request->id);

        if($model){
          $course = VolumnByCourse::where('volumn_id', $model->id)->get();
          foreach($course as $key_course=>$val_course){
            $val_course->delete();
          }
          $user = VolumnByUser::where('company_id', $model->company_id)
                  ->where('lot_id', $model->id)->get();
          foreach($user as $key_user=>$val_user){
            $user = User::where(function ($user) use ($val_user) {
              $user->where('internal_email',$val_user->email);
              $user->orWhere('email',$val_user->email);
              });
            $user = $user->first();

            if($user){
                $history = UserHistory::where('user_id',$user->id)
                ->where('get_type',6)
                ->where('company_id', $val_user->company_id)
                ->where('company_lot_id', $val_user->lot_id)
                ->where('expired','>=',Carbon::now())
                ->get();
                foreach($history as $val_his){
                  $val_his->expired = Carbon::now()->subdays(1);
                  $val_his->save();
                }
            }
            $val_user->delete();
          }

          $model->delete();
        }

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = VolumnByLot::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {
      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='lot_name'){
            array_push($where_search, ['volumn_lot_log.lot_name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='status'){
            array_push($where_search, ['volumn_lot_log.status', $value['search']['value']]);
          }else if($value['data']=='limit'){
            array_push($where_search, ['volumn_lot_log.limit', $value['search']['value']]);
          }else{
            array_push($where_search, ['volumn_lot_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('volumn_lot_log')
      ->select('volumn_lot_log.*')
      ->where('company_id',$request->company_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('volumn_lot_log')
      ->where('company_id',$request->company_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $user = VolumnByUser::where('company_id', $obj->company_id)
        ->where('lot_id', $obj->id)->count();

        if($user > 0){
          $obj['user'] = '
            จำนวน '. $user .' ราย';
        }else{
          $obj['user'] = '
            ยังไม่พบผู้ลงทะเบียน';
        }

        $import = DB::table('volumn_course_log')->where('volumn_course_log.volumn_id',$obj->id)->count();
        if($import>0){
          $obj['import'] = '
          <a href="'.url('/csisocietyadmin').'/volumn_course/'.$obj->company_id.'/'.$obj->id.'" class="btn btn-success">
            <i class="glyphicon glyphicon-edit"></i>
            จำนวน '. $import .'
          </a>';
        }else{
          $obj['import'] = ' 
          <a href="/csisocietyadmin/volumn_course/'.$obj->company_id.'/'.$obj->id.'" class="btn btn-success">
            <i class="glyphicon glyphicon-edit"></i>
            สร้าง
          </a>';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.route('admin').'/volumn_by_lot/delete\',\'datatable_gal\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        $obj['text_area'] = AppHelper::instance()->limitCharUTF8($obj['text_area'],30);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        $model = new VolumnByLot; 

        $model->company_id = $request->company_id;

        $lot_count = VolumnByLot::where('company_id', $request->company_id)->count();
        
        $model->lot = $lot_count + 1;

        $model->lot_name = isset($request->gallery_modal_lot_name) ? AppHelper::instance()->cleanInput($request->gallery_modal_lot_name) : '';
        $model->limit = isset($request->gallery_modal_limit) ? AppHelper::instance()->cleanInput($request->gallery_modal_limit) : '';
        
        $model->started_date = isset($request->gallery_modal_started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
        $model->end_date = isset($request->gallery_modal_end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_end_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
                        
        $model->ex_course = is_array($request->gallery_modal_ex_course) ? implode(",",$request->gallery_modal_ex_course) : '';
       
        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';


        //เช็ค Categories
        $list_tags = array();
        if(is_array($request->gallery_modal_domain)){
          $all_tags_val = $request->gallery_modal_domain;
          foreach ($all_tags_val as $key => $value) {
            if(!is_numeric($value)){
              $tags = CateDomain::where('title',$value)->first();
              if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
                $tags = new CateDomain();
                $tags->title = AppHelper::instance()->cleanInput($value);
                $tags->status = 1;
                $tags->save();
              }
            }else{
              $tags = CateDomain::where('id',$value)->first();
            }
            if($tags){
              array_push($list_tags,$tags->id);
            }
          }
        }
        $request->gallery_modal_domain = $list_tags;
        $model->domain = implode(",",$request->gallery_modal_domain);
        //เช็ค Categories

        $model->save();

        if(is_array($request->gallery_modal_domain)){ //ถ้า tags มีค่า
          foreach($request->gallery_modal_domain as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new VolumnDomain();
            $log->company_id = $model->company_id;
            $log->lot_id = $model->id;
            $log->domain_id = $value;
            $log->save();
          }
        }

        if(is_array($request->gallery_modal_ex_course)){ //ถ้า tags มีค่า
          foreach($request->gallery_modal_ex_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new VolumnByCourse();
            $log->company_id = $model->company_id;
            $log->volumn_id = $model->id;
            $log->course_id = $value;
            $log->save();
          }
        }

        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = VolumnByLot::find($request->id);

      if($model){

        $model->lot_name = isset($request->gallery_modal_lot_name) ? AppHelper::instance()->cleanInput($request->gallery_modal_lot_name) : '';
        $model->limit = isset($request->gallery_modal_limit) ? AppHelper::instance()->cleanInput($request->gallery_modal_limit) : '';
        
        $model->started_date = isset($request->gallery_modal_started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
        $model->end_date = isset($request->gallery_modal_end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_end_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');

        $old_tags = explode(',', $model->domain); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
        
        $old_tags = explode(',', $model->domain);
        $list_tags = array();
        if(is_array($request->gallery_modal_domain)){
          $all_tag_val = $request->gallery_modal_domain;
          foreach ($all_tag_val as $key => $value) {
            if(!is_numeric($value)){
              $tags = CateDomain::where('title',$value)->first();
              if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
                $tags = new CateDomain();
                $tags->title = AppHelper::instance()->cleanInput($value);
                $tags->status = 1;
                $tags->save();
              }
            }else{
              $tags = CateDomain::where('id',$value)->first();
            }
            if($tags){
              array_push($list_tags,$tags->id);
            }
          }
        }
        $request->gallery_modal_domain = $list_tags;
        $model->domain = implode(",",$request->gallery_modal_domain);
        if(is_array($request->gallery_modal_domain)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          if(count(array_diff($old_tags, $request->gallery_modal_domain))>0 || count(array_diff($request->gallery_modal_domain, $old_tags))>0){
            $tags = VolumnDomain::where('lot_id', $model->id);
            $tags->delete();

            foreach($request->gallery_modal_domain as $value) {
              $log = new VolumnDomain();
              $log->company_id = $model->company_id;
              $log->lot_id = $model->id;
              $log->domain_id = $value;
              $log->save();
            }
          }
        }else{
          $tags = VolumnDomain::where('lot_id', $model->id);
          $tags->delete();
        }
      
        $old_tags = explode(',', $model->ex_course); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
        $model->ex_course = is_array($request->gallery_modal_ex_course) ? implode(",",$request->gallery_modal_ex_course) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
        if(is_array($request->gallery_modal_ex_course)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          $diff_tags = array_diff($old_tags, $request->gallery_modal_ex_course); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

          if(count(array_diff($old_tags, $request->gallery_modal_ex_course))>0 || count(array_diff($request->gallery_modal_ex_course, $old_tags))>0){
            $log = VolumnByCourse::where('volumn_id', $model->id);
            $log->delete();

            foreach($request->gallery_modal_ex_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
              $log = new VolumnByCourse();
              $log->company_id = $model->company_id;
              $log->volumn_id = $model->id;
              $log->course_id = $value;
              $log->save();
            }
          }
        }else{
          $log = VolumnByCourse::where('volumn_id', $model->id);
          $log->delete();
        }
        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';
        $model->save();
        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = VolumnByLot::find($request->id);
      if($model){

        $volumn_course = VolumnByCourse::where('volumn_id', $model->id)->select('course_id')->get();
        $volumn_id = '';
        foreach($volumn_course as $key_course=>$val_course){
          if($volumn_id != ''){
            $volumn_id .= ',';
          }
          $volumn_id .= $val_course->course_id;
        }

        $model->ex_course = $volumn_id;
        $model->gallery_modal_domain = $model->domain;

        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }
}
