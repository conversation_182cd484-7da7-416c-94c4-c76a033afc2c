<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\UsersExamLog;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseExamAnswer;
use App\Models\VNE\Project;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class UserExamLogController extends Controller
{
    public $prefix = 'user_exam_log';
    public $project_url = 'user_exam_log';
    public $project_layout = 'admin.Lrk.user_exam_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      
      $course_id = $request->course_id;
      $lesson_id = $request->lesson_id;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','lesson_id'));
    }

    public function create(Request $request)
    {      
        $lesson_id = $request->lesson_id;
        $course_id = $request->course_id;

        $model = new UsersExamLog();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> url('csisocietyadmin/user_exam_log/'.$course_id.'/'.$lesson_id.'/create'),
          'btn'=>'Add',
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj','course_id','lesson_id'));
    }

    public function show(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        $lesson_id = $request->lesson_id;
        $course_id = $request->course_id;
        $user_id = $request->user_id;

        $user_exam = UsersExamLog::leftjoin('course_exam', 'course_exam.id', 'user_exam_log.exam_id')
        ->leftjoin('course_exam_answer', 'course_exam_answer.id', 'user_exam_log.answer')
        ->where('user_exam_log.lesson_id', $lesson_id)->where('user_exam_log.user_id', $user_id)
        ->select('user_exam_log.id as log_id', 'user_exam_log.point', 'course_exam.question_th', 'course_exam_answer.answer_th', 
                'course_exam.exam_type', 'user_exam_log.point', 'user_exam_log.answer_text', 'user_exam_log.exam', 'user_exam_log.file', 'user_exam_log.exam_id')
        ->groupBy('user_exam_log.exam_id')
        ->get();

        $model = UsersExamLog::get();

        foreach($model as $key=>$value){
          if($model){
            $obj = array(
              'type'=>'edit',
              'route'=>'/csisocietyadmin/user_exam_log/'.$request->course_id.'/'.$request->lesson_id.'/'.$value->user_id.'/edit',
              'prefix'=>$this->prefix,
              'project_url'=>$this->project_url,
              'project_layout'=>$this->project_layout
            );
            AppHelper::instance()->consoleLog($model);
            return view($this->project_layout.'.form', compact('value','obj','lesson_id','course_id','user_id','user_exam'));
          }else{
            return redirect(route('admin').'/'.$this->project_url);
          }
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_exam_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_exam_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_exam_log.id', $value['search']['value']]);
          }else if($value['data']=='name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='email'){
            array_push($where_search, ['user.email', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_exam_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_exam_log')
      ->leftjoin('user', 'user.id', 'user_exam_log.user_id')
      ->select('user_exam_log.id', 'user_exam_log.user_id','user.name', 'user.lastname', 'user.email')
      ->groupBy('user.id')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = count(DB::table('user_exam_log')
      ->leftjoin('user', 'user.id', 'user_exam_log.user_id')
      ->groupBy('user.id')
      ->where($where_search)
      ->pluck('user.id')->toArray());

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
        <a href="/csisocietyadmin/user_exam_log/'.$request->course_id.'/'.$request->lesson_id.'/'.$obj->user_id.'/edit'.'" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
        return redirect(route('admin').'/user_exam_log/'.$request->course_id.'/'.$request->lesson_id.'/edit/'.$model->id)->with('success', 'Data has been update');
    }

    public function update(Request $request)
    {
        $exam_log = UsersExamLog::where('course_id', $request->course_id)->where('lesson_id', $request->lesson_id)->where('user_exam_log.user_id', $request->user_id)->get();
        
        foreach($exam_log as $key=>$value){
          $model = UsersExamLog::find($value->id);
          if($model){

            $model->point = isset($request->point) ? AppHelper::instance()->cleanInput($request->point) : 0;
            if($model->point > 0){
              $model->point = 1;
            }else{
              $model->point = 0;
            }
            $model->save();

            // return redirect(route('admin').'/user_exam_log/'.$request->course_id.'/'.$request->lesson_id)->with('success', 'Data has been update');
          }
        }
       
        
    }

    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='question_export') {
                $data = DB::table('course_exam')
                ->join('course', 'course.id', '=', 'course_exam.course_id')
                ->join('course_lesson', 'course_lesson.id', '=', 'course_exam.lesson_id')
                ->select('course_exam.*', 'course.title_th as course_title', 'course_lesson.title_th as lesson_title')
                ->where('course_exam.course_id', $request->course_id)
                ->where('course_exam.lesson_id', $request->lesson_id)
                ->orderBy('id', 'desc')
                ->get();


                $ex_array[]=array(
                  'คอร์ส'=>'คอร์ส',
                  'บทเรียน'=>'บทเรียน',
                  'ประเภทคำถาม'=>'ประเภทคำถาม',
                  'คำถาม'=>'คำถาม',
                  'ตัวเลือก'=>'ตัวเลือก',
                  'คะแนน'=>'คะแนน',
                  'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
                );

                foreach ($data as $e) {
                  if($e->exam_type == 1){
                    $e->exam_type = 'ปรนัย';
                  }else if($e->exam_type == 2){
                    $e->exam_type = 'อัตนัย';
                  }else{
                    $e->exam_type = 'อัพโหลด';
                  }

                  $data_choice = CourseExamAnswer::where('course_id', $request->course_id)->where('lesson_id', $request->lesson_id)->where('exam_id', $e->id)->get();
                  
                  $answer_txt = '';
                  foreach($data_choice as $key=>$val){
                    if($answer_txt!=''){
                      $answer_txt.=' || ';
                    }
                    $answer_txt.=$val->answer_th;
                  }
                  $ex_array[]=array(
                  'คอร์ส'=>$e->course_title,
                  'บทเรียน'=>$e->lesson_title,
                  'ประเภทคำถาม'=>$e->exam_type,
                  'คำถาม'=>$e->question_th,
                  'ตัวเลือก'=>$answer_txt,
                  'คะแนน'=>$e->point,
                  'วันที่เก็บข้อมูล'=>$e->created_at,
                  );
                }
            }



            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    $export = new TempExport($ex_array);

                    return FacadesExcel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

    public function update_each(Request $request)
    {

        $exam_log = UsersExamLog::where('id', $request->log_id)->first();
        if($exam_log){
          $exam_log->point = isset($request->point) ? AppHelper::instance()->cleanInput($request->point) : 0;
          if($exam_log->point > 0){
            $exam_log->point = 1;
          }else{
            $exam_log->point = 0;
          }
          $exam_log->save();


          return redirect(route('admin').'/user_exam_log/'.$request->course_id.'/'.$request->lesson_id.'/'.$request->user_id.'/edit')->with('success', 'Data has been update');
        }
    }

  }
