<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\Course;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CateSpeaker;
use App\Models\Core\NotiAuto;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\NotiGlobalLog;
use App\Models\Core\Users;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class CourseLogController extends Controller
{
    public $prefix = 'course_log';
    public $project_url = 'course_log';
    public $project_layout = 'admin.Lrk.course_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      if (isset($request->from)) {
        $from = Carbon::parse($request->from)->format('Y-m-d H:i:s');
        $from_date = $request->from;
      } else {
        $from = '';
        $from_date = '';
      }
      if (isset($request->to)) {
        $to = Carbon::parse($request->to)->addDays(1)->format('Y-m-d H:i:s');
        $to_date = $request->to;
      } else {
        $to = '';
        $to_date = '';
      }
      $obj = array(
        'started_date' => $from,
        'end_date' => $to,
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout,
      );
      return view($this->project_layout.'.index', compact('obj','from_date','to_date'));
    }

    public function create()
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return new Dummy();
      }

      if (isset($request->from) && $request->from != '0') {
        $start_date = Carbon::parse($request->from)->format('Y-m-d H:i:s');
      } else {
        $start_date = '';
      }
      if (isset($request->to) && $request->to != '0') {
        $end_date = Carbon::parse($request->to)->addDays(1)->format('Y-m-d H:i:s');
      } else {
        $end_date = '';
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_order_list.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_order_list.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_order_list.id', $value['search']['value']]);
          }else if($value['data']=='course_id'){
            array_push($where_search, ['course.id', $value['search']['value']]);
          }else if($value['data']=='course_name'){
            array_push($where_search, ['course.title_th','LIKE','%'. $value['search']['value'].'%']);
          }else if($value['data']=='course_speaker'){
            array_push($where_search, ['course.speaker','LIKE','%'. $value['search']['value'].'%']);
          }else if($value['data']=='status'){
            array_push($where_search, ['user_order_log.status','LIKE','%'. $value['search']['value'].'%']);
          }else if($value['data']=='created_at'){
            array_push($where_search, ['user_order_log.created_at','LIKE','%'. $value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_order_list.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column
  
      if ($start_date != '' && $end_date == '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker',
                'user_order_log.status', 'user_order_log.created_at')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '>=', $start_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '>=', $start_date)
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }
      else if ($start_date == '' && $end_date != '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker',
                'user_order_log.status', 'user_order_log.created_at')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '<=', $end_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '<=', $end_date)
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }
      else if ($start_date == '' && $end_date == '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker',
                'user_order_log.status', 'user_order_log.created_at')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }
      else if ($start_date != '' && $end_date != '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker',
                'user_order_log.status', 'user_order_log.created_at')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereBetween('user_order_log.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereBetween('user_order_log.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }


        if ($start_date != '' && $end_date == ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)
          ->whereDate('user_order_log.created_at', '>=', $start_date)
          ->count();

          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)
          ->whereDate('user_order_log.created_at', '>=', $start_date)
          ->count();

          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])
          ->whereDate('user_order_log.created_at', '>=', $start_date)
          ->get();

          $obj['order_price'] = $obj['order_price']->sum('total_price');

          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){

            $speaker = explode(",",$obj['course_speaker']);

            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }
        }
        else if($start_date == '' && $end_date != ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)
          ->whereDate('user_order_log.created_at', '<=', $end_date)
          ->count();

          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)
          ->whereDate('user_order_log.created_at', '<=', $end_date)
          ->count();

          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])
          ->whereDate('user_order_log.created_at', '<=', $end_date)
          ->get();

          $obj['order_price'] = $obj['order_price']->sum('total_price');

          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){

            $speaker = explode(",",$obj['course_speaker']);

            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }
        }
        else if($start_date == '' && $end_date == ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();

          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();

          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])->get();

          $obj['order_price'] = $obj['order_price']->sum('total_price');

          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){

            $speaker = explode(",",$obj['course_speaker']);

            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }
        }
        else if($start_date != '' && $end_date != ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)
          ->whereBetween('user_order_log.created_at', [$start_date, $end_date])
          ->count();

          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)
          ->whereBetween('user_order_log.created_at', [$start_date, $end_date])
          ->count();

          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])
          ->whereBetween('user_order_log.created_at', [$start_date, $end_date])
          ->get();

          $obj['order_price'] = $obj['order_price']->sum('total_price');

          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){

            $speaker = explode(",",$obj['course_speaker']);

            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }

        }


        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function update(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='order_filter') {
          
          $data = DB::table('user_order_log');
          $data->join('user', 'user.id', 'user_order_log.user_id');
          $data->select('user_order_log.*', 'user.name as u_name', 'user.lastname as u_lastname', 'user.email', 
                        'user.mobile');
          $data->get();

          if (isset($request->payment)) {
            if ($request->payment != '9') {
              $data->where('user_order_log.payment_type', '=', $request->payment);
            } else {

            }
          }
          
          if (isset($request->order_no)) {
            $data->where('user_order_log.order_no', 'like', '%'.$request->order_no.'%');
          }
          
          if (isset($request->user_email)) {
            $data->where('user.email', 'like', '%'.$request->user_email.'%');
          }

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_order_list.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'หมายเลขสั่งซื้อ'=>'หมายเลขสั่งซื้อ',
            'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'เบอร์โทรศัพท์'=>'เบอร์โทรศัพท์',
            
            'ชื่อ - นามสกุล ผู้ซื้อ'=>'ชื่อ - นามสกุล ผู้ซื้อ',
            'อีเมล์ ผู้ซื้อ'=>'อีเมล์ ผู้ซื้อ',
            'เบอร์โทรศัพท์ ผู้ซื้อ'=>'เบอร์โทรศัพท์ ผู้ซื้อ',
            'ที่อยู่ ผู้ซื้อ'=>'ที่อยู่ ผู้ซื้อ',
            'ตำบล ผู้ซื้อ'=>'ตำบล ผู้ซื้อ',
            'อำเภอ ผู้ซื้อ'=>'อำเภอ ผู้ซื้อ',
            'จังหวัด ผู้ซื้อ'=>'จังหวัด ผู้ซื้อ',
            'รหัสไปรษณีย์ ผู้ซื้อ'=>'รหัสไปรษณีย์ ผู้ซื้อ',

            'ประเภทการจ่ายเงิน'=>'ประเภทการจ่ายเงิน',
            'จำนวนเงิน'=>'จำนวนเงิน',
            'ราคาส่วนลด'=>'ราคาส่วนลด',
            'ราคาสุทธิ'=>'ราคาสุทธิ',
            'สถานะการจ่ายเงิน'=>'สถานะการจ่ายเงิน',
            'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
          );

          foreach ($data as $key=>$e) {

            if($e->payment_type == 1){
              $e->payment_type = 'QR Code';
            }else if($e->payment_type == 2){
              $e->payment_type = 'Credit';
            }else{
              $e->payment_type = 'transfering';
            }

            if($e->receipt_type == 1){
              $e->receipt_type = 'ธรรมดา';
            }else{
              $e->receipt_type = 'นิติบุคคล';
            }

            if($e->receipt == 1){
              $e->receipt = 'ต้องการ';
            }else{
              $e->receipt = 'ไม่ต้องการ';
            }

            if($e->status == 1){
              $e->status = 'รอการชำระเงิน';
            }else if($e->status == 2){
              $e->status = 'สั่งซื้อสำเร็จ';
            }else if($e->status == 3){
              $e->status = 'ยกเลิกคำสั่งซื้อ';
            }else{
              $e->status = 'รอการตรวจสอบ';
            }

            $fullprice = UsersOrderList::where('order_id', $e->id)->sum('price');
            if($fullprice == 0){
              $fullprice = strval($fullprice);
            }else{
              $fullprice = $fullprice;
            }

            $dis_price = UsersOrderList::where('order_id', $e->id)->sum('discount_value');
            if($dis_price == 0){
              $disprice = strval($dis_price);
            }else{
              $disprice = $dis_price;
            }

            $total = UsersOrderList::where('order_id', $e->id)->sum('total_price');
            if($total == 0){
              $total = strval($total);
            }else{
              $total = $total;
            }

            $ex_array[]=array(
            'หมายเลขสั่งซื้อ'=>$e->order_no,
            'ชื่อ - นามสกุล'=>$e->u_name.' '.$e->u_lastname,
            'อีเมล์'=>$e->email,
            'เบอร์โทรศัพท์'=>$e->mobile,
            
            'ชื่อ - นามสกุล ผู้ซื้อ'=>$e->u_name.' '.$e->cus_lastname,
            'อีเมล์ ผู้ซื้อ'=>$e->cus_email,
            'เบอร์โทรศัพท์ ผู้ซื้อ'=>$e->cus_mobile,
            'ที่อยู่ ผู้ซื้อ'=>$e->doc_address,
            'ตำบล ผู้ซื้อ'=>$e->doc_subdistrict,
            'อำเภอ ผู้ซื้อ'=>$e->doc_district,
            'จังหวัด ผู้ซื้อ'=>$e->doc_province,
            'รหัสไปรษณีย์ ผู้ซื้อ'=>$e->doc_postcode,
            
            'ประเภทการจ่ายเงิน'=>$e->payment_type,
            'จำนวนเงิน'=>$fullprice,
            'ราคาส่วนลด'=>$disprice,
            'ราคาสุทธิ'=>$total,
            'สถานะการจ่ายเงิน'=>$e->status,
            'วันที่เก็บข้อมูล'=>$e->created_at,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

    public function dblist_date(DataTables $datatables, Request $request)
    {
  
      if (Auth::user()->level != '99' && Auth::user()->level != '88' && Auth::user()->level != '77') {
        return new Dummy();
      }
  
      if ($request->start_date != '0') {
        $start_date = Carbon::parse($request->start_date)->format('Y-m-d H:i:s');
      } else {
        $start_date = '';
      }
      if ($request->end_date != '0') {
        $end_date = Carbon::parse($request->end_date)->addDays(1)->format('Y-m-d H:i:s');
      } else {
        $end_date = '';
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');
  
      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if (!empty($value['search']['value'])) {
          array_push($where_search, [$value['data'], $value['search']['value']]);
        }
      }
  
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column
  
      if ($start_date != '' && $end_date == '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '>=', $start_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '>=', $start_date)
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }
      if ($start_date == '' && $end_date != '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '<=', $end_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereDate('user_order_log.created_at', '<=', $end_date)
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }
      if ($start_date == '' && $end_date == '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }
      if ($start_date != '' && $end_date != '') {
        $query = DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->select('user_order_list.id', 'course.id as course_id', 'course.title_th as course_name', 'course.speaker as course_speaker')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereBetween('user_order_log.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = count(DB::table('user_order_list')
        ->leftjoin('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
        ->leftjoin('course', 'course.id', 'user_order_list.course_id')
        ->groupBy('course_id')
        ->where('user_order_log.status', 2)
        ->whereBetween('user_order_log.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->pluck('user_order_list.course_id')->toArray());
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column
  
      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        if ($start_date != '' && $end_date == ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)
          ->whereDate('user_order_log.created_at', '>=', $start_date)->count();
  
          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();
  
          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])->get();
  
          $obj['order_price'] = $obj['order_price']->sum('total_price');
  
          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){
  
            $speaker = explode(",",$obj['course_speaker']);
  
            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }
        }
        if($start_date == '' && $end_date != ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();
  
          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();
  
          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])->get();
  
          $obj['order_price'] = $obj['order_price']->sum('total_price');
  
          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){
  
            $speaker = explode(",",$obj['course_speaker']);
  
            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }
        }
        if($start_date == '' && $end_date == ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();
  
          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();
  
          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])->get();
  
          $obj['order_price'] = $obj['order_price']->sum('total_price');
  
          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){
  
            $speaker = explode(",",$obj['course_speaker']);
  
            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }
        }
        if($start_date != '' && $end_date != ''){

          $obj['student_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();
  
          $obj['order_count'] = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->where('user_order_list.course_id', $obj['course_id'])->where('status', 2)->count();
  
          $obj['order_price'] = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->where('user_order_list.course_id', $obj['course_id'])->get();
  
          $obj['order_price'] = $obj['order_price']->sum('total_price');
  
          if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){
  
            $speaker = explode(",",$obj['course_speaker']);
  
            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->title_th;
              }
            }
            $obj['course_speaker'] = $speaker_name;
          }else{
            $obj['course_speaker'] = 'ไม่มีผู้สอน';
          }

        }
  
        $obj['action'] = '
            <div class="btn-group">
              <a href="' . $this->prefix . '/' . $obj->id . '/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
              <a href="javascript:void(0)" onclick="deleteDatatableItem(' . $obj->id . ',\'' . $this->prefix . '/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
            </div>';
        array_push($data_query, $obj);
      }
  
      $data = $datatables->of($data_query)
        ->with([
          'data' => $data_query,
          'draw' => $draw,
          "recordsTotal" => $count_total,
          "recordsFiltered" => $count_total,
        ])
        ->make(true);
      return $data;
    }

}
