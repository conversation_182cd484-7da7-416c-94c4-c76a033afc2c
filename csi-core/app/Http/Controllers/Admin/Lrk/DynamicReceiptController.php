<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\DynamicReceipt;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class DynamicReceiptController extends Controller
{
    public $prefix = 'dynamic_receipt';
    public $project_url = 'dynamic_receipt';
    public $project_layout = 'admin.Lrk.dynamic_receipt';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $count = DynamicReceipt::count();
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','count'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new DynamicReceipt();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = DynamicReceipt::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['cert_template.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['cert_template.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['cert_template.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['cert_template.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('cert_template')
      ->select('cert_template.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('cert_template')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new DynamicReceipt;
      $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
      $model->tax_no = isset($request->tax_no) ? AppHelper::instance()->cleanInput($request->tax_no) : null;
      $model->address = isset($request->address) ? AppHelper::instance()->cleanInputBr($request->address) : null;

      if ($request->hasFile('logo')) {
        $model->logo = AppHelper::instance()->saveImage($request->file('logo'),'/upload/receipt_img');
      }

      if ($request->hasFile('approve_signature')) {
        $model->approve_signature = AppHelper::instance()->saveImage($request->file('approve_signature'),'/upload/receipt_img');
      }

      if ($request->hasFile('receiver_signature')) {
        $model->receiver_signature = AppHelper::instance()->saveImage($request->file('receiver_signature'),'/upload/receipt_img');
      }

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = DynamicReceipt::find($id);
      $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
      $model->tax_no = isset($request->tax_no) ? AppHelper::instance()->cleanInput($request->tax_no) : null;
      $model->address = isset($request->address) ? AppHelper::instance()->cleanInputBr($request->address) : null;
      
      if ($request->hasFile('logo')) {
        if($model->logo!=null&&$model->logo!=''){
          AppHelper::instance()->removeImage($model->logo);
        }
        $model->logo = AppHelper::instance()->saveImage($request->file('logo'),'/upload/receipt_img');
      }else{
        if(empty($request->source_logo)){
          if($model->logo!=null&&$model->logo!=''){
            AppHelper::instance()->removeImage($model->logo);
          }
          $model->logo = null;
        }
      }
      
      if ($request->hasFile('approve_signature')) {
        if($model->approve_signature!=null&&$model->approve_signature!=''){
          AppHelper::instance()->removeImage($model->approve_signature);
        }
        $model->approve_signature = AppHelper::instance()->saveImage($request->file('approve_signature'),'/upload/receipt_img');
      }else{
        if(empty($request->source_approve_signature)){
          if($model->approve_signature!=null&&$model->approve_signature!=''){
            AppHelper::instance()->removeImage($model->approve_signature);
          }
          $model->approve_signature = null;
        }
      }
      
      if ($request->hasFile('receiver_signature')) {
        if($model->receiver_signature!=null&&$model->receiver_signature!=''){
          AppHelper::instance()->removeImage($model->receiver_signature);
        }
        $model->receiver_signature = AppHelper::instance()->saveImage($request->file('receiver_signature'),'/upload/receipt_img');
      }else{
        if(empty($request->source_receiver_signature)){
          if($model->receiver_signature!=null&&$model->receiver_signature!=''){
            AppHelper::instance()->removeImage($model->receiver_signature);
          }
          $model->receiver_signature = null;
        }
      }

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

}
