<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CourseSection;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\CourseSectionFileLog;
use App\Models\Core\CourseSectionLesson;
use Maatwebsite\Excel\Facades\Excel;


class CourseSectionController extends Controller
{
    public $prefix = 'course_section';
    public $project_url = 'course_section';
    public $project_layout = 'admin.Lrk.course_section';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      $model = CourseSection::find($request->id);
      
      CourseSection::where('course_id',$model->course_id)->where('position','>',$model->position)
      ->update(['position' => DB::raw('position - 1')]);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      $model = CourseSection::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course_section.status', $value['search']['value']]);
          }else if($value['data']=='title'){
            array_push($where_search, ['course_section.title', $value['search']['value']]);
          }else if($value['data']=='details'){
            array_push($where_search, ['course_section.details', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_section.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('course_section')
      ->select('course_section.*')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_section')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem5('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem5('.$obj->id.',\''.route('admin').'/course_section/delete\',\'datatable_faq\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new CourseSection; 

      $model->course_id = $request->course_id;

      $model->type = isset($request->gallery_modal_type) ? $request->gallery_modal_type : 1;
      $model->lesson_id = isset($request->gallery_modal_lesson_id) ? $request->gallery_modal_lesson_id : null;
  
      $model->title = isset($request->gallery_modal_title) ? AppHelper::instance()->cleanInput($request->gallery_modal_title) : null;
      $model->details = isset($request->gallery_modal_details) ? AppHelper::instance()->cleanInputBr($request->gallery_modal_details) : null;
      $model->lesson = is_array($request->gallery_modal_lesson) ? implode(",",$request->gallery_modal_lesson) : null;
      $model->file = is_array($request->gallery_modal_file) ? implode(",",$request->gallery_modal_file) : null;

      $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : 1;

      $count = CourseSection::where('course_id',$request->course_id)
      ->count();
      $model->position = $count + 1;

      $model->save();

      if(is_array($request->gallery_modal_lesson)){ //ถ้า tags มีค่า
        foreach($request->gallery_modal_lesson as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseSectionLesson();
          $log->section_id = $model->id;
          $log->course_id = $model->course_id;
          $log->lesson_id = $value;
          $log->save();
        }
      }
      if(is_array($request->gallery_modal_file)){ //ถ้า tags มีค่า
        foreach($request->gallery_modal_file as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseSectionFileLog();
          $log->section_id = $model->id;
          $log->course_id = $model->course_id;
          $log->file_id = $value;
          $log->save();
        }
      }
      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = CourseSection::find($request->id);

      if($model){

        $model->type = isset($request->gallery_modal_type) ? $request->gallery_modal_type : 1;
        if($request->gallery_modal_type == 3){
          $model->lesson_id = isset($request->gallery_modal_lesson_id) ? $request->gallery_modal_lesson_id : null; 
          $model->details = null;
        }else{
          $model->lesson_id = null;
          $model->details = isset($request->gallery_modal_details) ? AppHelper::instance()->cleanInputBr($request->gallery_modal_details) : null;
        }
    
        $model->title = isset($request->gallery_modal_title) ? AppHelper::instance()->cleanInput($request->gallery_modal_title) : null;
  
        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : 1;
        
        $old_tags = explode(',', $model->lesson);
        $model->lesson = is_array($request->gallery_modal_lesson) ? implode(",",$request->gallery_modal_lesson) : null;
        if(is_array($request->gallery_modal_lesson)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          $diff_tags = array_diff($old_tags, $request->gallery_modal_lesson);

          if(count(array_diff($old_tags, $request->gallery_modal_lesson))>0 || count(array_diff($request->gallery_modal_lesson, $old_tags))>0){
            $log = CourseSectionLesson::where('section_id', $model->id);
            $log->delete();

            foreach($request->gallery_modal_lesson as $value) {
              $log = new CourseSectionLesson();
              $log->section_id = $model->id;
              $log->course_id = $model->course_id;
              $log->lesson_id = $value;
              $log->save();
            }
          }
        }else{
          $log = CourseSectionLesson::where('section_id', $model->id);
          $log->delete();
        }

        $old_tags_file = explode(',', $model->file);
        $model->file = is_array($request->gallery_modal_file) ? implode(",",$request->gallery_modal_file) : null;
        if(is_array($request->gallery_modal_file)){
          if(!is_array($old_tags_file)){
            $old_tags_file = array();
          }

          $diff_tags = array_diff($old_tags_file, $request->gallery_modal_file);

          if(count(array_diff($old_tags_file, $request->gallery_modal_file))>0 || count(array_diff($request->gallery_modal_file, $old_tags_file))>0){
            $log = CourseSectionFileLog::where('section_id', $model->id);
            $log->delete();

            foreach($request->gallery_modal_file as $value) {
              $log = new CourseSectionFileLog();
              $log->section_id = $model->id;
              $log->course_id = $model->course_id;
              $log->file_id = $value;
              $log->save();
            }
          }
        }else{
          $log = CourseSectionFileLog::where('section_id', $model->id);
          $log->delete();
        }

        $model->save();

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = CourseSection::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }

    public function position_page(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CourseSection::where('course_id',$request->course_id)
                  ->where('id',$data[0])
                  ->first();

          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CourseSection::where('course_id',$request->course_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }
}
