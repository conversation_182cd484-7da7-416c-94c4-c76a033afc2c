<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\VolumnByCourse;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\UserHistory;
use App\Models\Core\VolumnByLot;
use App\Models\User;
use App\Models\VNE\Project;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class VolumnByCourseController extends Controller
{
    public $prefix = 'volumn_course';
    public $project_url = 'volumn_course';
    public $project_layout = 'admin.Lrk.volumn_course';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      
      $company_id = $request->company_id;
      $lot_id = $request->lot_id;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','company_id','lot_id'));
    }

    public function create(Request $request)
    {      
        $lot_id = $request->lot_id;
        $company_id = $request->company_id;

        $model = new VolumnByCourse();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> url('csisocietyadmin/volumn_course/'.$company_id.'/'.$lot_id.'/create'),
          'btn'=>'Add',
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj','company_id','lot_id'));
    }

    public function show(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        $lot_id = $request->lot_id;
        $company_id = $request->company_id;

        $model = VolumnByCourse::find($request->id);

        if($model){
          
          $obj = array(
            'type'=>'edit',
            'route'=>'/csisocietyadmin/volumn_course/'.$request->company_id.'/'.$request->lot_id.'/edit/'.$request->id,
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj','lot_id','company_id'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='course_name'){
            array_push($where_search, ['course.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['volumn_course_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('volumn_course_log')
      ->join('course', 'course.id', 'volumn_course_log.course_id')
      ->select('volumn_course_log.*', 'course.title_th as c_title', 'course.course_key as c_key')
      ->where('volumn_course_log.company_id', $request->company_id)
      ->where('volumn_course_log.volumn_id', $request->lot_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('volumn_course_log')
      ->join('course', 'course.id', 'volumn_course_log.course_id')
      ->where('volumn_course_log.company_id', $request->company_id)
      ->where('volumn_course_log.volumn_id', $request->lot_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        $obj['text_area'] = AppHelper::instance()->limitCharUTF8($obj['text_area'],30);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

        $model = new VolumnByCourse;
        $model->company_id = $request->company_id;
        $model->volumn_id = $request->lot_id;

        $model->course_id = isset($request->course_id) ? $request->course_id : null;
        
        $model->save();
        return redirect(route('admin').'/volumn_course/'.$model->company_id.'/'.$model->volumn_id)->with('success', 'Data has been update');

    }

    public function update(Request $request)
    {

        $model = VolumnByCourse::find($request->id);
        if($model){

          $model->company_id = $request->company_id;
          $model->volumn_id = $request->lot_id;
          $model->course_id = isset($request->course_id) ? $request->course_id : null;

          $model->save();

          return redirect(route('admin').'/volumn_course/'.$model->company_id.'/'.$model->volumn_id)->with('success', 'Data has been update');
        }
    }

    public function destroy(Request $request)
    {
        $model = VolumnByCourse::find($request->id);
        if($model){
          $model->delete();
        }

        return response()->json();
    }
    
    public function import(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;

      if(isset($request->excel)){
        $excels = json_decode($request->excel);
        if(count($excels)>0){
          foreach ($excels as &$excel) {
            $type = '';
            
            if(isset($excel->course_key)){
              if(!empty($excel->course_key)){
                $course = Course::where('course_key', $excel->course_key)->first();
                if($course){
                  $volumn_course = VolumnByCourse::where('company_id', $request->company_id)->where('volumn_id', $request->lot)->where('course_id', $course->id)->first();
                  if(!$volumn_course){
                    $model_excel = new VolumnByCourse();
                    $type = 'store';
                    
                    $model_excel->course_id = $course->id;
                    $model_excel->company_id = $request->company_id;
                    $model_excel->volumn_id = $request->lot_id;
                        
                    $response[$type]+=1;
                    
                    $model_excel->save();
                  }
                }
              }
            }
          }
        }
      }
      return json_decode($response, true);;
    }

    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='volumn_course_export') {
                $data = DB::table('course')
                ->select('course.id', 'course.course_key', 'course.title_th')
                ->where('course.trailer_media', '!=', 5)
                ->where('course.status', 1)
                ->orderBy('id', 'asc')
                ->get();

                $ex_array[]=array(
                  'id'=>'id',
                  'course_key'=>'course_key',
                  'title_th'=>'title_th',
                );

                foreach ($data as $e) {

                  $ex_array[]=array(
                    'id'=>$e->id,
                    'course_key'=>$e->course_key,
                    'title_th'=>$e->title_th,
                  );
                }
            }



            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    // Excel::create($request->filename, function ($excel) use ($ex_array) {
                    //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
                    //         $sheet->fromArray($ex_array);
                    //     });
                    // })->download('xlsx');
                    $export = new TempExport($ex_array);
                    // AppHelper::instance()->consoleLog($ex_array);

                    return Excel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                    );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

  }
