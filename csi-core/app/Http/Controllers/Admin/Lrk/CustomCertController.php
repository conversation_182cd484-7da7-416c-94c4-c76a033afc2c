<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\DynamicCert;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use Maatwebsite\Excel\Facades\Excel;


class CustomCertController extends Controller
{
    public $prefix = 'custom_cert';
    public $project_url = 'custom_cert';
    public $project_layout = 'admin.Lrk.custom_cert';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new DynamicCert();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = DynamicCert::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['cert_dynamic.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['cert_dynamic.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['cert_dynamic.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['cert_dynamic.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('cert_dynamic')
      ->select('cert_dynamic.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('cert_dynamic')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $course_tag = $request->course_id;

      if(is_array($course_tag)){ 
        foreach($course_tag as $value) { 
          $log = new DynamicCert();
          $log->course_id = $value;
          $log->lang_type = $request->lang_type;
          $log->course_director = $request->course_director;
          $log->course_director_en = $request->course_director_en;
          $log->director_position = $request->director_position;
          $log->director_position_en = $request->director_position_en;
          $log->course_director_2 = $request->course_director_2;
          $log->course_director_2_en = $request->course_director_2_en;
          $log->director_position_2 = $request->director_position_2;
          $log->director_position_2_en = $request->director_position_2_en;
          $log->course_director_3 = $request->course_director_3;
          $log->course_director_3_en = $request->course_director_3_en;
          $log->director_position_3 = $request->director_position_3;
          $log->director_position_3_en = $request->director_position_3_en;
          
          if ($request->hasFile('bg_color')) {
            $log->bg_color = AppHelper::instance()->saveImage($request->file('bg_color'),'/upload/certificate');
          }

          if ($request->hasFile('bg_color_en')) {
            $log->bg_color_en = AppHelper::instance()->saveImage($request->file('bg_color_en'),'/upload/certificate');
          }

          if ($request->hasFile('signature')) {
            $log->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
          }

          if ($request->hasFile('signature_en')) {
            $log->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
          }

          if ($request->hasFile('signature_2')) {
            $log->signature_2 = AppHelper::instance()->saveImage($request->file('signature_2'),'/upload/certificate');
          }

          if ($request->hasFile('signature_2_en')) {
            $log->signature_2_en = AppHelper::instance()->saveImage($request->file('signature_2_en'),'/upload/certificate');
          }

          if ($request->hasFile('signature_3')) {
            $log->signature_3 = AppHelper::instance()->saveImage($request->file('signature_3'),'/upload/certificate');
          }

          if ($request->hasFile('signature_3_en')) {
            $log->signature_3_en = AppHelper::instance()->saveImage($request->file('signature_3_en'),'/upload/certificate');
          }
          $course_name = Course::where('id', $log->course_id)->first();
          if($log->lang_type == 1){
            $log->couse_name = $course_name->title_th;
            $log->course_name_en = null;
          }else if($log->lang_type == 2){
            $log->couse_name = null;
            if($course_name->title_en != null && $course_name->title_en != ''){
              $log->course_name_en = $course_name->title_en;
            }else{
              $log->course_name_en = $course_name->title_th;
            }
          }else{
            $log->couse_name = $course_name->title_th;
            if($course_name->title_en != null && $course_name->title_en != ''){
              $log->course_name_en = $course_name->title_en;
            }else{
              $log->course_name_en = $course_name->title_th;
            }
          }

          $log->status = $request->status;

          $log->save();
        }
      }

      // AppHelper::instance()->consoleLog($course_tag);

      return redirect(route('admin').'/dynamic_cert')->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = DynamicCert::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = DynamicCert::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
