<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\Course;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CateAge;
use App\Models\Core\CateDepartment;
use App\Models\Core\CateGender;
use App\Models\Core\Categories;
use App\Models\Core\CategoriesSub;
use App\Models\Core\CateSpeaker;
use App\Models\Core\CouponCourse;
use App\Models\Core\CourseAgeLog;
use App\Models\Core\CourseCateLog;
use App\Models\Core\CourseCheckerLog;
use App\Models\Core\CourseDepartmentLog;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\CourseExamLog;
use App\Models\Core\CourseFileLog;
use App\Models\Core\CourseGenderLog;
use App\Models\Core\CourseLeranerLog;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\CourseLogoLog;
use App\Models\Core\CourseOrganLog;
use App\Models\Core\CourseRate;
use App\Models\Core\CourseSpeakerLog;
use App\Models\Core\CourseSubCate;
use App\Models\Core\CourseTagLog;
use App\Models\Core\UsersCommentLog;
use App\Models\Core\UsersCommentLikeLog;
use App\Models\Core\UsersExamLog;
use App\Models\Core\UsersExamPoint;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\Tags;
use App\Models\Core\UsersCartLog;
use App\Models\Core\UsersLearningLog;
use Maatwebsite\Excel\Facades\Excel;


class SeminarController extends Controller
{
    public $prefix = 'seminar';
    public $project_url = 'seminar';
    public $project_layout = 'admin.Lrk.seminar';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $course_all = Course::where('trailer_media', 4)->count();
      $course_open = Course::where('trailer_media', 4)->where('status', 1)->count();
      $course_closed = Course::where('trailer_media', 4)->where('status', 2)->count();
      $course_free = Course::where('trailer_media', 4)->where('is_free', 1)->where('status', 1)->count();
      $course_paid = Course::where('trailer_media', 4)->where('is_free', 2)->where('status', 1)->count();
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_all','course_open','course_closed','course_free','course_paid'));
    }

    public function create()
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = new Course();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      return redirect(route('admin'));
    }

    public function edit($id)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = Course::find($id);
      if($model){
        if($model->is_due == 2){
          $model->duration_time = 0;
        }
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['course.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['course.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['course.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      if(Auth::user()->level == 99){
        $query = DB::table('course')
        ->select('course.*')
        ->where('course.trailer_media', 4)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('course')
        ->where('course.trailer_media', 4)
        ->where($where_search)
        ->count();
      }else if(Auth::user()->level == '88'){
        $arr_id = CourseCheckerLog::where('admin_id', Auth::user()->id)->pluck('admin_id')->toArray();
        
        $query = DB::table('course')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->select('course.*', 'course_checker_log.admin_id')
        ->whereIn('course_checker_log.admin_id', $arr_id)
        ->where('course.trailer_media', 4)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('course')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->whereIn('course_checker_log.admin_id', $arr_id)
        ->where('course.trailer_media', 4)
        ->where($where_search)
        ->count();
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        // <a href="'.$this->prefix.'/'.$obj->id.'/view" class="btn btn-light" data-original-title="ข้อมูล" title="ข้อมูล"><i class="fa fa-eye"></i></a>

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = new Course;

      $slug_clean = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug = Course::where('course.slug','=', $slug_clean)->count();

      if($slug==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : AppHelper::instance()->cleanInputSlug($request->title_th);
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }

      $model->course_key = isset($request->course_key) ? AppHelper::instance()->cleanInput($request->course_key) : null;

      $model->course_lang = isset($request->course_lang) ?  AppHelper::instance()->cleanInput($request->course_lang) : 1;
      $model->course_type = isset($request->course_type) ?  AppHelper::instance()->cleanInput($request->course_type) : 1;
      $model->is_sub = isset($request->is_sub) ?  AppHelper::instance()->cleanInput($request->is_sub) : 2;
      $model->topic_type = isset($request->topic_type) ?  AppHelper::instance()->cleanInput($request->topic_type) : 1;
      $model->trailer_media = 4;
      $model->is_free = isset($request->is_free) ?  AppHelper::instance()->cleanInput($request->is_free) : 1;
      $model->internal = isset($request->internal) ?  AppHelper::instance()->cleanInput($request->internal) : 1;
      $model->is_suggess = isset($request->is_suggess) ?  AppHelper::instance()->cleanInput($request->is_suggess) : 1;

      $model->key_search = isset($request->key_search) ?  AppHelper::instance()->cleanInput($request->key_search) : null;

      $model->host = isset($request->host) ? $request->host : null;
      $model->level = isset($request->level) ? $request->level : null;

      $model->is_promotion = isset($request->is_promotion) ?  AppHelper::instance()->cleanInput($request->is_promotion) : 2;
      $model->pro_price = isset($request->pro_price) ?  AppHelper::instance()->cleanInput($request->pro_price) : 0;
      
      $model->checker = is_array($request->checker) ? implode(",",$request->checker) : '';
      
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = isset($request->title_en) ? AppHelper::instance()->cleanInput($request->title_en) : null;

      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null;
      $model->subtitle_en = isset($request->subtitle_en) ? AppHelper::instance()->cleanInput($request->subtitle_en) : null;

      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;
      $model->details_en = null;
      $model->price = isset($request->price) ? AppHelper::instance()->cleanInput($request->price) : 0;
        
      $model->is_hot = isset($request->is_hot) ?  AppHelper::instance()->cleanInput($request->is_hot) : 1;
      $model->is_new = isset($request->is_new) ?  AppHelper::instance()->cleanInput($request->is_new) : 1;
      $model->is_oculus = isset($request->is_oculus) ?  AppHelper::instance()->cleanInput($request->is_oculus) : 2;

      $model->logo = is_array($request->logo) ? implode(",",$request->logo) : '';
      $model->learner = is_array($request->learner) ? implode(",",$request->learner) : '';

      $model->is_cme = isset($request->is_cme) ? AppHelper::instance()->cleanInput($request->is_cme) : 1;
      if($model->is_cme == 1){
        $model->receive_point = isset($request->receive_point) ? AppHelper::instance()->cleanInput($request->receive_point) : 0;
      }else{
        $model->receive_point = 0;
      }

      $model->point_to_pass = isset($request->point_to_pass) ? AppHelper::instance()->cleanInput($request->point_to_pass) : 0;

      $model->is_due = isset($request->is_due) ? AppHelper::instance()->cleanInput($request->is_due) : 1;
      if($model->is_due == 2){
        $model->duration_time = 99999;
      }else{
        $model->duration_time = isset($request->duration_time) ? AppHelper::instance()->cleanInput($request->duration_time) : 99999;
      }

      $model->conference_date = isset($request->conference_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->conference_date)->format('Y-m-d H:i:s') : null;
      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;
      
      $model->started_learning = isset($request->started_learning) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_learning)->format('Y-m-d H:i:s') : null;

      if ($request->hasFile('image_th')) {
        $model->image_th = AppHelper::instance()->saveImage($request->file('image_th'),'/upload/course');
      }

      if ($request->hasFile('banner')) {
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/course');
      }

      if ($request->hasFile('og_image')) {
        $model->og_image = AppHelper::instance()->saveImage($request->file('og_image'),'/upload/course');
      }

      $count = Course::where('trailer_media', 4)->count();
      $model->position = $count + 1;

      $model->status = $request->status;
  
      //เช็ค Categories
      $list_tags = array();
      if(is_array($request->categories)){
        $all_tags_val = $request->categories;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Categories::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Categories();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Categories::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->categories = $list_tags;
      $model->categories = implode(",",$request->categories);
      //เช็ค Categories
  
      //เช็ค Department
      $list_tags = array();
      if(is_array($request->department)){
        $all_tags_val = $request->department;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateDepartment::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateDepartment();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateDepartment::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->department = $list_tags;
      $model->department = implode(",",$request->department);
      //เช็ค Department
  
      //เช็ค SPEAKER
      $list_tags = array();
      if(is_array($request->speaker)){
        $all_tags_val = $request->speaker;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateSpeaker::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateSpeaker();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateSpeaker::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->speaker = $list_tags;
      $model->speaker = implode(",",$request->speaker);
      //เช็ค SPEAKER
  
      //เช็ค Tags
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tags_val = $request->tag;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      //เช็ค Tags
  
      //เช็ค Gender
      $list_tags = array();
      if(is_array($request->gender)){
        $all_tags_val = $request->gender;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateGender::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateGender();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateGender::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->gender = $list_tags;
      $model->gender = implode(",",$request->gender);
      //เช็ค Gender
  
      //เช็ค Age
      $list_tags = array();
      if(is_array($request->age)){
        $all_tags_val = $request->age;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateAge::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateAge();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateAge::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->age = $list_tags;
      $model->age = implode(",",$request->age);
      //เช็ค Age

      $model->save();
  
      //Learner
      if(is_array($request->learner)){
        foreach($request->learner as $value) {
          $log = new CourseLeranerLog();
          $log->course_id = $model->id;
          $log->learner = $value;
          $log->save();
        }
      }
      //Learner
  
      //Logo
      if(is_array($request->logo)){
        foreach($request->logo as $value) {
          $log = new CourseLogoLog();
          $log->course_id = $model->id;
          $log->logo_id = $value;
          $log->save();
        }
      }
      //Logo

      if(is_array($request->checker)){ //ถ้า tags มีค่า
        foreach($request->checker as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseCheckerLog();
          $log->course_id = $model->id;
          $log->admin_id = $value;
          $log->save();
        }
      }
      
      //CATEGORIES
      if(is_array($request->categories)){ //ถ้า department มีค่า
        foreach($request->categories as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseCateLog();
          $log->course_id = $model->id;
          $log->cate_id = $value;
          $log->save();
        }
      }
      //CATEGORIES
      
      //DEPARTMENT
      if(is_array($request->department)){ //ถ้า department มีค่า
        foreach($request->department as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseDepartmentLog();
          $log->course_id = $model->id;
          $log->department = $value;
          $log->save();
        }
      }
      //DEPARTMENT
      
      //SPEAKER
      if(is_array($request->speaker)){ //ถ้า speaker มีค่า
        foreach($request->speaker as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseSpeakerLog();
          $log->course_id = $model->id;
          $log->speaker = $value;
          $log->save();
        }
      }
      //SPEAKER

      //TAG
      if(is_array($request->tag)){ //ถ้า tags มีค่า
        foreach($request->tag as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseTagLog();
          $log->course_id = $model->id;
          $log->tag_id = $value;
          $log->save();
        }
      }
      //TAG

      //GENDER
      if(is_array($request->gender)){ //ถ้า gender มีค่า
        foreach($request->gender as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseGenderLog();
          $log->course_id = $model->id;
          $log->gender_id = $value;
          $log->save();
        }
      }
      //GENDER

      //AGE
      if(is_array($request->age)){ //ถ้า age มีค่า
        foreach($request->age as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseAgeLog();
          $log->course_id = $model->id;
          $log->age_id = $value;
          $log->save();
        }
      }
      //AGE

      return redirect(route('admin').'/seminar/'.$model->id.'/edit/')->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $model = Course::find($id);

      //CATEGORIES
      $old_tags = explode(',', $model->categories);
      $list_tags = array();
      if(is_array($request->categories)){
        $all_tag_val = $request->categories;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Categories::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Categories();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Categories::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->categories = $list_tags;
      $model->categories = implode(",",$request->categories);
      if(is_array($request->categories)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->categories))>0 || count(array_diff($request->categories, $old_tags))>0){
          $tags = CourseCateLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->categories as $value) {
            $log = new CourseCateLog();
            $log->course_id = $model->id;
            $log->cate_id = $value;
            $log->save();
          }
        }
      }
      //CATEGORIES

      //DEPARTMENT
      $old_tags = explode(',', $model->department);
      $list_tags = array();
      if(is_array($request->department)){
        $all_tag_val = $request->department;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateDepartment::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateDepartment();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateDepartment::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->department = $list_tags;
      $model->department = implode(",",$request->department);
      if(is_array($request->department)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->department))>0 || count(array_diff($request->department, $old_tags))>0){
          $tags = CourseDepartmentLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->department as $value) {
            $log = new CourseDepartmentLog();
            $log->course_id = $model->id;
            $log->department = $value;
            $log->save();
          }
        }
      }
      //DEPARTMENT

      //Learner
      $old_tags = explode(',', $model->learner);
      $model->learner = is_array($request->learner) ? implode(",",$request->learner) : '';
      if(is_array($request->learner)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->learner);

        if(count(array_diff($old_tags, $request->learner))>0 || count(array_diff($request->learner, $old_tags))>0){
          $log = CourseLeranerLog::where('course_id', $model->id);
          $log->delete();

          foreach($request->learner as $value) {
            $log = new CourseLeranerLog();
            $log->course_id = $model->id;
            $log->learner = $value;
            $log->save();
          }
        }
      }else{
        $log = CourseLeranerLog::where('course_id', $model->id);
        $log->delete();
      }
      //Learner

      //SPEAKER
      $old_tags = explode(',', $model->speaker);
      $list_tags = array();
      if(is_array($request->speaker)){
        $all_tag_val = $request->speaker;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateSpeaker::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateSpeaker();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateSpeaker::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->speaker = $list_tags;
      $model->speaker = implode(",",$request->speaker);
      if(is_array($request->speaker)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->speaker))>0 || count(array_diff($request->speaker, $old_tags))>0){
          $tags = CourseSpeakerLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->speaker as $value) {
            $log = new CourseSpeakerLog();
            $log->course_id = $model->id;
            $log->speaker = $value;
            $log->save();
          }
        }
      }
      //SPEAKER

      //Tags
      $old_tags = explode(',', $model->tag);
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tag_val = $request->tag;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      if(is_array($request->tag)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->tag))>0 || count(array_diff($request->tag, $old_tags))>0){
          $tags = CourseTagLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->tag as $value) {
            $log = new CourseTagLog();
            $log->course_id = $model->id;
            $log->tag_id = $value;
            $log->save();
          }
        }
      }
      //TAG

      //GENDER
      $old_tags = explode(',', $model->gender);
      $list_tags = array();
      if(is_array($request->gender)){
        $all_tag_val = $request->gender;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateGender::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateGender();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateGender::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->gender = $list_tags;
      $model->gender = implode(",",$request->gender);
      if(is_array($request->gender)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->gender))>0 || count(array_diff($request->gender, $old_tags))>0){
          $tags = CourseGenderLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->gender as $value) {
            $log = new CourseGenderLog();
            $log->course_id = $model->id;
            $log->gender_id = $value;
            $log->save();
          }
        }
      }
      //GENDER

      //AGE
      $old_tags = explode(',', $model->age);
      $list_tags = array();
      if(is_array($request->age)){
        $all_tag_val = $request->age;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateAge::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateAge();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateAge::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->age = $list_tags;
      $model->age = implode(",",$request->age);
      if(is_array($request->age)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->age))>0 || count(array_diff($request->age, $old_tags))>0){
          $tags = CourseAgeLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->age as $value) {
            $log = new CourseAgeLog();
            $log->course_id = $model->id;
            $log->age_id = $value;
            $log->save();
          }
        }
      }
      //AGE

      //Logo
      $old_tags = explode(',', $model->logo);
      $model->logo = is_array($request->logo) ? implode(",",$request->logo) : '';
      if(is_array($request->logo)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->logo);

        if(count(array_diff($old_tags, $request->logo))>0 || count(array_diff($request->logo, $old_tags))>0){
          $log = CourseLogoLog::where('course_id', $model->id);
          $log->delete();

          foreach($request->logo as $value) {
            $log = new CourseLogoLog();
            $log->course_id = $model->id;
            $log->logo_id = $value;
            $log->save();
          }
        }
      }else{
        $log = CourseLogoLog::where('course_id', $model->id);
        $log->delete();
      }
      //Logo

      $model->is_hot = isset($request->is_hot) ?  $request->is_hot : $model->is_hot;
      $model->is_new = isset($request->is_new) ?  $request->is_new : $model->is_new;
      $model->is_oculus = isset($request->is_oculus) ?  AppHelper::instance()->cleanInput($request->is_oculus) : 2;
      
      $model->course_key = isset($request->course_key) ? AppHelper::instance()->cleanInput($request->course_key) : null;

      $model->course_lang = isset($request->course_lang) ?  AppHelper::instance()->cleanInput($request->course_lang) : $model->course_lang;
      $model->course_type = isset($request->course_type) ?  AppHelper::instance()->cleanInput($request->course_type) : 1;
      $model->is_sub = isset($request->is_sub) ?  AppHelper::instance()->cleanInput($request->is_sub) : 2;
      $model->topic_type = isset($request->topic_type) ?  AppHelper::instance()->cleanInput($request->topic_type) : $model->topic_type;
      $model->trailer_media = 4;
      $model->internal = isset($request->internal) ?  AppHelper::instance()->cleanInput($request->internal) : 1;

      $model->key_search = isset($request->key_search) ?  AppHelper::instance()->cleanInput($request->key_search) : null;

      $model->is_suggess = isset($request->is_suggess) ?  AppHelper::instance()->cleanInput($request->is_suggess) : $model->is_suggess;

      $model->host = isset($request->host) ? $request->host : null;
      $model->level = isset($request->level) ? $request->level : null;
      
      $model->is_promotion = isset($request->is_promotion) ? $request->is_promotion : $model->is_promotion;
      if($model->is_promotion == 1){
        $model->pro_price = isset($request->pro_price) ? AppHelper::instance()->cleanInput($request->pro_price) : 0;
      }else{
        $model->pro_price = 0;
      }
      

      if ($request->hasFile('banner')) {
        if($model->banner!=null&&$model->banner!=''){
          AppHelper::instance()->removeImage($model->banner);
        }
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/course');
      }else{
        if(empty($request->source_banner)){
          if($model->banner!=null&&$model->banner!=''){
            AppHelper::instance()->removeImage($model->banner);
          }
          $model->banner = null;
        }
      }

      if ($request->hasFile('og_image')) {
        if($model->og_image!=null&&$model->og_image!=''){
          AppHelper::instance()->removeImage($model->og_image);
        }
        $model->og_image = AppHelper::instance()->saveImage($request->file('og_image'),'/upload/course');
      }else{
        if(empty($request->source_og_image)){
          if($model->og_image!=null&&$model->og_image!=''){
            AppHelper::instance()->removeImage($model->og_image);
          }
          $model->og_image = null;
        }
      }
      
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = isset($request->title_en) ? AppHelper::instance()->cleanInput($request->title_en) : null;

      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null;
      $model->subtitle_en = isset($request->subtitle_en) ? AppHelper::instance()->cleanInput($request->subtitle_en) : null;
      
      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;

      if ($request->hasFile('image_th')) {
        if($model->image_th!=null&&$model->image_th!=''){
          AppHelper::instance()->removeImage($model->image_th);
        }
        $model->image_th = AppHelper::instance()->saveImage($request->file('image_th'),'/upload/course');
      }else{
        if(empty($request->source_image_th)){
          if($model->image_th!=null&&$model->image_th!=''){
            AppHelper::instance()->removeImage($model->image_th);
          }
          $model->image_th = null;
        }
      }

      $model->is_free = isset($request->is_free) ?  AppHelper::instance()->cleanInput($request->is_free) : $model->is_free;
      if($model->is_free == 1){
        $model->price = 0;
      }else{
        $model->price = isset($request->price) ? AppHelper::instance()->cleanInput($request->price) : 0;
      }
      
      $model->is_cme = isset($request->is_cme) ? AppHelper::instance()->cleanInput($request->is_cme) : 1;
      if($model->is_cme == 1){
        $model->receive_point = isset($request->receive_point) ? AppHelper::instance()->cleanInput($request->receive_point) : 0;
      }else{
        $model->receive_point = 0;
      }

      $model->point_to_pass = isset($request->point_to_pass) ? AppHelper::instance()->cleanInput($request->point_to_pass) : 0;

      $model->is_due = isset($request->is_due) ? AppHelper::instance()->cleanInput($request->is_due) : 1;
      if($model->is_due == 2){
        $model->duration_time = 99999;
      }else{
        $model->duration_time = isset($request->duration_time) ? AppHelper::instance()->cleanInput($request->duration_time) : 99999;
      }

      $model->conference_date = isset($request->conference_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->conference_date)->format('Y-m-d H:i:s') : null;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;
      
      $model->started_learning = isset($request->started_learning) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_learning)->format('Y-m-d H:i:s') : null;
            
      $slug_clean_th = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug_th = Course::where('course.slug','=', $slug_clean_th)->where('course.id','!=', $id)->count();
      if($slug_th==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : '';
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }

      $old_tags = explode(',', $model->checker); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
      $model->checker = is_array($request->checker) ? implode(",",$request->checker) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
      if(is_array($request->checker)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->checker); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

        if(count(array_diff($old_tags, $request->checker))>0 || count(array_diff($request->checker, $old_tags))>0){
          $log = CourseCheckerLog::where('course_id', $model->id);
          $log->delete();

          foreach($request->checker as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new CourseCheckerLog();
            $log->course_id = $model->id;
            $log->admin_id = $value;
            $log->save();
          }
        }
      }

      $model->status = $request->status;

      $model->course_duration = CourseLessonLog::where('course_id', $model->id)->sum(DB::raw('TIME_TO_SEC(duration)'));

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = Course::where('trailer_media',4)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = Course::where('trailer_media',4)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return response()->json();
      }

      $model = Course::find($request->id);

      $age = CourseAgeLog::where('course_id', $model->id)->get();
      foreach($age as $key_age => $val_age){
        $val_age->delete();
      }

      $cate = CourseCateLog::where('course_id', $model->id)->get();
      foreach($cate as $key_cate => $val_cate){
        $val_cate->delete();
      }

      $organs = CourseOrganLog::where('course_id', $model->id)->get();
      foreach($organs as $key_organs => $val_organs){
        $val_organs->delete();
      }

      $checker = CourseCheckerLog::where('course_id', $model->id)->get();
      foreach($checker as $key_checker => $val_checker){
        $val_checker->delete();
      }

      $dept = CourseDepartmentLog::where('course_id', $model->id)->get();
      foreach($dept as $key_dept => $val_dept){
        $val_dept->delete();
      }

      $exam = CourseExamLog::where('course_id', $model->id)->get();
      foreach($exam as $key_exam => $val_exam){
        $answer = CourseExamAnswer::where('exam_id', $val_exam->id)->get();
        foreach($answer as $key_answer => $val_answer){
          $val_answer->delete();
        }
        $val_exam->delete();
      }

      $file = CourseFileLog::where('course_id', $model->id)->get();
      foreach($file as $key_file => $val_file){
        $val_file->delete();
      }

      $gender = CourseGenderLog::where('course_id', $model->id)->get();
      foreach($gender as $key_gender => $val_gender){
        $val_gender->delete();
      }

      $learner = CourseLeranerLog::where('course_id', $model->id)->get();
      foreach($learner as $key_learner => $val_learner){
        $val_learner->delete();
      }

      $lesson = CourseLessonLog::where('course_id', $model->id)->get();
      foreach($lesson as $key_lesson => $val_lesson){
        $learning = UsersLearningLog::where('lesson_id', $val_lesson->id)->get();
        foreach($learning as $key_learning=>$val_learnint){
          $val_learnint->delete();
        }
        $val_lesson->delete();
      }

      $organs = CourseOrganLog::where('course_id', $model->id)->get();
      foreach($organs as $key_organs => $val_organs){
        $val_organs->delete();
      }

      $rate = CourseRate::where('course_id', $model->id)->get();
      foreach($rate as $key_rate => $val_rate){
        $val_rate->delete();
      }

      $speaker = CourseSpeakerLog::where('course_id', $model->id)->get();
      foreach($speaker as $key_speaker => $val_speaker){
        $val_speaker->delete();
      }

      $sub_cate = CourseSubCate::where('course_id', $model->id)->get();
      foreach($sub_cate as $key_sub_cate => $val_sub_cate){
        $val_sub_cate->delete();
      }

      $tag = CourseTagLog::where('course_id', $model->id)->get();
      foreach($tag as $key_tag => $val_tag){
        $val_tag->delete();
      }

      $coupon = CouponCourse::where('course_id', $model->id)->get();
      foreach($coupon as $key_coupon => $val_coupon){
        $val_coupon->delete();
      }

      $cart = UsersCartLog::where('course_id', $model->id)->get();
      foreach($cart as $key_cart => $val_cart){
        $val_cart->delete();
      }

      $comment = UsersCommentLog::where('course_id', $model->id)->get();
      foreach($comment as $key_comment => $val_comment){
        $like = UsersCommentLikeLog::where('course_id', $val_comment->id)->get();
        foreach($like as $key_like=>$val_like){
          $val_like->delete();
        }
        $val_comment->delete();
      }

      $exam = UsersExamLog::where('course_id', $model->id)->get();
      foreach($exam as $key_exam => $val_exam){
        $point = UsersExamPoint::where('exam_id', $val_exam->id)->get();
        foreach($point as $key_point=>$val_point){
          $val_point->delete();
        }
        $val_exam->delete();
      }
    
      $fav = UsersFavoriteLog::where('course_id', $model->id)->get();
      foreach($fav as $key_fav => $val_fav){
        $val_fav->delete();
      }

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return response()->json();
      }
      $model = Course::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
