<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\HomeGallery;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class HomeGalleryController extends Controller
{
    public $prefix = 'home_gallery';
    public $project_url = 'home_gallery';
    public $project_layout = 'admin.Lrk.home_gallery';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      $model = HomeGallery::find($request->id);
      
      HomeGallery::where('home_content_id',$model->home_content_id)->where('position','>',$model->position)
      ->update(['position' => DB::raw('position - 1')]);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      $model = HomeGallery::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['home_content_gallery.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['home_content_gallery.position', $value['search']['value']]);
          }else{
            array_push($where_search, ['home_content_gallery.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('home_content_gallery')
      ->select('home_content_gallery.*')
      ->where('home_content_id',$request->home_content_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('home_content_gallery')
      ->where('home_content_id',$request->home_content_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem3('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem3('.$obj->id.',\''.route('admin').'/home_gallery/delete\',\'datatable_home_gallery\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new HomeGallery; 

      $model->home_content_id = $request->home_content_id;

      $count = HomeGallery::where('home_content_id',$request->home_content_id)
      ->count();
      $model->position = $count + 1;

      if ($request->hasFile('gallery_modal_image_3')) {
          $model->image = AppHelper::instance()->saveImage($request->file('gallery_modal_image_3'),'product');
      }

      $model->status = isset($request->gallery_modal_status_3) ? $request->gallery_modal_status_3 : 1;

      $model->save();
      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = HomeGallery::find($request->id);

      if($model){

        if ($request->hasFile('gallery_modal_image_3')) {
          if($model->image!=null&&$model->image!=''){
            AppHelper::instance()->removeImage($model->image);
          }
          $model->image = AppHelper::instance()->saveImage($request->file('gallery_modal_image_3'),'product');
        }else{
          if(empty($request->source_gallery_modal_image_3)){
            if($model->image!=null&&$model->image!=''){
              AppHelper::instance()->removeImage($model->image);
            }
            $model->image = null;
          }
        }

        $model->status = isset($request->gallery_modal_status_3) ? $request->gallery_modal_status_3 : 1;

        $model->save();

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = HomeGallery::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }

    public function position_page(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = HomeGallery::where('home_content_id',$request->home_content_id)
                  ->where('id',$data[0])
                  ->first();

          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = HomeGallery::where('home_content_id',$request->home_content_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }
}
