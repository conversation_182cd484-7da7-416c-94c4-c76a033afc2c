<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\GroupCode;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Lays\BlogTagLog;
use App\Models\Lays\NotiLog;
use App\Models\Lays\SurveyAnswer;
use App\Models\Lays\SurveyLog;
use App\Models\Lays\SurveyQuestion;
use Maatwebsite\Excel\Facades\Excel;


class GroupCodeController extends Controller
{
    public $prefix = 'group_code';
    public $project_url = 'group_code';
    public $project_layout = 'admin.Lrk.group_code';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new GroupCode();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = GroupCode::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return new Dummy();
      }
      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['group_code.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['group_code.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['group_code.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['group_code.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('group_code')
      ->select('group_code.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('group_code')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>

        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        $model = new GroupCode;

        $model->group_code = isset($request->group_code) ? AppHelper::instance()->cleanInput($request->group_code) : null;

        $model->in_a = isset($request->in_a) ? $request->in_a : 1;
        $model->in_b = isset($request->in_b) ? $request->in_b : 1;
        $model->in_c = isset($request->in_c) ? $request->in_c : 1;
        $model->in_d = isset($request->in_d) ? $request->in_d : 1;

        $model->ex_a = isset($request->ex_a) ? $request->ex_a : 1;
        $model->ex_b = isset($request->ex_b) ? $request->ex_b : 1;
        $model->ex_c = isset($request->ex_c) ? $request->ex_c : 1;
        $model->ex_d = isset($request->ex_d) ? $request->ex_d : 1;
        $model->ex_e = isset($request->ex_e) ? $request->ex_e : 1;

        $model->status = $request->status;
  
        $model->save();
  
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        $model = GroupCode::find($id);

        $model->group_code = isset($request->group_code) ? AppHelper::instance()->cleanInput($request->group_code) : null;

        $model->in_a = isset($request->in_a) ? $request->in_a : 1;
        $model->in_b = isset($request->in_b) ? $request->in_b : 1;
        $model->in_c = isset($request->in_c) ? $request->in_c : 1;
        $model->in_d = isset($request->in_d) ? $request->in_d : 1;

        $model->ex_a = isset($request->ex_a) ? $request->ex_a : 1;
        $model->ex_b = isset($request->ex_b) ? $request->ex_b : 1;
        $model->ex_c = isset($request->ex_c) ? $request->ex_c : 1;
        $model->ex_d = isset($request->ex_d) ? $request->ex_d : 1;
        $model->ex_e = isset($request->ex_e) ? $request->ex_e : 1;

        $model->status = $request->status;

        $model->save();
        
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return response()->json();
      }

      $model = GroupCode::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return response()->json();
      }
      $model = GroupCode::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
