<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\EventLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class EventLogController extends Controller
{
    public $prefix = 'event_log';
    public $project_url = 'event_log';
    public $project_layout = 'admin.Lrk.event_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = EventLog::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = EventLog::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='u_lastname'){
            array_push($where_search, ['user.u_lastname', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.u_name', $value['search']['value']]);
          }else if($value['data']=='e_id'){
            array_push($where_search, ['user.e_id', $value['search']['value']]);
          }else{
            array_push($where_search, ['user.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('event_enrolment_log')
      ->join('user', 'user.id', 'event_enrolment_log.user_id')
      ->join('categories_department', 'categories_department.id', 'user.dept_id')
      ->select('event_enrolment_log.*', 'user.name as u_name', 'user.lastname as u_lastname', 'user.e_id', 'categories_department.title_th as d_name')
      ->where('event_id',$request->event_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('event_enrolment_log')
      ->join('user', 'user.id', 'event_enrolment_log.user_id')
      ->join('categories_department', 'categories_department.id', 'user.dept_id')
      ->where('event_id',$request->event_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.route('admin').'/article_log/delete\',\'datatable_gal\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $response = new Dummy();
      $response['status'] = 'false';

      $model = EventLog::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }
}
