<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\StaticSeminar;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

use Carbon\Carbon;
use App\Models\Dummy;


class StaticSeminarController extends Controller
{
    public $prefix = 'static_seminar';
    public $project_url = 'static_seminar';
    public $project_layout = 'admin.Lrk.static_seminar';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new StaticSeminar();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = StaticSeminar::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['static_seminar.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['static_seminar.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['static_seminar.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['static_seminar.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('static_seminar')
      ->select('static_seminar.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('static_seminar')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new StaticSeminar;
      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;
      $model->cta = isset($request->cta) ? AppHelper::instance()->cleanInput($request->cta) : null;
      $model->details = isset($request->details) ? AppHelper::instance()->convertDomHTML($request->details) : null;

      $count = StaticSeminar::all()->count();
      $model->position = $count + 1;

      if ($request->hasFile('banner_d')) {
        $model->banner_d = AppHelper::instance()->saveImage($request->file('banner_d'),'/upload/static_seminar');
      }

      if ($request->hasFile('banner_m')) {
        $model->banner_m = AppHelper::instance()->saveImage($request->file('banner_m'),'/upload/static_seminar');
      }

      $model->status = $request->status;

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = StaticSeminar::find($id);
      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;
      $model->cta = isset($request->cta) ? AppHelper::instance()->cleanInput($request->cta) : null;
      $model->details = isset($request->details) ? AppHelper::instance()->convertDomHTML($request->details) : null;
      
      if ($request->hasFile('banner_d')) {
        if($model->banner_d!=null&&$model->banner_d!=''){
          AppHelper::instance()->removeImage($model->banner_d);
        }
        $model->banner_d = AppHelper::instance()->saveImage($request->file('banner_d'),'/upload/static_seminar');
      }else{
        if(empty($request->source_banner_d)){
          if($model->banner_d!=null&&$model->banner_d!=''){
            AppHelper::instance()->removeImage($model->banner_d);
          }
          $model->banner_d = null;
        }
      }
      
      if ($request->hasFile('banner_m')) {
        if($model->banner_m!=null&&$model->banner_m!=''){
          AppHelper::instance()->removeImage($model->banner_m);
        }
        $model->banner_m = AppHelper::instance()->saveImage($request->file('banner_m'),'/upload/static_seminar');
      }else{
        if(empty($request->source_banner_m)){
          if($model->banner_m!=null&&$model->banner_m!=''){
            AppHelper::instance()->removeImage($model->banner_m);
          }
          $model->banner_m = null;
        }
      }

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = StaticSeminar::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = StaticSeminar::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = StaticSeminar::find($data[0]);
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = StaticSeminar::count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

}
