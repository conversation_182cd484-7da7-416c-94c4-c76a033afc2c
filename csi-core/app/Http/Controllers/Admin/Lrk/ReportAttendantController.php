<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class ReportAttendantController extends Controller
{
    public $prefix = 'report_attendant';
    public $project_url = 'report_attendant';
    public $project_layout = 'admin.Lrk.report_attendant';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {

      if (isset($request->from)) {
        $from = Carbon::parse($request->from)->format('Y-m-d H:i:s');
        $from_date = $request->from;
      } else {
        $from = '';
        $from_date = '';
      }
      if (isset($request->to)) {
        $to = Carbon::parse($request->to)->addDays(1)->format('Y-m-d H:i:s');
        $to_date = $request->to;
      } else {
        $to = '';
        $to_date = '';
      }

      $course_id = $request->course_id;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','from_date','to_date'));
    }

    public function create()
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      if (isset($request->from) && $request->from != '0') {
        $start_date = Carbon::parse($request->from)->format('Y-m-d H:i:s');
      } else {
        $start_date = '';
      }
      if (isset($request->to) && $request->to != '0') {
        $end_date = Carbon::parse($request->to)->addDays(1)->format('Y-m-d H:i:s');
      } else {
        $end_date = '';
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_learning_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_learning_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_learning_log.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='c_title'){
            array_push($where_search, ['course.title_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='l_title'){
            array_push($where_search, ['course_lesson.title_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_learning_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      if ($start_date != '' && $end_date == ''){
        $query = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->select('user.name as u_name', 'user.lastname as u_lastname', 'course.title_th as c_title',
                  'course_lesson.title_th as l_title', 'user_learning_log.created_at as log_created_at', 'user_learning_log.course_id', 
                  'user_learning_log.id', 'user_learning_log.updated_at as log_updated_at', 'user_learning_log.status', 'categories_department.title_th as user_t')
        ->where('user_learning_log.course_id', $request->course_id)
        ->whereDate('user_learning_log.log_created_at', '>=', $start_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->where('user_learning_log.course_id', $request->course_id)
        ->whereDate('user_learning_log.log_created_at', '>=', $start_date)
        ->where($where_search)
        ->count();
      }else if($start_date == '' && $end_date != '') {
        $query = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->select('user.name as u_name', 'user.lastname as u_lastname', 'course.title_th as c_title',
                  'course_lesson.title_th as l_title', 'user_learning_log.created_at as log_created_at', 'user_learning_log.course_id', 
                  'user_learning_log.id', 'user_learning_log.updated_at as log_updated_at', 'user_learning_log.status', 'categories_department.title_th as user_t')
        ->where('user_learning_log.course_id', $request->course_id)
        ->whereDate('user_learning_log.log_created_at', '<=', $end_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->where('user_learning_log.course_id', $request->course_id)
        ->whereDate('user_learning_log.log_created_at', '<=', $end_date)
        ->where($where_search)
        ->count();
      }else if($start_date == '' && $end_date == '') {
        $query = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->select('user.name as u_name', 'user.lastname as u_lastname', 'course.title_th as c_title',
                  'course_lesson.title_th as l_title', 'user_learning_log.created_at as log_created_at', 'user_learning_log.course_id', 
                  'user_learning_log.id', 'user_learning_log.updated_at as log_updated_at', 'user_learning_log.status', 'categories_department.title_th as user_t')
        ->where('user_learning_log.course_id', $request->course_id)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->where('user_learning_log.course_id', $request->course_id)
        ->where($where_search)
        ->count();
      }else if($start_date != '' && $end_date != '') {
        $query = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->select('user.name as u_name', 'user.lastname as u_lastname', 'course.title_th as c_title',
                  'course_lesson.title_th as l_title', 'user_learning_log.created_at as log_created_at', 'user_learning_log.course_id', 
                  'user_learning_log.id', 'user_learning_log.updated_at as log_updated_at', 'user_learning_log.status', 'categories_department.title_th as user_t')
        ->where('user_learning_log.course_id', $request->course_id)
        ->whereBetween('user_learning_log.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user_learning_log')
        ->join('user', 'user.id', 'user_learning_log.user_id')
        ->join('categories_department', 'categories_department.id', 'user.dept_id')
        ->join('course', 'course.id', 'user_learning_log.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
        ->where('user_learning_log.course_id', $request->course_id)
        ->whereBetween('user_learning_log.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->count();
      }
            

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['fullname'] = $value->u_name.' '.$value->u_lastname;

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/')->with('errors', 'Permission Deny');
    }

    public function update(Request $request, $id)
    {
      return redirect(route('admin').'/')->with('errors', 'Permission Deny');
    }

    public function filter(Request $request)
    {
      $course_id = $request->course_id;
      
      if(Auth::user()->level != '99' && Auth::user()->level != '66' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='report_attendant') {
          $data = DB::table('user_learning_log');
          $data->join('user', 'user.id', 'user_learning_log.user_id');
          $data->join('categories_department', 'categories_department.id', 'user.dept_id');
          $data->join('course', 'course.id', 'user_learning_log.course_id');
          $data->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id');
          $data->select('user.name as u_name', 'user.lastname as u_lastname', 'course.title_th as c_title',
                   'course_lesson.title_th as l_title', 'user_learning_log.created_at as log_created_at', 
                   'user_learning_log.updated_at as log_updated_at', 'user_learning_log.status', 'user_learning_log.user_id', 'categories_department.title_th as user_t');
          $data->where('user_learning_log.course_id', $course_id);

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_learning_log.log_created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'เริ่มเรียน (เวลา)'=>'เริ่มเรียน (เวลา)',
            'อัพเดท (เวลา)'=>'อัพเดท (เวลา)',
            'สถานะการเรียน'=>'สถานะการเรียน',
            'ประเภทผู้ใช้งาน'=>'ประเภทผู้ใช้งาน',
            'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
            'บทเรียน'=>'บทเรียน',
            'คอร์ส'=>'คอร์ส',
          );

          foreach ($data as $key=>$e) {

            if($e->status == 1){
              $e->status = 'Incomplete';
            }else{
              $e->status = 'Complete';
            }

            $ex_array[]=array(
            'เริ่มเรียน (เวลา)'=>$e->log_created_at,
            'อัพเดท (เวลา)'=>$e->log_updated_at,
            'สถานะการเรียน'=>$e->status,
            'ประเภทผู้ใช้งาน'=>$e->user_t,
            'ชื่อ - นามสกุล'=>$e->u_name.' '.$e->u_lastname,
            'บทเรียน'=>$e->l_title,
            'คอร์ส'=>$e->c_title,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

}
