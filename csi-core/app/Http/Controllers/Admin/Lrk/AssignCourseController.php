<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\AssignCourse;
use App\Models\Core\AssignCourseLog;
use App\Helpers\SimpleEnDeHelper;

use Illuminate\Http\Request;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\Dummy;
use App\Models\Core\ArticleCateLog;
use App\Models\Core\ArticleLog;
use App\Models\Core\ArticleSubCateLog;
use App\Models\Core\ArticleTagLog;
use App\Models\Core\AssignCourseGroup;
use App\Models\Core\CateDepartment;
use App\Models\Core\Course;
use App\Models\Core\CourseCateLog;
use App\Models\Core\CourseGroup;
use App\Models\Core\EmailDynamic;
use App\Models\Core\NotiAuto;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\Tags;
use App\Models\Core\UserHistory;
use App\Models\Core\UserLearnedTime;
use App\Models\Core\Users;
use App\Models\Core\UsersActivityLog;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\User;
use Carbon\Carbon;

class AssignCourseController extends Controller
{
    public $prefix = 'assign_course';
    public $project_url = 'assign_course';
    public $project_layout = 'admin.Lrk.assign_course';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new AssignCourse();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = AssignCourse::find($id);
      $article_id = $model->id;
      if($model){
        $obj = array(
          'type'=>'view',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.view', compact('model','obj','article_id'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = AssignCourse::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['assign_course.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['assign_course.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['assign_course.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['assign_course.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('assign_course')
      ->select('assign_course.id', 'assign_course.created_at', 'assign_course.remark')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('assign_course')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        
        // $obj['remark'] = AppHelper::instance()->cleanInputBr($obj['remark']);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      
      $user_array = array();
      $course_array = array();

      if(is_array($request->user)){
        $user_array = $request->user;
      }

      if($request->course_type==1){
        if(is_array($request->course_id)){
          $course_array = $request->course_id;
        }
      }else{
        //find course id as array
        if(isset($request->course_level)){
          $course_array = Course::where('level', $request->course_level)
                          ->pluck('id')->toArray();
        }
      }

      $is_save = false;
      
      if(count($user_array)>0&&count($course_array)>0){
        $model = new AssignCourse;

        $model->select_type = 1;
        $model->course_type = isset($request->course_type) ? $request->course_type : 1;
        $model->remark = isset($request->remark) ? AppHelper::instance()->cleanInputBr($request->remark) : 'No Remark';
        if($request->course_type==1){
          $model->course_arr = is_array($request->course_id) ? implode(",",$request->course_id) : null;
        }else{
          $model->course_arr = is_array($course_array) ? implode(",",$course_array) : null;
        }
          
        $model->user = is_array($request->user) ? implode(",",$request->user) : null;
        

        $model->save();

        if(is_array($request->user)){
          foreach($request->user as $value) {
            $log = new AssignCourseLog();
            $log->assign_id = $model->id;
            $log->user = $value;
            $log->save();
          }
        }
        $is_save = true;
      }else{
        return redirect(route('admin').'/'.$this->project_url)->with('errors', 'Data has not been found');
      }


      if($is_save){
        foreach($user_array as $value) {
          $user = Users::where('id', $value)
          ->select('id')
          ->first();
          
          if($user){
            $noti_auto = NotiAuto::select('id')->where('noti_auto.name', 'assign_course')->first();
            if($noti_auto){
              $noti_log = new NotiAutoLog();
              $noti_log->user_id = $user->id;
              $noti_log->noti_auto_id = $noti_auto->id;
              $noti_log->save();
            }
            foreach($course_array as $course_id){
              $course = Course::select('id','duration_time')->where('id',$course_id)->first();
              if($course){
                $check = UserHistory::where('user_id',$user->id)->where('course_id',$course->id)
                ->where('expired',Carbon::now()->addDays($course->duration_time))->first();
                if(!$check){
                  $history_log = new UserHistory();
                  $history_log->get_type = 7;
                  $history_log->user_id = $user->id;
                  $history_log->course_id = $course->id;
                  $history_log->expired = Carbon::now()->addDays($course->duration_time);
                  $history_log->assign_id = $model->id;
                  $history_log->save();
                  $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                  if($del_limit){
                    $del_limit->delete();
                  }
                  $response['status'] = 'success';
                }
              }
            }
            
          }
        }
      }
      
      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = AssignCourse::find($id);

      $model->course_id = isset($request->course_id) ? $request->course_id : 1;

      $course_name = Course::where('id', $model->course_id)->select('title_th')->first();

      $old_tags = explode(',', $model->user); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
      $model->user = is_array($request->user) ? implode(",",$request->user) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
      if(is_array($request->user)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->user); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

        if(count(array_diff($old_tags, $request->user))>0 || count(array_diff($request->user, $old_tags))>0){
          $log = AssignCourseLog::where('assign_id', $model->id);
          $log->delete();

          foreach($request->user as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new AssignCourseLog();
            $log->assign_id = $model->id;
            $log->course_name = $course_name->title_th;
            $log->user = $value;
            $log->save();
          }
        }
      }

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return response()->json();
      }

      $model = AssignCourse::find($request->id);

      $log = UserHistory::where('assign_id', $model->id)->get();
      foreach($log as $key_log => $val_log){
        $val_log->expired = Carbon::now();
        $val_log->assign_status = 2;
        $val_log->save();
      }

      $model->delete();

      return response()->json();
    }

}
