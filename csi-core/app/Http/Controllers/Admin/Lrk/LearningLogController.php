<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\UsersLearningLog;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class LearningLogController extends Controller
{
    public $prefix = 'learning_log';
    public $project_url = 'learning_log';
    public $project_layout = 'admin.Lrk.learning_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      
      $course_id = $request->course_id;
      $lesson_id = $request->lesson_id;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','lesson_id'));
    }

    public function create(Request $request)
    {      
        $lesson_id = $request->lesson_id;
        $course_id = $request->course_id;

        $model = new UsersLearningLog();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> url('csisocietyadmin/exam_log/'.$course_id.'/'.$lesson_id.'/create'),
          'btn'=>'Add',
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj','course_id','lesson_id'));
    }

    public function show(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        $lesson_id = $request->lesson_id;
        $course_id = $request->course_id;

        $model = UsersLearningLog::find($request->id);

        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>'/csisocietyadmin/exam_log/'.$request->course_id.'/'.$request->lesson_id.'/edit/'.$request->id,
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj','lesson_id','course_id'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_learning_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_learning_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_learning_log.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='lesson_title'){
            array_push($where_search, ['course_lesson.title_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_learning_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_learning_log')
      ->join('user', 'user.id', 'user_learning_log.user_id')
      ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
      ->select('user_learning_log.*', 'user.name as u_name', 'user.lastname as u_lastname', 'course_lesson.title_th as lesson_title')
      ->where('user_learning_log.action', 1)
      ->where('user_learning_log.course_id', $request->course_id)
      ->where('user_learning_log.lesson_id', $request->lesson_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_learning_log')
      ->join('user', 'user.id', 'user_learning_log.user_id')
      ->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id')
      ->where('user_learning_log.action', 1)
      ->where('user_learning_log.course_id', $request->course_id)
      ->where('user_learning_log.lesson_id', $request->lesson_id)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

    }

    public function update(Request $request)
    {

    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='learning_log_filter') {
          
          $data = DB::table('user_learning_log');
          $data->join('user', 'user.id', 'user_learning_log.user_id');
          $data->join('course_lesson', 'course_lesson.id', 'user_learning_log.lesson_id');
          $data->select('user_learning_log.*', 'user.name as u_name', 'user.lastname as u_lastname', 'user.email', 
                        'course_lesson.title_th as lesson_title', 'user.mobile');
          $data->where('user_learning_log.action', 1);
          $data->where('user_learning_log.course_id', $request->course_id);
          $data->where('user_learning_log.lesson_id', $request->lesson_id);
          $data->get();

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_learning_log.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'บทเรียน'=>'บทเรียน',
            'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'เบอร์โทรศัพท์'=>'เบอร์โทรศัพท์',
            'เวลาเรียน'=>'เวลาเรียน',
            'สถานะการเรียน'=>'สถานะการเรียน',
            'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
          );

          foreach ($data as $key=>$e) {

            if($e->status == 1){
              $e->status = 'incomplete';
            }elseif($e->status == 2){
              $e->status = 'complete';
            }


            $ex_array[]=array(
            'บทเรียน'=>$e->lesson_title,
            'ชื่อ - นามสกุล'=>$e->u_name.' '.$e->u_lastname,
            'อีเมล์'=>$e->email,
            'เบอร์โทรศัพท์'=>$e->mobile,
            'เวลาเรียน'=>$e->watching_time,
            'สถานะการเรียน'=>$e->status,
            'วันที่เก็บข้อมูล'=>$e->created_at,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

  }
