<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CourseExamLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseExamAnswer;
use Maatwebsite\Excel\Facades\Excel;


class CourseExamController extends Controller
{
    public $prefix = 'course_exam';
    public $project_url = 'course_exam';
    public $project_layout = 'admin.Lrk.course_exam';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      $model = CourseExamLog::find($request->id);

      $ans = CourseExamAnswer::where('exam_id', $model->id)->get();
      foreach($ans as $key_and => $val_ans){
        $val_ans->delete();
      }
      
      CourseExamLog::where('course_id',$model->course_id)->where('position','>',$model->position)
      ->update(['position' => DB::raw('position - 1')]);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      $model = CourseExamLog::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course_exam.status', $value['search']['value']]);
          }else if($value['data']=='title_th'){
            array_push($where_search, ['course_exam.title_th', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_exam.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('course_exam')
      ->select('course_exam.*')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_exam')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $answer = DB::table('course_exam_answer')->where('course_exam_answer.exam_id',$obj->id)->count();
        if($answer>0){
          $obj['answer'] = '
          <a href="'.url('/csisocietyadmin').'/exam_answer/'.$obj->id.'">
            จำนวน '. $answer .'
          </a>';
        }else{
          $obj['answer'] = ' 
          <a href="/csisocietyadmin/exam_answer/'.$obj->id.'/create" class="btn btn-success">
            <i class="glyphicon glyphicon-edit"></i>
            สร้าง
          </a>';
        }
          

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem3('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem3('.$obj->id.',\''.route('admin').'/course_exam/delete\',\'datatable_exam\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new CourseExamLog; 

      $model->course_id = $request->course_id;
  
  
      $model->question_th = isset($request->gallery_modal_question_th_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_question_th_3) : null;
      // $model->question_en = isset($request->gallery_modal_question_en_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_question_en_3) : null;
      $model->point = isset($request->gallery_modal_point_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_point_3) : 0;

      $model->status = isset($request->gallery_modal_status_3) ? $request->gallery_modal_status_3 : '';

      $count = CourseExamLog::where('course_id',$request->course_id)->count();
      $model->position = $count + 1;

      $model->save();
      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = CourseExamLog::find($request->id);

      if($model){
  
  
        $model->question_th = isset($request->gallery_modal_question_th_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_question_th_3) : null;
        // $model->question_en = isset($request->gallery_modal_question_en_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_question_en_3) : null;
        $model->point = isset($request->gallery_modal_point_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_point_3) : 0;
  
        $model->status = isset($request->gallery_modal_status_3) ? $request->gallery_modal_status_3 : '';

        $model->save();

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = CourseExamLog::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }

    public function position_page(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CourseExamLog::where('course_id',$request->course_id)
                  ->where('id',$data[0])
                  ->first();

          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CourseExamLog::where('course_id',$request->course_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }
}
