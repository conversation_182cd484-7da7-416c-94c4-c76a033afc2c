<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CateSpeaker;
use App\Models\Core\CourseView;
use App\Models\Core\UsersLearningLog;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;
use Maatwebsite\Lrk\Facades\Excel;


class ReportCourseController extends Controller
{
    public $prefix = 'report_course';
    public $project_url = 'report_course';
    public $project_layout = 'admin.Lrk.report_course';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );

      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return new Dummy();
      }
      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['course.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['course.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['course.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      if(Auth::user()->level == '99'){
        $query = DB::table('course')
        ->select('course.id', 'course.title_th', 'course.speaker as course_speaker')
        ->where('course.trailer_media', '!=', '5')
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('course')
        ->where('course.trailer_media', '!=', '5')
        ->where($where_search)
        ->count();
      }else{
        $query = DB::table('course')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->select('course.id', 'course.title_th', 'course.speaker as course_speaker', 'course_checker_log.admin_id')
        ->where('course.trailer_media', '!=', '5')
        ->where('course_checker_log.admin_id', Auth::user()->id)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('course')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->where('course.trailer_media', '!=', '5')
        ->where('course_checker_log.admin_id', Auth::user()->id)
        ->where($where_search)
        ->count();
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        if($obj['course_speaker'] != null || $obj['course_speaker'] != ''){

          $speaker = explode(",",$obj['course_speaker']);

          $speaker_name = '';
          foreach($speaker as $key_speaker=>$val_speaker){
            $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
            if($speaker_q){
              if($speaker_name != ''){
                $speaker_name .= ', ';
              }
              $speaker_name .= $speaker_q->title_th;
            }
          }
          $obj['course_speaker'] = $speaker_name;
        }else{
          $obj['course_speaker'] = 'ไม่มีผู้สอน';
        }

        $count_student = UsersLearningLog::where('course_id', $obj->id)->groupBy('user_id')->get();
        $count_student = Count($count_student);
        if($count_student){
          $obj['count_student'] = $count_student. ' ราย';
        }else{
          $obj['count_student'] = '0 ราย';
        }

        $count_incomplete = UsersLearningLog::where('course_id', $obj->id)->where('status', 1)->groupBy('user_id')->get();
        $count_incomplete = Count($count_incomplete);
        if($count_incomplete){
          $obj['count_incomplete'] = $count_incomplete. ' ราย';
        }else{
          $obj['count_incomplete'] = '0 ราย';
        }

        if($count_incomplete){
          $obj['count_complete'] = '0 ราย';
        }else{
          $count_complete = UsersLearningLog::where('course_id', $obj->id)->where('status', 2)->groupBy('user_id')->get();
          $count_complete = Count($count_complete);
          if($count_complete){
            $obj['count_complete'] = $count_complete. ' ราย';
          }else{
            $obj['count_complete'] = '0 ราย';
          }
        }

        $count_view = CourseView::where('course_id', $obj->id)->count();
        if($count_view){
          $obj['count_view'] = $count_view. ' ครั้ง';
        }else{
          $obj['count_view'] = '0 ครั้ง';
        }

        $obj['details'] = ' 
        <div class="btn-group">
          <a href="/csisocietyadmin/report_attendant/'.$obj->id.'" class="btn btn-details" style="background-color: blueviolet; color: aliceblue;">
            รายละเอียด
          </a>     
          <a href="/csisocietyadmin/report_student/'.$obj->id.'" class="btn btn-learn" style="background-color: forestgreen; color: aliceblue;">
            นักเรียน
          </a>      
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function update(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='course_export') {
              if(Auth::user()->level == '99'){
                $data = DB::table('course')
                ->select('course.id', 'course.title_th', 'course.speaker as course_speaker')
                ->where('course.trailer_media', '!=', '5')
                ->orderBy('course.created_at', 'asc')
                ->get();
              }else{
                $data = DB::table('course')
                ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
                ->select('course.id', 'course.title_th', 'course.speaker as course_speaker', 'course_checker_log.admin_id')
                ->where('course.trailer_media', '!=', '5')
                ->where('course_checker_log.admin_id', Auth::user()->id)
                ->orderBy('course.created_at', 'asc')
                ->get();
              }

                $ex_array[]=array(
                  'คอร์ส'=>'คอร์ส',
                  'ผู้สอน'=>'ผู้สอน',
                  'จำนวนนักเรียน'=>'จำนวนนักเรียน',
                  'กำลังเรียน'=>'กำลังเรียน',
                  'เรียนจบแล้ว'=>'เรียนจบแล้ว',
                  'จำนวนวิว'=>'จำนวนวิว',
                );

                foreach ($data as $e) {


                  if($e->course_speaker != null || $e->course_speaker != ''){
  
                    $speaker = explode(",",$e->course_speaker);
          
                    $speaker_name = '';
                    foreach($speaker as $key_speaker=>$val_speaker){
                      $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
                      if($speaker_q){
                        if($speaker_name != ''){
                          $speaker_name .= ', ';
                        }
                        $speaker_name .= $speaker_q->title_th;
                      }
                    }
                    $e->course_speaker = $speaker_name;
                  }else{
                    $e->course_speaker = 'ไม่มีผู้สอน';
                  }

                  $count_student = UsersLearningLog::where('course_id', $e->id)->groupBy('user_id')->get();
                  $count_student = Count($count_student);
                  if($count_student){
                    $count_student = $count_student. ' ราย';
                  }else{
                    $count_student = '0 ราย';
                  }
        
                  $count_incomplete = UsersLearningLog::where('course_id', $e->id)->where('status', 1)->groupBy('user_id')->get();
                  $count_incomplete = Count($count_incomplete);
                  if($count_incomplete){
                    $count_incomplete = $count_incomplete. ' ราย';
                  }else{
                    $count_incomplete = '0 ราย';
                  }

                  if($count_incomplete){
                    $count_complete = '0 ราย';
                  }else{
                    $count_complete = UsersLearningLog::where('course_id', $e->id)->where('status', 2)->groupBy('user_id')->get();
                    $count_complete = Count($count_complete);
                    if($count_complete){
                      $count_complete = $count_complete. ' ราย';
                    }else{
                      $count_complete = '0 ราย';
                    }
                  }

                  $count_view = CourseView::where('course_id', $e->id)->count();
                  if($count_view){
                    $count_view = $count_view. ' ครั้ง';
                  }else{
                    $count_view = '0 ครั้ง';
                  }

                  $ex_array[]=array(
                    'คอร์ส'=>$e->title_th,
                    'ผู้สอน'=>$e->course_speaker,
                    'จำนวนนักเรียน'=>$count_student,
                    'กำลังเรียน'=>$count_incomplete,
                    'เรียนจบแล้ว'=>$count_complete,
                    'จำนวนวิว'=>$count_view,
                  );
                }
            }

            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    $export = new TempExport($ex_array);

                    return FacadesExcel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

}
