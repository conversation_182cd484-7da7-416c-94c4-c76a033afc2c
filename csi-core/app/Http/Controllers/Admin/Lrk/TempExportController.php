<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;

use Maatwebsite\Excel\Concerns\FromArray;
use App\Export\TempExport;

use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class TempExportController implements FromArray
{

    public function export() {

      $ex_array = new Temp([

      ]);

    return Excel::download($ex_array, $request->filename.'.xlsx');
    }
}
