<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\Users;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\Core\CourseView;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;
use Maatwebsite\Lrk\Facades\Excel;


class ReportUserController extends Controller
{
    public $prefix = 'report_user';
    public $project_url = 'report_user';
    public $project_layout = 'admin.Lrk.report_user';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {

      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/'.$this->project_url);
      }

      if (isset($request->from)) {
        $from = Carbon::parse($request->from)->format('Y-m-d H:i:s');
        $from_date = $request->from;
      } else {
        $from = '';
        $from_date = '';
      }
      if (isset($request->to)) {
        $to = Carbon::parse($request->to)->addDays(1)->format('Y-m-d H:i:s');
        $to_date = $request->to;
      } else {
        $to = '';
        $to_date = '';
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );

      return view($this->project_layout.'.index', compact('obj','from_date','to_date'));
    }

    public function create()
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      if (isset($request->from) && $request->from != '0') {
        $start_date = Carbon::parse($request->from)->format('Y-m-d H:i:s');
      } else {
        $start_date = '';
      }
      if (isset($request->to) && $request->to != '0') {
        $end_date = Carbon::parse($request->to)->addDays(1)->format('Y-m-d H:i:s');
      } else {
        $end_date = '';
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_email'){
            array_push($where_search, ['user.email', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_e_id'){
            array_push($where_search, ['user.e_id', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column
      
      if ($start_date != '' && $end_date == ''){
        $query = DB::table('user')
        ->select('user.*')
        ->whereDate('user.created_at', '>=', $start_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user')
        ->whereDate('user.created_at', '>=', $start_date)
        ->where($where_search)
        ->count();
      }else if($start_date == '' && $end_date != ''){
        $query = DB::table('user')
        ->select('user.*')
        ->whereDate('user.created_at', '<=', $end_date)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user')
        ->whereDate('user.created_at', '<=', $end_date)
        ->where($where_search)
        ->count();
      }else if ($start_date == '' && $end_date == ''){
        $query = DB::table('user')
        ->select('user.*')
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user')
        ->where($where_search)
        ->count();
      }else if ($start_date != '' && $end_date != ''){
        $query = DB::table('user')
        ->select('user.*')
        ->whereBetween('user.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user')
        ->whereBetween('user.created_at', [$start_date, $end_date])
        ->where($where_search)
        ->count();
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $order_count = UsersOrderLog::where('user_id', $obj->id)->count();
        if($order_count){
          $list_count = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')->where('user_order_log.user_id', $obj->id)->count();
          if($list_count){
            $obj['count'] = $list_count.' รายการ';
          }else{
            $obj['count'] = '0 รายการ';
          }
        }else{
          $obj['count'] ='ไม่มีรายการ';
        }

        $view = CourseView::where('user_id', $obj->id)->count();
        if($view > 0){
          $obj['view'] = $view.' ครั้ง';
        }else{
          $obj['view'] = '0 ครั้ง';
        }

        $obj['details'] = ' 
        <div class="btn-group">
          <a href="/csisocietyadmin/report_activity/'.$obj->id.'" class="btn btn-details" style="background-color: blueviolet; color: aliceblue;">
            รายละเอียด
          </a>     
          <a href="/csisocietyadmin/report_result/'.$obj->id.'" class="btn btn-learn" style="background-color: forestgreen; color: aliceblue;">
            ผลการเรียน
          </a>      
          </div>';


        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function update(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='report_user') {
          $data = DB::table('user');
          $data->select('user.id', 'user.name', 'user.lastname', 'user.email');
          $data->orderBy('user.created_at', 'desc');

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user.created_at', [$from, $to]);
          }
          
          if(isset($request->getType) && $request->getType=='page' ){
            $response = new Dummy();
            $response['all_data'] = $data->count();
            return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
          }else{
            if(isset($request->page) && isset($request->limit)){
                $data->limit($request->limit);
                $data->offset(($request->page-1)*$request->limit);
            }
            $data = $data->get();
          }

          $ex_array[]=array(
            'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'จำนวนคอร์ส'=>'จำนวนคอร์ส',
            'จำนวนวิว'=>'จำนวนวิว',
          );

          foreach ($data as $key=>$e) {

            $order_count = UsersOrderLog::where('user_id', $e->id)->count();
            if($order_count){
              $list_count = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')->where('user_order_log.user_id', $e->id)->count();
              if($list_count){
                $count = $list_count.' รายการ';
              }else{
                $count = '0 รายการ';
              }
            }else{
              $count ='ไม่มีรายการ';
            }
  
            $view = CourseView::where('user_id', $e->id)->count();
            if($view > 0){
              $view_count = $view.' ครั้ง';
            }else{
              $view_count = '0 ครั้ง';
            }

            $ex_array[]=array(
            'ชื่อ - นามสกุล'=>$e->name.' '.$e->lastname,
            'อีเมล์'=>$e->email,
            'จำนวนคอร์ส'=>$count,
            'จำนวนวิว'=>$view_count,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);
                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

}
