<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\UsersOrderLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\CourseGroup;
use App\Models\Core\EmailDynamic;
use App\Models\Core\NotiAuto;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\NotiGlobalLog;
use App\Models\Core\Subscription;
use App\Models\Core\Users;
use App\Models\Core\UsersOrderList;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class OrderReportController extends Controller
{
    public $prefix = 'order_report';
    public $project_url = 'order_report';
    public $project_layout = 'admin.Lrk.order_report';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $order_count = UsersOrderLog::where('status', 2)->where('is_free', 2)->count();

      $price = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
      ->where('user_order_log.status', 2)->where('is_free', 2)
      ->select('user_order_list.price')
      ->get();
      $price_total = $price->sum('price');

      $order_discount = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
      ->where('user_order_log.status', 2)->where('is_free', 2)
      ->where('user_order_list.discount_value', '!=', null)
      ->select('user_order_list.discount_value', 'user_order_log.web_price')
      ->get();

      $discount = $order_discount->sum('discount_value');
      $discount_etc = $order_discount->sum('web_price');

      $discount_all = $discount+$discount_etc;

      
      $total = number_format($price_total-$discount_all,2);
      $price_total = number_format($price_total,2);
      $discount_all = number_format($discount+$discount_etc,2);

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout,
        'order_count' => $order_count,
        'price_total' => $price_total,
        'discount_all' => $discount_all,
        'total' => $total
      );
      AppHelper::instance()->consoleLog($order_count);
      AppHelper::instance()->consoleLog($price_total);
      AppHelper::instance()->consoleLog($discount_all);
      AppHelper::instance()->consoleLog($total);
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = UsersOrderLog::find($id);

      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      // Handle custom filter parameters
      $course_name = $request->get('course_name');
      $date_start = $request->get('date_start');
      $date_end = $request->get('date_end');
      $user_name = $request->get('user_name');
      $user_email_filter = $request->get('user_email_filter');

      // Debug: Log all request parameters
      Log::info('Order Report Request Parameters:', [
        'course_name' => $course_name,
        'date_start' => $date_start,
        'date_end' => $date_end,
        'user_name' => $user_name,
        'user_email_filter' => $user_email_filter,
        'all_params' => $request->all()
      ]);

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_order_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_order_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_order_log.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='school'){
            array_push($where_search, ['user.school', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='email'){
            array_push($where_search, ['user.email', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='mobile'){
            array_push($where_search, ['user.mobile', 'LIKE','%'.$value['search']['value'].'%']);
            
          }else if($value['data']=='payment_type'){
            if($value['search']['value'] == '21'){
              $valuePaymentSearch = 2;
              array_push($where_search, ['user_order_log.payment_type', $valuePaymentSearch]);
              array_push($where_search, ['user_order_log.credit_payment_type', 'full']);
            }else if($value['search']['value'] == '22'){
              $valuePaymentSearch = 2;
              array_push($where_search, ['user_order_log.payment_type', $valuePaymentSearch]);
              array_push($where_search, ['user_order_log.credit_payment_type', 'installment']);
            }else{
              array_push($where_search, ['user_order_log.payment_type', $value['search']['value']]);
            }
          }
          
          else{
            array_push($where_search, ['user_order_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_order_log')
      ->Join('user', 'user.id', 'user_order_log.user_id')
      ->select('user_order_log.*', 'user.name as u_name', 'user.lastname as u_lastname',
                'user.email as u_email', 'user.mobile as u_mobile', 'user.school')
      ->where('user_order_log.is_free', 2)
      ->where('user_order_log.status', 2)
      ->where($where_search);

      // Apply custom filters
      if ($user_name) {
        $query->where(function($q) use ($user_name) {
          $q->where('user.name', 'LIKE', '%'.$user_name.'%')
            ->orWhere('user.lastname', 'LIKE', '%'.$user_name.'%');
        });
      }

      if ($user_email_filter) {
        $query->where('user.email', 'LIKE', '%'.$user_email_filter.'%');
      }

      if ($date_start && $date_end) {
        $query->whereBetween('user_order_log.created_at', [$date_start.' 00:00:00', $date_end.' 23:59:59']);
      } elseif ($date_start) {
        $query->where('user_order_log.created_at', '>=', $date_start.' 00:00:00');
      } elseif ($date_end) {
        $query->where('user_order_log.created_at', '<=', $date_end.' 23:59:59');
      }

      // Apply course_name filter
      if ($course_name) {
        $query->whereExists(function($subquery) use ($course_name) {
          $subquery->select(DB::raw(1))
                   ->from('user_order_list')
                   ->whereColumn('user_order_list.order_id', 'user_order_log.id')
                   ->where(function($q) use ($course_name) {
                     $q->whereExists(function($courseQuery) use ($course_name) {
                       $courseQuery->select(DB::raw(1))
                                  ->from('course')
                                  ->whereColumn('course.id', 'user_order_list.course_id')
                                  ->where('course.title_th', 'LIKE', '%'.$course_name.'%');
                     })
                     ->orWhereExists(function($groupQuery) use ($course_name) {
                       $groupQuery->select(DB::raw(1))
                                 ->from('course_group')
                                 ->whereColumn('course_group.id', 'user_order_list.group_id')
                                 ->where('course_group.title', 'LIKE', '%'.$course_name.'%');
                     })
                     ->orWhereExists(function($subQuery) use ($course_name) {
                       $subQuery->select(DB::raw(1))
                               ->from('subscription')
                               ->whereColumn('subscription.id', 'user_order_list.subscription_id')
                               ->where('subscription.title', 'LIKE', '%'.$course_name.'%');
                     });
                   });
        });
      }

      // Debug: Log the query to see what's being executed
      $debugQuery = $query->toSql();
      $debugBindings = $query->getBindings();
      Log::info('Order Report Query:', ['sql' => $debugQuery, 'bindings' => $debugBindings]);
      
      $query = $query->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_query = DB::table('user_order_log')
      ->Join('user', 'user.id', 'user_order_log.user_id')
      ->where('user_order_log.is_free', 2)
      ->where('user_order_log.status', 2)
      ->where($where_search);

      // Apply same custom filters for count
      if ($user_name) {
        $count_query->where(function($q) use ($user_name) {
          $q->where('user.name', 'LIKE', '%'.$user_name.'%')
            ->orWhere('user.lastname', 'LIKE', '%'.$user_name.'%');
        });
      }

      if ($user_email_filter) {
        $count_query->where('user.email', 'LIKE', '%'.$user_email_filter.'%');
      }

      if ($date_start && $date_end) {
        $count_query->whereBetween('user_order_log.created_at', [$date_start.' 00:00:00', $date_end.' 23:59:59']);
      } elseif ($date_start) {
        $count_query->where('user_order_log.created_at', '>=', $date_start.' 00:00:00');
      } elseif ($date_end) {
        $count_query->where('user_order_log.created_at', '<=', $date_end.' 23:59:59');
      }

      // Apply course_name filter for count
      if ($course_name) {
        $count_query->whereExists(function($subquery) use ($course_name) {
          $subquery->select(DB::raw(1))
                   ->from('user_order_list')
                   ->whereColumn('user_order_list.order_id', 'user_order_log.id')
                   ->where(function($q) use ($course_name) {
                     $q->whereExists(function($courseQuery) use ($course_name) {
                       $courseQuery->select(DB::raw(1))
                                  ->from('course')
                                  ->whereColumn('course.id', 'user_order_list.course_id')
                                  ->where('course.title_th', 'LIKE', '%'.$course_name.'%');
                     })
                     ->orWhereExists(function($groupQuery) use ($course_name) {
                       $groupQuery->select(DB::raw(1))
                                 ->from('course_group')
                                 ->whereColumn('course_group.id', 'user_order_list.group_id')
                                 ->where('course_group.title', 'LIKE', '%'.$course_name.'%');
                     })
                     ->orWhereExists(function($subQuery) use ($course_name) {
                       $subQuery->select(DB::raw(1))
                               ->from('subscription')
                               ->whereColumn('subscription.id', 'user_order_list.subscription_id')
                               ->where('subscription.title', 'LIKE', '%'.$course_name.'%');
                     });
                   });
        });
      }

      $count_total = $count_query->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        if($value){

        }

        if($obj['total_price'] == 0){
          $obj['total_price'] = 'ฟรี';
        }

        if($obj['payment_type'] == 2){
          if($obj['credit_payment_type'] == 'full'){
            $obj['payment_type'] = '21';
          }else if($obj['credit_payment_type'] == 'installment'){
            $obj['payment_type'] = '22';
          }else{
            $obj['payment_type'] = '2';
          }
        }

        $code_all = UsersOrderList::where('order_id', $value->id)
        ->select('discount_code', 'course_id', 'group_id', 'subscription_id')
        ->get();
        $code_name = '';
        $course_name = '';

        foreach($code_all as $key_code=>$val_code){
          if($val_code->course_id != null){
            $course = Course::where('id',$val_code->course_id)->first();
            if($course){
              if($course_name != ''){
                $course_name .= ',<br> ';
              }
              $course_name .= $course->title_th;
            }
          }

          if($val_code->group_id != null){
            $group = CourseGroup::where('id',$val_code->group_id)->first();
            if($group){
              if($course_name != ''){
                $course_name .= ',<br> ';
              }
              $course_name .= $group->title;
            }
          }

          if($val_code->subscription_id != null){
            $sub = Subscription::where('id',$val_code->subscription_id)->first();
            if($sub){
              if($course_name != ''){
                $course_name .= ',<br> ';
              }
              $course_name .= $sub->title;
            }
          }

          if($code_name != ''){
            $code_name .= ', ';
          }
          $code_name .= $val_code->discount_code;
        }

        $obj['course_name'] = $course_name;
        $obj['code'] = $code_name;

        if($value->web_code != null){
          $obj['web_code'] = $value->web_code;
        }else{
          $obj['web_code'] = 'ไม่มีส่วนลด';
        }


        $obj['full_name'] = $obj['u_name'].' '.$obj['u_lastname'];

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='order_report') {
          
          $data = DB::table('user_order_log');
          $data->join('user', 'user.id', 'user_order_log.user_id');
          $data->select('user_order_log.*', 'user.name as u_name', 'user.lastname as u_lastname', 'user.email', 
                        'user.mobile', 'user.school');
          $data->where('user_order_log.is_free', 2);
          $data->where('user_order_log.status', 2);
          // $data->groupBy('user_order_log.order_no');
          $data->get();

          if (isset($request->payment)) {
            if ($request->payment != '9') {
              $data->where('user_order_log.payment_type', '=', $request->payment);
            } else {

            }
          }
          
          if (isset($request->order_no)) {
            $data->where('user_order_log.order_no', 'like', '%'.$request->order_no.'%');
          }
          
          if (isset($request->user_email)) {
            $data->where('user.email', 'like', '%'.$request->user_email.'%');
          }

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_order_log.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'หมายเลขสั่งซื้อ'=>'หมายเลขสั่งซื้อ',
            'รายการสั่งซื้อ'=>'รายการสั่งซื้อ',
            'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'เบอร์โทรศัพท์'=>'เบอร์โทรศัพท์',
            'โรงเรียน'=>'โรงเรียน',
            
            'ชื่อ - นามสกุล ผู้ซื้อ'=>'ชื่อ - นามสกุล ผู้ซื้อ',
            'อีเมล์ ผู้ซื้อ'=>'อีเมล์ ผู้ซื้อ',
            'เบอร์โทรศัพท์ ผู้ซื้อ'=>'เบอร์โทรศัพท์ ผู้ซื้อ',
            'ที่อยู่ ผู้ซื้อ'=>'ที่อยู่ ผู้ซื้อ',
            'ตำบล ผู้ซื้อ'=>'ตำบล ผู้ซื้อ',
            'อำเภอ ผู้ซื้อ'=>'อำเภอ ผู้ซื้อ',
            'จังหวัด ผู้ซื้อ'=>'จังหวัด ผู้ซื้อ',
            'รหัสไปรษณีย์ ผู้ซื้อ'=>'รหัสไปรษณีย์ ผู้ซื้อ',

            'ประเภทการจ่ายเงิน'=>'ประเภทการจ่ายเงิน',
            'จำนวนเงิน'=>'จำนวนเงิน',
            'คูปองส่วนลดอื่นๆ'=>'คูปองส่วนลดอื่นๆ',
            'ราคาส่วนลด'=>'ราคาส่วนลด',
            'ราคาสุทธิ'=>'ราคาสุทธิ',
            'สถานะการจ่ายเงิน'=>'สถานะการจ่ายเงิน',
            'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
            'รายละเอียดข้อมูล'=>'รายละเอียดข้อมูล',
          );

          foreach ($data as $key=>$e) {

            if($e->payment_type == 1){
              $e->payment_type = 'QR Code';

            }else if($e->payment_type == 2){  //payment type credit (full, installment)
              // $e->payment_type = 'Credit';
              if($e->credit_payment_type == 'installment'){
                $e->payment_type = 'Credit (Installment)';
              }else{
                $e->payment_type = 'Credit (Full)';
              }

            }else if($e->payment_type == 3){
              $e->payment_type = 'Cash';
            }else if($e->payment_type == 4){
              $e->payment_type = 'Transfer';
            }else{
              $e->payment_type = 'transfering';
            }

            if($e->receipt_type == 1){
              $e->receipt_type = 'ธรรมดา';
            }else{
              $e->receipt_type = 'นิติบุคคล';
            }

            if($e->receipt == 1){
              $e->receipt = 'ต้องการ';
            }else{
              $e->receipt = 'ไม่ต้องการ';
            }

            if($e->status == 1){
              $e->status = 'รอการชำระเงิน';
            }else if($e->status == 2){
              $e->status = 'สั่งซื้อสำเร็จ';
            }else if($e->status == 3){
              $e->status = 'ยกเลิกคำสั่งซื้อ';
            }else{
              $e->status = 'รอการตรวจสอบ';
            }

            $fullprice = UsersOrderList::where('order_id', $e->id)->sum('price');
            if($fullprice == 0){
              $fullprice = strval($fullprice);
            }else{
              $fullprice = $fullprice;
            }

            $dis_price = UsersOrderList::where('order_id', $e->id)->sum('discount_value');
            if($dis_price == 0 && $e->web_code == null){
              $disprice = strval($dis_price);
            }else{
              $disprice = $dis_price + $e->web_price;
            }

            $total = UsersOrderList::where('order_id', $e->id)->sum('total_price');
            if($total == 0 && $e->web_code == null){
              $total = strval($total);
            }else{
              $total = $total - $e->web_price;
            }

            $code_all = UsersOrderList::where('order_id', $e->id)
            ->select('discount_code', 'course_id', 'group_id', 'subscription_id')
            ->get();
            $code_name = '';
            $course_name = '';

            foreach($code_all as $key_code=>$val_code){
              if($val_code->course_id != null){
                $course = Course::where('id',$val_code->course_id)->first();
                if($course){
                  if($course_name != ''){
                    $course_name .= ',<br> ';
                  }
                  $course_name .= $course->title_th;
                }
              }

              if($val_code->group_id != null){
                $group = CourseGroup::where('id',$val_code->group_id)->first();
                if($group){
                  if($course_name != ''){
                    $course_name .= ',<br> ';
                  }
                  $course_name .= $group->title;
                }
              }

              if($val_code->subscription_id != null){
                $sub = Subscription::where('id',$val_code->subscription_id)->first();
                if($sub){
                  if($course_name != ''){
                    $course_name .= ',<br> ';
                  }
                  $course_name .= $sub->title;
                }
              }

              if($code_name != ''){
                $code_name .= ', ';
              }
              $code_name .= $val_code->discount_code;
            }

            $obj['course_name'] = $course_name;
            $obj['code'] = $code_name;

            if($e->web_code != null){
              $web_code = $e->web_code;
            }else{
              $web_code = 'ไม่มีส่วนลด';
            }

            $ex_array[]=array(
            'หมายเลขสั่งซื้อ'=>$e->order_no,
            'รายการสั่งซื้อ'=>$course_name,
            'ชื่อ - นามสกุล'=>$e->u_name.' '.$e->u_lastname,
            'อีเมล์'=>$e->email,
            'เบอร์โทรศัพท์'=>$e->mobile,
            'โรงเรียน'=>$e->school,
            
            'ชื่อ - นามสกุล ผู้ซื้อ'=>$e->u_name.' '.$e->cus_lastname,
            'อีเมล์ ผู้ซื้อ'=>$e->cus_email,
            'เบอร์โทรศัพท์ ผู้ซื้อ'=>$e->cus_mobile,
            'ที่อยู่ ผู้ซื้อ'=>$e->doc_address,
            'ตำบล ผู้ซื้อ'=>$e->doc_subdistrict,
            'อำเภอ ผู้ซื้อ'=>$e->doc_district,
            'จังหวัด ผู้ซื้อ'=>$e->doc_province,
            'รหัสไปรษณีย์ ผู้ซื้อ'=>$e->doc_postcode,
            
            'ประเภทการจ่ายเงิน'=>$e->payment_type,
            'จำนวนเงิน'=>$fullprice,
            'คูปองส่วนลดอื่นๆ'=>$web_code,
            'ราคาส่วนลด'=>$disprice,
            'ราคาสุทธิ'=>$total,
            'สถานะการจ่ายเงิน'=>$e->status,
            'วันที่เก็บข้อมูล'=>$e->created_at,
            'รายละเอียดข้อมูล'=>$course_name.'  '.$e->u_name.' '.$e->u_lastname.'  '.$e->mobile.'  '.$e->doc_address.'  '.$e->doc_subdistrict.'  '.$e->doc_district.'  '.$e->doc_province.'  '.$e->doc_postcode,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

    public function school_export (Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '44'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='school_export') {
          
          $data = DB::table('user_order_log');
          $data->join('user', 'user.id', 'user_order_log.user_id');
          $data->select('user_order_log.user_id', 'user.school', DB::raw('COUNT(user.school) as max_schooling'));
          $data->groupBy('user.school');
          $data->orderBy('max_schooling', 'desc');
          $data->limit(20);
          $data = $data->get();

          $ex_array[]=array(
            'โรงเรียน'=>'โรงเรียน',
            'จำนวนการออเดอร์'=>'จำนวนการออเดอร์',
          );

          foreach ($data as $key=>$e) {

            $ex_array[]=array(
            'โรงเรียน'=>$e->school,
            'จำนวนการออเดอร์'=>$e->max_schooling,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

    public function course_search(Request $request)
    {
        if(Auth::user()->level != '99' && Auth::user()->level != '44'){
            return response()->json([]);
        }

        $query = $request->get('query');
        $results = [];

        if(strlen($query) > 2) {
            // Search in courses
            $courses = Course::where('title_th', 'LIKE', '%'.$query.'%')
                           ->select('title_th as name', DB::raw("'course' as type"))
                           ->limit(10)
                           ->get();

            // Search in course groups
            $groups = CourseGroup::where('title', 'LIKE', '%'.$query.'%')
                                ->select('title as name', DB::raw("'group' as type"))
                                ->limit(10)
                                ->get();

            // Search in subscriptions
            $subscriptions = Subscription::where('title', 'LIKE', '%'.$query.'%')
                                       ->select('title as name', DB::raw("'subscription' as type"))
                                       ->limit(10)
                                       ->get();

            $results = collect()
                      ->merge($courses)
                      ->merge($groups)
                      ->merge($subscriptions)
                      ->take(20)
                      ->toArray();
        }

        return response()->json($results);
    }

    public function getData(Request $request)
    {
        if(Auth::user()->level != '99' && Auth::user()->level != '44'){
            return response()->json(['data' => [], 'total' => 0]);
        }

        $page = $request->get('page', 1);
        $limit = $request->get('limit', 25);
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        $search = $request->get('search', '');
        
        // Get filter parameters
        $course_name = $request->get('course_name');
        $date_start = $request->get('date_start');
        $date_end = $request->get('date_end');
        $user_name = $request->get('user_name');
        $user_email_filter = $request->get('user_email_filter');

        // Debug log
        Log::info('Dynamic Table Request:', [
            'filters' => compact('course_name', 'date_start', 'date_end', 'user_name', 'user_email_filter'),
            'params' => compact('page', 'limit', 'sort', 'direction', 'search')
        ]);

        // Base query
        $query = DB::table('user_order_log')
            ->join('user', 'user.id', '=', 'user_order_log.user_id')
            ->select(
                'user_order_log.*',
                'user.name as u_name',
                'user.lastname as u_lastname',
                'user.email as u_email',
                'user.mobile as u_mobile',
                'user.school'
            )
            ->where('user_order_log.is_free', 2)
            ->where('user_order_log.status', 2);

        // Apply filters
        if ($user_name) {
            $query->where(function($q) use ($user_name) {
                $q->where('user.name', 'LIKE', '%'.$user_name.'%')
                  ->orWhere('user.lastname', 'LIKE', '%'.$user_name.'%');
            });
        }

        if ($user_email_filter) {
            $query->where('user.email', 'LIKE', '%'.$user_email_filter.'%');
        }

        if ($date_start && $date_end) {
            $query->whereBetween('user_order_log.created_at', [$date_start.' 00:00:00', $date_end.' 23:59:59']);
        } elseif ($date_start) {
            $query->where('user_order_log.created_at', '>=', $date_start.' 00:00:00');
        } elseif ($date_end) {
            $query->where('user_order_log.created_at', '<=', $date_end.' 23:59:59');
        }

        // Apply course_name filter
        if ($course_name) {
            $query->whereExists(function($subquery) use ($course_name) {
                $subquery->select(DB::raw(1))
                         ->from('user_order_list')
                         ->whereColumn('user_order_list.order_id', 'user_order_log.id')
                         ->where(function($q) use ($course_name) {
                             $q->whereExists(function($courseQuery) use ($course_name) {
                                 $courseQuery->select(DB::raw(1))
                                            ->from('course')
                                            ->whereColumn('course.id', 'user_order_list.course_id')
                                            ->where('course.title_th', 'LIKE', '%'.$course_name.'%');
                             })
                             ->orWhereExists(function($groupQuery) use ($course_name) {
                                 $groupQuery->select(DB::raw(1))
                                           ->from('course_group')
                                           ->whereColumn('course_group.id', 'user_order_list.group_id')
                                           ->where('course_group.title', 'LIKE', '%'.$course_name.'%');
                             })
                             ->orWhereExists(function($subQuery) use ($course_name) {
                                 $subQuery->select(DB::raw(1))
                                         ->from('subscription')
                                         ->whereColumn('subscription.id', 'user_order_list.subscription_id')
                                         ->where('subscription.title', 'LIKE', '%'.$course_name.'%');
                             });
                         });
            });
        }

        // Apply search
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('user_order_log.order_no', 'LIKE', '%'.$search.'%')
                  ->orWhere('user.name', 'LIKE', '%'.$search.'%')
                  ->orWhere('user.lastname', 'LIKE', '%'.$search.'%')
                  ->orWhere('user.email', 'LIKE', '%'.$search.'%')
                  ->orWhere('user.mobile', 'LIKE', '%'.$search.'%')
                  ->orWhere('user.school', 'LIKE', '%'.$search.'%');
            });
        }

        // Get total count
        $total = $query->count();

        // Apply sorting
        $allowedSorts = ['order_no', 'full_name', 'u_email', 'u_mobile', 'school', 'total_price', 'payment_type', 'status', 'created_at'];
        if (in_array($sort, $allowedSorts)) {
            if ($sort === 'full_name') {
                $query->orderBy('user.name', $direction);
            } else {
                $query->orderBy($sort === 'u_email' ? 'user.email' : 'user_order_log.'.$sort, $direction);
            }
        } else {
            $query->orderBy('user_order_log.created_at', 'desc');
        }

        // Apply pagination
        $offset = ($page - 1) * $limit;
        $results = $query->offset($offset)->limit($limit)->get();

        // Process results
        $data = [];
        foreach ($results as $item) {
            // Get course names
            $course_name = '';
            $code_all = UsersOrderList::where('order_id', $item->id)
                ->select('discount_code', 'course_id', 'group_id', 'subscription_id')
                ->get();

            foreach($code_all as $val_code) {
                if($val_code->course_id != null) {
                    $course = Course::where('id',$val_code->course_id)->first();
                    if($course) {
                        if($course_name != '') $course_name .= ', ';
                        $course_name .= $course->title_th;
                    }
                }
                if($val_code->group_id != null) {
                    $group = CourseGroup::where('id',$val_code->group_id)->first();
                    if($group) {
                        if($course_name != '') $course_name .= ', ';
                        $course_name .= $group->title;
                    }
                }
                if($val_code->subscription_id != null) {
                    $sub = Subscription::where('id',$val_code->subscription_id)->first();
                    if($sub) {
                        if($course_name != '') $course_name .= ', ';
                        $course_name .= $sub->title;
                    }
                }
            }

            // Format payment type for display
            $payment_type = $item->payment_type;
            if($payment_type == 2) {
                if($item->credit_payment_type == 'full') {
                    $payment_type = '21';
                } else if($item->credit_payment_type == 'installment') {
                    $payment_type = '22';
                }
            }

            $data[] = [
                'id' => $item->id,
                'order_no' => $item->order_no,
                'course_name' => $course_name,
                'full_name' => $item->u_name . ' ' . $item->u_lastname,
                'u_email' => $item->u_email,
                'u_mobile' => $item->u_mobile,
                'school' => $item->school,
                'web_code' => $item->web_code ?: 'ไม่มีส่วนลด',
                'total_price' => $item->total_price == 0 ? 'ฟรี' : number_format($item->total_price, 2),
                'payment_type' => $payment_type,
                'status' => $item->status,
                'created_at' => $item->created_at
            ];
        }

        return response()->json([
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    public function exportSlips(Request $request)
    {
        if(Auth::user()->level != '99' && Auth::user()->level != '44'){
            return redirect(route('admin').'/')->with('errors', 'Permission Deny');
        }

        // Get filter parameters
        $course_name = $request->get('course_name');
        $date_start = $request->get('date_start');
        $date_end = $request->get('date_end');
        $user_name = $request->get('user_name');
        $user_email_filter = $request->get('user_email_filter');

        // Build query with same filters as getData method
        $query = DB::table('user_order_log')
            ->join('user', 'user.id', '=', 'user_order_log.user_id')
            ->select(
                'user_order_log.*',
                'user.name as u_name',
                'user.lastname as u_lastname',
                'user.email as u_email'
            )
            ->where('user_order_log.is_free', 2)
            ->where('user_order_log.status', 2)
            ->whereNotNull('user_order_log.image_slip')
            ->where('user_order_log.image_slip', '!=', '');

        // Apply same filters as getData method
        if ($user_name) {
            $query->where(function($q) use ($user_name) {
                $q->where('user.name', 'LIKE', '%'.$user_name.'%')
                  ->orWhere('user.lastname', 'LIKE', '%'.$user_name.'%');
            });
        }

        if ($user_email_filter) {
            $query->where('user.email', 'LIKE', '%'.$user_email_filter.'%');
        }

        if ($date_start && $date_end) {
            $query->whereBetween('user_order_log.created_at', [$date_start.' 00:00:00', $date_end.' 23:59:59']);
        } elseif ($date_start) {
            $query->where('user_order_log.created_at', '>=', $date_start.' 00:00:00');
        } elseif ($date_end) {
            $query->where('user_order_log.created_at', '<=', $date_end.' 23:59:59');
        }

        // Apply course_name filter
        if ($course_name) {
            $query->whereExists(function($subquery) use ($course_name) {
                $subquery->select(DB::raw(1))
                         ->from('user_order_list')
                         ->whereColumn('user_order_list.order_id', 'user_order_log.id')
                         ->where(function($q) use ($course_name) {
                             $q->whereExists(function($courseQuery) use ($course_name) {
                                 $courseQuery->select(DB::raw(1))
                                            ->from('course')
                                            ->whereColumn('course.id', 'user_order_list.course_id')
                                            ->where('course.title_th', 'LIKE', '%'.$course_name.'%');
                             })
                             ->orWhereExists(function($groupQuery) use ($course_name) {
                                 $groupQuery->select(DB::raw(1))
                                           ->from('course_group')
                                           ->whereColumn('course_group.id', 'user_order_list.group_id')
                                           ->where('course_group.title', 'LIKE', '%'.$course_name.'%');
                             })
                             ->orWhereExists(function($subQuery) use ($course_name) {
                                 $subQuery->select(DB::raw(1))
                                         ->from('subscription')
                                         ->whereColumn('subscription.id', 'user_order_list.subscription_id')
                                         ->where('subscription.title', 'LIKE', '%'.$course_name.'%');
                             });
                         });
            });
        }

        $results = $query->get();

        // Log export request
        Log::info('Export Slips Request', [
            'user_id' => Auth::user()->id,
            'filters' => compact('course_name', 'date_start', 'date_end', 'user_name', 'user_email_filter'),
            'total_orders_found' => $results->count()
        ]);

        if ($results->isEmpty()) {
            return redirect()->back()->with('error', 'ไม่พบสลิปที่ต้องการ Export');
        }

        // Create ZIP file
        try {
            $zip = new \ZipArchive();
            $zipFileName = 'payment_slips_' . date('Y-m-d_H-i-s') . '.zip';
            $zipPath = storage_path('app/temp/' . $zipFileName);
            
            // Create temp directory if not exists
            $tempDir = storage_path('app/temp');
            if (!file_exists($tempDir)) {
                if (!mkdir($tempDir, 0755, true)) {
                    return redirect()->back()->with('error', 'ไม่สามารถสร้างโฟลเดอร์ชั่วคราวได้');
                }
            }

            if ($zip->open($zipPath, \ZipArchive::CREATE) !== TRUE) {
                return redirect()->back()->with('error', 'ไม่สามารถสร้างไฟล์ ZIP ได้');
            }

            $addedFiles = 0;
            
            foreach ($results as $order) {
                if (!empty($order->image_slip)) {
                    // Handle different image_slip formats
                    $imagePath = $order->image_slip;
                    
                    // If it's a full URL, extract the path part
                    if (strpos($imagePath, 'http') === 0) {
                        $parsedUrl = parse_url($imagePath);
                        $imagePath = ltrim($parsedUrl['path'], '/');
                    }
                    
                    // Try different possible paths
                    $possiblePaths = [
                        public_path($imagePath),                    // Direct path
                        public_path('upload/' . $imagePath),       // With upload prefix
                        public_path('upload/' . basename($imagePath)), // Just filename
                    ];
                    
                    $slipPath = null;
                    foreach ($possiblePaths as $path) {
                        if (file_exists($path) && is_readable($path)) {
                            $slipPath = $path;
                            break;
                        }
                    }
                    
                    if ($slipPath) {
                        // Create meaningful filename
                        $fileExtension = pathinfo($slipPath, PATHINFO_EXTENSION);
                        $fileName = 'Order_' . $order->order_no . '_' . $order->u_name . '_' . $order->u_lastname . '.' . $fileExtension;
                        // Clean filename for ZIP compatibility
                        $fileName = preg_replace('/[^a-zA-Z0-9._-]/', '_', $fileName);
                        
                        if ($zip->addFile($slipPath, $fileName)) {
                            $addedFiles++;
                        }
                    } else {
                        // Log missing file for debugging
                        Log::warning('Slip image not found', [
                            'order_id' => $order->id,
                            'order_no' => $order->order_no,
                            'image_slip' => $order->image_slip,
                            'tried_paths' => $possiblePaths
                        ]);
                    }
                }
            }

            $zip->close();

        } catch (\Exception $e) {
            Log::error('ZIP Creation Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการสร้างไฟล์ ZIP: ' . $e->getMessage());
        }

        if ($addedFiles == 0) {
            if (file_exists($zipPath)) {
                unlink($zipPath);
            }
            return redirect()->back()->with('error', 'ไม่พบไฟล์สลิปที่สามารถ Export ได้');
        }

        // Log export activity
        Log::info('Slip Export', [
            'user_id' => Auth::user()->id,
            'filters' => compact('course_name', 'date_start', 'date_end', 'user_name', 'user_email_filter'),
            'exported_files' => $addedFiles,
            'total_orders' => $results->count(),
            'zip_path' => $zipPath,
            'zip_exists' => file_exists($zipPath),
            'zip_size' => file_exists($zipPath) ? filesize($zipPath) : 0
        ]);

        // Check if file exists before download
        if (!file_exists($zipPath)) {
            Log::error('ZIP file not found: ' . $zipPath);
            return redirect()->back()->with('error', 'ไม่พบไฟล์ ZIP ที่สร้าง');
        }

        if (filesize($zipPath) == 0) {
            Log::error('ZIP file is empty: ' . $zipPath);
            if (file_exists($zipPath)) {
                unlink($zipPath);
            }
            return redirect()->back()->with('error', 'ไฟล์ ZIP ว่างเปล่า');
        }

        Log::info('Starting download for: ' . $zipPath . ' (Size: ' . filesize($zipPath) . ' bytes)');

        // Return file download and delete after download
        try {
            return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            // Manual cleanup if auto-delete fails
            if (file_exists($zipPath)) {
                unlink($zipPath);
            }
            Log::error('Slip Export Download Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการดาวน์โหลดไฟล์: ' . $e->getMessage());
        }
    }

}
