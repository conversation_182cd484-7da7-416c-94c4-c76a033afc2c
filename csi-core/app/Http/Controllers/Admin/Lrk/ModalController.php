<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Sample\Modal;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class ModalController extends Controller
{
    public $prefix = 'modal';
    public $project_url = 'Sample/modal';
    public $project_layout = 'admin.Sample.modal';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {

        $model = Modal::find($request->id);

        Modal::where('position','>',$model->position)
          ->update(['position' => DB::raw('position - 1')]);

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = Modal::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();
        // $model->id;

        return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {
      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='type_name'){
            array_push($where_search, ['types.name', 'LIKE','%'.$value['search']['value'].'%']);
          }elseif($value['data']=='status'){
            array_push($where_search, ['modal.status', $value['search']['value']]);
          }else{
            array_push($where_search, ['modal.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('modal')
      ->leftjoin('types', 'types.id', '=', 'modal.type_id')
      ->select('modal.*', 'types.name as type_name')
      ->where('temps_id',$request->temps_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('modal')
      ->leftjoin('types', 'types.id', '=', 'modal.type_id')
      ->where('temps_id',$request->temps_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.route('admin').'/Unif/product_log/delete\',\'datatable_gal\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        $obj['text_area'] = AppHelper::instance()->limitCharUTF8($obj['text_area'],30);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new Modal; 

      $model->temps_id = $request->temps_id;

      if ($request->hasFile('gallery_modal_image')) {
          $model->image = AppHelper::instance()->saveImage($request->file('gallery_modal_image'),'product');
        }

      //file 
      if ($request->hasFile('gallery_modal_file')) {
        $model->file = AppHelper::instance()->saveFile($request->file('gallery_modal_file'));
      }

      $model->type_id = isset($request->gallery_modal_type) ? $request->gallery_modal_type : '';
      $model->text = isset($request->gallery_modal_text) ? AppHelper::instance()->cleanInput($request->gallery_modal_text) : '';
      $model->html = isset($request->gallery_modal_html) ? AppHelper::instance()->convertDomHTML($request->gallery_modal_html) : '';
      $model->tags = is_array($request->gallery_modal_tags) ? implode(",",$request->gallery_modal_tags) : '';
      $model->text_area = isset($request->gallery_modal_text_area) ? AppHelper::instance()->cleanInput($request->gallery_modal_text_area) : '';
      $model->date = isset($request->gallery_modal_date) ? Carbon::createFromFormat('d-m-Y', $request->gallery_modal_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
      $model->datetime = isset($request->gallery_modal_datetime) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_datetime)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');


      $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';

      $count = Modal::where('temps_id',$request->temps_id)
      ->count();

      $model->position = $count + 1;
      $model->save();
      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = Modal::find($request->id);

      if($model){

        if ($request->hasFile('gallery_modal_image')) {
            if($model->image!=null&&$model->image!=''){
              AppHelper::instance()->removeImage($model->image);
            }
            $model->image = AppHelper::instance()->saveImage($request->file('gallery_modal_image'),'product');
        }else{
          if(empty($request->source_gallery_modal_image)){
            if($model->image!=null&&$model->image!=''){
              AppHelper::instance()->removeImage($model->image);
            }
            $model->image = null;
          }
        }
        // file
        if ($request->hasFile('gallery_modal_file')) {
          if($model->file!=null&&$model->file!=''){
            AppHelper::instance()->removeFile($model->file);
          }
          $model->file = AppHelper::instance()->saveFile($request->file('gallery_modal_file'));
        }else{
          if(empty($request->source_gallery_modal_file)){
            if($model->file!=null&&$model->file!=''){
              AppHelper::instance()->removeFile($model->file);
            }
            $model->file = null;
          }
        }
  
        $model->type_id = isset($request->gallery_modal_type) ? $request->gallery_modal_type : '';
        $model->text = isset($request->gallery_modal_text) ? AppHelper::instance()->cleanInput($request->gallery_modal_text) : '';
        $model->html = isset($request->gallery_modal_html) ? AppHelper::instance()->convertDomHTML($request->gallery_modal_html) : '';
        $model->tags = is_array($request->gallery_modal_tags) ? implode(",",$request->gallery_modal_tags) : '';
        $model->text_area = isset($request->gallery_modal_text_area) ? AppHelper::instance()->cleanInput($request->gallery_modal_text_area) : '';
        $model->date = isset($request->gallery_modal_date) ? Carbon::createFromFormat('d-m-Y', $request->gallery_modal_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
        $model->datetime = isset($request->gallery_modal_datetime) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_datetime)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
  
  
        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';
        $model->save();
        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = Modal::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }

    public function position_page(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = Modal::where('temps_id',$request->temps_id)
                  ->where('id',$data[0])
                  ->first();

          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = Modal::where('temps_id',$request->temps_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }
}
