<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\CourseExamLog;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseExamAnswer;
use App\Models\VNE\Project;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class CourseExamLogController extends Controller
{
    public $prefix = 'exam_log';
    public $project_url = 'exam_log';
    public $project_layout = 'admin.Lrk.exam_log';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      
      $course_id = $request->course_id;
      $lesson_id = $request->lesson_id;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','lesson_id'));
    }

    public function create(Request $request)
    {      
        $lesson_id = $request->lesson_id;
        $course_id = $request->course_id;

        if(isset($request->type)){
          $setting_center = $request->type;
        }else{
          $setting_center = '';
        }

        $model = new CourseExamLog();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> url('csisocietyadmin/exam_log/'.$course_id.'/'.$lesson_id.'/create'),
          'btn'=>'Add',
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj','course_id','lesson_id','setting_center'));
    }

    public function show(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        $lesson_id = $request->lesson_id;
        $course_id = $request->course_id;

        if(isset($request->type)){
          $setting_center = $request->type;
        }else{
          $setting_center = '';
        }
        $model = CourseExamLog::find($request->id);
        // $course_id = CourseExamLog::where('course_exam.id', $lesson_id)->select('course_exam.course_id')->first();

        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>'/csisocietyadmin/exam_log/'.$request->course_id.'/'.$request->lesson_id.'/edit/'.$request->id,
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj','lesson_id','course_id','setting_center'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='question_th'){
            array_push($where_search, ['course_exam.question_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='point'){
            array_push($where_search, ['course_exam.point', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_exam.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('course_exam')
      ->select('course_exam.*')
      ->where('course_exam.lesson_id', $request->lesson_id)
      ->where($where_search) //
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_exam')
      ->where('course_exam.lesson_id', $request->lesson_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="/csisocietyadmin/exam_log/'.$request->course_id.'/'.$request->lesson_id.'/edit/'.$obj->id.'" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        $obj['text_area'] = AppHelper::instance()->limitCharUTF8($obj['text_area'],30);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

        $model = new CourseExamLog;
        $model->course_id = $request->course_id;
        $model->lesson_id = $request->lesson_id;

        $model->question_th = isset($request->question_th) ? AppHelper::instance()->cleanInput($request->question_th) : null;
        $model->point = isset($request->point) ? AppHelper::instance()->cleanInput($request->point) : 0;
        $model->exam_type = isset($request->exam_type) ? AppHelper::instance()->cleanInput($request->exam_type) : 1;
        $model->answer_type = isset($request->answer_type) ? AppHelper::instance()->cleanInput($request->answer_type) : 1;
        $model->file_pptx = isset($request->file_pptx) ? AppHelper::instance()->cleanInput($request->file_pptx) : null;
        $model->file_excel = isset($request->file_excel) ? AppHelper::instance()->cleanInput($request->file_excel) : null;
        $model->file_word = isset($request->file_word) ? AppHelper::instance()->cleanInput($request->file_word) : null;

        $count = CourseExamLog::where('lesson_id', $request->lesson_id)->count();
        $model->position = $count + 1;

        $model->status = $request->status;
        $model->save();
        if($model->exam_type == 1){
          return redirect(route('admin').'/exam_log/'.$request->course_id.'/'.$request->lesson_id.'/edit/'.$model->id)->with('success', 'Data has been update');
        }else{
          return redirect(route('admin').'/exam_log/'.$request->course_id.'/'.$request->lesson_id)->with('success', 'Data has been update');
        }
    }

    public function update(Request $request)
    {

        $model = CourseExamLog::find($request->id);
        if($model){

          $model->course_id = $request->course_id;
          $model->lesson_id = $request->lesson_id;
          $model->question_th = isset($request->question_th) ? AppHelper::instance()->cleanInput($request->question_th) : null;
          $model->point = isset($request->point) ? AppHelper::instance()->cleanInput($request->point) : 0;
          $model->exam_type = isset($request->exam_type) ? AppHelper::instance()->cleanInput($request->exam_type) : 1;
          $model->answer_type = isset($request->answer_type) ? AppHelper::instance()->cleanInput($request->answer_type) : 1;
          $model->file_pptx = isset($request->file_pptx) ? AppHelper::instance()->cleanInput($request->file_pptx) : null;
          $model->file_excel = isset($request->file_excel) ? AppHelper::instance()->cleanInput($request->file_excel) : null;
          $model->file_word = isset($request->file_word) ? AppHelper::instance()->cleanInput($request->file_word) : null;

          $model->status = $request->status;
          $model->save();
          if($request->setting == 'setting_center'){
            return redirect(route('admin').'/exam_center')->with('success', 'Data has been update');

          }else{
            return redirect(route('admin').'/exam_log/'.$request->course_id.'/'.$request->lesson_id)->with('success', 'Data has been update');

          }
        }
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CourseExamLog::where('lesson_id',$request->lesson_id)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CourseExamLog::where('lesson_id',$request->lesson_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {
        $model = CourseExamLog::find($request->id);

        $answer = CourseExamAnswer::where('exam_id', $model->id)->get();
        foreach($answer as $key_ans=>$value_ans){
          $value_ans->delete();
        }

        CourseExamLog::where('lesson_id', $model->lesson_id)->where('position','>',$model->position)
          ->update(['position' => DB::raw('position - 1')]);
        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = CourseExamLog::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        // $model->timestamps = false;
        $model->save();

        return response()->json();
    }

    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='question_export') {
                $data = DB::table('course_exam')
                ->join('course', 'course.id', '=', 'course_exam.course_id')
                ->join('course_lesson', 'course_lesson.id', '=', 'course_exam.lesson_id')
                ->select('course_exam.*', 'course.title_th as course_title', 'course_lesson.title_th as lesson_title')
                ->where('course_exam.course_id', $request->course_id)
                ->where('course_exam.lesson_id', $request->lesson_id)
                ->orderBy('id', 'desc')
                ->get();


                $ex_array[]=array(
                  'คอร์ส'=>'คอร์ส',
                  'บทเรียน'=>'บทเรียน',
                  'ประเภทคำถาม'=>'ประเภทคำถาม',
                  'คำถาม'=>'คำถาม',
                  'ตัวเลือก'=>'ตัวเลือก',
                  'คะแนน'=>'คะแนน',
                  'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
                );

                foreach ($data as $e) {
                  if($e->exam_type == 1){
                    $e->exam_type = 'ปรนัย';
                  }else if($e->exam_type == 2){
                    $e->exam_type = 'อัตนัย';
                  }else{
                    $e->exam_type = 'อัพโหลด';
                  }

                  $data_choice = CourseExamAnswer::where('course_id', $request->course_id)->where('lesson_id', $request->lesson_id)->where('exam_id', $e->id)->get();
                  
                  $answer_txt = '';
                  foreach($data_choice as $key=>$val){
                    if($answer_txt!=''){
                      $answer_txt.=' || ';
                    }
                    $answer_txt.=$val->answer_th;
                  }
                  $ex_array[]=array(
                  'คอร์ส'=>$e->course_title,
                  'บทเรียน'=>$e->lesson_title,
                  'ประเภทคำถาม'=>$e->exam_type,
                  'คำถาม'=>$e->question_th,
                  'ตัวเลือก'=>$answer_txt,
                  'คะแนน'=>$e->point,
                  'วันที่เก็บข้อมูล'=>$e->created_at,
                  );
                }
            }



            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    $export = new TempExport($ex_array);

                    return FacadesExcel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

  }
