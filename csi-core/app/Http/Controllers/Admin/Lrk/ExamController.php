<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\UsersExamPoint;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseCheckerLog;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\CourseExamLog;
use App\Models\Core\UserExamImage;
use App\Models\Core\UsersExamLog;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class ExamController extends Controller
{
    public $prefix = 'exam';
    public $project_url = 'exam';
    public $project_layout = 'admin.Lrk.exam';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new UsersExamPoint();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        $lesson_id = $request->lesson_id;
        $course_id = $request->course_id;
        $user_id = $request->user_id;
        $round = $request->round;

        $user_exam = UsersExamLog::leftjoin('course_exam', 'course_exam.id', 'user_exam_log.exam_id')
        ->leftjoin('course_exam_answer', 'course_exam_answer.id', 'user_exam_log.answer')
        ->select('user_exam_log.id as log_id', 'user_exam_log.point', 'course_exam.question_th', 'course_exam_answer.answer_th', 
                'course_exam.exam_type', 'user_exam_log.point', 'user_exam_log.answer_text', 'user_exam_log.exam', 'user_exam_log.file', 'user_exam_log.exam_id')
        ->where('course_exam.lesson_id', $lesson_id)
        ->where('user_exam_log.user_id', $user_id)
        ->where('user_exam_log.round',$round)
        ->get();

        foreach($user_exam as $value){
          if($value->exam_type==3){
            $value->image_log = UserExamImage::where('exam_log_id',$value->log_id)->get();
          }else{
            $value->image_log = array();
          }
        }

        $user_point = UsersExamPoint::where('lesson_id',$lesson_id)->where('course_id',$course_id)->where('user_id',$user_id)->where('round',$round)->first();
        if($user_point){
          $remark = $user_point->remark;
        }else{
          $remark='';
        }
        $obj = array(
          'type'=>'edit',
          'route'=>'/csisocietyadmin/exam/'.$request->course_id.'/'.$request->lesson_id.'/'.$user_id.'/edit',
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('obj','lesson_id','course_id','user_id','user_exam','round','remark'));
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_exam_point.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_exam_point.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_exam_point.id', $value['search']['value']]);
          }else if($value['data']=='title'){
            array_push($where_search, ['course.title_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='lesson'){
            array_push($where_search, ['course_lesson.title_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_exam_point.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      if(Auth::user()->level == '99'){
        $query = DB::table('user_exam_point')
        ->join('course', 'course.id', 'user_exam_point.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
        ->join('user', 'user.id', 'user_exam_point.user_id')
        ->select('user_exam_point.round','user_exam_point.id', 'user_exam_point.course_id', 'user_exam_point.lesson_id', 'user_exam_point.user_id', 'user_exam_point.check_status', 
                'course.title_th as title', 'course_lesson.title_th as lesson', 'user.name as u_name', 'user.lastname as u_lastname', 'user_exam_point.created_at')
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user_exam_point')
        ->join('course', 'course.id', 'user_exam_point.course_id')
        ->join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
        ->join('user', 'user.id', 'user_exam_point.user_id')
        ->where($where_search)
        ->count();
      }else if(Auth::user()->level == '88'){
        $arr_id = CourseCheckerLog::where('admin_id', Auth::user()->id)->pluck('admin_id')->toArray();

        $query = DB::table('user_exam_point')
        ->join('course', 'course.id', 'user_exam_point.course_id')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->join('admin', 'admin.id', 'course_checker_log.admin_id')
        ->join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
        ->join('user', 'user.id', 'user_exam_point.user_id')
        ->select('user_exam_point.round','user_exam_point.id', 'user_exam_point.course_id', 'user_exam_point.lesson_id', 'user_exam_point.user_id', 'user_exam_point.check_status', 
                'course.title_th as title', 'course_lesson.title_th as lesson', 'user.name as u_name', 'user.lastname as u_lastname', 'user_exam_point.created_at')
        ->whereIn('course_checker_log.admin_id', $arr_id)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('user_exam_point')
        ->join('course', 'course.id', 'user_exam_point.course_id')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->join('admin', 'admin.id', 'course_checker_log.admin_id')
        ->join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
        ->join('user', 'user.id', 'user_exam_point.user_id')
        ->whereIn('course_checker_log.admin_id', $arr_id)
        ->where($where_search)
        ->count();
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
        <a href="/csisocietyadmin/exam/'.$obj->course_id.'/'.$obj->lesson_id.'/'.$obj->user_id.'/'.$obj->round.'/edit'.'" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = UsersExamPoint::find($id);
      
      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return response()->json();
      }
      $model = UsersExamPoint::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='exam_all_filter') {
          
          $data = DB::table('user_exam_point');
          $data->leftjoin('course', 'course.id', 'user_exam_point.course_id');
          $data->leftjoin('user', 'user.id', 'user_exam_point.user_id');
          $data->leftjoin('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id');
          $data->select('user_exam_point.*', 'user.name as u_name', 'user.lastname as u_lastname', 'user.email', 
                        'user.mobile', 'course.title_th as c_title', 'course_lesson.title_th as l_title');
          $data->get();

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_order_list.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'ครั้งที่'=>'ครั้งที่',
            'คอร์ส'=>'คอร์ส',
            'บทเรียน'=>'บทเรียน',
            'ชื่อ'=>'ชื่อ',
            'นามสกุล'=>'นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'เบอร์โทรศัพท์'=>'เบอร์โทรศัพท์',
            'คะแนน'=>'คะแนน',
            'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
          );

          foreach ($data as $key=>$e) {


            $ex_array[]=array(
              'ครั้งที่'=>$e->round,
              'คอร์ส'=>$e->c_title,
              'บทเรียน'=>$e->l_title,
              'ชื่อ'=>$e->u_name,
              'นามสกุล'=>$e->u_lastname,
              'อีเมล์'=>$e->email,
              'เบอร์โทรศัพท์'=>$e->mobile,
              'คะแนน'=>$e->point,
              'วันที่เก็บข้อมูล'=>$e->created_at,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

}
