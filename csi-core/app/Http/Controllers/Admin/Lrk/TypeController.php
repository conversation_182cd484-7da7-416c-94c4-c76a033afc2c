<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Sample\Type;
use App\Models\Dummy;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;


class TypeController extends Controller
{
    public $prefix = 'type';
    public $project_url = 'Sample/type';
    public $project_layout = 'admin.Sample.type';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
        $model = new Type();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> route($this->prefix.'.store'),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      $model = Type::find($id);
        if($model){
          $obj = array(
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.view', compact('model','obj'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function edit($id)
    {
        $model = Type::find($id);
        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>route($this->prefix.'.update',$model->id),
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          
          return view($this->project_layout.'.form', compact('model','obj'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['types.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['types.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['types.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['types.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('types')
            ->select('types.*')
            ->where($where_search) //
            ->orderBy($orderby, $order_sort)
            ->offset($start) //
            ->limit($length) //
            ->get();

      $count_total = DB::table('types')
      ->where($where_search)->count();


        $data_query = array();
        foreach ($query as $key => $value) {
          $obj = new Dummy();
          foreach ($value as $key_data => $data) {
            $obj[$key_data] = $data;
          }
          $obj['action'] = '
          <div class="btn-group">
            <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
            <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
          </div>';
          array_push($data_query, $obj);
        }

        $data = $datatables->of($data_query)
              ->with([
                'data' => $data_query,
                'draw' => $draw,
                "recordsTotal" => $count_total,
                "recordsFiltered" => $count_total,
              ])
              ->make(true);

        return $data;
    }

    public function store(Request $request)
    {
      $request->validate([
          'name'=>'required',
      ]);

      $model = new Type;

      $model->name = isset($request->name) ? $request->name : '';

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Content has been added');
    }

    public function update(Request $request, $id)
    {

        $model = Type::find($id);

        $model->name = isset($request->name) ? $request->name : '';

        $model->save();

        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Content has been update');
    }

    public function destroy(Request $request)
    {
        $model = Type::find($request->id);

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = Type::find($request->id);

        if($model->status=='1'){
          $model->status = '0';
        }
        else{
          $model->status = '1';
        }

        $model->timestamps = false;
        $model->save();

        return response()->json();
    }

  }
