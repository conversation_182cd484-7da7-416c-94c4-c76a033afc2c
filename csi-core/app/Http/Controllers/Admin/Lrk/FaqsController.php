<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\Faqs;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Models\Core\FaqsComment;
use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class FaqsController extends Controller
{
    public $prefix = 'faqs';
    public $project_url = 'faqs';
    public $project_layout = 'admin.Lrk.faqs';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $count = Faqs::count();
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','count'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Faqs();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = Faqs::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['faqs.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['faqs.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['faqs.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['faqs.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('faqs')
      ->select('faqs.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('faqs')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Faqs;
      $model->question = isset($request->question) ? AppHelper::instance()->cleanInput($request->question) : null;
      $model->answer = isset($request->answer) ? AppHelper::instance()->convertDomHTML($request->answer) : null;
      $model->type = isset($request->type) ? AppHelper::instance()->cleanInputBr($request->type) : 1;

      $model->status = $request->status;

      $count = Faqs::count();
      $model->position = $count + 1;

      $model->save();

      return redirect(route('admin').'/faqs/'.$model->id.'/edit/')->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = Faqs::find($id);
      $model->question = isset($request->question) ? AppHelper::instance()->cleanInput($request->question) : null;
      $model->answer = isset($request->answer) ? AppHelper::instance()->convertDomHTML($request->answer) : null;
      $model->type = isset($request->type) ? AppHelper::instance()->cleanInputBr($request->type) : 1;
      
      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = Faqs::find($request->id);

      if($model){
        $comment = FaqsComment::where('faq_id', $model->id)->get();
        foreach($comment as $key => $val){
          $val->delete();
        }

        Faqs::where('position','>',$model->position)
        ->update(['position' => DB::raw('position - 1')]);
        $model->delete();
      }
      

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = Faqs::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function position(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = Faqs::where('id',$data[0])
                  ->first();

          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = Faqs::count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

}
