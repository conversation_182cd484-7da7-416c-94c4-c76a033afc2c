<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CateDepartment;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseDepartmentLog;
use App\Models\Core\SeminarDepartmentLog;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel;


class CateDepartmentController extends Controller
{
    public $prefix = 'categories_department';
    public $project_url = 'categories_department';
    public $project_layout = 'admin.Lrk.categories_department';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
        $model = new CateDepartment();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> route($this->prefix.'.store'),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
        $model = CateDepartment::find($id);
        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>route($this->prefix.'.update',$model->id),
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['categories_department.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['categories_department.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['categories_department.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['categories_department.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('categories_department')
      ->select('categories_department.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('categories_department')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      $model = new CateDepartment;
      $model->plant_dev = isset($request->plant_dev) ? AppHelper::instance()->cleanInput($request->plant_dev) : null;
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->div_center = isset($request->div_center) ? AppHelper::instance()->cleanInput($request->div_center) : null;
      $model->div_title = isset($request->div_title) ? AppHelper::instance()->cleanInput($request->div_title) : null;
      $model->gid = isset($request->gid) ? AppHelper::instance()->cleanInput($request->gid) : null;


      $model->plant = isset($request->plant) ? AppHelper::instance()->cleanInput($request->plant) : null;
      $model->group = isset($request->group) ? AppHelper::instance()->cleanInput($request->group) : null;
      $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : null;
      $model->cent = isset($request->cent) ? AppHelper::instance()->cleanInput($request->cent) : null;
      $model->div = isset($request->div) ? AppHelper::instance()->cleanInput($request->div) : null;
      $model->dept = isset($request->dept) ? AppHelper::instance()->cleanInput($request->dept) : null;
      $model->sec = isset($request->sec) ? AppHelper::instance()->cleanInput($request->sec) : null;

      $model->short_name = $model->cent.$model->div.$model->dept;
      $model->hrms_dept_id = $model->cent.'-'.$model->div.'-'.$model->dept.'-'.$model->dept;
      $model->hrms_full_id = $model->plant.'-'.$model->group.'-'.$model->type.'-'.$model->cent.'-'.$model->div.'-'.$model->dept.'-'.$model->sec;
      $model->upload_hrms = $model->plant.$model->group.$model->type.$model->cent.$model->div.$model->dept.$model->sec;

      $model->status = $request->status;

      $count = CateDepartment::all()->count();
      $model->position = $count + 1;

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

        $model = CateDepartment::find($id);
        if($model){
          $model->plant_dev = isset($request->plant_dev) ? AppHelper::instance()->cleanInput($request->plant_dev) : null;
          $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
          $model->div_center = isset($request->div_center) ? AppHelper::instance()->cleanInput($request->div_center) : null;
          $model->div_title = isset($request->div_title) ? AppHelper::instance()->cleanInput($request->div_title) : null;
          $model->gid = isset($request->gid) ? AppHelper::instance()->cleanInput($request->gid) : null;
    
    
          $model->plant = isset($request->plant) ? AppHelper::instance()->cleanInput($request->plant) : null;
          $model->group = isset($request->group) ? AppHelper::instance()->cleanInput($request->group) : null;
          $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : null;
          $model->cent = isset($request->cent) ? AppHelper::instance()->cleanInput($request->cent) : null;
          $model->div = isset($request->div) ? AppHelper::instance()->cleanInput($request->div) : null;
          $model->dept = isset($request->dept) ? AppHelper::instance()->cleanInput($request->dept) : null;
          $model->sec = isset($request->sec) ? AppHelper::instance()->cleanInput($request->sec) : null;
    
          $model->short_name = $model->cent.$model->div.$model->dept;
          $model->hrms_dept_id = $model->cent.'-'.$model->div.'-'.$model->dept.'-'.$model->dept;
          $model->hrms_full_id = $model->plant.'-'.$model->group.'-'.$model->type.'-'.$model->cent.'-'.$model->div.'-'.$model->dept.'-'.$model->sec;
          $model->upload_hrms = $model->plant.$model->group.$model->type.$model->cent.$model->div.$model->dept.$model->sec;
    
          $model->status = $request->status;
          $model->save();
  
          return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
        }else{
          return redirect(route('admin').'/'.$this->project_url)->with('erros', 'Data has not been found');
        }

    }

    public function destroy(Request $request)
    {

        $model = CateDepartment::find($request->id);
      
        CateDepartment::where('position','>',$model->position)
        ->update(['position' => DB::raw('position - 1')]);

        $user = User::where('dept_id', $model->id)->get();
        foreach($user as $key_user => $val_user){
          $val_user->dept_id = 999;
        }

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = CateDepartment::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CateDepartment::find($data[0]);
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CateDepartment::count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }
    
    public function import_(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;

      if(isset($request->excel)){
        $excels = json_decode($request->excel);
        if(count($excels)>0){
          foreach ($excels as &$excel) {
            $type = '';
            
            if(isset($excel->code)){
              if(!empty($excel->code)){
                $code_dup = CateDepartment::where('title_th', AppHelper::instance()->cleanInput($excel->Department))->first();
                if(!$code_dup){
                  $model_excel = new CateDepartment();
                  $type = 'store';

                  $model_excel->title_th = isset($excel->Department) ? AppHelper::instance()->cleanInput($excel->Department) : null;
                  $model_excel->center = isset($excel->Center) ? AppHelper::instance()->cleanInput($excel->Center) : null;
                  $model_excel->division = isset($excel->Division) ? AppHelper::instance()->cleanInput($excel->Division) : null;
                  $model_excel->dept_code = isset($excel->Department_code) ? AppHelper::instance()->cleanInput($excel->Department_code) : null;
                  $model_excel->short_name = $model_excel->center.$model_excel->division.$model_excel->dept_code;
                  $model_excel->gid = isset($excel->GID) ? AppHelper::instance()->cleanInput($excel->GID) : null;
                  $model_excel->hrms_dept_id = isset($excel->HRMS_DepID) ? AppHelper::instance()->cleanInput($excel->HRMS_DepID) : null;
                  $model_excel->status = 1;

                  $count = CateDepartment::all()->count();
                  $model_excel->position = $count + 1;

                  $response[$type]+=1;
                  
                  $model_excel->save();
                }else{
                  $model_excel = CateDepartment::find($code_dup->id)();
                  $type = 'update';
                  $model_excel->center = isset($excel->Center) ? AppHelper::instance()->cleanInput($excel->Center) : null;
                  $model_excel->division = isset($excel->Division) ? AppHelper::instance()->cleanInput($excel->Division) : null;
                  $model_excel->dept_code = isset($excel->Department_code) ? AppHelper::instance()->cleanInput($excel->Department_code) : null;
                  $model_excel->short_name = $model_excel->center.$model_excel->division.$model_excel->dept_code;
                  $model_excel->gid = isset($excel->GID) ? AppHelper::instance()->cleanInput($excel->GID) : null;
                  $model_excel->hrms_dept_id = isset($excel->HRMS_DepID) ? AppHelper::instance()->cleanInput($excel->HRMS_DepID) : null;
                  $model_excel->status = 1;
                  $model_excel->save();

                }
              }
            }
          }
        }
      }
      return json_decode($response, true);;
    }
    

    public function import(Request $request)
    {
      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;

      if(isset($request->excel)){
        $excels = json_decode($request->excel);
        if(count($excels)>0){
          foreach ($excels as &$excel) {
            $type = '';
            //แก้ไข key ที่ต้องการเช็คซ้ำ ตัวอย่าง $excel->id ซึ่งจะเป็น id , email หรืออะไรก็ได้ ถ้ามีซ้ำใน db ก็จะทำการอัพเดทข้อมูลแทน
            if(isset($excel->Department)){
                if(!empty($excel->Department)){
                  $result = CateDepartment::where('plant','=', $excel->Plant)
                  ->where('group','=', $excel->Group)->where('type','=', $excel->Type)
                  ->where('cent','=', $excel->Cent)->where('div','=', $excel->Div)
                  ->where('dept','=', $excel->Dept)->where('sec','=', $excel->Sec)
                  ->first();
                  //จบเรื่อง key ที่ต้องการเช็คซ้ำ

                  if($result){
                    $model_excel = CateDepartment::find($result->id);
                    $type = 'update';
                  }else{
                    $model_excel = new CateDepartment();
                    $type = 'store';
                  }
                }else{
                  $model_excel = new CateDepartment();
                  $type = 'store';
                }
            }else{
              $model_excel = new CateDepartment();
              $type = 'store';
            }

            $model_excel->plant_dev = isset($excel->Plant_dept) ? AppHelper::instance()->cleanInput($excel->Plant_dept) : null;
            $model_excel->title_th = isset($excel->Department) ? AppHelper::instance()->cleanInput($excel->Department) : null;
            $model_excel->div_center = isset($excel->Center) ? AppHelper::instance()->cleanInput($excel->Center) : null;
            $model_excel->div_title = isset($excel->Division) ? AppHelper::instance()->cleanInput($excel->Division) : null;
            $model_excel->gid = isset($excel->GID) ? AppHelper::instance()->cleanInput($excel->GID) : null;

    
            $model_excel->plant = isset($excel->Plant) ? AppHelper::instance()->cleanInput($excel->Plant) : null;
            $model_excel->group = isset($excel->Group) ? AppHelper::instance()->cleanInput($excel->Group) : null;
            $model_excel->type = isset($excel->Type) ? AppHelper::instance()->cleanInput($excel->Type) : null;
            $model_excel->cent = isset($excel->Cent) ? AppHelper::instance()->cleanInput($excel->Cent) : null;
            $model_excel->div = isset($excel->Div) ? AppHelper::instance()->cleanInput($excel->Div) : null;
            $model_excel->dept = isset($excel->Dept) ? AppHelper::instance()->cleanInput($excel->Dept) : null;
            $model_excel->sec = isset($excel->Sec) ? AppHelper::instance()->cleanInput($excel->Sec) : null;
            
            $model_excel->short_name = $model_excel->cent.$model_excel->div.$model_excel->dept;
            $model_excel->hrms_dept_id = $model_excel->cent.'-'.$model_excel->div.'-'.$model_excel->dept.'-'.$model_excel->dept;
            $model_excel->hrms_full_id = $model_excel->plant.'-'.$model_excel->group.'-'.$model_excel->type.'-'.$model_excel->cent.'-'.$model_excel->div.'-'.$model_excel->dept.'-'.$model_excel->sec;
            $model_excel->upload_hrms = $model_excel->plant.$model_excel->group.$model_excel->type.$model_excel->cent.$model_excel->div.$model_excel->dept.$model_excel->sec;

            $model_excel->status = 1;

            $count = CateDepartment::all()->count();
            $model_excel->position = $count + 1;

            $response[$type]+=1;
            $model_excel->save();
            
          }
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);;
    }

}
