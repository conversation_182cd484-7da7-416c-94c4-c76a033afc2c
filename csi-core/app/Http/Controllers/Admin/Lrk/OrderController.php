<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;
use App\Service\PeakService;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\UsersOrderLog;
use App\Models\Core\Course;
use App\Models\Core\CourseGroup;
use App\Models\Core\EmailDynamic;
use App\Models\Core\NotiAuto;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\NotiGlobalLog;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\UserGiftLog;
use App\Models\Core\UserHistory;
use App\Models\Core\UserLearnedTime;
use App\Models\Core\Users;
use App\Models\Core\UsersOrderList;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class OrderController extends Controller
{
  public $prefix = 'order';
  public $project_url = 'order';
  public $project_layout = 'admin.Lrk.order';

  public function __construct()
  {
    $this->middleware('auth:admin');
  }

  public function index()
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '66') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }

    $order_count = UsersOrderLog::where('status', 2)->count();

    $price = UsersOrderList::select('price')->get();
    $price_total = $price->sum('price');

    $order_discount = UsersOrderList::select('discount_value')->where('discount_value', '!=', null)->get();
    $discount = $order_discount->sum('discount_value');

    $order_total = UsersOrderList::select('total_price')->get();
    $total = $order_total->sum('total_price');

    $obj = array(
      'route' => '/csisocietyadmin/order-status_update',
      'prefix' => $this->prefix,
      'project_url' => $this->project_url,
      'project_layout' => $this->project_layout,
      'order_count' => $order_count,
      'price_total' => $price_total,
      'discount' => $discount,
      'total' => $total
    );
    AppHelper::instance()->consoleLog($order_count);
    AppHelper::instance()->consoleLog($price_total);
    AppHelper::instance()->consoleLog($discount);
    AppHelper::instance()->consoleLog($total);
    AppHelper::instance()->consoleLog($obj);
    return view($this->project_layout . '.index', compact('obj'));
  }

  public function create()
  {
    return redirect(route('admin') . '/' . $this->project_url);
  }

  public function show($id)
  {
    return redirect(route('admin') . '/' . $this->project_url);
  }

  public function edit($id)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '66') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = UsersOrderLog::with(['checkoutFieldSubmissions.checkoutField'])->find($id);

    if ($model) {
      $course = UsersOrderList::where('order_id', $model->id)->where('course_id', '!=', null)->count();
      $group = UsersOrderList::where('order_id', $model->id)->where('group_id', '!=', null)->count();
      $subscription = UsersOrderList::where('order_id', $model->id)->where('subscription_id', '!=', null)->count();
      
      // Get checkout field submissions grouped by field
      $checkoutFieldSubmissions = $model->checkoutFieldSubmissions->groupBy('checkout_field_id')->map(function ($submissions) {
        return $submissions->first(); // Take the first submission for each field
      });

      $obj = array(
        'type' => 'edit',
        'route' => route($this->prefix . '.update', $model->id),
        'prefix' => $this->prefix,
        'project_url' => $this->project_url,
        'project_layout' => $this->project_layout
      );
      AppHelper::instance()->consoleLog($model);
      return view($this->project_layout . '.form', compact('model', 'obj', 'course', 'group', 'subscription', 'checkoutFieldSubmissions'));
    } else {
      return redirect(route('admin') . '/' . $this->project_url);
    }
  }

  public function dblist(DataTables $datatables, Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '66') {
      return new Dummy();
    }

    $draw = $request->get('draw');
    $start = $request->get('start');
    $length = $request->get('length');

    //custom search
    $where_search = array();
    foreach ($request->get('columns') as $key => $value) {
      if (!empty($value['search']['value'])) {
        if ($value['data'] == 'status') {
          array_push($where_search, ['user_order_log.status', $value['search']['value']]);
        } else if ($value['data'] == 'position') {
          array_push($where_search, ['user_order_log.position', $value['search']['value']]);
        } else if ($value['data'] == 'id') {
          array_push($where_search, ['user_order_log.id', $value['search']['value']]);
        } else if ($value['data'] == 'u_name') {
          array_push($where_search, ['user.name', 'LIKE', '%' . $value['search']['value'] . '%']);
        } else if ($value['data'] == 'u_lastname') {
          array_push($where_search, ['user.lastname', 'LIKE', '%' . $value['search']['value'] . '%']);
        } else if ($value['data'] == 'course_name') {
          array_push($where_search, ['course.title_th', 'LIKE', '%' . $value['search']['value'] . '%']);
        } else if ($value['data'] == 'payment_type') {
          if ($value['search']['value'] == '21') {
            $valuePaymentSearch = 2;
            array_push($where_search, ['user_order_log.payment_type', $valuePaymentSearch]);
            array_push($where_search, ['user_order_log.credit_payment_type', 'full']);
          } else if ($value['search']['value'] == '22') {
            $valuePaymentSearch = 2;
            array_push($where_search, ['user_order_log.payment_type', $valuePaymentSearch]);
            array_push($where_search, ['user_order_log.credit_payment_type', 'installment']);
          } else {
            array_push($where_search, ['user_order_log.payment_type', $value['search']['value']]);
          }
        } else {
          array_push($where_search, ['user_order_log.' . $value['data'], 'LIKE', '%' . $value['search']['value'] . '%']);
        }
      }
    }

    $index = $request->get('order')[0]['column'];
    $orderby = $request->get('columns')[$index]['data'];
    $order_sort = $request->get('order')[0]['dir'];

    //number column
    if ($orderby == 'position') {
      $orderby = 'id';
    }
    //number column

    $query = DB::table('user_order_log')
      ->leftJoin('user', 'user.id', 'user_order_log.user_id')
      ->leftJoin('course_lesson', 'course_lesson.id', 'user_order_log.media_id')
      ->leftJoin('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
      ->leftJoin('course', 'course.id', 'user_order_list.course_id')
      ->select(
        'user_order_log.*',
        'user.name as u_name',
        'user.lastname as u_lastname',
        'course.title_th as course_name',
        'user.email as u_email',
        'user.mobile as u_mobile'
      )
      ->groupBy('user_order_log.id')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

    $count_total = count(DB::table('user_order_log')
      ->leftJoin('user', 'user.id', 'user_order_log.user_id')
      ->leftJoin('course_lesson', 'course_lesson.id', 'user_order_log.media_id')
      ->leftJoin('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
      ->leftJoin('course', 'course.id', 'user_order_list.course_id')
      ->groupBy('user_order_log.id')
      ->where($where_search)
      ->pluck('user_order_log.id')->toArray());

    //number column
    if ($order_sort == 'desc') {
      $no_start = $start;
    } else {
      $no_start = $start + 1;
    }
    $query_array = json_decode(json_encode($query), true);
    foreach ($query_array as $key => $value) {
      if ($order_sort == 'desc') {
        $query_array[$key]['position'] = $count_total - $no_start;
        $no_start++;
      } else {
        $query_array[$key]['position'] = $no_start;
        $no_start++;
      }
    }
    $query = json_decode(json_encode($query_array));
    //number column

    $data_query = array();
    foreach ($query as $key => $value) {
      $obj = new Dummy();
      foreach ($value as $key_data => $data) {
        $obj[$key_data] = $data;
      }

      if ($obj['total_price'] == 0) {
        $obj['total_price'] = 'ฟรี';
      }

      $obj['full_name'] = $obj['u_name'] . ' ' . $obj['u_lastname'];

      $obj['action'] = '
        <div class="btn-group">
          <a href="' . $this->prefix . '/' . $obj->id . '/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
      array_push($data_query, $obj);
    }

    $data = $datatables->of($data_query)
      ->with([
        'data' => $data_query,
        'draw' => $draw,
        "recordsTotal" => $count_total,
        "recordsFiltered" => $count_total,
      ])
      ->make(true);
    return $data;
  }

  public function store(Request $request)
  {
    return redirect(route('admin') . '/' . $this->project_url);
  }

  public function update(Request $request, $id)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '66') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }
    $model = UsersOrderLog::find($id);
    if ($model) {
      $old_status = $model->status;
      $model->status = $request->status;
      $model->payment_remark = isset($request->payment_remark) ? AppHelper::instance()->cleanInput($request->payment_remark) : null;
      $model->tax_remark = isset($request->tax_remark) ? AppHelper::instance()->cleanInput($request->tax_remark) : null;

      if ($model->status == 2) {
        $order_list = UsersOrderList::where('order_id', $model->id)->get();
        $user_check = User::where('id', $model->user_id)->first();
        $zoom_fail = 0;
        foreach ($order_list as $val) {
          $course_check = Course::join('course_lesson', 'course_lesson.course_id', 'course.id')
            ->select('course.*', 'course_lesson.zoom_id')
            ->where('course.id', $val->course_id)->where('course.status', 1)
            ->where('course.started_date', '<=', Carbon::now())->first();
          if ($course_check && $course_check->trailer_media == 2 && $user_check) {
            $assign_zoom = AppHelper::instance()->assignZoom($course_check->zoom_id, $user_check->email, $user_check->name, $user_check->lastname);
            if ($assign_zoom->status == 'approve') {
              $val->registrant_id = $assign_zoom->registrant_id;
              $val->save();
            } else {
              $zoom_fail++;
            }
          }
        }
        if ($zoom_fail == 0) {
          $model->receipt_path = env('APP_URL') . '/receipt/' . SimpleEnDeHelper::instance()->encryptString($model->id);
          $model->created_receipt_date = Carbon::now('Asia/Bangkok');
          if ($model->buy_type == 2 && $old_status != $model->status) {
            $gift_log = new UserGiftLog();
            $gift_log->user_id = $model->user_id;
            $gift_log->order_id = $model->id;
            $gift_log->receive_email = $model->receive_email;
            $gift_log->save();

            $email_dynamic = EmailDynamic::where('id', 10)->where('status', 1)->first();
            if ($email_dynamic) {
              $replace_detail = $email_dynamic->details;
              $replace_detail = str_replace("{{sender}}", $model->name . ' ' . $model->lastname, $replace_detail);
              if (isset($model->receive_message) && $model->receive_message != null && $model->receive_message != '' && $model->receive_message != 'null') {
                $replace_detail = str_replace("{{message}}", 'ข้อความ : ' . $model->receive_message, $replace_detail);
              } else {
                $replace_detail = str_replace("{{message}}", '', $replace_detail);
              }
              $order_gift = UsersOrderList::where('user_order_list.order_id', $model->id)->get();
              foreach ($order_gift as $key => $value) {
                $order_gift[$key]['title_th'] = null;
                $order_gift[$key]['subtitle_th'] = null;
                $order_gift[$key]['image_th'] = null;
                $order_gift[$key]['course_link'] = null;
                if ($value->type == 1) {
                  $course_log = Course::where('id', $value->course_id)->first();
                  if ($course_log) {
                    $order_gift[$key]['title_th'] = $course_log->title_th;
                    $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                    $order_gift[$key]['image_th'] = $course_log->image_th;
                    $order_gift[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
                  }
                } else if ($value->type == 2) {
                  $course_log = CourseGroup::where('id', $value->group_id)->first();
                  if ($course_log) {
                    $order_gift[$key]['title_th'] = $course_log->title;
                    $order_gift[$key]['subtitle_th'] = $course_log->details;
                    $order_gift[$key]['image_th'] = $course_log->thumb;
                  }
                } else if ($value->type == 3) {
                  $course_log = Subscription::where('id', $value->subscription_id)->first();
                  if ($course_log) {
                    $order_gift[$key]['title_th'] = $course_log->title;
                    $order_gift[$key]['subtitle_th'] = $course_log->details;
                    $order_gift[$key]['image_th'] = $course_log->thumb;
                  }
                }
              }
              $order_replace = '';
              foreach ($order_gift as $value) {
                if ($value->type == 1) {
                  $order_replace .=
                    '<tr>
                        <td style="width:30px;"></td>
                        <td style="width: 140px">
                            <a href="' . $value->course_link . '">
                              <img src="' . $value->image_th . '" style="width: 140px;">
                            </a>
                        </td>
                        <td style="padding: 10px;">
                          <a href="' . $value->course_link . '">
                            <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                          </a>
                        </td>
                    </tr>';
                } else {
                  $order_replace .=
                    '<tr>
                        <td style="width:30px;"></td>
                        <td style="width: 140px">
                            <img src="' . $value->image_th . '" style="width: 140px;">
                        </td>
                        <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                        ' . $value->subtitle_th . '
                        </td>
                    </tr>';
                }
              }
              $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
              $replace_detail = str_replace("{{link}}", "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id), $replace_detail);
              $obj = new Dummy();
              $obj['subject'] = $email_dynamic->subject;
              $obj['code'] = $replace_detail;
              $obj['email'] = $model->receive_email;
              $obj['cc'] = [];
              $obj['bcc'] = [];
              AppHelper::instance()->mailTemplate($obj);
            }
            $receive_user = User::where('email', $model->receive_email)->first();
            if ($receive_user) {
              $noti_log = new NotiAutoLog();
              $noti_log->user_id = $receive_user->id;
              $noti_log->image = '/assets/images/qr.png';
              $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
              $noti_log->description = 'จากคุณ' . $model->name . ' ' . $model->lastname;
              $noti_log->link = "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id);
              $noti_log->save();
            }
          }

          $this->createPeakInvoiceAndReceipt($model);
        } else {
          $model->status = 4;
        }
      } else {
        $model->created_receipt_date = null;
        $gift_log = UserGiftLog::where('user_id', $model->user_id)->where('order_id', $model->id)->first();
        if ($gift_log) {
          $gift_log->delete();
        }
      }

      $model->save();
      if ($model->buy_type == 1 && $old_status != $model->status) {
        $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
          ->select(
            'user_order_log.id',
            'user_order_log.receive_email',
            'user_order_log.receive_message',
            'user_order_log.user_id',
            'user_order_log.buy_type',
            'user_order_log.created_receipt_date',
            'user_order_list.type',
            'user_order_list.course_id',
            'user_order_list.group_id',
            'user_order_list.subscription_id',
            'user_order_list.id as list_id'
          )
          ->where('user_order_log.status', 2)->where('user_order_log.id', $model->id)->get();
        foreach ($order_history_arr as $order_history) {
          if ($order_history && $order_history->buy_type == 1) {
            if ($order_history->type == 1) {
              $course = Course::where('id', $order_history->course_id)->first();
              if ($course) {
                $history_log = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $order_history->course_id)
                  ->first();
                if (!$history_log) {
                  $history_log = new UserHistory();
                }
                if ($course->trailer_media == 2) {
                  $history_log->zoom_join_url = $course->zoom_join_url;
                  $response['zoom_join_url'] = $history_log->zoom_join_url;
                }
                $history_log->get_type = 2;
                $history_log->order_list_id = $order_history->list_id;
                $history_log->user_id = $order_history->user_id;
                $history_log->course_id = $order_history->course_id;
                $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                $history_log->save();
                $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                if ($del_limit) {
                  $del_limit->delete();
                }
                $response['status'] = 'success';
              }
            } else if ($order_history->type == 2) {
              $group = CourseGroup::where('id', $order_history->group_id)->first();
              if ($group) {
                $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                foreach ($course as $val_course) {
                  $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                    ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                  if (!$check) {
                    $history_log = new UserHistory();
                    if ($val_course->trailer_media == 2) {
                      $history_log->zoom_join_url = $val_course->zoom_join_url;
                      $response['zoom_join_url'] = $history_log->zoom_join_url;
                    }
                    $history_log->get_type = 2;
                    $history_log->order_list_id = $order_history->list_id;
                    $history_log->user_id = $order_history->user_id;
                    $history_log->course_id = $val_course->id;
                    $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                    $history_log->save();
                    $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                    if ($del_limit) {
                      $del_limit->delete();
                    }
                    $response['status'] = 'success';
                  }
                }
              }
            } else if ($order_history->type == 3) {
              $subscription = Subscription::where('id', $order_history->subscription_id)->first();
              if ($subscription) {
                $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                $subscription_log = new SubscriptionLog();
                $subscription_log->order_list_id = $order_history->list_id;
                $subscription_log->user_id = $order_history->user_id;
                $subscription_log->subscription_id = $subscription->id;
                if ($last_sub) {
                  $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                } else {
                  $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                }
                $subscription_log->save();
              }
            }
          }
        }
      }
      if ($model->status == 2 && $old_status != $model->status) {
        $email = Users::select('id', 'name', 'lastname', 'email')->where('id', $model->user_id)->first();


        $order['discount'] = 0;
        $order['total'] = 0;
        $order_list = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
          ->select('user_order_list.*', 'user_order_log.web_price as discount_web')
          ->where('user_order_list.order_id', $model->id)->get();

        $order_total = 0;
        $discount_web = 0;
        foreach ($order_list as $key => $value) {
          $order_list[$key]['title_th'] = null;
          $order_list[$key]['subtitle_th'] = null;
          $order_list[$key]['image_th'] = null;
          $order_list[$key]['course_link'] = null;
          if ($value->type == 1) {
            $course_log = Course::where('id', $value->course_id)->first();
            if ($course_log) {
              $order_list[$key]['title_th'] = $course_log->title_th;
              $order_list[$key]['subtitle_th'] = $course_log->subtitle_th;
              $order_list[$key]['image_th'] = $course_log->image_th;
              $order_list[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
            }
          } else if ($value->type == 2) {
            $course_log = CourseGroup::where('id', $value->group_id)->first();
            if ($course_log) {
              $order_list[$key]['title_th'] = $course_log->title;
              $order_list[$key]['subtitle_th'] = $course_log->details;
              $order_list[$key]['image_th'] = $course_log->thumb;
            }
          }
          if ($value->discount_web != null && $value->discount_web != '' && $value->discount_web != 0 && $value->discount_web != 'null' && $value->discount_web > $discount_web) {
            $discount_web = $value->discount_web;
          }
          $order_total += $value->total_price;
          $order_list[$key]['total_price'] = number_format($value->total_price, 2);
        }

        $order_total -= $discount_web;

        $order['total_comma'] = number_format($order['total'] + ($order['total'] * 3 / 100), 2);
        $order['list_data'] = $order_list;

        if ($email) {
          $email_dynamic = EmailDynamic::where('id', 2)->where('status', 1)->first();
          if ($email_dynamic) {
            $replace_detail = $email_dynamic->details;
            $replace_detail = str_replace("{{name}}", $email->name, $replace_detail);
            $replace_detail = str_replace("{{lastname}}", $email->lastname, $replace_detail);
            $replace_detail = str_replace("{{order_no}}", $model->order_no, $replace_detail);
            $replace_detail = str_replace("{{order_date}}", $model->created_at, $replace_detail);
            if ($discount_web == 0) {
              $replace_detail = str_replace("{{discount_web}}", '', $replace_detail);
            } else {
              $replace_detail = str_replace("{{discount_web}}", 'ส่วนลดคูปองเงินสด : -' . number_format($discount_web, 2) . ' THB', $replace_detail);
            }
            $replace_detail = str_replace("{{link}}", '<a href="https://csisociety.com/dashboard/history" target="_blank">https://csisociety.com/dashboard/history</a>', $replace_detail);
            if ($model->payment_type == 1) {
              $replace_detail = str_replace("{{payment_channel}}", 'QR Code', $replace_detail);
            } else if ($model->payment_type == 2) {
              $replace_detail = str_replace("{{payment_channel}}", 'Credit/Debit', $replace_detail);
            } else {
              $replace_detail = str_replace("{{payment_channel}}", 'โอนเงิน', $replace_detail);
            }
            $replace_detail = str_replace("{{order_total}}", number_format($order_total, 2), $replace_detail);
            $order_replace = '';
            foreach ($order_list as $value) {
              if ($value->type == 1) {
                $order_replace .=
                  '<tr>
                      <td style="width:30px;"></td>
                      <td style="width: 140px">
                        <a href="' . $value->course_link . '">
                          <img src="' . $value->image_th . '" style="width: 140px;">
                        </a>
                      </td>
                      <td style="padding: 10px;">
                        <a href="' . $value->course_link . '">
                          <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                        </a>
                      </td>
                      <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                      <td style="width:30px;"></td>
                  </tr>';
              } else {
                $order_replace .=
                  '<tr>
                      <td style="width:30px;"></td>
                      <td style="width: 140px">
                          <img src="' . $value->image_th . '" style="width: 140px;">
                      </td>
                      <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                      ' . $value->subtitle_th . '
                      </td>
                      <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                      <td style="width:30px;"></td>
                  </tr>';
              }
            }
            $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
            $obj = new Dummy();
            $obj['subject'] = $email_dynamic->subject;
            $obj['code'] = $replace_detail;
            $obj['email'] = $email->email;
            $obj['cc'] = [];
            $obj['bcc'] = [];
            AppHelper::instance()->mailTemplate($obj);
          }
        }
      } else if ($model->status == 3) {
        $noti_auto = NotiAuto::where('noti_auto.name', 'reject_payment')->first();
        if ($noti_auto) {
          $noti_log = new NotiAutoLog();
          $noti_log->user_id = $model->user_id;
          $noti_log->noti_auto_id = $noti_auto->id;
          $noti_log->save();
        }

             // ลบข้อมูลที่สร้างมาจาก status 2
            $order_list = UsersOrderList::where('order_id', $model->id)->get();
            foreach ($order_list as $key_list => $val_list) {
              // ลบ UserHistory ที่เกี่ยวข้อง
              $histories = UserHistory::where('order_list_id', $val_list->id)->get();
              foreach ($histories as $history) {
                $history->delete();
              }

              // ลบ SubscriptionLog ที่เกี่ยวข้อง (สำหรับ subscription)
              $subscription_logs = SubscriptionLog::where('order_list_id', $val_list->id)->get();
              foreach ($subscription_logs as $sub_log) {
                $sub_log->delete();
              }
            }
            
      } else {
        $order_list = UsersOrderList::where('order_id', $model->id)->get();
        foreach ($order_list as $key_list => $val_list) {
          $history = UserHistory::where('order_list_id', $val_list->id)->first();
          if ($history) {
            $history->expired = carbon::now();
            $history->save();
          }
        }
      }
    }

    return redirect(route('admin') . '/' . $this->project_url);
  }

  public function sheetlist(Request $request)
  {
    // $data = DB::table('user_order_log')
    //       ->leftJoin('user', 'user.id', 'user_order_log.user_id')
    //       ->leftJoin('course_lesson', 'course_lesson.id', 'user_order_log.media_id')
    //       ->leftJoin('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
    //       ->leftJoin('course', 'course.id', 'user_order_list.course_id')
    //       ->select('user_order_log.id', 'user_order_log.image_slip', 'user_order_log.amount', 'user_order_log.by_bank', DB::raw('DATE_FORMAT(user_order_log.t_date, "%Y-%m-%d %H:%i:%s") as t_date'), 'user_order_log.total_price', 
    //       'user_order_log.payment_type', DB::raw('DATE_FORMAT(user_order_log.created_at, "%Y-%m-%d") as created_at'),'user_order_log.status as order_status', 'user.name as u_name', 'user.lastname as u_lastname', 
    //       'course.title_th as course_name', 'user.email as u_email', 'user.mobile as u_mobile',
    //       DB::raw('(CASE 
    //       WHEN user_order_log.status = "1" THEN "<h1>รอการชำระเงิน</h1>" 
    //       WHEN user_order_log.status = "2" THEN "<h2>สั่งซื้อสำเร็จ</h2>" 
    //       WHEN user_order_log.status = "3" THEN "<h3>ยกเลิกคำสั่งซื้อ</h3>" 
    //       ELSE "<h4>รอการตรวจสอบ</h4>" 
    //       END) AS order_status'), 
    //       DB::raw('(CASE 
    //       WHEN user_order_log.payment_type = "1" THEN "QR code" 
    //       WHEN user_order_log.payment_type = "2" THEN "Credit" 
    //       ELSE "Transfer" 
    //       END) AS payment_type'))
    //       ->groupBy('user_order_log.id')
    //       ->where('user_order_log.status',4)
    //       ->get();

    $data = DB::table('user_order_log')
      ->leftJoin('user', 'user.id', 'user_order_log.user_id')
      ->leftJoin('course_lesson', 'course_lesson.id', 'user_order_log.media_id')
      ->leftJoin('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
      ->leftJoin('course', 'course.id', 'user_order_list.course_id')
      ->select(
        'user_order_log.id',
        'user_order_log.image_slip',
        'user_order_log.amount',
        'user_order_log.by_bank',
        DB::raw('DATE_FORMAT(user_order_log.t_date, "%Y-%m-%d %H:%i:%s") as t_date'),
        'user_order_log.total_price',
        'user_order_log.payment_type',
        'user_order_log.credit_payment_type',
        DB::raw('DATE_FORMAT(user_order_log.created_at, "%Y-%m-%d") as created_at'),
        'user_order_log.status as order_status',
        'user.name as u_name',
        'user.lastname as u_lastname',
        'course.title_th as course_name',
        'user.email as u_email',
        'user.mobile as u_mobile',
        DB::raw('(CASE 
            WHEN user_order_log.status = "1" THEN "<h1>รอการชำระเงิน</h1>" 
            WHEN user_order_log.status = "2" THEN "<h2>สั่งซื้อสำเร็จ</h2>" 
            WHEN user_order_log.status = "3" THEN "<h3>ยกเลิกคำสั่งซื้อ</h3>" 
            ELSE "<h4>รอการตรวจสอบ</h4>" 
        END) AS order_status'),
        DB::raw('(CASE 
            WHEN user_order_log.payment_type = "1" THEN "QR code" 
            WHEN user_order_log.payment_type = "2" AND user_order_log.credit_payment_type = "full" THEN "Credit (Full)" 
            WHEN user_order_log.payment_type = "2" AND user_order_log.credit_payment_type = "installment" THEN "Credit (Installment)" 
            ELSE "Transfer" 
        END) AS payment_type')
      )
      ->groupBy('user_order_log.id')
      ->where('user_order_log.status', 4)
      ->get();
    return response()->json($data);
  }

  public function status_update(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '66') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    }

    if ($request->change_status != '' || $request->change_status != null) {
      $order_array = explode(',', $request->order_array);
      foreach ($order_array as $key_order => $val_order) {
        $order = UsersOrderLog::where('id', $val_order)->first();
        if ($order) {

          $old_status = $order->status;
          $order->status = $request->change_status;

          if ($order->status == 2) {
            $order_list = UsersOrderList::where('order_id', $order->id)->get();
            $user_check = User::where('id', $order->user_id)->first();
            $zoom_fail = 0;
            foreach ($order_list as $val) {
              $course_check = Course::join('course_lesson', 'course_lesson.course_id', 'course.id')
                ->select('course.*', 'course_lesson.zoom_id')
                ->where('course.id', $val->course_id)->where('course.status', 1)
                ->where('course.started_date', '<=', Carbon::now())->first();
              if ($course_check && $course_check->trailer_media == 2 && $user_check) {
                $assign_zoom = AppHelper::instance()->assignZoom($course_check->zoom_id, $user_check->email, $user_check->name, $user_check->lastname);
                if ($assign_zoom->status == 'approve') {
                  $val->registrant_id = $assign_zoom->registrant_id;
                  $val->save();
                } else {
                  $zoom_fail++;
                }
              }
            }
            if ($zoom_fail == 0) {
              $order->receipt_path = env('APP_URL') . '/receipt/' . SimpleEnDeHelper::instance()->encryptString($order->id);
              $order->created_receipt_date = Carbon::now('Asia/Bangkok');
              if ($order->buy_type == 2 && $old_status != $order->status) {
                $gift_log = new UserGiftLog();
                $gift_log->user_id = $order->user_id;
                $gift_log->order_id = $order->id;
                $gift_log->receive_email = $order->receive_email;
                $gift_log->save();

                $email_dynamic = EmailDynamic::where('id', 10)->where('status', 1)->first();
                if ($email_dynamic) {
                  $replace_detail = $email_dynamic->details;
                  $replace_detail = str_replace("{{sender}}", $order->name . ' ' . $order->lastname, $replace_detail);
                  if (isset($order->receive_message) && $order->receive_message != null && $order->receive_message != '' && $order->receive_message != 'null') {
                    $replace_detail = str_replace("{{message}}", 'ข้อความ : ' . $order->receive_message, $replace_detail);
                  } else {
                    $replace_detail = str_replace("{{message}}", '', $replace_detail);
                  }
                  $order_gift = UsersOrderList::where('user_order_list.order_id', $order->id)->get();
                  foreach ($order_gift as $key => $value) {
                    $order_gift[$key]['title_th'] = null;
                    $order_gift[$key]['subtitle_th'] = null;
                    $order_gift[$key]['image_th'] = null;
                    $order_gift[$key]['course_link'] = null;
                    if ($value->type == 1) {
                      $course_log = Course::where('id', $value->course_id)->first();
                      if ($course_log) {
                        $order_gift[$key]['title_th'] = $course_log->title_th;
                        $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                        $order_gift[$key]['image_th'] = $course_log->image_th;
                        $order_gift[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
                      }
                    } else if ($value->type == 2) {
                      $course_log = CourseGroup::where('id', $value->group_id)->first();
                      if ($course_log) {
                        $order_gift[$key]['title_th'] = $course_log->title;
                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                        $order_gift[$key]['image_th'] = $course_log->thumb;
                      }
                    } else if ($value->type == 3) {
                      $course_log = Subscription::where('id', $value->subscription_id)->first();
                      if ($course_log) {
                        $order_gift[$key]['title_th'] = $course_log->title;
                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                        $order_gift[$key]['image_th'] = $course_log->thumb;
                      }
                    }
                  }
                  $order_replace = '';
                  foreach ($order_gift as $value) {
                    if ($value->type == 1) {
                      $order_replace .=
                        '<tr>
                            <td style="width:30px;"></td>
                            <td style="width: 140px">
                                <a href="' . $value->course_link . '">
                                  <img src="' . $value->image_th . '" style="width: 140px;">
                                </a>
                            </td>
                            <td style="padding: 10px;">
                              <a href="' . $value->course_link . '">
                                <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                              </a>
                            </td>
                        </tr>';
                    } else {
                      $order_replace .=
                        '<tr>
                            <td style="width:30px;"></td>
                            <td style="width: 140px">
                                <img src="' . $value->image_th . '" style="width: 140px;">
                            </td>
                            <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                            ' . $value->subtitle_th . '
                            </td>
                        </tr>';
                    }
                  }
                  $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
                  $replace_detail = str_replace("{{link}}", "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id), $replace_detail);
                  $obj = new Dummy();
                  $obj['subject'] = $email_dynamic->subject;
                  $obj['code'] = $replace_detail;
                  $obj['email'] = $order->receive_email;
                  $obj['cc'] = [];
                  $obj['bcc'] = [];
                  AppHelper::instance()->mailTemplate($obj);
                }
                $receive_user = User::where('email', $order->receive_email)->first();
                if ($receive_user) {
                  $noti_log = new NotiAutoLog();
                  $noti_log->user_id = $receive_user->id;
                  $noti_log->image = '/assets/images/qr.png';
                  $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
                  $noti_log->description = 'จากคุณ' . $order->name . ' ' . $order->lastname;
                  $noti_log->link = "https://csisociety.com/gift/" . SimpleEnDeHelper::instance()->encryptString($gift_log->id);
                  $noti_log->save();
                }
              }

              $this->createPeakInvoiceAndReceipt($order);
            } else {
              $order->status = 4;
            }
          } else {
            $order->created_receipt_date = null;
            $gift_log = UserGiftLog::where('user_id', $order->user_id)->where('order_id', $order->id)->first();
            if ($gift_log) {
              $gift_log->delete();
            }
          }

          $order->save();

          if ($order->buy_type == 1 && $old_status != $order->status) {
            $order_history_arr = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
              ->select(
                'user_order_log.id',
                'user_order_log.receive_email',
                'user_order_log.receive_message',
                'user_order_log.user_id',
                'user_order_log.buy_type',
                'user_order_log.created_receipt_date',
                'user_order_list.type',
                'user_order_list.course_id',
                'user_order_list.group_id',
                'user_order_list.subscription_id',
                'user_order_list.id as list_id'
              )
              ->where('user_order_log.status', 2)->where('user_order_log.id', $order->id)->get();
            foreach ($order_history_arr as $order_history) {
              if ($order_history && $order_history->buy_type == 1) {
                if ($order_history->type == 1) {
                  $course = Course::where('id', $order_history->course_id)->first();
                  if ($course) {
                    $history_log = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $order_history->course_id)
                      ->first();
                    if (!$history_log) {
                      $history_log = new UserHistory();
                    }
                    if ($course->trailer_media == 2) {
                      $history_log->zoom_join_url = $course->zoom_join_url;
                      $response['zoom_join_url'] = $history_log->zoom_join_url;
                    }
                    $history_log->get_type = 2;
                    $history_log->order_list_id = $order_history->list_id;
                    $history_log->user_id = $order_history->user_id;
                    $history_log->course_id = $order_history->course_id;
                    $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                    $history_log->save();
                    $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                    if ($del_limit) {
                      $del_limit->delete();
                    }
                    $response['status'] = 'success';
                  }
                } else if ($order_history->type == 2) {
                  $group = CourseGroup::where('id', $order_history->group_id)->first();
                  if ($group) {
                    $course = Course::join('group_log', 'group_log.course_id', 'course.id')->select('course.id', 'course.duration_time', 'course.trailer_media', 'course.zoom_join_url')->where('group_id', $group->id)->get();
                    foreach ($course as $val_course) {
                      $check = UserHistory::where('order_list_id', $order_history->list_id)->where('user_id', $order_history->user_id)->where('course_id', $val_course->id)
                        ->where('expired', Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                      if (!$check) {
                        $history_log = new UserHistory();
                        if ($val_course->trailer_media == 2) {
                          $history_log->zoom_join_url = $val_course->zoom_join_url;
                          $response['zoom_join_url'] = $history_log->zoom_join_url;
                        }
                        $history_log->get_type = 2;
                        $history_log->order_list_id = $order_history->list_id;
                        $history_log->user_id = $order_history->user_id;
                        $history_log->course_id = $val_course->id;
                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                        $history_log->save();
                        $del_limit = UserLearnedTime::where('user_id', $history_log->user_id)->where('course_id', $history_log->course_id)->first();
                        if ($del_limit) {
                          $del_limit->delete();
                        }
                        $response['status'] = 'success';
                      }
                    }
                  }
                } else if ($order_history->type == 3) {
                  $subscription = Subscription::where('id', $order_history->subscription_id)->first();
                  if ($subscription) {
                    $last_sub = SubscriptionLog::where('expired', '>=', Carbon::now())->where('user_id', $order_history->user_id)->where('subscription_id', $subscription->id)->orderby('expired', 'desc')->first();
                    $subscription_log = new SubscriptionLog();
                    $subscription_log->order_list_id = $order_history->list_id;
                    $subscription_log->user_id = $order_history->user_id;
                    $subscription_log->subscription_id = $subscription->id;
                    if ($last_sub) {
                      $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                    } else {
                      $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                    }
                    $subscription_log->save();
                  }
                }
              }
            }
          }

          if ($order->status == 2 && $old_status != $order->status) {
            $email = Users::select('id', 'name', 'lastname', 'email')->where('id', $order->user_id)->first();


            $order['discount'] = 0;
            $order['total'] = 0;
            $order_list = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
              ->select('user_order_list.*', 'user_order_log.web_price as discount_web')
              ->where('user_order_list.order_id', $order->id)->get();

            $order_total = 0;
            $discount_web = 0;
            foreach ($order_list as $key => $value) {
              $order_list[$key]['title_th'] = null;
              $order_list[$key]['subtitle_th'] = null;
              $order_list[$key]['image_th'] = null;
              $order_list[$key]['course_link'] = null;
              if ($value->type == 1) {
                $course_log = Course::where('id', $value->course_id)->first();
                if ($course_log) {
                  $order_list[$key]['title_th'] = $course_log->title_th;
                  $order_list[$key]['subtitle_th'] = $course_log->subtitle_th;
                  $order_list[$key]['image_th'] = $course_log->image_th;
                  $order_list[$key]['course_link'] = 'https://csisociety.com/course/' . $course_log->slug;
                }
              } else if ($value->type == 2) {
                $course_log = CourseGroup::where('id', $value->group_id)->first();
                if ($course_log) {
                  $order_list[$key]['title_th'] = $course_log->title;
                  $order_list[$key]['subtitle_th'] = $course_log->details;
                  $order_list[$key]['image_th'] = $course_log->thumb;
                }
              }
              if ($value->discount_web != null && $value->discount_web != '' && $value->discount_web != 0 && $value->discount_web != 'null' && $value->discount_web > $discount_web) {
                $discount_web = $value->discount_web;
              }
              $order_total += $value->total_price;
              $order_list[$key]['total_price'] = number_format($value->total_price, 2);
            }

            $order_total -= $discount_web;

            $order['total_comma'] = number_format($order['total'] + ($order['total'] * 3 / 100), 2);
            $order['list_data'] = $order_list;

            if ($email) {
              $email_dynamic = EmailDynamic::where('id', 2)->where('status', 1)->first();
              if ($email_dynamic) {
                $replace_detail = $email_dynamic->details;
                $replace_detail = str_replace("{{name}}", $email->name, $replace_detail);
                $replace_detail = str_replace("{{lastname}}", $email->lastname, $replace_detail);
                $replace_detail = str_replace("{{order_no}}", $order->order_no, $replace_detail);
                $replace_detail = str_replace("{{order_date}}", $order->created_at, $replace_detail);
                if ($discount_web == 0) {
                  $replace_detail = str_replace("{{discount_web}}", '', $replace_detail);
                } else {
                  $replace_detail = str_replace("{{discount_web}}", 'ส่วนลดคูปองเงินสด : -' . number_format($discount_web, 2) . ' THB', $replace_detail);
                }
                $replace_detail = str_replace("{{link}}", '<a href="https://csisociety.com/dashboard/history" target="_blank">https://csisociety.com/dashboard/history</a>', $replace_detail);
                if ($order->payment_type == 1) {
                  $replace_detail = str_replace("{{payment_channel}}", 'QR Code', $replace_detail);
                } else if ($order->payment_type == 2) {
                  $replace_detail = str_replace("{{payment_channel}}", 'Credit/Debit', $replace_detail);
                } else {
                  $replace_detail = str_replace("{{payment_channel}}", 'โอนเงิน', $replace_detail);
                }
                $replace_detail = str_replace("{{order_total}}", number_format($order_total, 2), $replace_detail);
                $order_replace = '';
                foreach ($order_list as $value) {
                  if ($value->type == 1) {
                    $order_replace .=
                      '<tr>
                          <td style="width:30px;"></td>
                          <td style="width: 140px">
                            <a href="' . $value->course_link . '">
                              <img src="' . $value->image_th . '" style="width: 140px;">
                            </a>
                          </td>
                          <td style="padding: 10px;">
                            <a href="' . $value->course_link . '">
                              <b>' . $value->title_th . '</b><br>' . $value->subtitle_th . '
                            </a>
                          </td>
                          <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                          <td style="width:30px;"></td>
                      </tr>';
                  } else {
                    $order_replace .=
                      '<tr>
                          <td style="width:30px;"></td>
                          <td style="width: 140px">
                              <img src="' . $value->image_th . '" style="width: 140px;">
                          </td>
                          <td style="padding: 10px;"><b>' . $value->title_th . '</b><br>
                          ' . $value->subtitle_th . '
                          </td>
                          <td style="width: 100px;text-align: right;"><b>' . $value->total_price . ' THB</b></td>
                          <td style="width:30px;"></td>
                      </tr>';
                  }
                }
                $replace_detail = str_replace("{{order_list}}", $order_replace, $replace_detail);
                $obj = new Dummy();
                $obj['subject'] = $email_dynamic->subject;
                $obj['code'] = $replace_detail;
                $obj['email'] = $email->email;
                $obj['cc'] = [];
                $obj['bcc'] = [];
                AppHelper::instance()->mailTemplate($obj);
              }
            }
          } else if ($order->status == 3) {
            $noti_auto = NotiAuto::where('noti_auto.name', 'reject_payment')->first();
            if ($noti_auto) {
              $noti_log = new NotiAutoLog();
              $noti_log->user_id = $order->user_id;
              $noti_log->noti_auto_id = $noti_auto->id;
              $noti_log->save();
            }

             // ลบข้อมูลที่สร้างมาจาก status 2
            $order_list = UsersOrderList::where('order_id', $order->id)->get();
            foreach ($order_list as $key_list => $val_list) {
              // ลบ UserHistory ที่เกี่ยวข้อง
              $histories = UserHistory::where('order_list_id', $val_list->id)->get();
              foreach ($histories as $history) {
                $history->delete();
              }

              // ลบ SubscriptionLog ที่เกี่ยวข้อง (สำหรับ subscription)
              $subscription_logs = SubscriptionLog::where('order_list_id', $val_list->id)->get();
              foreach ($subscription_logs as $sub_log) {
                $sub_log->delete();
              }
            }
          } else {
            $order_list = UsersOrderList::where('order_id', $order->id)->get();
            foreach ($order_list as $key_list => $val_list) {
              $history = UserHistory::where('order_list_id', $val_list->id)->first();
              if ($history) {
                $history->expired = carbon::now();
                $history->save();
              }
            }
          }
        }
      }
    }
    return redirect(route('admin') . '/' . $this->project_url);
  }

  public function filter(Request $request)
  {
    if (Auth::user()->level != '99' && Auth::user()->level != '66') {
      return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
    } else {
      if ($request->name == 'order_filter') {

        $data = DB::table('user_order_log');
        $data->join('user', 'user.id', 'user_order_log.user_id');
        $data->select(
          'user_order_log.*',
          'user.name as u_name',
          'user.lastname as u_lastname',
          'user.email',
          'user.mobile'
        );
        $data->where('user_order_log.is_free', '=', 2);
        $data->where('user_order_log.status', 4);
        $data->get();

        if (isset($request->payment)) {
          if ($request->payment != '9') {
            $data->where('user_order_log.payment_type', '=', $request->payment);
          } else {
          }
        }

        if (isset($request->order_no)) {
          $data->where('user_order_log.order_no', 'like', '%' . $request->order_no . '%');
        }

        if (isset($request->user_email)) {
          $data->where('user.email', 'like', '%' . $request->user_email . '%');
        }

        if (isset($request->created_at_start) && isset($request->created_at_to)) {
          $from = Carbon::parse($request->created_at_start)->toDateString();
          $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
          $data->whereBetween('user_order_log.created_at', [$from, $to]);
        }
        $data = $data->get();

        $ex_array[] = array(
          'หมายเลขสั่งซื้อ' => 'หมายเลขสั่งซื้อ',
          'รูปแบบการซื้อ' => 'รูปแบบการซื้อ',

          'ชื่อ - นามสกุล' => 'ชื่อ - นามสกุล',
          'อีเมล์' => 'อีเมล์',
          'เบอร์โทรศัพท์' => 'เบอร์โทรศัพท์',

          'ชื่อ - นามสกุล ผู้ซื้อ' => 'ชื่อ - นามสกุล ผู้ซื้อ',
          'อีเมล์ ผู้ซื้อ' => 'อีเมล์ ผู้ซื้อ',
          'เบอร์โทรศัพท์ ผู้ซื้อ' => 'เบอร์โทรศัพท์ ผู้ซื้อ',
          'ที่อยู่ ผู้ซื้อ' => 'ที่อยู่ ผู้ซื้อ',
          'ตำบล ผู้ซื้อ' => 'ตำบล ผู้ซื้อ',
          'อำเภอ ผู้ซื้อ' => 'อำเภอ ผู้ซื้อ',
          'จังหวัด ผู้ซื้อ' => 'จังหวัด ผู้ซื้อ',
          'รหัสไปรษณีย์ ผู้ซื้อ' => 'รหัสไปรษณีย์ ผู้ซื้อ',

          'ประเภทการจ่ายเงิน' => 'ประเภทการจ่ายเงิน',
          'Reference' => 'Reference',
          'ชำระเงินโดย' => 'ชำระเงินโดย',
          'วันที่และเวลา' => 'วันที่และเวลา',
          'จำนวนเงิน' => 'จำนวนเงิน',
          'ราคาส่วนลด' => 'ราคาส่วนลด',
          'ราคาสุทธิ' => 'ราคาสุทธิ',
          'สถานะการจ่ายเงิน' => 'สถานะการจ่ายเงิน',
          'วันที่เก็บข้อมูล' => 'วันที่เก็บข้อมูล',
        );

        foreach ($data as $key => $e) {

          if ($e->payment_type == 1) {
            $e->payment_type = 'QR Code';
          } else if ($e->payment_type == 2) {
            $e->payment_type = 'Credit';
          } else {
            $e->payment_type = 'transfering';
          }

          if ($e->reference != null || $e->reference != '') {
            $e->reference = $e->reference;
          } else {
            $e->reference = 'ไม่มีข้อมูล';
          }

          if ($e->receipt_type == 1) {
            $e->receipt_type = 'ธรรมดา';
          } else {
            $e->receipt_type = 'นิติบุคคล';
          }

          if ($e->receipt == 1) {
            $e->receipt = 'ต้องการ';
          } else {
            $e->receipt = 'ไม่ต้องการ';
          }

          if ($e->status == 1) {
            $e->status = 'รอการชำระเงิน';
          } else if ($e->status == 2) {
            $e->status = 'สั่งซื้อสำเร็จ';
          } else if ($e->status == 3) {
            $e->status = 'ยกเลิกคำสั่งซื้อ';
          } else {
            $e->status = 'รอการตรวจสอบ';
          }

          $fullprice = UsersOrderList::where('order_id', $e->id)->sum('price');
          if ($fullprice == 0) {
            $fullprice = strval($fullprice);
          } else {
            $fullprice = $fullprice;
          }

          $dis_price = UsersOrderList::where('order_id', $e->id)->sum('discount_value');
          if ($dis_price == 0 && $e->web_code == null) {
            $disprice = strval($dis_price);
          } else {
            $disprice = $dis_price + $e->web_price;
          }

          $total = UsersOrderList::where('order_id', $e->id)->sum('total_price');
          if ($total == 0 && $e->web_code == null) {
            $total = strval($total);
          } else {
            $total = $total - $e->web_price;
          }

          if ($e->buy_type == 1) {
            $e->buy_type = 'แบบปกติ';
          } else {
            $e->buy_type = 'ส่งของขวัญ';
          }

          $ex_array[] = array(
            'หมายเลขสั่งซื้อ' => $e->order_no,
            'รูปแบบการซื้อ' => $e->buy_type,
            'ชื่อ - นามสกุล' => $e->cus_name . ' ' . $e->u_lastname,
            'อีเมล์' => $e->email,
            'เบอร์โทรศัพท์' => $e->mobile,

            'ชื่อ - นามสกุล ผู้ซื้อ' => $e->u_name . ' ' . $e->cus_lastname,
            'อีเมล์ ผู้ซื้อ' => $e->cus_email,
            'เบอร์โทรศัพท์ ผู้ซื้อ' => $e->cus_mobile,
            'ที่อยู่ ผู้ซื้อ' => $e->doc_address,
            'ตำบล ผู้ซื้อ' => $e->doc_subdistrict,
            'อำเภอ ผู้ซื้อ' => $e->doc_district,
            'จังหวัด ผู้ซื้อ' => $e->doc_province,
            'รหัสไปรษณีย์ ผู้ซื้อ' => $e->doc_postcode,

            'ประเภทการจ่ายเงิน' => $e->payment_type,
            'Reference' => $e->reference,
            'ชำระเงินโดย' => $e->by_bank,
            'วันที่และเวลา' => $e->t_date,
            'จำนวนเงิน' => $fullprice,
            'ราคาส่วนลด' => $disprice,
            'ราคาสุทธิ' => $total,
            'สถานะการจ่ายเงิน' => $e->status,
            'วันที่เก็บข้อมูล' => $e->created_at,
          );
        }
      }

      $export_type = isset($request->export_type) ? $request->export_type : 'excel';
      if (isset($ex_array)) {
        if ($export_type == 'excel') {
          $export = new TempExport($ex_array);

          return FacadesExcel::download($export, $request->filename . '.xlsx');
        } else {
          $headers = array(
            "Content-Encoding" => 'UTF-8',
            "Content-type" => "text/csv; charset=UTF-8",
            "Content-Disposition" => "attachment; " . $request->filename . ".csv",
            "Pragma" => "no-cache",
            "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
            "Expires" => "0"
          );
          echo "\xEF\xBB\xBF";

          $keys = array_keys($ex_array[0]);
          $callback = function () use ($ex_array, $keys) {
            $fp = fopen('php://output', 'w');
            fputcsv($fp, $keys);
            foreach ($ex_array as $fields) {
              fputcsv($fp, $fields);
            }
            fclose($fp);
          };

          return response()->stream($callback, 200, $headers);
        }
      }
    }
  }


  public function destroy(Request $request)
  {
    $response = new Dummy();
    $response['status'] = 'false';
    $response['model_pos'] = array();

    $model = UsersOrderLog::find($request->id);
    if ($model) {

      $list = UsersOrderList::where('order_id', $model->id)->get();
      foreach ($list as $key_list => $val_list) {
        $history = UserHistory::where('order_list_id', $val_list->id)->first();
        if ($history) {
          $history->expired = carbon::now();
          $history->save();
        }
        $val_list->delete();
      }

      $model->delete();
      $response['status'] = 'success';
    }

    return json_decode($response, true);
  }


  // // เพิ่มเมธอดใหม่สำหรับสร้าง invoice และ receipt
  // private function createPeakInvoiceAndReceipt($order)
  // {
  //   try {
  //     $peakService = new PeakService();

  //     // ดึงข้อมูล order list
  //     $orderList = UsersOrderList::where('order_id', $order->id)->first();
  //     if (!$orderList) {
  //       return;
  //     }

  //     // ดึงข้อมูล user
  //     $user = Users::find($order->user_id);
  //     if (!$user) {
  //       return;
  //     }

  //     // ดึงข้อมูล course/product
  //     $course = null;
  //     if ($orderList->course_id) {
  //       $course = Course::find($orderList->course_id);
  //     }

  //     if (!$course) {
  //       return;
  //     }

  //     // เตรียมข้อมูลสำหรับสร้าง contact
  //     $contactData = [
  //       'name' => $user->name . ' ' . $user->lastname,
  //       'type' => 'Customer',
  //       'address' => $order->address ?? '',
  //       'tax_number' => $user->tax_number ?? '',
  //       'sub_district' => $order->subdistrict ?? '',
  //       'district' => $order->district ?? '',
  //       'province' => $order->province ?? '',
  //       'post_code' => $order->postcode ?? '',
  //       'email' => $user->email,
  //       'first_name' => $user->name,
  //       'last_name' => $user->lastname,
  //       'phone' => $user->mobile ?? '',
  //       'branch_code' => '00000'
  //     ];

  //     // สร้าง contact (หรือใช้ที่มีอยู่)
  //     $contactId = $user->peak_contact_id;
  //     if (!$contactId) {
  //       $contactResult = $peakService->createContact($contactData);
  //       if ($contactResult['status']) {
  //         $contactId = $contactResult['id'];
  //         $user->peak_contact_id = $contactId;
  //         $user->peak_sync_status = 2;
  //         $user->peak_last_sync = Carbon::now();
  //         $user->save();
  //       } else {
  //         return;
  //       }
  //     }

  //     // สร้าง product (หรือใช้ที่มีอยู่)
  //     $productCode = $course->peak_product_code;
  //     if (!$productCode) {
  //       $productData = [
  //         'name' => $course->title_th,
  //         'price' => $orderList->price,
  //         'description' => $course->subtitle_th ?? ''
  //       ];

  //       $productResult = $peakService->createProduct($productData);
  //       if ($productResult['status']) {
  //         $productCode = $productResult['code'];
  //         $course->peak_product_id = $productResult['id'];
  //         $course->peak_product_code = $productCode;
  //         $course->peak_sync_status = 2;
  //         $course->peak_last_sync = Carbon::now();
  //         $course->save();
  //       } else {
  //         return;
  //       }
  //     }

  //     // เตรียมข้อมูลสำหรับสร้าง invoice
  //     $invoiceData = [
  //       'contact_id' => $contactId,
  //       'product_code' => $productCode,
  //       'quantity' => 1,
  //       'subtotal' => $orderList->total_price,
  //       'discount' => $orderList->discount_value ?? 0,
  //       'created_at' => $order->created_at,
  //       'is_tax_invoice' => 1
  //     ];

  //     // สร้าง invoice
  //     $invoiceResult = $peakService->createInvoice($invoiceData);
  //     if ($invoiceResult['status']) {
  //       $order->peak_invoice_id = $invoiceResult['id'];
  //       $order->peak_invoice_link = $invoiceResult['invoices_link'] ?? null;

  //       // เตรียมข้อมูลสำหรับสร้าง receipt
  //       $receiptData = [
  //         'contact_id' => $contactId,
  //         'product_code' => $productCode,
  //         'quantity' => 1,
  //         'total' => $orderList->total_price,
  //         'tax_status' => 1
  //       ];

  //       // สร้าง receipt
  //       $receiptResult = $peakService->createReceipt($receiptData);
  //       if ($receiptResult['status']) {
  //         $order->peak_receipt_id = $receiptResult['id'];
  //         $order->peak_receipt_link = $receiptResult['receipt_link'] ?? null;
  //         $order->peak_sync_status = 2;
  //         $order->peak_last_sync = Carbon::now();
  //       } else {
  //         $order->peak_sync_status = 3;
  //         $order->peak_error_message = $receiptResult['message'];
  //       }
  //     } else {
  //       $order->peak_sync_status = 3;
  //       $order->peak_error_message = $invoiceResult['message'];
  //     }

  //     $order->save();
  //   } catch (\Exception $e) {
  //     $order->peak_sync_status = 3;
  //     $order->peak_error_message = $e->getMessage();
  //     $order->peak_last_sync = Carbon::now();
  //     $order->save();
  //   }
  // }

  private function createPeakInvoiceAndReceipt($order)
  {
    try {
      $peakService = new PeakService();

      // ดึงข้อมูล order list
      $orderList = UsersOrderList::where('order_id', $order->id)->first();
      if (!$orderList) {
        return;
      }

      // ดึงข้อมูล user
      $user = Users::find($order->user_id);
      if (!$user) {
        return;
      }

      // ดึงข้อมูล product ตาม type ของ order
      $product = null;
      $productCode = null;
      $productName = '';
      $productPrice = $orderList->price;

      if ($orderList->type == 1) {
        // Course
        $product = Course::find($orderList->course_id);
        if ($product) {
          $productCode = $product->peak_product_code;
          $productName = $product->title_th;
        }
      } elseif ($orderList->type == 3) {
        // Subscription
        $product = Subscription::find($orderList->subscription_id);
        if ($product) {
          $productCode = $product->peak_product_code;
          $productName = $product->title;
        }
      }

      if (!$product) {
        return;
      }

      // เตรียมข้อมูลสำหรับสร้าง contact
      $contactData = [
        'name' => $user->name . ' ' . $user->lastname,
        'type' => 'Customer',
        'address' => $order->address ?? '',
        'tax_number' => $user->tax_number ?? '',
        'sub_district' => $order->subdistrict ?? '',
        'district' => $order->district ?? '',
        'province' => $order->province ?? '',
        'post_code' => $order->postcode ?? '',
        'email' => $user->email,
        'first_name' => $user->name,
        'last_name' => $user->lastname,
        'phone' => $user->mobile ?? '',
        'branch_code' => '00000'
      ];

      // สร้าง contact (หรือใช้ที่มีอยู่)
      $contactId = $user->peak_contact_id;
      if (!$contactId) {
        $contactResult = $peakService->createContact($contactData);
        if ($contactResult['status']) {
          $contactId = $contactResult['id'];
          $user->peak_contact_id = $contactId;
          $user->peak_sync_status = 2;
          $user->peak_last_sync = Carbon::now();
          $user->save();
        } else {
          return;
        }
      }

      // สร้าง product (หรือใช้ที่มีอยู่)
      if (!$productCode) {
        $productData = [
          'name' => $productName,
          'price' => $productPrice,
          'description' => $product->subtitle_th ?? $product->details ?? $productName
        ];

        $productResult = $peakService->createProduct($productData);
        if ($productResult['status']) {
          $productCode = $productResult['code'];
          $product->peak_product_id = $productResult['id'];
          $product->peak_product_code = $productCode;
          $product->peak_sync_status = 2;
          $product->peak_last_sync = Carbon::now();
          $product->save();
        } else {
          return;
        }
      }

      // เตรียมข้อมูลสำหรับสร้าง invoice
      $invoiceData = [
        'contact_id' => $contactId,
        'product_code' => $productCode,
        'quantity' => 1,
        'subtotal' => $orderList->total_price,
        'discount' => $orderList->discount_value ?? 0,
        'created_at' => $order->created_at,
        'is_tax_invoice' => 1
      ];

      // สร้าง invoice
      $invoiceResult = $peakService->createInvoice($invoiceData);
      if ($invoiceResult['status']) {
        $order->peak_invoice_id = $invoiceResult['id'];
        $order->peak_invoice_link = $invoiceResult['invoices_link'] ?? null;

        // เตรียมข้อมูลสำหรับสร้าง receipt
        $receiptData = [
          'contact_id' => $contactId,
          'product_code' => $productCode,
          'quantity' => 1,
          'total' => $orderList->total_price,
          'tax_status' => 1
        ];

        // สร้าง receipt
        $receiptResult = $peakService->createReceipt($receiptData);
        if ($receiptResult['status']) {
          $order->peak_receipt_id = $receiptResult['id'];
          $order->peak_receipt_link = $receiptResult['receipt_link'] ?? null;
          $order->peak_sync_status = 2;
          $order->peak_last_sync = Carbon::now();
        } else {
          $order->peak_sync_status = 3;
          $order->peak_error_message = $receiptResult['message'];
        }
      } else {
        $order->peak_sync_status = 3;
        $order->peak_error_message = $invoiceResult['message'];
      }

      $order->save();
    } catch (\Exception $e) {
      $order->peak_sync_status = 3;
      $order->peak_error_message = $e->getMessage();
      $order->peak_last_sync = Carbon::now();
      $order->save();
    }
  }
}
