<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\BulkCoupon;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\BulkCouponCode;
use App\Models\Core\BulkCouponExCate;
use App\Models\Core\BulkCouponExCourse;
use App\Models\Core\BulkCouponGroup;
use App\Models\Core\BulkCouponInCate;
use App\Models\Core\BulkCouponInCourse;
use App\Models\Core\BulkCouponSub;
use App\Models\Core\BulkSponsorLog;
use App\Models\Core\SubscriptionExCate;
use App\Models\Core\SubscriptionExCourse;
use App\Models\Core\SubscriptionInCate;
use Maatwebsite\Excel\Facades\Excel;


class BulkCouponController extends Controller
{
    public $prefix = 'bulk_coupon';
    public $project_url = 'bulk_coupon';
    public $project_layout = 'admin.Lrk.bulk_coupon';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new BulkCoupon();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = BulkCoupon::find($id);
      $article_id = $model->id;
      if($model){
        $obj = array(
          'type'=>'view',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.view', compact('model','obj','article_id'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = BulkCoupon::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['coupons_bulk_gen.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['coupons_bulk_gen.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['coupons_bulk_gen.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['coupons_bulk_gen.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('coupons_bulk_gen')
      ->select('coupons_bulk_gen.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('coupons_bulk_gen')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column
      
      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new BulkCoupon;

      $model->coupon_type = 1;

      $model->no = isset($request->no) ? AppHelper::instance()->cleanInput($request->no) : 0;
      $model->discount_type = isset($request->discount_type) ? $request->discount_type : null;
      $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : null;
      $model->prefix = isset($request->prefix) ? AppHelper::instance()->cleanInput($request->prefix) : null;
      
      $model->min_spend = isset($request->min_spend) ? AppHelper::instance()->cleanInput($request->min_spend) : null;
      $model->max_spend = isset($request->max_spend) ? AppHelper::instance()->cleanInput($request->max_spend) : null;

      // $model->individual_use_only = isset($request->individual_use_only) ? $request->individual_use_only : 2;
      $model->exclude_sale_item = isset($request->exclude_sale_item) ? $request->exclude_sale_item : 2;

      $model->allow_email = isset($request->allow_email) ? AppHelper::instance()->cleanInput($request->allow_email) : null;
      
      $model->limitation_coupon = isset($request->limitation_coupon) ? AppHelper::instance()->cleanInput($request->limitation_coupon) : null;
      $model->limitation_user = isset($request->limitation_user) ? AppHelper::instance()->cleanInput($request->limitation_user) : null;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_date)->format('Y-m-d H:i:s') : null;

      $model->select_cate_option = isset($request->select_cate_option) ? $request->select_cate_option : 99;

      $model->excluding_course = is_array($request->excluding_course) ? implode(",",$request->excluding_course) : null;
      $model->only_cate = is_array($request->only_cate) ? implode(",",$request->only_cate) : null;
      $model->excluding_cate = is_array($request->excluding_cate) ? implode(",",$request->excluding_cate) : null;
      // $model->sub_tag = is_array($request->sub_tag) ? implode(",",$request->sub_tag) : null;
      // $model->group_tag = is_array($request->group_tag) ? implode(",",$request->group_tag) : null;

      $model->status = $request->status;

      $model->save();

      if($model->no != 0 && $model->prefix != '' && $model->prefix != null){
        for($i = 0; $i < $model->no; $i++){
          $code_log = new BulkCouponCode;
          $code_log->coupons_bulk_id = $model->id;
          $code_log->code = $model->prefix.AppHelper::instance()->generateRandomString(6);
          $code_log->status = 1;
          $code_log->save();
        }
      }
  
      if(is_array($request->excluding_course)){ //ถ้า tags มีค่า
        foreach($request->excluding_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new BulkCouponExCourse();
          $log->coupons_bulk_id = $model->id;
          $log->course_id = $value;
          $log->save();
        }
      }

      if(is_array($request->excluding_cate)){ //ถ้า tags มีค่า
        foreach($request->excluding_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new BulkCouponExCate();
          $log->coupons_bulk_id = $model->id;
          $log->cate_id = $value;
          $log->save();
        }
      }

      if(is_array($request->only_cate)){ //ถ้า tags มีค่า
        foreach($request->only_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new BulkCouponInCate();
          $log->coupons_bulk_id = $model->id;
          $log->cate_id = $value;
          $log->save();
        }
      }

      if($model->no > 0){
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }else{
        return redirect(route('admin').'/bulk_coupon/'.$model->id.'/edit/')->with('success', 'Data has been update');
      }
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = BulkCoupon::find($id);

      // $model->coupon_type = isset($request->coupon_type) ? $request->coupon_type : null;

      // $model->no = isset($request->no) ? AppHelper::instance()->cleanInput($request->no) : null;
      $model->discount_type = isset($request->discount_type) ? $request->discount_type : null;
      $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : null;
      // $model->prefix = isset($request->prefix) ? AppHelper::instance()->cleanInput($request->prefix) : null;
      
      $model->min_spend = isset($request->min_spend) ? AppHelper::instance()->cleanInput($request->min_spend) : null;
      $model->max_spend = isset($request->max_spend) ? AppHelper::instance()->cleanInput($request->max_spend) : null;

      // $model->individual_use_only = isset($request->individual_use_only) ? $request->individual_use_only : 2;
      $model->exclude_sale_item = isset($request->exclude_sale_item) ? $request->exclude_sale_item : 2;

      $model->allow_email = isset($request->allow_email) ? AppHelper::instance()->cleanInput($request->allow_email) : null;
      
      $model->limitation_coupon = isset($request->limitation_coupon) ? AppHelper::instance()->cleanInput($request->limitation_coupon) : null;
      $model->limitation_user = isset($request->limitation_user) ? AppHelper::instance()->cleanInput($request->limitation_user) : null;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_date)->format('Y-m-d H:i:s') : null;

      if($model->coupon_type == 1){

        $old_tags = explode(',', $model->excluding_course); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
        $model->excluding_course = is_array($request->excluding_course) ? implode(",",$request->excluding_course) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
        if(is_array($request->excluding_course)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }
  
          $diff_tags = array_diff($old_tags, $request->excluding_course); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร
  
          if(count(array_diff($old_tags, $request->excluding_course))>0 || count(array_diff($request->excluding_course, $old_tags))>0){
            $log = BulkCouponExCourse::where('coupons_bulk_id', $model->id);
            $log->delete();
  
            foreach($request->excluding_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
              $log = new BulkCouponExCourse();
              $log->coupons_bulk_id = $model->id;
              $log->course_id = $value;
              $log->save();
            }
          }
        }else{
          $log = BulkCouponExCourse::where('coupons_bulk_id', $model->id);
          $log->delete();
        }

        $model->select_cate_option = isset($request->select_cate_option) ? $request->select_cate_option : 1;

        if($model->select_cate_option == 1){
          $old_tags = explode(',', $model->only_cate); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
          $model->only_cate = is_array($request->only_cate) ? implode(",",$request->only_cate) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
          if(is_array($request->only_cate)){
            if(!is_array($old_tags)){
              $old_tags = array();
            }
    
            $diff_tags = array_diff($old_tags, $request->only_cate); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร
    
            if(count(array_diff($old_tags, $request->only_cate))>0 || count(array_diff($request->only_cate, $old_tags))>0){
              $log = BulkCouponInCate::where('coupons_bulk_id', $model->id);
              $log->delete();
    
              foreach($request->only_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
                $log = new BulkCouponInCate();
                $log->coupons_bulk_id = $model->id;
                $log->cate_id = $value;
                $log->save();
              }
            }
          }else{
            $log = BulkCouponInCate::where('coupons_bulk_id', $model->id);
            $log->delete();
          }

          $model->excluding_cate = null;
  
          $ex_cate = BulkCouponExCate::where('coupons_bulk_id', $model->id)->delete();
          // $bulk_sub = BulkCouponSub::where('bulk_id', $model->id)->delete();
          // $bulk_group = BulkCouponGroup::where('bulk_id', $model->id)->delete();
  
        }else{
          $old_tags = explode(',', $model->excluding_cate); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
          $model->excluding_cate = is_array($request->excluding_cate) ? implode(",",$request->excluding_cate) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
          if(is_array($request->excluding_cate)){
            if(!is_array($old_tags)){
              $old_tags = array();
            }
    
            $diff_tags = array_diff($old_tags, $request->excluding_cate); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร
    
            if(count(array_diff($old_tags, $request->excluding_cate))>0 || count(array_diff($request->excluding_cate, $old_tags))>0){
              $log = BulkCouponExCate::where('coupons_bulk_id', $model->id);
              $log->delete();
    
              foreach($request->excluding_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
                $log = new BulkCouponExCate();
                $log->coupons_bulk_id = $model->id;
                $log->cate_id = $value;
                $log->save();
              }
            }
          }else{
            $log = BulkCouponExCate::where('coupons_bulk_id', $model->id);
            $log->delete();
          }
  
          $model->only_cate = null;

          $in_cate = BulkCouponInCate::where('coupons_bulk_id', $model->id)->delete();
          // $bulk_sub = BulkCouponSub::where('bulk_id', $model->id)->delete();
          // $bulk_group = BulkCouponGroup::where('bulk_id', $model->id)->delete();
        }
      }      

      $model->status = $request->status;
      $model->save();



      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = BulkCoupon::find($request->id);

      $code_log = BulkCouponCode::where('coupons_bulk_id', $model->id)->delete();
      $ex_course = BulkCouponExCourse::where('coupons_bulk_id', $model->id)->delete();
      $ex_cate = BulkCouponExCate::where('coupons_bulk_id', $model->id)->delete();
      $in_cate = BulkCouponInCate::where('coupons_bulk_id', $model->id)->delete();
      // $bulk_sub = BulkCouponSub::where('bulk_id', $model->id)->delete();
      // $bulk_group = BulkCouponGroup::where('bulk_id', $model->id)->delete();

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = BulkCoupon::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
