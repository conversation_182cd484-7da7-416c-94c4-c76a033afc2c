<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CourseLessonLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use Maatwebsite\Excel\Facades\Excel;


class CourseLessonController extends Controller
{
    public $prefix = 'course_lesson';
    public $project_url = 'course_lesson';
    public $project_layout = 'admin.Lrk.course_lesson';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      $model = CourseLessonLog::find($request->id);

      CourseLessonLog::where('course_id',$model->course_id)->where('position','>',$model->position)
      ->update(['position' => DB::raw('position - 1')]);

      $model->delete();
      
      $course = Course::where('id', $model->course_id)->first();
      if($course){
        $course_duration = CourseLessonLog::where('course_id', $model->id)->where('status', 1)->sum(DB::raw('TIME_TO_SEC(duration)'));
        $course->course_duration = $course_duration;
        $course->save();
      }

      return response()->json();
    }

    public function status(Request $request)
    {
      $model = CourseLessonLog::find($request->id);
      
      if($model->status=='1'){
        $model->status = '2';
        $model->save();

        $course_duration_1 = CourseLessonLog::where('course_id', $model->course_id)
        ->where('status', 1)->sum(DB::raw('TIME_TO_SEC(duration)'));
        $course = Course::where('id',$model->course_id)->first();
        if($course){
          $course->course_duration = $course_duration_1;
          $course->save();
        }
      }
      else{
        $model->status = '1';
        $model->save();

        $course_duration_1 = CourseLessonLog::where('course_id', $model->course_id)
        ->where('status', 1)->sum(DB::raw('TIME_TO_SEC(duration)'));
        $course = Course::where('id',$model->course_id)->first();
        if($course){
          $course->course_duration = $course_duration_1;
          $course->save();
        }
      }

      return response()->json($course);
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course_lesson.status', $value['search']['value']]);
          }else if($value['data']=='title_th'){
            array_push($where_search, ['course_lesson.title_th', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_lesson.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('course_lesson')
      ->select('course_lesson.*')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_lesson')
      ->where('course_id',$request->course_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $exam_log = DB::table('user_exam_log')->where('user_exam_log.course_id',$obj->course_id)->where('user_exam_log.lesson_id',$obj->id)
        ->groupBy('user_exam_log.user_id')->get();
        
        $exam_count = count($exam_log);
        if($exam_count > 0){
          $obj['exam_count'] = '
          <a href="'.url('/csisocietyadmin').'/user_exam_log/'.$obj->course_id.'/'.$obj->id.'">
            จำนวน '. $exam_count .' ราย
          </a>';
        }else{
          $obj['exam_count'] = '0 ราย';
        }

        $exam = DB::table('course_exam')->where('course_exam.course_id',$obj->course_id)->where('course_exam.lesson_id',$obj->id)->count();
        if($exam>0){
          $obj['exam'] = '
          <a href="'.url('/csisocietyadmin').'/exam_log/'.$obj->course_id.'/'.$obj->id.'">
            จำนวน '. $exam .'
          </a>';
        }else{
          $obj['exam'] = ' 
          <a href="/csisocietyadmin/exam_log/'.$obj->course_id.'/'.$obj->id.'/create" class="btn btn-success">
            <i class="glyphicon glyphicon-edit"></i>
            สร้าง
          </a>';
        }

        $log_learning = DB::table('user_learning_log')->where('user_learning_log.course_id',$obj->course_id)->where('user_learning_log.lesson_id',$obj->id)
        ->count();
        if($log_learning > 0){
          $obj['log_learning'] = '
          <a href="'.url('/csisocietyadmin').'/learning_log/'.$obj->course_id.'/'.$obj->id.'">
            จำนวน '. $log_learning .' ราย
          </a>';
        }else{
          $obj['log_learning'] = '0 ราย';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem2('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem2('.$obj->id.',\''.route('admin').'/course_lesson/delete\',\'datatable_lesson\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new CourseLessonLog; 

      $model->course_id = $request->course_id;
  
      $model->title_th = isset($request->gallery_modal_title_th_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_title_th_2) : null;
      // $model->title_en = isset($request->gallery_modal_title_en_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_title_en_2) : null;

      $model->lesson_key = isset($request->gallery_modal_lesson_key_2) ? AppHelper::instance()->cleanInputBr($request->gallery_modal_lesson_key_2) : null;
      $model->email = isset($request->gallery_modal_email_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_email_2) : null;
      $model->link = isset($request->gallery_modal_link_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_link_2) : null;
      $model->link_2 = isset($request->gallery_modal_link_2_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_link_2_2) : null;
      $model->link_3 = isset($request->gallery_modal_link_2_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_link_2_3) : null;
      $model->price = isset($request->gallery_modal_price) ? AppHelper::instance()->cleanInput($request->gallery_modal_price) : 0;
      $model->point = isset($request->gallery_modal_point) ? AppHelper::instance()->cleanInput($request->gallery_modal_point) : 0;
      $model->exam_type = isset($request->gallery_modal_exam_type) ? AppHelper::instance()->cleanInput($request->gallery_modal_exam_type) : 1;
      if($model->exam_type == 1){
        $model->exam_time = 999999;
      }else{
        $model->exam_time = isset($request->gallery_modal_exam_time) ? AppHelper::instance()->cleanInput($request->gallery_modal_exam_time) : 999999;
      }
      $model->duration = isset($request->gallery_modal_duration) ? AppHelper::instance()->cleanInput($request->gallery_modal_duration) : null;

      $model->is_skip = isset($request->gallery_modal_is_skip) ? $request->gallery_modal_is_skip : 1;
      $model->is_teaser = isset($request->gallery_modal_is_teaser_2) ? $request->gallery_modal_is_teaser_2 : 1;
      $model->shortcut = isset($request->gallery_modal_shortcut) ? $request->gallery_modal_shortcut : 2;

      $model->skip_intro = isset($request->gallery_modal_skip_intro) ? $request->gallery_modal_skip_intro : 2;
      $model->skip_sec = isset($request->gallery_modal_skip_sec) ? AppHelper::instance()->cleanInput($request->gallery_modal_skip_sec) : 0;

      $model->status = isset($request->gallery_modal_status_2) ? $request->gallery_modal_status_2 : '';

      $count = CourseLessonLog::where('course_id',$request->course_id)
      ->count();
      $model->position = $count + 1;

      if ($request->hasFile('gallery_modal_image_th_2')) {
        $model->image_th = AppHelper::instance()->saveImage($request->file('gallery_modal_image_th_2'),'seminar');
      }

      $model->save();

      $course = Course::where('id', $model->course_id)->first();
      if($course){
        $course_duration = CourseLessonLog::where('course_id', $model->id)->sum(DB::raw('TIME_TO_SEC(duration)'));
        $course->course_duration = $course_duration;
        $course->save();
      }

      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = CourseLessonLog::find($request->id);

      if($model){
  
  
        $model->title_th = isset($request->gallery_modal_title_th_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_title_th_2) : null;
  
        $model->lesson_key = isset($request->gallery_modal_lesson_key_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_lesson_key_2) : null;
        $model->email = isset($request->gallery_modal_email_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_email_2) : null;
        $model->link = isset($request->gallery_modal_link_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_link_2) : null;
        $model->link_2 = isset($request->gallery_modal_link_2_2) ? AppHelper::instance()->cleanInput($request->gallery_modal_link_2_2) : null;
        $model->link_3 = isset($request->gallery_modal_link_2_3) ? AppHelper::instance()->cleanInput($request->gallery_modal_link_2_3) : null;
        $model->price = isset($request->gallery_modal_price) ? AppHelper::instance()->cleanInput($request->gallery_modal_price) : 0;
        $model->point = isset($request->gallery_modal_point) ? AppHelper::instance()->cleanInput($request->gallery_modal_point) : 0;
        $model->exam_type = isset($request->gallery_modal_exam_type) ? AppHelper::instance()->cleanInput($request->gallery_modal_exam_type) : 1;
        if($model->exam_type == 1){
          $model->exam_time = 999999;
        }else{
          $model->exam_time = isset($request->gallery_modal_exam_time) ? AppHelper::instance()->cleanInput($request->gallery_modal_exam_time) : 999999;
        }
        $model->duration = isset($request->gallery_modal_duration) ? AppHelper::instance()->cleanInput($request->gallery_modal_duration) : null;
  
        $model->is_skip = isset($request->gallery_modal_is_skip) ? $request->gallery_modal_is_skip : 1;
        $model->is_teaser = isset($request->gallery_modal_is_teaser_2) ? $request->gallery_modal_is_teaser_2 : 1;
        $model->shortcut = isset($request->gallery_modal_shortcut) ? $request->gallery_modal_shortcut : 2;
  
        $model->skip_intro = isset($request->gallery_modal_skip_intro) ? $request->gallery_modal_skip_intro : 2;
        if($model->skip_intro == 1){
          $model->skip_sec = $request->gallery_modal_skip_sec;
        }else{
          $model->skip_sec = 0;
        }

        $model->status = isset($request->gallery_modal_status_2) ? $request->gallery_modal_status_2 : '';

        if ($request->hasFile('gallery_modal_image_th_2')) {
          if($model->image_th!=null&&$model->image_th!=''){
            AppHelper::instance()->removeImage($model->image_th);
          }
          $model->image_th = AppHelper::instance()->saveImage($request->file('gallery_modal_image_th_2'),'seminar');
        }else{
          if(empty($request->source_gallery_modal_image_th_2)){
            if($model->image_th!=null&&$model->image_th!=''){
              AppHelper::instance()->removeImage($model->image_th);
            }
            $model->image_th = null;
          }
        }

        $model->save();

        $course = Course::where('id', $model->course_id)->first();
        $course_duration_1 = CourseLessonLog::where('course_id', $model->course_id)
        ->where('status', 1)->sum(DB::raw('TIME_TO_SEC(duration)'));
        $course = Course::where('id',$model->course_id)->first();
        if($course){
          $course->course_duration = $course_duration_1;
          $course->save();
        }

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = CourseLessonLog::find($request->id);
      if($model){
        if($model->exam_type == 1){
          $model->exam_time = '';
        }
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }

    public function position_page(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CourseLessonLog::where('course_id',$request->course_id)
                  ->where('id',$data[0])
                  ->first();

          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CourseLessonLog::where('course_id',$request->course_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }
}
