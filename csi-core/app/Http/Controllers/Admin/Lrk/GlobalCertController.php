<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\GlobalCert;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CertGlobalLog;
use Maatwebsite\Excel\Facades\Excel;


class GlobalCertController extends Controller
{
    public $prefix = 'cert_global';
    public $project_url = 'cert_global';
    public $project_layout = 'admin.Lrk.cert_global';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new GlobalCert();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = GlobalCert::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['cert_global.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['cert_global.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['cert_global.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['cert_global.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('cert_global')
      ->select('cert_global.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('cert_global')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new GlobalCert;
      $model->lang_type = isset($request->lang_type) ? AppHelper::instance()->cleanInput($request->lang_type) : 1;
      $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : 1;
      $model->category = is_array($request->category) ? implode(",",$request->category) : null;
      
      $model->course_director = isset($request->course_director) ? AppHelper::instance()->cleanInput($request->course_director) : null;
      $model->course_director_en = isset($request->course_director_en) ? AppHelper::instance()->cleanInput($request->course_director_en) : null;
      $model->director_position = isset($request->director_position) ? AppHelper::instance()->cleanInputBr($request->director_position) : null;
      $model->director_position_en = isset($request->director_position_en) ? AppHelper::instance()->cleanInputBr($request->director_position_en) : null;
      
      
      $model->course_director_2 = isset($request->course_director_2) ? AppHelper::instance()->cleanInput($request->course_director_2) : null;
      $model->course_director_2_en = isset($request->course_director_2_en) ? AppHelper::instance()->cleanInput($request->course_director_2_en) : null;
      $model->director_position_2 = isset($request->director_position_2) ? AppHelper::instance()->cleanInputBr($request->director_position_2) : null;
      $model->director_position_2_en = isset($request->director_position_2_en) ? AppHelper::instance()->cleanInputBr($request->director_position_2_en) : null;
      
      $model->course_director_3 = isset($request->course_director_3) ? AppHelper::instance()->cleanInput($request->course_director_3) : null;
      $model->course_director_3_en = isset($request->course_director_3_en) ? AppHelper::instance()->cleanInput($request->course_director_3_en) : null;
      $model->director_position_3 = isset($request->director_position_3) ? AppHelper::instance()->cleanInputBr($request->director_position_3) : null;
      $model->director_position_3_en = isset($request->director_position_3_en) ? AppHelper::instance()->cleanInputBr($request->director_position_3_en) : null;
      
      if ($request->hasFile('bg_color')) {
        $model->bg_color = AppHelper::instance()->saveImage($request->file('bg_color'),'/upload/certificate');
      }

      if ($request->hasFile('bg_color_en')) {
        $model->bg_color_en = AppHelper::instance()->saveImage($request->file('bg_color_en'),'/upload/certificate');
      }

      if ($request->hasFile('signature')) {
        $model->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
      }
      if ($request->hasFile('signature_en')) {
        $model->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
      }

      if ($request->hasFile('signature_2')) {
        $model->signature_2 = AppHelper::instance()->saveImage($request->file('signature_2'),'/upload/certificate');
      }

      if ($request->hasFile('signature_2_en')) {
        $model->signature_2_en = AppHelper::instance()->saveImage($request->file('signature_2_en'),'/upload/certificate');
      }

      if ($request->hasFile('signature_3')) {
        $model->signature_3 = AppHelper::instance()->saveImage($request->file('signature_3'),'/upload/certificate');
      }
      if ($request->hasFile('signature_3_en')) {
        $model->signature_3_en = AppHelper::instance()->saveImage($request->file('signature_3_en'),'/upload/certificate');
      }

      $model->status = $request->status;

      $model->save();
  
      //Group Code
      if(is_array($request->category)){
        foreach($request->category as $value) {
          $log = new CertGlobalLog();
          $log->cert_global_id = $model->id;
          $log->category_id = $value;
          $log->save();
        }
      }
      //Group Code

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = GlobalCert::find($id);

      $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : 1;
      $model->lang_type = isset($request->lang_type) ? AppHelper::instance()->cleanInput($request->lang_type) : 1;

      //Group Code
      $old_tags = explode(',', $model->category);
      $model->category = is_array($request->category) ? implode(",",$request->category) : '';
      if(is_array($request->category)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->category);

        if(count(array_diff($old_tags, $request->category))>0 || count(array_diff($request->category, $old_tags))>0){
          $log = CertGlobalLog::where('cert_global_id', $model->id);
          $log->delete();

          foreach($request->category as $value) {
            $log = new CertGlobalLog();
            $log->cert_global_id = $model->id;
            $log->category_id = $value;
            $log->save();
          }
        }
      }else{
        $log = CertGlobalLog::where('cert_global_id', $model->id);
        $log->delete();
      }
      //Group Code
      
      if($model->lang_type == 1){
        // $model->course_name_en = null;
        // $model->couse_name = isset($request->couse_name) ? AppHelper::instance()->cleanInputBr($request->couse_name) : null;
      

        if ($request->hasFile('bg_color')) {
          if($model->bg_color!=null&&$model->bg_color!=''){
            AppHelper::instance()->removeImage($model->bg_color);
          }
          $model->bg_color = AppHelper::instance()->saveImage($request->file('bg_color'),'/upload/certificate');
        }else{
          if(empty($request->source_bg_color)){
            if($model->bg_color!=null&&$model->bg_color!=''){
              AppHelper::instance()->removeImage($model->bg_color);
            }
            $model->bg_color = null;
          }
        }
      
        $model->bg_color_en = null;

      }else if($model->lang_type == 2){
        // $model->course_name_en = isset($request->course_name_en) ? AppHelper::instance()->cleanInputBr($request->course_name_en) : null;
        // $model->couse_name = null;

        if ($request->hasFile('bg_color_en')) {
          if($model->bg_color_en!=null&&$model->bg_color_en!=''){
            AppHelper::instance()->removeImage($model->bg_color_en);
          }
          $model->bg_color_en = AppHelper::instance()->saveImage($request->file('bg_color_en'),'/upload/certificate');
        }else{
          if(empty($request->source_bg_color_en)){
            if($model->bg_color_en!=null&&$model->bg_color_en!=''){
              AppHelper::instance()->removeImage($model->bg_color_en);
            }
            $model->bg_color_en = null;
          }
        }
      
        $model->bg_color = null;

      }else{
        // $model->course_name_en = isset($request->course_name_en) ? AppHelper::instance()->cleanInputBr($request->course_name_en) : null;
        // $model->couse_name = isset($request->couse_name) ? AppHelper::instance()->cleanInputBr($request->couse_name) : null;
        
        if ($request->hasFile('bg_color')) {
          if($model->bg_color!=null&&$model->bg_color!=''){
            AppHelper::instance()->removeImage($model->bg_color);
          }
          $model->bg_color = AppHelper::instance()->saveImage($request->file('bg_color'),'/upload/certificate');
        }else{
          if(empty($request->source_bg_color)){
            if($model->bg_color!=null&&$model->bg_color!=''){
              AppHelper::instance()->removeImage($model->bg_color);
            }
            $model->bg_color = null;
          }
        }

        if ($request->hasFile('bg_color_en')) {
          if($model->bg_color_en!=null&&$model->bg_color_en!=''){
            AppHelper::instance()->removeImage($model->bg_color_en);
          }
          $model->bg_color_en = AppHelper::instance()->saveImage($request->file('bg_color_en'),'/upload/certificate');
        }else{
          if(empty($request->source_bg_color_en)){
            if($model->bg_color_en!=null&&$model->bg_color_en!=''){
              AppHelper::instance()->removeImage($model->bg_color_en);
            }
            $model->bg_color_en = null;
          }
        }
      }

      if($model->type == 1){
        if($model->lang_type == 1){
          $model->course_director = isset($request->course_director) ? AppHelper::instance()->cleanInput($request->course_director) : null;
          $model->director_position = isset($request->director_position) ? AppHelper::instance()->cleanInputBr($request->director_position) : null;
          
          if ($request->hasFile('signature')) {
            if($model->signature!=null&&$model->signature!=''){
              AppHelper::instance()->removeImage($model->signature);
            }
            $model->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
          }else{
            if(empty($request->source_signature)){
              if($model->signature!=null&&$model->signature!=''){
                AppHelper::instance()->removeImage($model->signature);
              }
              $model->signature = null;
            }
          }

          $model->course_director_en = null;
          $model->director_position_en = null;
          $model->course_director_2 = null;
          $model->course_director_2_en = null;
          $model->director_position_2_en = null;
          $model->course_director_3 = null;
          $model->course_director_3_en = null;
          $model->director_position_3_en = null;
          $model->signature_2 = null;
          $model->signature_2_en = null;
          $model->signature_3 = null;
          $model->signature_3_en = null;
        }else if($model->lang_type == 2){
          $model->course_director_en = isset($request->course_director_en) ? AppHelper::instance()->cleanInput($request->course_director_en) : null;
          $model->director_position_en = isset($request->director_position_en) ? AppHelper::instance()->cleanInputBr($request->director_position_en) : null;
          
          if ($request->hasFile('signature_en')) {
            if($model->signature_en!=null&&$model->signature_en!=''){
              AppHelper::instance()->removeImage($model->signature_en);
            }
            $model->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_en)){
              if($model->signature_en!=null&&$model->signature_en!=''){
                AppHelper::instance()->removeImage($model->signature_en);
              }
              $model->signature_en = null;
            }
          }
          
          $model->course_director = null;
          $model->director_position = null;
          $model->signature = null;

          $model->course_director_2 = null;
          $model->course_director_2_en = null;
          $model->director_position_2 = null;
          $model->course_director_3 = null;
          $model->course_director_3_en = null;
          $model->director_position_3 = null;
          $model->signature_2 = null;
          $model->signature_2_en = null;
          $model->signature_3 = null;
          $model->signature_3_en = null;
        }else{
          $model->course_director = isset($request->course_director) ? AppHelper::instance()->cleanInput($request->course_director) : null;
          $model->course_director_en = isset($request->course_director_en) ? AppHelper::instance()->cleanInput($request->course_director_en) : null;
          $model->director_position = isset($request->director_position) ? AppHelper::instance()->cleanInputBr($request->director_position) : null;
          $model->director_position_en = isset($request->director_position_en) ? AppHelper::instance()->cleanInputBr($request->director_position_en) : null;
          
          if ($request->hasFile('signature')) {
            if($model->signature!=null&&$model->signature!=''){
              AppHelper::instance()->removeImage($model->signature);
            }
            $model->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
          }else{
            if(empty($request->source_signature)){
              if($model->signature!=null&&$model->signature!=''){
                AppHelper::instance()->removeImage($model->signature);
              }
              $model->signature = null;
            }
          }

          if ($request->hasFile('signature_en')) {
            if($model->signature_en!=null&&$model->signature_en!=''){
              AppHelper::instance()->removeImage($model->signature_en);
            }
            $model->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_en)){
              if($model->signature_en!=null&&$model->signature_en!=''){
                AppHelper::instance()->removeImage($model->signature_en);
              }
              $model->signature_en = null;
            }
          }

          $model->course_director_2 = null;
          $model->course_director_2_en = null;
          $model->director_position_2_en = null;
          $model->course_director_3 = null;
          $model->course_director_3_en = null;
          $model->director_position_3_en = null;
          $model->signature_2 = null;
          $model->signature_2_en = null;
          $model->signature_3 = null;
          $model->signature_3_en = null;
        }

      }else if($model->type == 2){
        if($model->lang_type == 1){
          $model->course_director = isset($request->course_director) ? AppHelper::instance()->cleanInput($request->course_director) : null;
          $model->course_director_2 = isset($request->course_director_2) ? AppHelper::instance()->cleanInput($request->course_director_2) : null;
          $model->director_position = isset($request->director_position) ? AppHelper::instance()->cleanInputBr($request->director_position) : null;
          $model->director_position_2 = isset($request->director_position_2) ? AppHelper::instance()->cleanInputBr($request->director_position_2) : null;

          if ($request->hasFile('signature')) {
            if($model->signature!=null&&$model->signature!=''){
              AppHelper::instance()->removeImage($model->signature);
            }
            $model->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
          }else{
            if(empty($request->source_signature)){
              if($model->signature!=null&&$model->signature!=''){
                AppHelper::instance()->removeImage($model->signature);
              }
              $model->signature = null;
            }
          }

          if ($request->hasFile('signature_2')) {
            if($model->signature_2!=null&&$model->signature_2!=''){
              AppHelper::instance()->removeImage($model->signature_2);
            }
            $model->signature_2 = AppHelper::instance()->saveImage($request->file('signature_2'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2)){
              if($model->signature_2!=null&&$model->signature_2!=''){
                AppHelper::instance()->removeImage($model->signature_2);
              }
              $model->signature_2 = null;
            }
          }

          $model->director_position_en = null;
          $model->signature_en = null;

          $model->course_director_en = null;
          $model->signature_2_en = null;
          $model->director_position_2_en = null;

          $model->course_director_3 = null;
          $model->signature_3 = null;
          $model->director_position_3_en = null;
        }else if($model->lang_type == 2){
          $model->course_director_en = isset($request->course_director_en) ? AppHelper::instance()->cleanInput($request->course_director_en) : null;
          $model->course_director_2_en = isset($request->course_director_2_en) ? AppHelper::instance()->cleanInput($request->course_director_2_en) : null;
          
          $model->director_position_en = isset($request->director_position_en) ? AppHelper::instance()->cleanInputBr($request->director_position_en) : null;
          $model->director_position_2_en = isset($request->director_position_2_en) ? AppHelper::instance()->cleanInputBr($request->director_position_2_en) : null;
          
          if ($request->hasFile('signature_en')) {
            if($model->signature_en!=null&&$model->signature_en!=''){
              AppHelper::instance()->removeImage($model->signature_en);
            }
            $model->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_en)){
              if($model->signature_en!=null&&$model->signature_en!=''){
                AppHelper::instance()->removeImage($model->signature_en);
              }
              $model->signature_en = null;
            }
          }

          if ($request->hasFile('signature_2_en')) {
            if($model->signature_2_en!=null&&$model->signature_2_en!=''){
              AppHelper::instance()->removeImage($model->signature_2_en);
            }
            $model->signature_2_en = AppHelper::instance()->saveImage($request->file('signature_2_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2_en)){
              if($model->signature_2_en!=null&&$model->signature_2_en!=''){
                AppHelper::instance()->removeImage($model->signature_2_en);
              }
              $model->signature_2_en = null;
            }
          }

          $model->director_position = null;
          $model->signature = null;

          $model->course_director_2 = null;
          $model->director_position_2 = null;
          $model->signature_2 = null;

          $model->course_director_3 = null;
          $model->director_position_3 = null;
          $model->signature_3 = null;
        }else{
          $model->course_director = isset($request->course_director) ? AppHelper::instance()->cleanInput($request->course_director) : null;
          $model->course_director_en = isset($request->course_director_en) ? AppHelper::instance()->cleanInput($request->course_director_en) : null;
          $model->director_position = isset($request->director_position) ? AppHelper::instance()->cleanInputBr($request->director_position) : null;
          
          $model->course_director_2 = isset($request->course_director_2) ? AppHelper::instance()->cleanInput($request->course_director_2) : null;
          $model->course_director_2_en = isset($request->course_director_2_en) ? AppHelper::instance()->cleanInput($request->course_director_2_en) : null;
          $model->director_position_2_en = isset($request->director_position_2_en) ? AppHelper::instance()->cleanInputBr($request->director_position_2_en) : null;
          
          if ($request->hasFile('signature')) {
            if($model->signature!=null&&$model->signature!=''){
              AppHelper::instance()->removeImage($model->signature);
            }
            $model->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
          }else{
            if(empty($request->source_signature)){
              if($model->signature!=null&&$model->signature!=''){
                AppHelper::instance()->removeImage($model->signature);
              }
              $model->signature = null;
            }
          }

          if ($request->hasFile('signature_en')) {
            if($model->signature_en!=null&&$model->signature_en!=''){
              AppHelper::instance()->removeImage($model->signature_en);
            }
            $model->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_en)){
              if($model->signature_en!=null&&$model->signature_en!=''){
                AppHelper::instance()->removeImage($model->signature_en);
              }
              $model->signature_en = null;
            }
          }

          if ($request->hasFile('signature_2')) {
            if($model->signature_2!=null&&$model->signature_2!=''){
              AppHelper::instance()->removeImage($model->signature_2);
            }
            $model->signature_2 = AppHelper::instance()->saveImage($request->file('signature_2'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2)){
              if($model->signature_2!=null&&$model->signature_2!=''){
                AppHelper::instance()->removeImage($model->signature_2);
              }
              $model->signature_2 = null;
            }
          }

          if ($request->hasFile('signature_2_en')) {
            if($model->signature_2_en!=null&&$model->signature_2_en!=''){
              AppHelper::instance()->removeImage($model->signature_2_en);
            }
            $model->signature_2_en = AppHelper::instance()->saveImage($request->file('signature_2_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2_en)){
              if($model->signature_2_en!=null&&$model->signature_2_en!=''){
                AppHelper::instance()->removeImage($model->signature_2_en);
              }
              $model->signature_2_en = null;
            }
          }

          $model->course_director_3 = null;
          $model->course_director_3_en = null;
          $model->director_position_3 = null;
          $model->director_position_3_en = null;
          $model->signature_3 = null;
          $model->signature_3_en = null;
        }

      }else if($model->type == 3){
        if($model->lang_type == 1){
          $model->course_director = isset($request->course_director) ? AppHelper::instance()->cleanInput($request->course_director) : null;
          $model->director_position = isset($request->director_position) ? AppHelper::instance()->cleanInputBr($request->director_position) : null;  
          
          $model->course_director_2 = isset($request->course_director_2) ? AppHelper::instance()->cleanInput($request->course_director_2) : null;
          $model->director_position_2 = isset($request->director_position_2) ? AppHelper::instance()->cleanInputBr($request->director_position_2) : null;  
          
          $model->course_director_3 = isset($request->course_director_3) ? AppHelper::instance()->cleanInput($request->course_director_3) : null;  
          $model->director_position_3 = isset($request->director_position_3) ? AppHelper::instance()->cleanInputBr($request->director_position_3) : null;  
        
        
          if ($request->hasFile('signature')) {
            if($model->signature!=null&&$model->signature!=''){
              AppHelper::instance()->removeImage($model->signature);
            }
            $model->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
          }else{
            if(empty($request->source_signature)){
              if($model->signature!=null&&$model->signature!=''){
                AppHelper::instance()->removeImage($model->signature);
              }
              $model->signature = null;
            }
          }
  
          if ($request->hasFile('signature_2')) {
            if($model->signature_2!=null&&$model->signature_2!=''){
              AppHelper::instance()->removeImage($model->signature_2);
            }
            $model->signature_2 = AppHelper::instance()->saveImage($request->file('signature_2'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2)){
              if($model->signature_2!=null&&$model->signature_2!=''){
                AppHelper::instance()->removeImage($model->signature_2);
              }
              $model->signature_2 = null;
            }
          }
  
          if ($request->hasFile('signature_3')) {
            if($model->signature_3!=null&&$model->signature_3!=''){
              AppHelper::instance()->removeImage($model->signature_3);
            }
            $model->signature_3 = AppHelper::instance()->saveImage($request->file('signature_3'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_3)){
              if($model->signature_3!=null&&$model->signature_3!=''){
                AppHelper::instance()->removeImage($model->signature_3);
              }
              $model->signature_3 = null;
            }
          }

          $model->course_director_en = null;
          $model->director_position_en = null;
          $model->signature_en = null;

          $model->course_director_2_en = null;
          $model->director_position_2_en = null;
          $model->signature_2_en = null;

          $model->course_director_3_en = null;
          $model->director_position_3_en = null;
          $model->signature_3_en = null;
        }else if($model->lang_type == 2){
          $model->course_director_en = isset($request->course_director_en) ? AppHelper::instance()->cleanInput($request->course_director_en) : null;
          $model->director_position_en = isset($request->director_position_en) ? AppHelper::instance()->cleanInputBr($request->director_position_en) : null;
          $model->course_director_2_en = isset($request->course_director_2_en) ? AppHelper::instance()->cleanInput($request->course_director_2_en) : null;
          $model->director_position_2_en = isset($request->director_position_2_en) ? AppHelper::instance()->cleanInputBr($request->director_position_2_en) : null;
          $model->course_director_3_en = isset($request->course_director_3_en) ? AppHelper::instance()->cleanInput($request->course_director_3_en) : null;  
          $model->director_position_3_en = isset($request->director_position_3_en) ? AppHelper::instance()->cleanInputBr($request->director_position_3_en) : null;  
        
          if ($request->hasFile('signature_en')) {
            if($model->signature_en!=null&&$model->signature_en!=''){
              AppHelper::instance()->removeImage($model->signature_en);
            }
            $model->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_en)){
              if($model->signature_en!=null&&$model->signature_en!=''){
                AppHelper::instance()->removeImage($model->signature_en);
              }
              $model->signature_en = null;
            }
          }
  
          if ($request->hasFile('signature_2_en')) {
            if($model->signature_2_en!=null&&$model->signature_2_en!=''){
              AppHelper::instance()->removeImage($model->signature_2_en);
            }
            $model->signature_2_en = AppHelper::instance()->saveImage($request->file('signature_2_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2_en)){
              if($model->signature_2_en!=null&&$model->signature_2_en!=''){
                AppHelper::instance()->removeImage($model->signature_2_en);
              }
              $model->signature_2_en = null;
            }
          }
  
          if ($request->hasFile('signature_3_en')) {
            if($model->signature_3_en!=null&&$model->signature_3_en!=''){
              AppHelper::instance()->removeImage($model->signature_3_en);
            }
            $model->signature_3_en = AppHelper::instance()->saveImage($request->file('signature_3_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_3_en)){
              if($model->signature_3_en!=null&&$model->signature_3_en!=''){
                AppHelper::instance()->removeImage($model->signature_3_en);
              }
              $model->signature_3_en = null;
            }
          }

          $model->course_director = null;
          $model->director_position = null;
          $model->signature = null;

          $model->course_director_2 = null;
          $model->director_position_2 = null;
          $model->signature_2 = null;

          $model->course_director_2 = null;
          $model->director_position_3 = null;
          $model->signature_3 = null;
        }else{
          $model->course_director = isset($request->course_director) ? AppHelper::instance()->cleanInput($request->course_director) : null;
          $model->director_position = isset($request->director_position) ? AppHelper::instance()->cleanInputBr($request->director_position) : null;
          
          $model->course_director_2 = isset($request->course_director_2) ? AppHelper::instance()->cleanInput($request->course_director_2) : null;
          $model->director_position_2 = isset($request->director_position_2) ? AppHelper::instance()->cleanInputBr($request->director_position_2) : null;
          
          $model->course_director_3 = isset($request->course_director_3) ? AppHelper::instance()->cleanInput($request->course_director_3) : null;  
          $model->director_position_3 = isset($request->director_position_3) ? AppHelper::instance()->cleanInputBr($request->director_position_3) : null;  
        
          $model->course_director_en = isset($request->course_director_en) ? AppHelper::instance()->cleanInput($request->course_director_en) : null;
          $model->director_position_en = isset($request->director_position_en) ? AppHelper::instance()->cleanInputBr($request->director_position_en) : null;
          
          $model->course_director_2_en = isset($request->course_director_2_en) ? AppHelper::instance()->cleanInput($request->course_director_2_en) : null;
          $model->director_position_2_en = isset($request->course_director_2_en) ? AppHelper::instance()->cleanInputBr($request->course_director_2_en) : null;
          
          $model->course_director_3_en = isset($request->course_director_3_en) ? AppHelper::instance()->cleanInput($request->course_director_3_en) : null;  
          $model->director_position_3_en = isset($request->director_position_3_en) ? AppHelper::instance()->cleanInputBr($request->director_position_3_en) : null;  
          
          if ($request->hasFile('signature')) {
            if($model->signature!=null&&$model->signature!=''){
              AppHelper::instance()->removeImage($model->signature);
            }
            $model->signature = AppHelper::instance()->saveImage($request->file('signature'),'/upload/certificate');
          }else{
            if(empty($request->source_signature)){
              if($model->signature!=null&&$model->signature!=''){
                AppHelper::instance()->removeImage($model->signature);
              }
              $model->signature = null;
            }
          }

          if ($request->hasFile('signature_en')) {
            if($model->signature_en!=null&&$model->signature_en!=''){
              AppHelper::instance()->removeImage($model->signature_en);
            }
            $model->signature_en = AppHelper::instance()->saveImage($request->file('signature_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_en)){
              if($model->signature_en!=null&&$model->signature_en!=''){
                AppHelper::instance()->removeImage($model->signature_en);
              }
              $model->signature_en = null;
            }
          }
  
          if ($request->hasFile('signature_2')) {
            if($model->signature_2!=null&&$model->signature_2!=''){
              AppHelper::instance()->removeImage($model->signature_2);
            }
            $model->signature_2 = AppHelper::instance()->saveImage($request->file('signature_2'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2)){
              if($model->signature_2!=null&&$model->signature_2!=''){
                AppHelper::instance()->removeImage($model->signature_2);
              }
              $model->signature_2 = null;
            }
          }
  
          if ($request->hasFile('signature_2_en')) {
            if($model->signature_2_en!=null&&$model->signature_2_en!=''){
              AppHelper::instance()->removeImage($model->signature_2_en);
            }
            $model->signature_2_en = AppHelper::instance()->saveImage($request->file('signature_2_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_2_en)){
              if($model->signature_2_en!=null&&$model->signature_2_en!=''){
                AppHelper::instance()->removeImage($model->signature_2_en);
              }
              $model->signature_2_en = null;
            }
          }
  
          if ($request->hasFile('signature_3')) {
            if($model->signature_3!=null&&$model->signature_3!=''){
              AppHelper::instance()->removeImage($model->signature_3);
            }
            $model->signature_3 = AppHelper::instance()->saveImage($request->file('signature_3'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_3)){
              if($model->signature_3!=null&&$model->signature_3!=''){
                AppHelper::instance()->removeImage($model->signature_3);
              }
              $model->signature_3 = null;
            }
          }
  
          if ($request->hasFile('signature_3_en')) {
            if($model->signature_3_en!=null&&$model->signature_3_en!=''){
              AppHelper::instance()->removeImage($model->signature_3_en);
            }
            $model->signature_3_en = AppHelper::instance()->saveImage($request->file('signature_3_en'),'/upload/certificate');
          }else{
            if(empty($request->source_signature_3_en)){
              if($model->signature_3_en!=null&&$model->signature_3_en!=''){
                AppHelper::instance()->removeImage($model->signature_3_en);
              }
              $model->signature_3_en = null;
            }
          }
        }
      }else{
        $model->signature = null;
        $model->signature_2 = null;
        $model->signature_3 = null;

        $model->signature_en = null;
        $model->signature_2_en = null;
        $model->signature_3_en = null;

        $model->course_director = null;
        $model->course_director_en = null;
        $model->director_position = null;
        $model->director_position_en = null;

        $model->course_director_2 = null;
        $model->course_director_2_en = null;
        $model->director_position_2 = null;
        $model->director_position_2_en = null;

        $model->course_director_3 = null;
        $model->course_director_3_en = null;
        $model->director_position_3 = null;
        $model->director_position_3_en = null;
      }
      
      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = GlobalCert::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = GlobalCert::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
