<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use App\Services\SubscriptionMainService;
use Illuminate\Http\Request;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\Auth;

class SubscriptionMainController extends Controller
{
    public $prefix = 'subscription_main';
    public $project_url = 'subscription_main';
    public $project_layout = 'admin.Lrk.subscription_main';

    protected $subscriptionMainService;

    public function __construct(SubscriptionMainService $subscriptionMainService)
    {
        $this->middleware('auth:admin');
        $this->subscriptionMainService = $subscriptionMainService;
    }

    public function index()
    {
        if (Auth::user()->level != '99') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $count = $this->subscriptionMainService->getCount();
        $obj = array(
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );
        
        return view($this->project_layout . '.index', compact('obj', 'count'));
    }

    public function create()
    {
        if (Auth::user()->level != '99') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $data = $this->subscriptionMainService->prepareDataForCreate();
        
        $obj = array(
            'type' => 'create',
            'route' => route($this->prefix . '.store'),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', array_merge($data, compact('obj')));
    }

    public function store(Request $request)
    {
        if (Auth::user()->level != '99') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $this->subscriptionMainService->createSubscriptionMain($request);

        return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been created');
    }

    public function dblist()
    {
        if (Auth::user()->level != '99') {
            return response()->json(['errors' => 'Permission Deny']);
        }

        $data = $this->subscriptionMainService->getWithSubscriptions();

        return Datatables::of($data)
            ->editColumn('cover_image', function ($item) {
                if ($item->cover_image) {
                    return '<img src="' . asset($item->cover_image) . '" width="60" height="40" class="img-thumbnail">';
                }
                return '<span class="badge badge-secondary">ไม่มีรูป</span>';
            })
            ->editColumn('title', function ($item) {
                return '<strong>' . $item->title . '</strong>';
            })
            ->editColumn('price', function ($item) {
                if ($item->price) {
                    return '<span class="badge badge-success">' . number_format($item->price, 2) . ' บาท</span>';
                }
                return '-';
            })
            ->editColumn('original_price', function ($item) {
                if ($item->original_price) {
                    $discount = '';
                    if ($item->price && $item->original_price > $item->price) {
                        $percent = round((($item->original_price - $item->price) / $item->original_price) * 100);
                        $discount = '<br><small class="text-danger">ลด ' . $percent . '%</small>';
                    }
                    return '<span class="text-muted">' . number_format($item->original_price, 2) . ' บาท</span>' . $discount;
                }
                return '-';
            })
            ->editColumn('period', function ($item) {
                return $item->period ? $item->period . ' วัน' : '-';
            })
            ->editColumn('subscriptions_count', function ($item) {
                $count = $item->subscriptions->count();
                $badge_class = $count > 0 ? 'badge-info' : 'badge-secondary';
                return '<span class="badge ' . $badge_class . '">' . $count . ' รายการ</span>';
            })
            ->editColumn('is_featured', function ($item) {
                return $item->is_featured
                    ? '<span class="badge badge-warning">แนะนำ</span>'
                    : '<span class="badge badge-light">ไม่แนะนำ</span>';
            })
            ->editColumn('is_popular', function ($item) {
                return $item->is_popular
                    ? '<span class="badge badge-primary">ยอดนิยม</span>'
                    : '<span class="badge badge-light">ไม่ยอดนิยม</span>';
            })
            ->editColumn('status', function ($item) {
                return $item->status == 1
                    ? '<span class="badge badge-success">เปิดใช้งาน</span>'
                    : '<span class="badge badge-danger">ปิดใช้งาน</span>';
            })
            ->editColumn('position', function ($item) {
                return '<span class="badge badge-secondary">' . $item->position . '</span>';
            })
            ->editColumn('created_at', function ($item) {
                return $item->created_at->format('d/m/Y H:i');
            })
            ->addColumn('action', function ($item) {
                $editUrl = route('admin') . '/subscription_main/' . $item->id . '/edit';
                $viewSubscriptionsUrl = route('admin') . '/subscript_non_code?main_id=' . $item->id;

                return '
            <div class="btn-group">
                <a href="' . $editUrl . '" class="btn btn-sm btn-info" title="แก้ไข">
                    <i class="fa fa-edit"></i>
                </a>
                <a href="' . $viewSubscriptionsUrl . '" class="btn btn-sm btn-success" title="ดู Subscriptions">
                    <i class="fa fa-list"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteData(' . $item->id . ', \'/csisocietyadmin/subscription_main/delete\', \'datatable_subscription_main\')" class="btn btn-sm btn-danger" title="ลบ">
                    <i class="fa fa-times"></i>
                </a>
            </div>';
            })
            ->rawColumns(['cover_image', 'title', 'price', 'original_price', 'subscriptions_count', 'is_featured', 'is_popular', 'status', 'position', 'action'])
            ->make(true);
    }

    public function destroy(Request $request)
    {
        if (Auth::user()->level != '99') {
            return response()->json(['errors' => 'Permission Deny']);
        }

        $result = $this->subscriptionMainService->deleteSubscriptionMain($request->id);

        return response()->json(['success' => $result]);
    }

    public function status(Request $request)
    {
        if (Auth::user()->level != '99') {
            return response()->json(['errors' => 'Permission Deny']);
        }

        $result = $this->subscriptionMainService->toggleStatus($request->id);

        return response()->json(['success' => $result]);
    }

    public function position(Request $request)
    {
        if (Auth::user()->level != '99') {
            return response()->json(['errors' => 'Permission Deny']);
        }

        $result = $this->subscriptionMainService->updatePosition($request->id, $request->position);

        return response()->json(['success' => $result]);
    }

    public function edit($id)
    {
        if (Auth::user()->level != '99') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $data = $this->subscriptionMainService->prepareDataForEdit($id);

        if (!$data) {
            return redirect(route('admin') . '/' . $this->project_url);
        }

        $obj = array(
            'type' => 'edit',
            'route' => route($this->prefix . '.update', $id),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', array_merge($data, compact('obj')));
    }

    public function update(Request $request, $id)
    {
        if (Auth::user()->level != '99') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $result = $this->subscriptionMainService->updateSubscriptionMain($request, $id);

        if (!$result) {
            return redirect(route('admin') . '/' . $this->project_url)->with('errors', 'Data not found');
        }

        return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been updated');
    }
}