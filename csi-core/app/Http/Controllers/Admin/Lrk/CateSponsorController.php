<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CateSponsor;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CouponSponsor;
use Maatwebsite\Excel\Facades\Excel;


class CateSponsorController extends Controller
{
    public $prefix = 'categories_sponsor';
    public $project_url = 'categories_sponsor';
    public $project_layout = 'admin.Lrk.categories_sponsor';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
        $model = new CateSponsor();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> route($this->prefix.'.store'),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
        $model = CateSponsor::find($id);
        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>route($this->prefix.'.update',$model->id),
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['sponsor.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['sponsor.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['sponsor.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['sponsor.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('sponsor')
      ->select('sponsor.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('sponsor')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      $model = new CateSponsor;
      $model->name_th = isset($request->name_th) ? AppHelper::instance()->cleanInput($request->name_th) : null;
      $model->name_en = isset($request->name_en) ? AppHelper::instance()->cleanInput($request->name_en) : null;
      if ($request->hasFile('image')) {
        $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/sponsor');
      }
      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

        $model = CateSponsor::find($id);
        $model->name_th = isset($request->name_th) ? AppHelper::instance()->cleanInput($request->name_th) : null;
        $model->name_en = isset($request->name_en) ? AppHelper::instance()->cleanInput($request->name_en) : null;

        if ($request->hasFile('image')) {
          if($model->image!=null&&$model->image!=''){
            AppHelper::instance()->removeImage($model->image);
          }
          $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/sponsor');
        }else{
          if(empty($request->source_image)){
            if($model->image!=null&&$model->image!=''){
              AppHelper::instance()->removeImage($model->image);
            }
            $model->image = null;
          }
        }
        $model->status = $request->status;
        $model->save();

        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {

        $model = CateSponsor::find($request->id);

        $coupon = CouponSponsor::where('sponsor_id', $model->id)->get();
        foreach($coupon as $key_coupon => $val_coupon){
          $val_coupon->delete();
        }

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = CateSponsor::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

}
