<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\Course;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CouponCourse;
use App\Models\Core\CourseCateLog;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\CourseExamLog;
use App\Models\Core\CourseFileLog;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\CourseOrganLog;
use App\Models\Core\CourseRate;
use App\Models\Core\CourseTagLog;
use App\Models\Core\UsersCommentLog;
use App\Models\Core\UsersCommentLikeLog;
use App\Models\Core\UsersExamLog;
use App\Models\Core\UsersExamPoint;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\Tags;
use App\Models\Core\UsersCartLog;
use App\Models\Core\UsersLearningLog;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class InfographicController extends Controller
{
    public $prefix = 'infographic';
    public $project_url = 'infographic';
    public $project_layout = 'admin.Lrk.infographic';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $course_all = Course::where('trailer_media', 5)->count();
      $course_open = Course::where('trailer_media', 5)->where('status', 1)->count();
      $course_closed = Course::where('trailer_media', 5)->where('status', 2)->count();

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_all','course_open','course_closed'));
    }

    public function create()
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = new Course();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      return redirect(route('admin'));
    }

    public function edit($id)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = Course::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['course.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['course.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['course.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      if(Auth::user()->level == 99){
        $query = DB::table('course')
        ->select('course.*')
        ->where('trailer_media', 5)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('course')
        ->where('trailer_media', 5)
        ->where($where_search)
        ->count();
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        
        // <a href="'.$this->prefix.'/'.$obj->id.'/view" class="btn btn-light" data-original-title="ข้อมูล" title="ข้อมูล"><i class="fa fa-eye"></i></a>

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = new Course;

      $slug_clean = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug = Course::where('course.slug','=', $slug_clean)->count();

      if($slug==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : AppHelper::instance()->cleanInputSlug($request->title_th);
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }

      // $model->course_key = isset($request->course_key) ? AppHelper::instance()->cleanInput($request->course_key) : null;

      // $model->course_lang = isset($request->course_lang) ?  AppHelper::instance()->cleanInput($request->course_lang) : 1;
      // $model->course_type = isset($request->course_type) ?  AppHelper::instance()->cleanInput($request->course_type) : 1;
      $model->trailer_media = 5;
      // $model->is_suggess = isset($request->is_suggess) ?  AppHelper::instance()->cleanInput($request->is_suggess) : 1;
      // $model->topic_type = isset($request->topic_type) ?  AppHelper::instance()->cleanInput($request->topic_type) : 1;

      $model->key_search = isset($request->key_search) ?  AppHelper::instance()->cleanInput($request->key_search) : null;
      
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = isset($request->title_en) ? AppHelper::instance()->cleanInput($request->title_en) : null;

      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null;
      $model->subtitle_en = isset($request->subtitle_en) ? AppHelper::instance()->cleanInput($request->subtitle_en) : null;
      
      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;
      $model->profit = isset($request->profit) ? AppHelper::instance()->convertDomHTML($request->profit) : null;
      $model->details_en = null;

      // $model->price = isset($request->price) ? AppHelper::instance()->cleanInput($request->price) : 0;
        
      // $model->is_hot = isset($request->is_hot) ?  AppHelper::instance()->cleanInput($request->is_hot) : 1;
      // $model->is_new = isset($request->is_new) ?  AppHelper::instance()->cleanInput($request->is_new) : 1;

      // $model->point_to_pass = isset($request->point_to_pass) ? AppHelper::instance()->cleanInput($request->point_to_pass) : 0;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;
      
      
      if ($request->hasFile('image_th')) {
        $model->image_th = AppHelper::instance()->saveImage($request->file('image_th'),'/upload/course');
      }

      if ($request->hasFile('banner')) {
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/course');
      }

      $count = Course::where('trailer_media', 5)->count();
      $model->position = $count + 1;

      $model->status = $request->status;
  
      //เช็ค Tags
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tags_val = $request->tag;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      //เช็ค Tags

      $model->save();

      //TAG
      if(is_array($request->tag)){ //ถ้า tags มีค่า
        foreach($request->tag as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseTagLog();
          $log->course_id = $model->id;
          $log->tag_id = $value;
          $log->save();
        }
      }
      //TAG

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $model = Course::find($id);

      //Tags
      $old_tags = explode(',', $model->tag);
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tag_val = $request->tag;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      if(is_array($request->tag)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->tag))>0 || count(array_diff($request->tag, $old_tags))>0){
          $tags = CourseTagLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->tag as $value) {
            $log = new CourseTagLog();
            $log->course_id = $model->id;
            $log->tag_id = $value;
            $log->save();
          }
        }
      }
      //TAG

      // $model->is_hot = isset($request->is_hot) ?  $request->is_hot : $model->is_hot;
      // $model->is_new = isset($request->is_new) ?  $request->is_new : $model->is_new;

      // $model->course_key = isset($request->course_key) ? AppHelper::instance()->cleanInput($request->course_key) : null;

      // $model->course_lang = isset($request->course_lang) ?  AppHelper::instance()->cleanInput($request->course_lang) : $model->course_lang;
      // $model->course_type = isset($request->course_type) ?  AppHelper::instance()->cleanInput($request->course_type) : 1;
      $model->trailer_media = 5;

      $model->key_search = isset($request->key_search) ?  AppHelper::instance()->cleanInput($request->key_search) : null;
      
      // $model->topic_type = isset($request->topic_type) ?  AppHelper::instance()->cleanInput($request->topic_type) : 1;

      // $model->is_suggess = isset($request->is_suggess) ?  AppHelper::instance()->cleanInput($request->is_suggess) : $model->is_suggess;


      if ($request->hasFile('banner')) {
        if($model->banner!=null&&$model->banner!=''){
          AppHelper::instance()->removeImage($model->banner);
        }
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/course');
      }else{
        if(empty($request->source_banner)){
          if($model->banner!=null&&$model->banner!=''){
            AppHelper::instance()->removeImage($model->banner);
          }
          $model->banner = null;
        }
      }
      
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = isset($request->title_en) ? AppHelper::instance()->cleanInput($request->title_en) : null;

      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null;
      $model->subtitle_en = isset($request->subtitle_en) ? AppHelper::instance()->cleanInput($request->subtitle_en) : null;

      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;

      if ($request->hasFile('image_th')) {
        if($model->image_th!=null&&$model->image_th!=''){
          AppHelper::instance()->removeImage($model->image_th);
        }
        $model->image_th = AppHelper::instance()->saveImage($request->file('image_th'),'/upload/course');
      }else{
        if(empty($request->source_image_th)){
          if($model->image_th!=null&&$model->image_th!=''){
            AppHelper::instance()->removeImage($model->image_th);
          }
          $model->image_th = null;
        }
      }

      // $model->point_to_pass = isset($request->point_to_pass) ? AppHelper::instance()->cleanInput($request->point_to_pass) : 0;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;
      
      $slug_clean_th = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug_th = Course::where('course.slug','=', $slug_clean_th)->where('course.id','!=', $id)->count();
      if($slug_th==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : '';
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }

      $model->status = $request->status;

      $model->course_duration = null;

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = Course::where('trailer_media',1)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = Course::count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $model = Course::find($request->id);

      $cate = CourseCateLog::where('course_id', $model->id)->get();
      foreach($cate as $key_cate => $val_cate){
        $val_cate->delete();
      }

      $lesson = CourseLessonLog::where('course_id', $model->id)->get();
      foreach($lesson as $key_lesson => $val_lesson){
        $learning = UsersLearningLog::where('lesson_id', $val_lesson->id)->get();
        foreach($learning as $key_learning=>$val_learnint){
          $val_learnint->delete();
        }
        $val_lesson->delete();
      }

      $exam = CourseExamLog::where('course_id', $model->id)->get();
      foreach($exam as $key_exam => $val_exam){
        $answer = CourseExamAnswer::where('exam_id', $val_exam->id)->get();
        foreach($answer as $key_answer => $val_answer){
          $val_answer->delete();
        }
        $val_exam->delete();
      }

      $file = CourseFileLog::where('course_id', $model->id)->get();
      foreach($file as $key_file => $val_file){
        $val_file->delete();
      }

      $rate = CourseRate::where('course_id', $model->id)->get();
      foreach($rate as $key_rate => $val_rate){
        $val_rate->delete();
      }

      $tag = CourseTagLog::where('course_id', $model->id)->get();
      foreach($tag as $key_tag => $val_tag){
        $val_tag->delete();
      }

      $coupon = CouponCourse::where('course_id', $model->id)->get();
      foreach($coupon as $key_coupon => $val_coupon){
        $val_coupon->delete();
      }

      $cart = UsersCartLog::where('course_id', $model->id)->get();
      foreach($cart as $key_cart => $val_cart){
        $val_cart->delete();
      }

      $comment = UsersCommentLog::where('course_id', $model->id)->get();
      foreach($comment as $key_comment => $val_comment){
        $like = UsersCommentLikeLog::where('course_id', $val_comment->id)->get();
        foreach($like as $key_like=>$val_like){
          $val_like->delete();
        }
        $val_comment->delete();
      }

      $exam = UsersExamLog::where('course_id', $model->id)->get();
      foreach($exam as $key_exam => $val_exam){
        $point = UsersExamPoint::where('exam_id', $val_exam->id)->get();
        foreach($point as $key_point=>$val_point){
          $val_point->delete();
        }
        $val_exam->delete();
      }
    
      $fav = UsersFavoriteLog::where('course_id', $model->id)->get();
      foreach($fav as $key_fav => $val_fav){
        $val_fav->delete();
      }

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return response()->json();
      }
      $model = Course::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='order_log_filter') {
          
          $data = DB::table('user_order_list');
          $data->join('user_order_log', 'user_order_log.id', 'user_order_list.order_id');
          $data->join('user', 'user.id', 'user_order_log.user_id');
          $data->join('course', 'course.id', 'user_order_list.course_id');
          $data->select('user_order_list.*', 'user.name as u_name', 'user.lastname as u_lastname', 'user.email', 
                        'course.title_th', 'user.mobile', 'user_order_log.payment_type', 'user_order_log.status',
                      'user_order_log.receipt', 'user_order_log.receipt_type');
          $data->where('user_order_list.course_id', $request->id);
          $data->where('user_order_log.status', 2);
          $data->where('user_order_log.payment_type', 3);
          $data->get();

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_order_list.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'บทเรียน'=>'บทเรียน',
            'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'เบอร์โทรศัพท์'=>'เบอร์โทรศัพท์',
            'ประเภทการจ่ายเงิน'=>'ประเภทการจ่ายเงิน',
            'ประเภทใบเสร็จ'=>'ประเภทใบเสร็จ',
            'ราคาคอร์ส'=>'ราคาคอร์ส',
            'โค๊ดส่วนลด'=>'โค๊ดส่วนลด',
            'ราคาส่วนลด'=>'ราคาส่วนลด',
            'ราคาสุทธิ'=>'ราคาสุทธิ',
            'สถานะการจ่ายเงิน'=>'สถานะการจ่ายเงิน',
            'วันที่เก็บข้อมูล'=>'วันที่เก็บข้อมูล',
          );

          foreach ($data as $key=>$e) {

            if($e->payment_type == 1){
              $e->payment_type = 'QR Code';
            }else if($e->payment_type == 2){
              $e->payment_type = 'Credit';
            }else{
              $e->payment_type = 'transfering';
            }

            if($e->receipt_type == 1){
              $e->receipt_type = 'ธรรมดา';
            }else{
              $e->receipt_type = 'นิติบุคคล';
            }

            if($e->status == 1){
              $e->status = 'pending';
            }else if($e->status == 2){
              $e->status = 'success';
            }else if($e->status == 3){
              $e->status = 'reject';
            }

            if($e->discount_value == 0){
              $discount = strval($e->discount_value);
            }else{
              $discount = $e->discount_value;
            }

            $ex_array[]=array(
            'บทเรียน'=>$e->title_th,
            'ชื่อ - นามสกุล'=>$e->u_name.' '.$e->u_lastname,
            'อีเมล์'=>$e->email,
            'เบอร์โทรศัพท์'=>$e->mobile,
            'ประเภทการจ่ายเงิน'=>$e->payment_type,
            'ประเภทใบเสร็จ'=>$e->receipt_type,
            'ราคาคอร์ส'=>$e->price,
            'โค๊ดส่วนลด'=>$e->discount_code,
            'ราคาส่วนลด'=>$discount,
            'ราคาสุทธิ'=>$e->total_price,
            'สถานะการจ่ายเงิน'=>$e->status,
            'วันที่เก็บข้อมูล'=>$e->created_at,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='info_export') {
                $data = DB::table('course')
                ->select('id', 'title_th', 'subtitle_th', 'tag as index')
                ->where('course.trailer_media', 5)
                ->orderBy('course.id', 'asc')
                ->get();

                $ex_array[]=array(
                  'id'=>'id',
                  'title_th'=>'title_th',
                  'subtitle_th'=>'subtitle_th',
                  'index'=>'index',
                );

                foreach ($data as $e) {

                  $data_index = CourseTagLog::join('tag', 'tag.id', 'course_tag_log.tag_id')->where('course_tag_log.course_id', $e->id)->get();
                  
                  $tag = '';
                  foreach($data_index as $key=>$val){
                    if($tag!=''){
                      $tag.=',';
                    }
                    $tag.=$val->title;
                  }

                  $ex_array[]=array(
                    'id'=>$e->id,
                    'title_th'=>$e->title_th,
                    'subtitle_th'=>$e->subtitle_th,
                    'index'=>$tag,
                  );
                }
            }

            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    $export = new TempExport($ex_array);

                    return FacadesExcel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

    public function import(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;

      if(isset($request->excel)){
        $excels = json_decode($request->excel);
        if(count($excels)>0){
          foreach ($excels as &$excel) {
            $type = '';
            //แก้ไข key ที่ต้องการเช็คซ้ำ ตัวอย่าง $excel->id ซึ่งจะเป็น id , email หรืออะไรก็ได้ ถ้ามีซ้ำใน db ก็จะทำการอัพเดทข้อมูลแทน
            if(isset($excel->id)){
                if(!empty($excel->id)){
                  $result = Course::where('id','=', $excel->id)->first();
                  if($result){
                    $model_excel = Course::find($result->id);
                    $type = 'update';
                  }else{
                    $model_excel = new Course();
                    $type = 'Course';
                  }
                }else{
                  $model_excel = new Course();
                  $type = 'store';
                }
            }else{
              $model_excel = new Course();
              $type = 'store';
            }


            $model_excel->id = isset($excel->id) ? $excel->id : null;
            $model_excel->title_th = isset($excel->title_th) ? $excel->title_th : null;
            $model_excel->subtitle_th = isset($excel->subtitle_th) ? $excel->subtitle_th : null;

            //tag
            $tag_arr = '';
            $keyword = '';
            if(isset($excel->index)){
              $excel->index = explode(',', $excel->index);
              foreach($excel->index as $tag){
                $tag_query = Tags::where('title',$tag)->first();
                if(!$tag_query){
                  $tag_query = new Tags();
                  $tag_query->title = $tag;
                  $tag_query->title = trim($tag_query->title);
                  $tag_query->save();

                  $model_excel->tag = $tag_query->id;
                }else{
                  $model_excel->tag = $tag_query->id;
                }
                if($tag_arr != ''){
                  $tag_arr .= ',';
                }
                $tag_arr .= $tag_query->id;
                if($keyword != ''){
                  $keyword .= ',';
                }
                $keyword .= $tag_query->title;
                $model_excel->tag = $tag_arr;
              }
            }
            //tag
              
            $model_excel->key_search = $keyword;

            $response[$type]+=1;
            $model_excel->save();

            //tag
            if(isset($tag_arr)){
              $tag_ex = explode(',', $tag_arr);
              if(is_array($tag_ex)){ 
                foreach($tag_ex as $value) { 
                  $log = new CourseTagLog();
                  $log->course_id = $model_excel->id;
                  $log->tag_id	 = $value;
                  $log->save();
                }
              }
            }
            //tag
          }
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);;
    }
}
