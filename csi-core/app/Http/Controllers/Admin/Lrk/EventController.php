<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\Event;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\ArticleCateLog;
use App\Models\Core\ArticleLog;
use App\Models\Core\ArticleSubCateLog;
use App\Models\Core\ArticleTagLog;
use App\Models\Core\EventLog;
use App\Models\Core\EventTrainerLog;
use App\Models\Core\Tags;
use App\Models\Core\UsersActivityLog;
use App\Models\Core\UsersFavoriteLog;
use Maatwebsite\Excel\Facades\Excel;


class EventController extends Controller
{
    public $prefix = 'event';
    public $project_url = 'event';
    public $project_layout = 'admin.Lrk.event';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Event();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = Event::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['event.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['event.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['event.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['event.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('event')
      ->select('event.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('event')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Event;

      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->short_title = isset($request->short_title) ? AppHelper::instance()->cleanInput($request->short_title) : null;
      $model->subtitle = isset($request->subtitle) ? AppHelper::instance()->cleanInputBr($request->subtitle) : null;
      $model->details = isset($request->details) ? AppHelper::instance()->cleanInputBr($request->details) : null;

      $model->is_limit = isset($request->is_limit) ? AppHelper::instance()->cleanInput($request->is_limit) : 1;
      $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : 0;

      $model->public_date = isset($request->public_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->public_date)->format('Y-m-d H:i:s') : null;
      $model->started_enroll = isset($request->started_enroll) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_enroll)->format('Y-m-d H:i:s') : null;
      $model->end_enroll = isset($request->end_enroll) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_enroll)->format('Y-m-d H:i:s') : null;
      $model->started_event = isset($request->started_event) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_event)->format('Y-m-d H:i:s') : null;
      $model->end_event = isset($request->end_event) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_event)->format('Y-m-d H:i:s') : null;

      if ($request->hasFile('image')) {
        $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/article');
      }

      $model->trainer_name = is_array($request->trainer_name) ? implode(",",$request->trainer_name) : null;

      $model->status = $request->status;

      $model->save();

      if(is_array($request->trainer_name)){
        foreach($request->trainer_name as $value) {
          $log = new EventTrainerLog();
          $log->event_id = $model->id;
          $log->speaker_id = $value;
          $log->save();
        }
      }

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = Event::find($id);

      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->short_title = isset($request->short_title) ? AppHelper::instance()->cleanInput($request->short_title) : null;
      $model->subtitle = isset($request->subtitle) ? AppHelper::instance()->cleanInputBr($request->subtitle) : null;
      $model->details = isset($request->details) ? AppHelper::instance()->cleanInputBr($request->details) : null;

      $model->is_limit = isset($request->is_limit) ? AppHelper::instance()->cleanInput($request->is_limit) : 1;
      if($model->is_limit == 1){
        $model->amount = 0;
      }else{
        $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : 1;
      }

      $model->public_date = isset($request->public_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->public_date)->format('Y-m-d H:i:s') : null;
      $model->started_enroll = isset($request->started_enroll) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_enroll)->format('Y-m-d H:i:s') : null;
      $model->end_enroll = isset($request->end_enroll) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_enroll)->format('Y-m-d H:i:s') : null;
      $model->started_event = isset($request->started_event) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_event)->format('Y-m-d H:i:s') : null;
      $model->end_event = isset($request->end_event) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_event)->format('Y-m-d H:i:s') : null;

      if ($request->hasFile('image')) {
        if($model->image!=null&&$model->image!=''){
          AppHelper::instance()->removeImage($model->image);
        }
        $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/article');
      }else{
        if(empty($request->source_image)){
          if($model->image!=null&&$model->image!=''){
            AppHelper::instance()->removeImage($model->image);
          }
          $model->image = null;
        }
      }

      $old_tags = explode(',', $model->trainer_name); 
      $model->trainer_name = is_array($request->trainer_name) ? implode(",",$request->trainer_name) : null; 
      if(is_array($request->trainer_name)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->trainer_name); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

        if(count(array_diff($old_tags, $request->trainer_name))>0 || count(array_diff($request->trainer_name, $old_tags))>0){
          $log = EventTrainerLog::where('event_id', $model->id);
          $log->delete();

          foreach($request->trainer_name as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new EventTrainerLog();
            $log->event_id = $model->id;
            $log->speaker_id = $value;
            $log->save();
          }
        }
      }else{
        $log = EventTrainerLog::where('event_id', $model->id);
        $log->delete();
      }

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = Event::find($request->id);

      if($model){
        $event_log = EventTrainerLog::where('event_id',$model->id)->delete();
        $event_enrol = EventLog::where('event_id',$model->id)->delete();
        $model->delete();
      }

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = Event::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
