<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CoursePromotion;
use App\Models\Core\CoursePromotionLog;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CouponWebCode;
use App\Models\Core\CouponWebSponsor;
use App\Models\Core\Course;

class CoursePromotionController extends Controller
{
    public $prefix = 'course_promotion';
    public $project_url = 'course_promotion';
    public $project_layout = 'admin.Lrk.course_promotion';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new CoursePromotion();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = CoursePromotion::find($id);
      $article_id = $model->id;
      if($model){
        $obj = array(
          'type'=>'view',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.view', compact('model','obj','article_id'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = CoursePromotion::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course_promotion.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['course_promotion.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['course_promotion.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_promotion.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('course_promotion')
      ->select('course_promotion.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_promotion')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new CoursePromotion;

      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->promotion_type = isset($request->promotion_type) ? $request->promotion_type : null;
      $model->value = isset($request->value) ? AppHelper::instance()->cleanInput($request->value) : null;

      $model->pro_started = isset($request->pro_started) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_started)->format('Y-m-d H:i:s') : null;
      $model->pro_end = isset($request->pro_end) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_end)->format('Y-m-d H:i:s') : null;
      $model->pro_period = isset($request->pro_period) ?  AppHelper::instance()->cleanInput($request->pro_period) : 1;

      $model->course = is_array($request->course) ? implode(",",$request->course) : null;

      // $model->status = $request->status;

      $model->is_all = isset($request->is_all) ? $request->is_all : 2;

      $model->save();

      if($model->is_all == 2){
        if(is_array($request->course)){
          foreach($request->course as $value) {
            $course = Course::where('id', $value)->first();
            if($course){
              $course->is_promotion = 1;
              $course->pro_started = $model->pro_started;
              $course->pro_end = $model->pro_end;
              $course->pro_period = $model->pro_period;
              $course->promotion_type = $model->promotion_type;
                if($course->promotion_type == 1){
                  $total =  $course->price - $model->value;
                  if($total < 0){
                    $course->pro_price = 0;
                  }else{
                    $course->pro_price = $total;
                  }
                }else{
                  $total =  $course->price-($course->price*($model->value/100));
                  if($total < 0){
                    $course->pro_price = 0;
                  }else{
                    $course->pro_price = $total;
                  }
                }
              $course->save();
            }
            $log = new CoursePromotionLog();
            $log->promotion_id = $model->id;
            $log->course_id = $value;
            $log->save();
          }
        }
      }else{
        $course_all = Course::select('id')->get();
        foreach($course_all as $value){
          $course = Course::where('id', $value->id)->first();
          if($course){
            $course->is_promotion = 1;
            $course->pro_started = $model->pro_started;
            $course->pro_end = $model->pro_end;
            $course->pro_period = $model->pro_period;
            $course->promotion_type = $model->promotion_type;
              if($course->promotion_type == 1){
                $total =  $course->price - $model->value;
                if($total < 0){
                  $course->pro_price = 0;
                }else{
                  $course->pro_price = $total;
                }
              }else{
                $total =  $course->price-($course->price*($model->value/100));
                if($total < 0){
                  $course->pro_price = 0;
                }else{
                  $course->pro_price = $total;
                }
              }
            $course->save();
          }
          $log = new CoursePromotionLog();
          $log->promotion_id = $model->id;
          $log->course_id = $value->id;
          $log->save();
        }
      }

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = CoursePromotion::find($id);

      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->promotion_type = isset($request->promotion_type) ? $request->promotion_type : null;
      $model->value = isset($request->value) ? AppHelper::instance()->cleanInput($request->value) : null;

      $model->pro_started = isset($request->pro_started) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_started)->format('Y-m-d H:i:s') : null;
      $model->pro_end = isset($request->pro_end) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_end)->format('Y-m-d H:i:s') : null; 
      $model->pro_period = isset($request->pro_period) ?  AppHelper::instance()->cleanInput($request->pro_period) : 1;

      $model->is_all = isset($request->is_all) ? $request->is_all : 2;

      if($model->is_all == 2){
        $old_tags = explode(',', $model->course);
        $model->course = is_array($request->course) ? implode(",",$request->course) : '';
        if(is_array($request->course)){
          $log = CoursePromotionLog::where('promotion_id', $model->id)->get();
          foreach($log as $del){
            $course = Course::find($del->course_id);
            $course->is_promotion = 2;
            $course->pro_price = 0;
            $course->pro_started = null;
            $course->pro_end = null;
            $course->pro_period = 1;
            $course->save();
            $del->delete();
          }
  
          foreach($request->course as $value) {
            $course = Course::find($value);
            if($course){
              $course->is_promotion = 1;
              $course->pro_started = $model->pro_started;
              $course->pro_end = $model->pro_end;
              $course->pro_period = $model->pro_period;
              $course->promotion_type = $model->promotion_type;
                if($course->promotion_type == 1){
                  $total =  $course->price - $model->value;
                  if($total < 0){
                    $course->pro_price = 0;
                  }else{
                    $course->pro_price = $total;
                  }
                }else{
                  $total =  $course->price-($course->price*($model->value/100));
                  if($total < 0){
                    $course->pro_price = 0;
                  }else{
                    $course->pro_price = $total;
                  }
                }
              $course->save();
            }
            $log = new CoursePromotionLog();
            $log->promotion_id = $model->id;
            $log->course_id = $value;
            $log->save();
          }
        }else{
          $log = CoursePromotionLog::where('promotion_id', $model->id);
          $log->delete();
        }
      }else{
        $model->course = null;
        $log = CoursePromotionLog::where('promotion_id', $model->id)->get();
        foreach($log as $del){
          $course = Course::find($del->course_id);
          $course->is_promotion = 2;
          $course->pro_price = 0;
          $course->pro_started = null;
          $course->pro_end = null;
          $course->pro_period = 1;
          $course->save();
          $del->delete();
        }
        $course_all = Course::select('id')->get();
        foreach($course_all as $value) {
          $course = Course::where('id', $value->id)->first();
          if($course){
            $course->is_promotion = 1;
            $course->pro_started = $model->pro_started;
            $course->pro_end = $model->pro_end;
            $course->pro_period = $model->pro_period;
            $course->promotion_type = $model->promotion_type;
              if($course->promotion_type == 1){
                $total =  $course->price - $model->value;
                if($total < 0){
                  $course->pro_price = 0;
                }else{
                  $course->pro_price = $total;
                }
              }else{
                $total =  $course->price-($course->price*($model->value/100));
                if($total < 0){
                  $course->pro_price = 0;
                }else{
                  $course->pro_price = $total;
                }
              }
            $course->save();
          }
          $log = new CoursePromotionLog();
          $log->promotion_id = $model->id;
          $log->course_id = $value->id;
          $log->save();
        }
      }

      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = CoursePromotion::find($request->id);

      $log = CoursePromotionLog::where('promotion_id', $model->id)->get();
      foreach($log as $del){
        $course = Course::find($del->course_id);
        $course->is_promotion = 2;
        $course->pro_price = 0;
        $course->pro_started = null;
        $course->pro_end = null;
        $course->pro_period = 1;
        $course->save();
        $del->delete();
      }
      
      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = CoursePromotion::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
