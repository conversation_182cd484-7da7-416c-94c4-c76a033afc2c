<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\DynamicCert;
use App\Models\Core\DynamicCertGroup;
use App\Models\Core\UserGraduateLog;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class ReportController extends Controller
{
    public $prefix = 'report';
    public $project_url = 'report';
    public $project_layout = 'admin.Lrk.report';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      return new Dummy();
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function update(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='report_cert_filter') {
          
          $data = DB::table('report_download_cert_log');
          $data->join('user', 'user.id', 'report_download_cert_log.user_id');
          $data->select('report_download_cert_log.*', 'user.name as u_name', 'user.lastname as u_lastname');

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('report_download_cert_log.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'ชื่อ'=>'ชื่อ',
            'นามสกุล'=>'นามสกุล',
            'วันที่'=>'วันที่',
          );

          foreach ($data as $key=>$e) {

            $ex_array[]=array(
              'ชื่อ'=>$e->u_name,
              'นามสกุล'=>$e->u_lastname,
            'วันที่'=>$e->created_at,
            );
          }
        }else if($request->name=='report_progress_filter'){
          
          $data = DB::table('user_certificate_log');
          $data->join('user', 'user.id', 'user_certificate_log.user_id');
          $data->select('user_certificate_log.*', 'user.name as u_name', 'user.lastname as u_lastname');

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_certificate_log.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'ชื่อ'=>'ชื่อ',
            'นามสกุล'=>'นามสกุล',
            'ประเภท'=>'ประเภท',
            'ใบประกาศ'=>'ใบประกาศ',
            'วันที่'=>'วันที่',
          );

          foreach ($data as $key=>$e) {

            $cert = '';
            if($e->certificate_type == 1){
              $cer_name = DynamicCert::where('id', $e->cert_id)->first();
              if($cer_name){
                if($cer_name->lang_type == 2){
                  $cert = $cer_name->course_name_en;
                }else{
                  $cert = $cer_name->couse_name;
                }
              }
            }else if($e->certificate_type == 3){
              $cer_name = DynamicCertGroup::where('id', $e->group_cert_id)->first();
              if($cer_name){
                $cert = $cer_name->series_name;
              }
            }else{
              $cer_name = UserGraduateLog::join('course', 'course.id', 'user_graduate_log.course_id')->where('user_graduate_log.id', $e->graduate_id)->first();
              if($cer_name){
                $cert = $cer_name->title_th;
              }
            }

            if($e->certificate_type == 1){
              $e->certificate_type = 'Course';
            }else if($e->certificate_type == 2){
              $e->certificate_type = 'Global';
            }else{
              $e->certificate_type = 'Series';
            }
            $ex_array[]=array(
            'ชื่อ'=>$e->u_name,
            'นามสกุล'=>$e->u_lastname,
            'ประเภท'=>$e->certificate_type,
            'ใบประกาศ'=>$cert,
            'วันที่'=>$e->created_at,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

}
