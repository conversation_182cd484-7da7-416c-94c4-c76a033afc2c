<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Exports\CourseMultipleSheetExport;


use App\Http\Controllers\Controller;

use App\Models\Core\Course;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Article;
use App\Models\Core\CateAge;
use App\Models\Core\CateBusinessUnit;
use App\Models\Core\CateDepartment;
use App\Models\Core\CateGender;
use App\Models\Core\Categories;
use App\Models\Core\CategoriesSub;
use App\Models\Core\CateHost;
use App\Models\Core\CateInteresting;
use App\Models\Core\CateJobFunction;
use App\Models\Core\CateKeyword;
use App\Models\Core\CateLearner;
use App\Models\Core\CateSpeaker;
use App\Models\Core\CateSubUnit;
use App\Models\Core\CouponCourse;
use App\Models\Core\CourseAgeLog;
use App\Models\Core\CourseBusinessLog;
use App\Models\Core\CourseCateLog;
use App\Models\Core\CourseCheckerLog;
use App\Models\Core\CourseDepartmentLog;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\CourseExamLog;
use App\Models\Core\CourseFileLog;
use App\Models\Core\CourseGenderLog;
use App\Models\Core\CourseInteresting;
use App\Models\Core\CourseJobFunction;
use App\Models\Core\CourseJobLevel;
use App\Models\Core\CourseKeywordLog;
use App\Models\Core\CourseLeranerLog;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\CourseLogoLog;
use App\Models\Core\CourseRate;
use App\Models\Core\CourseSpeakerLog;
use App\Models\Core\CourseSubCate;
use App\Models\Core\CourseSubUnitLog;
use App\Models\Core\CourseTagLog;
use App\Models\Core\DynamicCert;
use App\Models\Core\GroupCode;
use App\Models\Core\UsersCommentLog;
use App\Models\Core\UsersCommentLikeLog;
use App\Models\Core\UsersExamLog;
use App\Models\Core\UsersExamPoint;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\Tags;
use App\Models\Core\UsersCartLog;
use App\Models\Core\UsersLearningLog;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class CourseInterController extends Controller
{
    public $prefix = 'course_inter';
    public $project_url = 'course_inter';
    public $project_layout = 'admin.Lrk.course_inter';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $course_all = Course::where('trailer_media', 1)->where('international', 2)->count();
      $course_open = Course::where('trailer_media', 1)->where('international', 2)->where('status', 1)->count();
      $course_closed = Course::where('trailer_media', 1)->where('international', 2)->where('status', 2)->count();
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_all','course_open','course_closed'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = new Course();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
    }

    public function edit($id)
    {

      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = Course::find($id);
      if($model){
        if($model->is_due == 2){
          $model->duration_time = 0;
        }
        $model->department = implode(',',CourseDepartmentLog::where('course_id',$model->id)->pluck('course_department_log.department')->toArray());
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['course.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['course.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['course.id', $value['search']['value']]);
          }else if($value['data']=='l_title'){
            array_push($where_search, ['categories_level.title_th', $value['search']['value']]);
          }else{
            array_push($where_search, ['course.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      if(Auth::user()->level == 99){
        $query = DB::table('course')
        ->leftjoin('categories_level', 'categories_level.id', 'course.level')
        ->leftjoin('categories', 'categories.id', 'course.categories')
        ->select('course.*', 'categories_level.title_th as l_title','categories.title_th as sub_section')
        ->where('course.trailer_media', 1)
        ->where('course.international', 2)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('course')
        ->leftjoin('categories_level', 'categories_level.id', 'course.level')
        ->leftjoin('categories', 'categories.id', 'course.categories')
        ->where('course.trailer_media', 1)
        ->where('course.international', 2)
        ->where($where_search)
        ->count();
      }else if(Auth::user()->level == '88'){
        $arr_id = CourseCheckerLog::where('admin_id', Auth::user()->id)->pluck('admin_id')->toArray();
        $query = DB::table('course')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->leftjoin('categories_level', 'categories_level.id', 'course.level')
        ->leftjoin('categories', 'categories.id', 'course.categories')
        ->select('course.*', 'course_checker_log.admin_id', 'categories_level.title_th as l_title','categories.title_th as sub_section')
        ->whereIn('course_checker_log.admin_id', $arr_id)
        ->where('course.trailer_media', 1)
        ->where('course.international', 2)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();

        $count_total = DB::table('course')
        ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
        ->leftjoin('categories_level', 'categories_level.id', 'course.level')
        ->leftjoin('categories', 'categories.id', 'course.categories')
        ->whereIn('course_checker_log.admin_id', $arr_id)
        ->where('course.trailer_media', 1)
        ->where('course.international', 2)
        ->where($where_search)
        ->count();
      }

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
          
        if($value->categories != '' || $value->categories != null){
          $categories = explode(",",$value->categories);
          $cate_name = '';
          foreach($categories as $key_cate=>$val_cate){
            $cate_q = Categories::where('id', $val_cate)->select('title_th')->first();
            if($cate_q){
              if($cate_name != ''){
                $cate_name .= ', ';
              }
              $cate_name .= $cate_q->title_th;
            }
          }
          $obj['sub_sec'] = $cate_name;
        }else{
          $obj['sub_sec'] = 'ไม่มีข้อมูล';
        }
        
        if($value->learner != '' || $value->learner != null){
          $learner = explode(",",$value->learner);
          $cate_name = '';
          foreach($learner as $key_cate=>$val_cate){
            $cate_q = CateLearner::where('id', $val_cate)->select('title_th')->first();
            if($cate_q){
              if($cate_name != ''){
                $cate_name .= ', ';
              }
              $cate_name .= $cate_q->title_th;
            }
          }
          $obj['cate_learner'] = $cate_name;
        }else{
          $obj['cate_learner'] = 'ไม่มีข้อมูล';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $model = new Course;

      $slug_clean = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug = Course::where('course.slug','=', $slug_clean)->count();

      if($slug==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : AppHelper::instance()->cleanInputSlug($request->title_th);
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }
      
      $model->course_key = isset($request->course_key) ? AppHelper::instance()->cleanInput($request->course_key) : null;


      $model->trailer_media = 1;
      $model->international = 2;
      // $model->is_suggess = isset($request->is_suggess) ?  AppHelper::instance()->cleanInput($request->is_suggess) : 1;
      // $model->is_option = isset($request->is_option) ? $request->is_option : 1;

      $model->level = isset($request->level) ? $request->level : null;
      $model->key_search = isset($request->key_search) ?  AppHelper::instance()->cleanInput($request->key_search) : null;

      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = null;
      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInputBr($request->subtitle_th) : null;
      $model->subtitle_en = null;
      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;
      $model->details_en = null;
      $model->profit = isset($request->profit) ? AppHelper::instance()->convertDomHTML($request->profit) : null;
      $model->profit_en = null;

      $model->teaser = isset($request->teaser) ? AppHelper::instance()->cleanInput($request->teaser) : null;

      $model->is_cert = isset($request->is_cert) ?  AppHelper::instance()->cleanInput($request->is_cert) : 2;
        
      $model->is_hot = isset($request->is_hot) ?  AppHelper::instance()->cleanInput($request->is_hot) : 1;
      $model->is_new = isset($request->is_new) ?  AppHelper::instance()->cleanInput($request->is_new) : 1;

      $model->is_cme = isset($request->is_cme) ? AppHelper::instance()->cleanInput($request->is_cme) : 1;
      if($model->is_cme == 1){
        $model->receive_point = isset($request->receive_point) ? AppHelper::instance()->cleanInput($request->receive_point) : 0;
      }else{
        $model->receive_point = 0;
      }

      $model->is_due = isset($request->is_due) ? AppHelper::instance()->cleanInput($request->is_due) : 1;
      if($model->is_due == 2){
        $model->duration_time = 99999;
      }else{
        $model->duration_time = isset($request->duration_time) ? AppHelper::instance()->cleanInput($request->duration_time) : 99999;
      }

      $model->is_free = isset($request->is_free) ?  AppHelper::instance()->cleanInput($request->is_free) : 1;
      $model->price = isset($request->price) ?  AppHelper::instance()->cleanInput($request->price) : 1;

      $model->is_time = isset($request->is_time) ?  AppHelper::instance()->cleanInput($request->is_time) : 2;
      $model->time_set = isset($request->time_set) ?  AppHelper::instance()->cleanInput($request->time_set) : 0;
      $model->is_doc = isset($request->is_doc) ?  AppHelper::instance()->cleanInput($request->is_doc) : 2;
      $model->doc_text = isset($request->doc_text) ?  AppHelper::instance()->cleanInput($request->doc_text) : null;

      
      $model->is_promotion = isset($request->is_promotion) ?  AppHelper::instance()->cleanInput($request->is_promotion) : 2;
      $model->pro_price = isset($request->pro_price) ?  AppHelper::instance()->cleanInput($request->pro_price) : 0;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;
      
      $model->started_learning = isset($request->started_learning) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_learning)->format('Y-m-d H:i:s') : null;
      $model->pro_started = isset($request->pro_started) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_started)->format('Y-m-d H:i:s') : null;
      $model->pro_end = isset($request->pro_end) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_end)->format('Y-m-d H:i:s') : null;
      $model->pro_period = isset($request->pro_period) ?  AppHelper::instance()->cleanInput($request->pro_period) : 1;
      if ($request->hasFile('image_th')) {
        $model->image_th = AppHelper::instance()->saveImage($request->file('image_th'),'/upload/course');
      }

      if ($request->hasFile('banner_m')) {
        $model->banner_m = AppHelper::instance()->saveImage($request->file('banner_m'),'/upload/course');
      }

      if ($request->hasFile('banner')) {
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/course');
      }

      if ($request->hasFile('og_image')) {
        $model->og_image = AppHelper::instance()->saveImage($request->file('og_image'),'/upload/course');
      }

      $count = Course::where('trailer_media', 1)->where('international', 2)->count();
      $model->position = $count + 1;

      $model->categories = is_array($request->categories) ? implode(",",$request->categories) : null;

      $model->status = $request->status;
  
      //เช็ค SPEAKER
      $list_tags = array();
      if(is_array($request->speaker)){
        $all_tags_val = $request->speaker;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateSpeaker::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateSpeaker();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateSpeaker::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->speaker = $list_tags;
      $model->speaker = implode(",",$request->speaker);
      //เช็ค SPEAKER
  
      //เช็ค Tags
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tags_val = $request->tag;
        foreach ($all_tags_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      //เช็ค Tags

      $model->save();
      
      //SPEAKER
      if(is_array($request->speaker)){ //ถ้า speaker มีค่า
        foreach($request->speaker as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseSpeakerLog();
          $log->course_id = $model->id;
          $log->speaker = $value;
          $log->save();
        }
      }
      //SPEAKER

      //TAG
      if(is_array($request->tag)){ //ถ้า tags มีค่า
        foreach($request->tag as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseTagLog();
          $log->course_id = $model->id;
          $log->tag_id = $value;
          $log->save();
        }
      }
      //TAG

      //categories
      if(is_array($request->categories)){ //ถ้า categories มีค่า
        foreach($request->categories as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CourseCateLog();
          $log->course_id = $model->id;
          $log->cate_id = $value;
          $log->save();
        }
      }
      //categories

      return redirect(route('admin').'/course_inter/'.$model->id.'/edit/')->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $model = Course::find($id);

      //SPEAKER
      $old_tags = explode(',', $model->speaker);
      $list_tags = array();
      if(is_array($request->speaker)){
        $all_tag_val = $request->speaker;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = CateSpeaker::where('title_th',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new CateSpeaker();
              $tags->title_th = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = CateSpeaker::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->speaker = $list_tags;
      $model->speaker = implode(",",$request->speaker);
      if(is_array($request->speaker)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->speaker))>0 || count(array_diff($request->speaker, $old_tags))>0){
          $tags = CourseSpeakerLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->speaker as $value) {
            $log = new CourseSpeakerLog();
            $log->course_id = $model->id;
            $log->speaker = $value;
            $log->save();
          }
        }
      }else{
        $tags = CourseSpeakerLog::where('course_id', $model->id);
        $tags->delete();
      }
      //SPEAKER

      //Tags
      $old_tags = explode(',', $model->tag);
      $list_tags = array();
      if(is_array($request->tag)){
        $all_tag_val = $request->tag;
        foreach ($all_tag_val as $key => $value) {
          if(!is_numeric($value)){
            $tags = Tags::where('title',$value)->first();
            if(!$tags&&AppHelper::instance()->cleanInput($value)!=''){
              $tags = new Tags();
              $tags->title = AppHelper::instance()->cleanInput($value);
              $tags->status = 1;
              $tags->save();
            }
          }else{
            $tags = Tags::where('id',$value)->first();
          }
          if($tags){
            array_push($list_tags,$tags->id);
          }
        }
      }
      $request->tag = $list_tags;
      $model->tag = implode(",",$request->tag);
      if(is_array($request->tag)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        if(count(array_diff($old_tags, $request->tag))>0 || count(array_diff($request->tag, $old_tags))>0){
          $tags = CourseTagLog::where('course_id', $model->id);
          $tags->delete();

          foreach($request->tag as $value) {
            $log = new CourseTagLog();
            $log->course_id = $model->id;
            $log->tag_id = $value;
            $log->save();
          }
        }
      }else{
        $tags = CourseTagLog::where('course_id', $model->id);
        $tags->delete();
      }
      //TAG

      //categories
      $old_tags = explode(',', $model->categories);
      $model->categories = is_array($request->categories) ? implode(",",$request->categories) : null;
      if(is_array($request->categories)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->categories);

        if(count(array_diff($old_tags, $request->categories))>0 || count(array_diff($request->categories, $old_tags))>0){
          $log = CourseCateLog::where('course_id', $model->id);
          $log->delete();

          foreach($request->categories as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new CourseCateLog();
            $log->course_id = $model->id;
            $log->cate_id = $value;
            $log->save();
          }
        }
      }else{
        $log = CourseCateLog::where('course_id', $model->id);
        $log->delete();
      }
      //CATEGORY

      // $model->is_option = isset($request->is_option) ?  $request->is_option : $model->is_option;
      // $model->is_suggess = isset($request->is_suggess) ?  AppHelper::instance()->cleanInput($request->is_suggess) : $model->is_suggess;

      $model->is_hot = isset($request->is_hot) ?  $request->is_hot : $model->is_hot;
      $model->is_new = isset($request->is_new) ?  $request->is_new : $model->is_new;
      $model->course_key = isset($request->course_key) ? AppHelper::instance()->cleanInput($request->course_key) : null;
      $model->trailer_media = 1;
      $model->international = 2;
      $model->is_cert = isset($request->is_cert) ?  AppHelper::instance()->cleanInput($request->is_cert) : 2;

      $model->key_search = isset($request->key_search) ?  AppHelper::instance()->cleanInput($request->key_search) : null;

      $model->level = isset($request->level) ? $request->level : null;

      if ($request->hasFile('banner')) {
        if($model->banner!=null&&$model->banner!=''){
          AppHelper::instance()->removeImage($model->banner);
        }
        $model->banner = AppHelper::instance()->saveImage($request->file('banner'),'/upload/course');
      }else{
        if(empty($request->source_banner)){
          if($model->banner!=null&&$model->banner!=''){
            AppHelper::instance()->removeImage($model->banner);
          }
          $model->banner = null;
        }
      }

      if ($request->hasFile('og_image')) {
        if($model->og_image!=null&&$model->og_image!=''){
          AppHelper::instance()->removeImage($model->og_image);
        }
        $model->og_image = AppHelper::instance()->saveImage($request->file('og_image'),'/upload/course');
      }else{
        if(empty($request->source_og_image)){
          if($model->og_image!=null&&$model->og_image!=''){
            AppHelper::instance()->removeImage($model->og_image);
          }
          $model->og_image = null;
        }
      }
      
      $model->title_th = isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null;
      $model->title_en = null;

      $model->subtitle_th = isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null;
      $model->subtitle_en = null;

      $model->details_th = isset($request->details_th) ? AppHelper::instance()->convertDomHTML($request->details_th) : null;
      $model->details_en = null;

      $model->profit = isset($request->profit) ? AppHelper::instance()->convertDomHTML($request->profit) : null;
      $model->profit_en = null;

      if ($request->hasFile('image_th')) {
        if($model->image_th!=null&&$model->image_th!=''){
          AppHelper::instance()->removeImage($model->image_th);
        }
        $model->image_th = AppHelper::instance()->saveImage($request->file('image_th'),'/upload/course');
      }else{
        if(empty($request->source_image_th)){
          if($model->image_th!=null&&$model->image_th!=''){
            AppHelper::instance()->removeImage($model->image_th);
          }
          $model->image_th = null;
        }
      }

      if ($request->hasFile('banner_m')) {
        if($model->banner_m!=null&&$model->banner_m!=''){
          AppHelper::instance()->removeImage($model->banner_m);
        }
        $model->banner_m = AppHelper::instance()->saveImage($request->file('banner_m'),'/upload/course');
      }else{
        if(empty($request->source_banner_m)){
          if($model->banner_m!=null&&$model->banner_m!=''){
            AppHelper::instance()->removeImage($model->banner_m);
          }
          $model->banner_m = null;
        }
      }

      $model->is_cme = isset($request->is_cme) ? AppHelper::instance()->cleanInput($request->is_cme) : 1;
      if($model->is_cme == 1){
        $model->receive_point = isset($request->receive_point) ? AppHelper::instance()->cleanInput($request->receive_point) : 0;
      }else{
        $model->receive_point = 0;
      }

      $model->is_due = isset($request->is_due) ? AppHelper::instance()->cleanInput($request->is_due) : 1;
      if($model->is_due == 2){
        $model->duration_time = 99999;
      }else{
        $model->duration_time = isset($request->duration_time) ? AppHelper::instance()->cleanInput($request->duration_time) : 99999;
      }
      
      $model->is_free = isset($request->is_free) ?  AppHelper::instance()->cleanInput($request->is_free) : $model->is_free;
      if($model->is_free == 1){
        $model->price = 0;
      }else{
        $model->price = isset($request->price) ? AppHelper::instance()->cleanInput($request->price) : 0;
      }

      $model->is_time = isset($request->is_time) ?  AppHelper::instance()->cleanInput($request->is_time) : $model->is_time;
      if($model->is_time == 2){
        $model->time_set = 0;
      }else{
        $model->time_set = isset($request->time_set) ? AppHelper::instance()->cleanInput($request->time_set) : 0;
      }

      $model->is_doc = isset($request->is_doc) ?  AppHelper::instance()->cleanInput($request->is_doc) : $model->is_doc;
      if($model->is_doc == 2){
        $model->doc_text = null;
      }else{
        $model->doc_text = isset($request->doc_text) ? AppHelper::instance()->cleanInput($request->doc_text) : null;
      }

      $model->is_promotion = isset($request->is_promotion) ? $request->is_promotion : $model->is_promotion;
      if($model->is_promotion == 1){
        $model->pro_price = isset($request->pro_price) ? AppHelper::instance()->cleanInput($request->pro_price) : 0;
        $model->pro_started = isset($request->pro_started) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_started)->format('Y-m-d H:i:s') : null;
        $model->pro_end = isset($request->pro_end) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->pro_end)->format('Y-m-d H:i:s') : null;
        $model->pro_period = isset($request->pro_period) ?  AppHelper::instance()->cleanInput($request->pro_period) : 1;
      }else{
        $model->pro_price = 0;
        $model->pro_started = null;
        $model->pro_end = null;
        $model->pro_period = 1;
      }

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;
      
      $model->started_learning = isset($request->started_learning) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_learning)->format('Y-m-d H:i:s') : null;
      
      $slug_clean_th = AppHelper::instance()->cleanInputSlug($request->slug);
      $slug_th = Course::where('course.slug','=', $slug_clean_th)->where('course.id','!=', $id)->count();
      if($slug_th==0){
        $model->slug = isset($request->slug) ? AppHelper::instance()->cleanInputSlug($request->slug) : '';
      }else{
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $ramdomNumber = '';
        for ($i = 0; $i < 4; $i++) {
            $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
        }
        $slug_plus = $ramdomNumber;
        $slug_text = $request->slug.''.$slug_plus;
        $model->slug = AppHelper::instance()->cleanInputSlug($slug_text);
      }

      $old_tags = explode(',', $model->checker); 
      $model->checker = is_array($request->checker) ? implode(",",$request->checker) : ''; 
      if(is_array($request->checker)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->checker); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

        if(count(array_diff($old_tags, $request->checker))>0 || count(array_diff($request->checker, $old_tags))>0){
          $log = CourseCheckerLog::where('course_id', $model->id);
          $log->delete();

          foreach($request->checker as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new CourseCheckerLog();
            $log->course_id = $model->id;
            $log->admin_id = $value;
            $log->save();
          }
        }
      }

      $model->status = $request->status;

      $model->course_duration = CourseLessonLog::where('course_id', $model->id)->where('status', 1)->sum(DB::raw('TIME_TO_SEC(duration)'));

      $model->save();


      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function position(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = Course::where('trailer_media',1)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = Course::where('trailer_media',1)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin'));
      }

      $model = Course::find($request->id);

      $age = CourseAgeLog::where('course_id', $model->id)->get();
      foreach($age as $key_age => $val_age){
        $val_age->delete();
      }

      $cate = CourseCateLog::where('course_id', $model->id)->get();
      foreach($cate as $key_cate => $val_cate){
        $val_cate->delete();
      }

      $checker = CourseCheckerLog::where('course_id', $model->id)->get();
      foreach($checker as $key_checker => $val_checker){
        $val_checker->delete();
      }

      $dept = CourseDepartmentLog::where('course_id', $model->id)->get();
      foreach($dept as $key_dept => $val_dept){
        $val_dept->delete();
      }

      $lesson = CourseLessonLog::where('course_id', $model->id)->get();
      foreach($lesson as $key_lesson => $val_lesson){
        $learning = UsersLearningLog::where('lesson_id', $val_lesson->id)->get();
        foreach($learning as $key_learning=>$val_learnint){
          $val_learnint->delete();
        }
        $val_lesson->delete();
      }

      $exam = CourseExamLog::where('course_id', $model->id)->get();
      foreach($exam as $key_exam => $val_exam){
        $answer = CourseExamAnswer::where('exam_id', $val_exam->id)->get();
        foreach($answer as $key_answer => $val_answer){
          $val_answer->delete();
        }
        $val_exam->delete();
      }

      $file = CourseFileLog::where('course_id', $model->id)->get();
      foreach($file as $key_file => $val_file){
        $val_file->delete();
      }

      $gender = CourseGenderLog::where('course_id', $model->id)->get();
      foreach($gender as $key_gender => $val_gender){
        $val_gender->delete();
      }

      $learner = CourseLeranerLog::where('course_id', $model->id)->get();
      foreach($learner as $key_learner => $val_learner){
        $val_learner->delete();
      }

      $rate = CourseRate::where('course_id', $model->id)->get();
      foreach($rate as $key_rate => $val_rate){
        $val_rate->delete();
      }

      $speaker = CourseSpeakerLog::where('course_id', $model->id)->get();
      foreach($speaker as $key_speaker => $val_speaker){
        $val_speaker->delete();
      }

      $tag = CourseTagLog::where('course_id', $model->id)->get();
      foreach($tag as $key_tag => $val_tag){
        $val_tag->delete();
      }

      // $coupon = CouponCourse::where('course_id', $model->id)->get();
      // foreach($coupon as $key_coupon => $val_coupon){
      //   $val_coupon->delete();
      // }

      $cart = UsersCartLog::where('course_id', $model->id)->get();
      foreach($cart as $key_cart => $val_cart){
        $val_cart->delete();
      }

      $comment = UsersCommentLog::where('course_id', $model->id)->get();
      foreach($comment as $key_comment => $val_comment){
        $like = UsersCommentLikeLog::where('course_id', $val_comment->id)->get();
        foreach($like as $key_like=>$val_like){
          $val_like->delete();
        }
        $val_comment->delete();
      }

      $exam = UsersExamLog::where('course_id', $model->id)->get();
      foreach($exam as $key_exam => $val_exam){
        $point = UsersExamPoint::where('exam_id', $val_exam->id)->get();
        foreach($point as $key_point=>$val_point){
          $val_point->delete();
        }
        $val_exam->delete();
      }
    
      $fav = UsersFavoriteLog::where('course_id', $model->id)->get();
      foreach($fav as $key_fav => $val_fav){
        $val_fav->delete();
      }

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return response()->json();
      }
      $model = Course::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }else if($model->status=='2'){
        $model->status = '3';
      }else if($model->status=='3'){
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function synce(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;
      $response['offset'] = '';
      if(isset($request->store)){
        $response['store'] = intVal($request->store);
      }
      if(isset($request->update)){
        $response['update'] = intVal($request->update);
      }
      if(isset($request->offset)){
        $api_url = 'https://api.airtable.com/v0/appoGtygIszKFoaFn/Lrk-Content%20Library?offset='.str_replace("\/","/",$request->offset);
      }else{
        $api_url = 'https://api.airtable.com/v0/appoGtygIszKFoaFn/Lrk-Content%20Library';
      }

      $curl = \curl_init();

      curl_setopt_array($curl, array(
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
          'Authorization: Bearer key7Q9eTPLinAr3S3',
          'Cookie: brw=brwbSHsKUcO9LpHaJ'
        ),
      ));

      $response_curl = curl_exec($curl);
      // AppHelper::instance()->consoleLog(json_decode($response));

      curl_close($curl);
      if($response_curl){
        $res_curl = json_decode($response_curl);
        if(isset($res_curl->offset)){
          $response['offset'] = $res_curl->offset;
        }
        if(isset($res_curl->records)){
          $excels = json_decode($response_curl)->records;
          if(count($excels)>0){
            foreach ($excels as &$excel) {
              $type = '';
              $field = $excel->fields;
              
              $field_en = json_decode(json_encode($field),true);
                if(isset($field_en['Content ID'])){
                  if(!empty($field_en['Content ID'])){
                    $result = Course::where('course_key','=', $field_en['Content ID'])->first();
                    if($result){
                      $model_excel = Course::find($result->id);
                      $type = 'update';
                    }else{
                      $model_excel = new Course();
                      $type = 'store';
                    }
                  }else{
                    $model_excel = new Course();
                    $type = 'store';
                  }
                }

                $trust_air = false;

                if($type=='update'&&isset($field_en['Time Stamp'])){
                  try {
                    $time_server = Carbon::createFromFormat('Y-m-d H:i:s', $model_excel->updated_at)->setTimezone('Asia/Bangkok');
                    $time_air = Carbon::createFromFormat('Y-m-d H:i:s', $field_en['Time Stamp'])->setTimezone('Asia/Bangkok');
                    if($time_air->greaterThan($time_server)){
                      $trust_air = true;
                    }
                  } catch (\Exception $e) {
                  }
                  
                }
                
                $info = Article::where('is_article', 3)->delete();

                // เพิ่มหรืออัพเดทข้อมูลตามชื่อหัวของ column ในไฟล์ excel
                if($trust_air||$type=='store'){
                  $model_excel->title_th = isset($field->CourseName) ? AppHelper::instance()->cleanInput($field->CourseName) : null;
                }


                if($model_excel->category_air == '' || $model_excel->category_air == null){
                  $model_excel->category_air = isset($field->Category) ? AppHelper::instance()->cleanInput($field->Category) : null;
                }else{
                  if($trust_air||$type=='store'){
                    $model_excel->category_air = isset($field->Category) ? AppHelper::instance()->cleanInput($field->Category) : null;
                  }
                }

                if($model_excel->media_type == '' || $model_excel->media_type == null){
                  $model_excel->media_type = isset($field_en['Media Type']) ? AppHelper::instance()->cleanInput($field_en['Media Type']) : null;
                }else{
                  if($trust_air||$type=='store'){
                    $model_excel->media_type = isset($field_en['Media Type']) ? AppHelper::instance()->cleanInput($field_en['Media Type']) : null;
                  }
                }

                if($model_excel->sku_type == '' || $model_excel->sku_type == null){
                  $model_excel->sku_type = isset($field_en['SKU Type']) ? AppHelper::instance()->cleanInput($field_en['SKU Type']) : null;
                }else{
                  if($trust_air||$type=='store'){
                    $model_excel->sku_type = isset($field_en['SKU Type']) ? AppHelper::instance()->cleanInput($field_en['SKU Type']) : null;
                  }
                }
                
                $model_excel->course_key = isset($field_en['Content ID']) ? AppHelper::instance()->cleanInput($field_en['Content ID']) : null;

                if(isset($field_en['Year Member Pack'])){
                  if($field_en['Year Member Pack'] == 0){
                    $model_excel->is_sub = 2;
                  }else{
                    $model_excel->is_sub = 1;
                  }
                }else{
                  $model_excel->is_sub = 2;
                }

                if(isset($field->certificate)){
                  if($field->certificate == 1){
                    $model_excel->is_cert = 1;
                  }else{
                    $model_excel->is_cert = 2;
                  }
                }else{
                  $model_excel->is_cert = 2;
                }

                if(isset($field_en['CME Points'])){
                  if($field_en['CME Points'] != 0 || $field_en['CME Points'] != '' || $field_en['CME Points'] != null){
                    $model_excel->is_cme = 1;
                    $model_excel->receive_point = $field_en['CME Points'];
                  }else{
                    $model_excel->is_cme = 2;
                    $model_excel->receive_point = 0;
                  }
                }else{
                  $model_excel->is_cme = 2;
                  $model_excel->receive_point = 0;
                }

                $slug_clean = AppHelper::instance()->cleanInputSlug($model_excel->title_th);
                $slug = Course::where('course.slug','=', $slug_clean)->count();

                if($slug == 0){
                  $model_excel->slug = isset($model_excel->title_th) ? AppHelper::instance()->cleanInputSlug($model_excel->title_th) : null;
                }else{
                  $characters = '0123456789';
                  $charactersLength = strlen($characters);
                  $ramdomNumber = '';
                  for ($i = 0; $i < 4; $i++) {
                      $ramdomNumber .= $characters[rand(0, $charactersLength - 1)];
                  }
                  $slug_plus = $ramdomNumber;
                  $slug_text = $model_excel->title_th.''.$slug_plus;
                  $model_excel->slug = AppHelper::instance()->cleanInputSlug($slug_text);
                }

                if($type=='store'){
                  $model_excel->image_th = '/assets/images/course_10.jpg';
                  $model_excel->banner = '/assets/images/sample_new.jpeg';
                  $model_excel->status = 2;
                }

                $model_excel->is_suggess = 2;
                $model_excel->is_promotion = 2;
                
                //Content Category (on Platform)
                if($trust_air||$type=='store'){
                  if(isset($field_en['Content Menu'])){
                    if($field_en['Content Menu'] == 'คอร์สออนไลน์'){
                      $model_excel->trailer_media = 1;
                    }else if($field_en['Content Menu'] == 'Infographic'){
                      $model_excel->trailer_media = 5;
                    }else if($field_en['Content Menu'] == 'ประชุมวิชาการ'){
                      $model_excel->trailer_media = 4;
                    }
                  }
                }
                //Content Category (on Platform)

                //Set Price
                if(isset($field_en['Price'])){
                  if(isset($field_en['Set Price'])){
                    if($field_en['Price'] == 'Free'){
                      if($field_en['Set Price'] == 'Free'){
                        $model_excel->is_free = 1;
                        $model_excel->price = 0;
                        $model_excel->pro_price = 0;
                      }else{
                        $model_excel->is_free = 2;
                        $model_excel->price = isset($field_en['Set Price']) ? AppHelper::instance()->cleanInput($field_en['Set Price']) : 0;
                        $model_excel->is_promotion = 1;
                        $model_excel->pro_price = 0;
                      }
                    }else{
                      if($field_en['Set Price'] == 'Free'){
                        $model_excel->is_free = 1;
                        $model_excel->price = 0;
                        $model_excel->is_promotion = 1;
                        $model_excel->pro_price = isset($field_en['Price']) ? AppHelper::instance()->cleanInput($field_en['Price']) : 0;
                      }else{
                        $model_excel->is_free = 2;
                        $model_excel->price = isset($field_en['Set Price']) ? AppHelper::instance()->cleanInput($field_en['Set Price']) : 0;
                        $model_excel->is_promotion = 1;
                        $model_excel->pro_price = isset($field_en['Price']) ? AppHelper::instance()->cleanInput($field_en['Price']) : 0;
                      }
                    }
                  }else{
                    if($field_en['Price'] == 'Free'){
                      $model_excel->is_free = 1;
                      $model_excel->price = 0;
                    }else{
                      $model_excel->is_free = 2;
                      $model_excel->price = isset($field_en['Price']) ? AppHelper::instance()->cleanInput($field_en['Price']) : 0;
                    }
                  }
                }else{
                  if(isset($field_en['Set Price'])){
                    if($field_en['Set Price'] == 'Free'){
                      $model_excel->is_free = 1;
                      $model_excel->price = 0;
                    }else{
                      $model_excel->is_free = 2;
                      $model_excel->price = isset($field_en['Set Price']) ? AppHelper::instance()->cleanInput($field_en['Set Price']) : 0;
                    }
                  }
                }
                //Set Price

                //Web Public Date
                try {
                  $date_time = Carbon::parse($field_en['Web Public Date'])->format('Y-m-d H:i:s');
                  
                  $model_excel->started_date = $date_time;
                } catch (\Exception $e) {
                  $model_excel->started_date = null;
                }
                //Web Public Date

                //Level
                if($trust_air||$type=='store'){
                  if(isset($field->Level)){
                    if($field->Level == 'Beginner'){
                      $model_excel->level = 1;
                    }else if($field->Level == 'Advance'){
                      $model_excel->level = 2;
                    }else if($field->Level == 'Intermediate'){
                      $model_excel->level = 3;
                    }
                  }
                }
                //Level

                //Group ID
                // if(isset($field_en['Group ID'])){
                //   $group_id = GroupCode::where('group_code', $field_en['Group ID'])->first();
                //   if(!$group_id){
                //     $new_group = new GroupCode();
                //     $new_group->group_code = $field_en['Group ID'];
                //     $new_group->status = 2;
                //     $new_group->save();
                //     $model_excel->group_code = $new_group->group_code;
                //   }else{
                //     $model_excel->group_code = $group_id->group_code;
                //   }
                // }
                //Group ID

                //Content Group
                if($trust_air||$type=='store'){
                  if(isset($field_en['Sub Section'])){
                    $cate_dup = Categories::where('title_th', $field_en['Sub Section'])->first();
                    if(!$cate_dup){
                      $cate = new Categories();
                      $cate->title_th = $field_en['Sub Section'];
                      $cate->status = 1;
    
                      $count = Categories::all()->count();
                      $cate->position = $count + 1;
    
                      $cate->save();
                      $model_excel->categories = $cate->id;
                    }else{
                      $model_excel->categories = $cate_dup->id;
                    }
                  }
                }
                //Content Group

                //learner
                if($trust_air||$type=='store'){
                  $learner_arr = '';
                
                  if(isset($field_en['Learner'])){
                    foreach($field_en['Learner'] as $learner){
                      $learner_query = CateLearner::where('title_th',$learner)->first();
                      if(!$learner_query){
                        $learner_query = new CateLearner();
                        $learner_query->title_th = $learner;
                        $learner_query->title_th = trim($learner_query->title_th);
    
                        $count = CateLearner::all()->count();
                        $learner_query->position = $count + 1;
    
                        $learner_query->save();
                        $model_excel->learner = $learner_query->id;
                      }else{
                        $model_excel->learner = $learner_query->id;
                      }
                      if($learner_arr != ''){
                        $learner_arr .= ',';
                      }
                      $learner_arr .= $learner_query->id;
                      $model_excel->learner = $learner_arr;
                    }
                  }
                }
                //learner
                
                $response[$type]+=1;

                $model_excel->save();

                //Content Group
                if($trust_air||$type=='store'){
                  $content_group = CourseCateLog::where('course_id', $model_excel->id)->delete();
    
                  $new_cate_log = new CourseCateLog();
                  $new_cate_log->cate_id = $model_excel->categories;
                  $new_cate_log->course_id = $model_excel->id;
                  $new_cate_log->save();
                }
                //Content Group

                //learner
                if($trust_air||$type=='store'){
                  if(isset($learner_arr) && $learner_arr != 0){
                    $learner_ex = explode(',', $learner_arr);
                    if(is_array($learner_ex)){ 
                      foreach($learner_ex as $value) { 
                        $log = new CourseLeranerLog();
                        $log->course_id = $model_excel->id;
                        $log->learner	 = $value;
                        $log->save();
                      }
                    }
                  }
                }
                //learner

                //remove dup course speaker log
                $speakerRoles = CourseSpeakerLog::where('course_id',$model_excel->id)->groupBy('course_id', 'speaker')->get();
                $speakerRolesId = array_column($speakerRoles ->toArray(), 'id');
                CourseSpeakerLog::where('course_id',$model_excel->id)->whereNotIn('id', $speakerRolesId )->delete();
                //remove dup course speaker log

                // AppHelper::instance()->consoleLog($excel);
            }
          }
        }
        
      }
      $count_all = Course::count();
      if($count_all!=0){
        $response['percent'] = number_format(($response['store']+$response['update'])*100/$count_all);
      }else{
        $response['percent'] = 0;
      }
      
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
      // if($response['offset']==''){
      //   return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
      // }else{
      //   return redirect('/seedbankadmin/course_synce?offset='.$response['offset'].'&store='.$response['store'].'&update='.$response['update']);
      // }
    }
    public function synce_page(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      return view('admin.sync');
    }

    public function export(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='course_inter_export') {
          
          $data = DB::table('course');
          $data->leftjoin('categories_level', 'categories_level.id', 'course.level');
          $data->leftjoin('categories_learner', 'categories_learner.id', 'course.learner');
          $data->leftjoin('categories', 'categories.id', 'course.categories');
          $data->select(
              'course.id',
              'course.international',
              'course.title_th',
              'course.price',
              'course.pro_price',
              'course.is_free',
              'course.is_promotion',
              'course.course_key',
              'course.speaker',
              'course.trailer_media',
              'course.receive_point',
              'course.started_date',
              'course.end_date',
              'course.started_learning',
              'course.status',
              'course.created_at',
              'course.last_sync',
              'categories_level.title_th as c_level',
              'categories_learner.title_th as c_learner',
              'categories.title_th as c_cate',
              'course.sku_type',
              'course.is_sub',
              'course.is_cert',
              'course.updated_at',
              'course.categories',
              'course.speaker',
              'course.learner',
              'course.tag',
              'course.key_search',
              'course.media_type',
              'course.content_group',
              'course.category_air',
              'course.sub_category_air',
              'course.no_sku_in_group',
              'course.sku_in_group',
              'course.production_source',
              'course.remark',
              'course.content_format',
          );
          $data->where('course.trailer_media', '!=' ,5);
          $data->where('course.international', '=' ,2);
          $data->orderBy('course.id', 'asc');
          $data = $data->get();

          $ex_array[]=array(
            'Content ID'=>'Content ID',
            'Category'=>'Category',
            'Content Menu'=>'Content Menu',
            'Sub Section'=>'Sub Section',
            'Course Type'=>'Course Type',
            'CourseName'=>'CourseName',
            'Speaker'=>'Speaker',
            'Level'=>'Level',
            'Tag'=>'Tag',
            'Keyword'=>'Keyword',
            'Set Price'=>'Set Price',
            'Price'=>'Price',
           
            'Total Durations (mins)'=>'Total Durations (mins)',
            'Number of EP'=>'Number of EP',
            'Web Public Date'=>'Web Public Date',
            'Active Date'=>'Active Date',
            'Expire Date'=>'Expire Date',
            'Status'=>'Status',
            'certificate'=>'certificate',
            'Exam'=>'Exam',
            'Production Source'=>'Production Source',
            'Remarks'=>'Remarks',
            'Time Stamp'=>'Time Stamp',
          );

          foreach ($data as $key=>$e) {

            if($e->international == 1){
              $e->international = 'Normal';
            }else{
              $e->international = 'International';
            }

            $price = '';
            $set_price = '';

            if($e->is_free == 1 && $e->pro_price == 0 && $e->price == 0){
              $price = 'Free';
              $set_price = 'Free';
            }else if($e->is_free == 2 && $e->pro_price == 0 && $e->is_promotion == 1 && $e->price != 0){
              $set_price = $e->price;
              $price ='Free';
            }else if($e->is_free == 1 && $e->pro_price != 0 && $e->price == 0 && $e->is_promotion == 1){
              $set_price = 'Free';
              $price = $e->pro_price;
            }else if($e->is_free == 2 && $e->pro_price != 0 && $e->price != 0 && $e->is_promotion == 1){
              $set_price = $e->price;
              $price = $e->pro_price;
            }else if($e->is_free == 1 && $e->price == 0){
              $price = 'Free';
            }else if($e->is_free == 2 && $e->price != 0){
              $price = $e->price;
            }else if($e->is_free == 1 && $e->price == 0){
              $set_price = 'Free';
            }else if($e->is_free == 2 && $e->price != 0){
              $set_price = $e->price;
            }

            if($e->is_sub == 1){
              $e->is_sub = '1';
            }else{
              $e->is_sub = '0';
            }

            $duration = CourseLessonLog::where('course_id', $e->id)->where('status', 1)->sum(DB::raw('TIME_TO_SEC(duration)'));

            if ($duration) {
                $total_duration = round($duration / 60, 2);
            } else {
                $total_duration = '0';
            }

            $ep = CourseLessonLog::where('course_id', $e->id)->where('status', 1)->count();
            if ($ep > 0) {
                $ep = $ep;
            } else {
                $ep = ' ';
            }

            $exam = CourseExamLog::where('course_id', $e->id)->where('status', 1)->count();
            if ($exam) {
                $exam = 'Yes';
            } else {
                $exam = 'No';
            }

            if($e->is_cert == 1){
              $e->is_cert = '1';
            }else{
              $e->is_cert = '0';
            }

            if($e->trailer_media == 1){
              $e->trailer_media = 'คอร์สออนไลน์';
            }else if($e->trailer_media == 4){
              $e->trailer_media = 'ประชุมวิชาการ';
            }else if($e->trailer_media == 5){
              $e->trailer_media = 'Infographic';
            }
            
            $categories = explode(",",$e->categories);
            $cate_name = '';
            foreach($categories as $key_cate=>$val_cate){
              $cate_q = Categories::where('id', $val_cate)->select('title_th')->first();
              if($cate_q){
                if($cate_name != ''){
                  $cate_name .= ', ';
                }
                $cate_name .= $cate_q->title_th;
              }
            }

            $speaker = explode(",",$e->speaker);
            $speaker_name = '';
            foreach($speaker as $key_speaker=>$val_speaker){
              $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
              if($speaker_q){
                if($speaker_name != ''){
                  $speaker_name .= ', ';
                }
                $speaker_name .= $speaker_q->name;
              }
            }

            $tag = explode(",",$e->tag);
            $tag_name = '';
            foreach($tag as $key_tag=>$val_tag){
              $tag_q = Tags::where('id', $val_tag)->first();
              if($tag_q){
                if($tag_name != ''){
                  $tag_name .= ', ';
                }
                $tag_name .= $tag_q->title;
              }
            }
            
            $today = Carbon::now();
            $public_date = $e->started_date;
            $learning_date = $e->started_learning;

            if($public_date != null && $learning_date != null){
              if($today >= $public_date && $today >= $learning_date){
                $e->status = 'Online';
              }else if($today >= $public_date  && $today < $learning_date){
                $e->status = 'Online-Comimg Soon';
              }else{
                $e->status = 'Draff';
              }
            }else if($public_date == null && $learning_date != null){
              if($today >= $learning_date){
                $e->status = 'Online';
              }else if($today < $learning_date){
                $e->status = 'Online-Comimg Soon';
              }else{
                $e->status = 'Draff';
              }
            }else if($public_date != null && $learning_date == null){
              if($today >= $public_date && $learning_date == null){
                $e->status = 'Online-Comimg Soon';
              }else{
                $e->status = 'Draff';
              }
            }else{
              $e->status = ' ';
            }

            $ex_array[]=array(
              'Content ID'=>$e->course_key,
              'Category'=>$e->category_air,
              'Content Menu'=>$e->trailer_media,
              'Sub Section'=>$cate_name,
              'Course Type'=>$e->international,
              'CourseName'=>$e->title_th,
              'Speaker'=>$speaker_name,

              'Level'=>$e->c_level,
              'Tag'=>$tag_name,
              'Keyword'=>$e->key_search,

              'Set Price'=>$set_price,
              'Price'=>$price,
              'Total Durations (mins)'=>$total_duration,
              'Number of EP'=>$ep,
              'Web Public Date'=>$e->started_date,
              'Active Date'=>$e->started_learning,
              'Expire Date'=>$e->end_date,
              'Status'=>$e->status,
              'certificate'=>$e->is_cert,
              'Exam'=>$exam,
              'Production Source'=>$e->production_source,
              'Remarks'=>$e->remark,
              'Time Stamp'=>$e->updated_at,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

    public function _export_(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='course_export') {

                return FacadesExcel::download(new CourseMultipleSheetExport(), $request->filename.'.xlsx');

            }

            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                  if ($request->name=='course_export') {
                    $export = new CourseMultipleSheetExport();
                    return FacadesExcel::download($export, $request->filename.'.xlsx');
                  }else{
                    $export = new TempExport($ex_array);
                    return FacadesExcel::download($export, $request->filename.'.xlsx');
                  }

                } 
                else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                  );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

    public function exportSpeaker(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='speaker_export') {
                $data = DB::table('categories_speaker')
                ->select('categories_speaker.id', 'categories_speaker.name', 'categories_speaker.title_th')
                ->orderBy('categories_speaker.id', 'asc')
                ->get();

                $ex_array[]=array(
                  'id'=>'id',
                  'Name'=>'Name',
                  'Full_Name'=>'Full_Name',
                );

                foreach ($data as $e) {

                  if($e->status == 1){
                    $e->status = 'Active';
                  }else{
                    $e->status = 'Inactive';
                  }


                  $ex_array[]=array(
                    'id'=>$e->id,
                    'Name'=>$e->name,
                    'Full_Name'=>$e->title_th,
                    
                  );
                }
            }

            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    $export = new TempExport($ex_array);

                    return FacadesExcel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }
}
