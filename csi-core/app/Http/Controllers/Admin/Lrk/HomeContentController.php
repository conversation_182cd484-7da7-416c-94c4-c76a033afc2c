<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\HomeContent;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\HomeDepartmentLog;
use App\Models\Core\HomeDiseaseLog;
use App\Models\Core\HomeGenderLog;
use App\Models\Core\HomeLearnerLog;
use App\Models\Core\HomeLevelLog;
use App\Models\Core\HomeOrgansLog;
use App\Models\Core\HomePopular;
use App\Models\Core\HomeSpeaker;
use App\Models\Core\HomeSpeakerLog;
use App\Models\Core\HomeSubCateLog;
use App\Models\Core\HomeTagLog;
use Maatwebsite\Excel\Facades\Excel;


class HomeContentController extends Controller
{
    public $prefix = 'home_content';
    public $project_url = 'home_content';
    public $project_layout = 'admin.Lrk.home_content';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
        $model = new HomeContent();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> route($this->prefix.'.store'),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
        try {
            $model = HomeContent::find($id);
            if($model){
              if($model->type==19){
                $model->group_id_1 = $model->group_id;
              }else if($model->type==17){
                $model->group_id_2 = $model->group_id;
              }

              
              $count_speaker = HomeSpeaker::where('home_content_id', $id)->count();
              $count_popular = HomePopular::where('home_content_id', $id)->count();

              $obj = array(
                'type'=>'edit',
                'route'=>route($this->prefix.'.update',$model->id),
                'prefix'=>$this->prefix,
                'project_url'=>$this->project_url,
                'project_layout'=>$this->project_layout
              );
              return view($this->project_layout.'.form', compact('model','obj','count_speaker','count_popular'));
            }else{
              return redirect(route('admin').'/'.$this->project_url)->with('error', 'ไม่พบข้อมูล');
            }
        } catch (\Exception $e) {
            return redirect(route('admin').'/'.$this->project_url)->with('error', 'เกิดข้อผิดพลาด: ' . $e->getMessage());
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['home_content.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['home_content.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['home_content.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['home_content.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('home_content')
      ->select('home_content.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('home_content')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }
      
      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      $model = new HomeContent;

      $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : null;
      $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
      $model->color = isset($request->color) ? AppHelper::instance()->cleanInput($request->color) : null;
      $model->show_video_details = isset($request->show_video_details) ? AppHelper::instance()->cleanInput($request->show_video_details) : 2;
      
      // บันทึก size สำหรับ type ต่างๆ ที่ต้องการ
      // ไม่บันทึก size สำหรับ type 17, 23, 24 และ type 30 เมื่อ show_video_details = 1
      if($model->type == 17 || $model->type == 23 || $model->type == 24){
        $model->size = null;
      }else if($model->type == 30 && $model->show_video_details == 1){
        $model->size = null;
      }else{
        // บันทึก size สำหรับ type อื่นๆ ทั้งหมด
        $model->size = isset($request->size) ? AppHelper::instance()->cleanInput($request->size) : null;
        if(in_array($model->type, [31, 32, 33, 35, 36, 37])){
          $model->show_video_details = 2;
        }
      }
      $model->sub_day = isset($request->sub_day) ? AppHelper::instance()->cleanInput($request->sub_day) : null;
      $model->popular_all = isset($request->popular_all) ? AppHelper::instance()->cleanInput($request->popular_all) : 1;
      
      $model->playlist = isset($request->playlist) ? AppHelper::instance()->cleanInput($request->playlist) : null;

      $count = HomeContent::all()->count();
      $model->position = $count + 1;

      $model->tag = is_array($request->tag) ? implode(",",$request->tag) : '';
      $model->category = is_array($request->category) ? implode(",",$request->category) : '';
      // $model->gender = is_array($request->gender) ? implode(",",$request->gender) : null;
      $model->level = is_array($request->level) ? implode(",",$request->level) : null;
      // $model->department = is_array($request->department) ? implode(",",$request->department) : null;
      $model->speaker = is_array($request->speaker) ? implode(",",$request->speaker) : null;
      // $model->learner = is_array($request->learner) ? implode(",",$request->learner) : null;

      // สำหรับ Type 31 (คอร์ส Online) ให้ใช้ subscription_main_id แทน category
      if($model->type == 31){
        $model->subscription_main_id = isset($request->subscription_main_id) ? AppHelper::instance()->cleanInput($request->subscription_main_id) : null;
        $model->category = ''; // ไม่ใช้ category สำหรับ Type 31
      }else{
        $model->subscription_main_id = null;
      }

      if($model->type == 17){
        $model->group_id = isset($request->group_id) ? $request->group_id : null;
        $model->name = 'series';
      }else{
        $model->group_id = null;
      }

      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      $model->subtitle = isset($request->subtitle) ? AppHelper::instance()->cleanInputBr($request->subtitle) : null;

      if($model->type != 29 && $model->type != 34){
        if ($request->hasFile('background')) {
          $model->background = AppHelper::instance()->saveImage($request->file('background'),'/upload/home_content');
        }
      }else{
        $model->background = null;
      }

      $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;
      $model->cta = isset($request->cta) ? AppHelper::instance()->cleanInput($request->cta) : null;


      $model->status = $request->status;
      $model->save();

      if(is_array($request->tag)){ 
        foreach($request->tag as $value) { 
          $log = new HomeTagLog();
          $log->home_content_id = $model->id;
          $log->tag = $value;
          $log->save();
        }
      }

      if(is_array($request->level)){ 
        foreach($request->level as $value) { 
          $log = new HomeLevelLog();
          $log->home_content_id = $model->id;
          $log->level = $value;
          $log->save();
        }
      }

      if(is_array($request->speaker)){ 
        foreach($request->speaker as $value) { 
          $log = new HomeSpeakerLog();
          $log->home_content_id = $model->id;
          $log->speaker = $value;
          $log->save();
        }
      }

      if($model->type ==10 || $model->type == 21 || $model->type == 24 || $model->type == 29 || $model->type == 35 || $model->type == 36 || $model->type == 37){
        return redirect(route('admin').'/home_content/'.$model->id.'/edit/')->with('success', 'Data has been update');
      }else{
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }

    }

    public function update(Request $request, $id)
    {

        $model = HomeContent::find($id);

        $model->type = isset($request->type) ? AppHelper::instance()->cleanInput($request->type) : null;

        if($model->type == 23){
          $model->link = null;
          $model->cta = null;
        }else{
          $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;
          $model->cta = isset($request->cta) ? AppHelper::instance()->cleanInput($request->cta) : null;
        }

        if($model->type == 24 || $model->type == 23 || $model->type == 29){
          $model->subtitle = isset($request->subtitle) ? AppHelper::instance()->cleanInputBr($request->subtitle) : null;
        }else{
          $model->subtitle = null;
        }

        if($model->type ==10){
          $model->sub_day = isset($request->sub_day) ? AppHelper::instance()->cleanInput($request->sub_day) : null;
        }else{
          $model->sub_day = null;
        }

        if($model->type ==5 || $model->type == 6){
          $model->name = null;
          $model->color = null;
          $model->size = null;
        }else if($model->type == 23){
          $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
          $model->color = null;
          $model->size = null;
        }
        else{
          $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
          $model->color = isset($request->color) ? AppHelper::instance()->cleanInput($request->color) : null;

          if($model->type == 17 || $model->type == 23 || $model->type == 24){
            $model->size = null;
            $model->show_video_details = 1;
          }else if($model->type == 30){
            if($request->show_video_details == 2){
              $model->show_video_details = 2;
              $model->size = isset($request->size) ? AppHelper::instance()->cleanInput($request->size) : null;
            }else{
              $model->show_video_details = 1;
              $model->size = null;
            }
          }else{
            $model->size = isset($request->size) ? AppHelper::instance()->cleanInput($request->size) : null;
            if(in_array($model->type, [31, 32, 33, 35, 36, 37])){
              $model->show_video_details = 2;
            }else{
              $model->show_video_details = 1;
            }
          }
        }

        if($model->type == 19){
          $model->group_id = isset($request->group_id_1) ? $request->group_id_1 : null;
          $model->name = null;
        }else if($model->type == 17){
          $model->group_id = isset($request->group_id_2) ? $request->group_id_2 : null;
          $model->name = null;
        }else{
          $model->group_id = null;
        }
        if($model->type == 29){
          $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
          $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
          $model->subtitle = isset($request->subtitle) ? AppHelper::instance()->cleanInputBr($request->subtitle) : null;
          $model->show_video_details = 2;
          $model->size = isset($request->size) ? AppHelper::instance()->cleanInput($request->size) : null;
          $model->link = isset($request->link) ? AppHelper::instance()->cleanInput($request->link) : null;
          $model->cta = isset($request->cta) ? AppHelper::instance()->cleanInput($request->cta) : null;
          $model->background = null; // Type 29 ไม่ใช้ background
        }else if($model->type == 34){
          $model->background = null; // Type 34 ไม่ใช้ background
        }else{
          $model->title = null;
          // บันทึก background สำหรับ type อื่นๆ ที่ไม่ใช่ 29 และ 34
          if ($request->hasFile('background')) {
            if($model->background!=null&&$model->background!=''){
              AppHelper::instance()->removeImage($model->background);
            }
            $model->background = AppHelper::instance()->saveImage($request->file('background'),'/upload/home_content');
          }else{
            if(empty($request->source_background)){
              if($model->background!=null&&$model->background!=''){
                AppHelper::instance()->removeImage($model->background);
              }
              $model->background = null;
            }
          }
        }
      
        $model->playlist = isset($request->playlist) ? AppHelper::instance()->cleanInput($request->playlist) : null;

        //CATE
        $old_tags = explode(',', $model->tag); 
        $model->tag = is_array($request->tag) ? implode(",",$request->tag) : ''; 
        
        //CATEGORY และ SUBSCRIPTION_MAIN
        // สำหรับ Type 31 (คอร์ส Online) ให้ใช้ subscription_main_id แทน category
        if($model->type == 31){
          $model->subscription_main_id = isset($request->subscription_main_id) ? AppHelper::instance()->cleanInput($request->subscription_main_id) : null;
          $model->category = '';
        }else{
          $model->subscription_main_id = null;
          $model->category = is_array($request->category) ? implode(",",$request->category) : ''; 
        }
        
        if(is_array($request->tag)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          $diff_tags = array_diff($old_tags, $request->tag); 

          if(count(array_diff($old_tags, $request->tag))>0 || count(array_diff($request->tag, $old_tags))>0){
            $log = HomeTagLog::where('home_content_id', $model->id);
            $log->delete();

            foreach($request->tag as $value) { 
              $log = new HomeTagLog();
              $log->home_content_id = $model->id;
              $log->tag = $value;
              $log->save();
            }
          }
        }
        //

        //SPEAKER
        $old_tags = explode(',', $model->speaker); 
        $model->speaker = is_array($request->speaker) ? implode(",",$request->speaker) : ''; 
        if(is_array($request->speaker)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          $diff_tags = array_diff($old_tags, $request->speaker); 

          if(count(array_diff($old_tags, $request->speaker))>0 || count(array_diff($request->speaker, $old_tags))>0){
            $log = HomeSpeakerLog::where('home_content_id', $model->id);
            $log->delete();

            foreach($request->speaker as $value) { 
              $log = new HomeSpeakerLog();
              $log->home_content_id = $model->id;
              $log->speaker = $value;
              $log->save();
            }
          }
        }
        //

        //LEVEL
        $old_tags = explode(',', $model->level); 
        $model->level = is_array($request->level) ? implode(",",$request->level) : ''; 
        if(is_array($request->level)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          $diff_tags = array_diff($old_tags, $request->level); 

          if(count(array_diff($old_tags, $request->level))>0 || count(array_diff($request->level, $old_tags))>0){
            $log = HomeLevelLog::where('home_content_id', $model->id);
            $log->delete();

            foreach($request->level as $value) { 
              $log = new HomeLevelLog();
              $log->home_content_id = $model->id;
              $log->level = $value;
              $log->save();
            }
          }
        }
        //
        
        $model->popular_all = isset($request->popular_all) ? AppHelper::instance()->cleanInput($request->popular_all) : 1;

        //LEARNER
        // $old_tags = explode(',', $model->learner); 
        // $model->learner = is_array($request->learner) ? implode(",",$request->learner) : ''; 
        // if(is_array($request->learner)){
        //   if(!is_array($old_tags)){
        //     $old_tags = array();
        //   }

        //   $diff_tags = array_diff($old_tags, $request->learner); 

        //   if(count(array_diff($old_tags, $request->learner))>0 || count(array_diff($request->learner, $old_tags))>0){
        //     $log = HomeLearnerLog::where('home_content_id', $model->id);
        //     $log->delete();

        //     foreach($request->learner as $value) { 
        //       $log = new HomeLearnerLog();
        //       $log->home_content_id = $model->id;
        //       $log->learner = $value;
        //       $log->save();
        //     }
        //   }
        // }
        //

        //GENDER
        // $old_tags = explode(',', $model->gender); 
        // $model->gender = is_array($request->gender) ? implode(",",$request->gender) : ''; 
        // if(is_array($request->gender)){
        //   if(!is_array($old_tags)){
        //     $old_tags = array();
        //   }

        //   $diff_tags = array_diff($old_tags, $request->gender); 

        //   if(count(array_diff($old_tags, $request->gender))>0 || count(array_diff($request->gender, $old_tags))>0){
        //     $log = HomeGenderLog::where('home_content_id', $model->id);
        //     $log->delete();

        //     foreach($request->gender as $value) { 
        //       $log = new HomeGenderLog();
        //       $log->home_content_id = $model->id;
        //       $log->gender = $value;
        //       $log->save();
        //     }
        //   }
        // }
        //

        //DEPARTMENT
        // $old_tags = explode(',', $model->department); 
        // $model->department = is_array($request->department) ? implode(",",$request->department) : ''; 
        // if(is_array($request->department)){
        //   if(!is_array($old_tags)){
        //     $old_tags = array();
        //   }

        //   $diff_tags = array_diff($old_tags, $request->department); 

        //   if(count(array_diff($old_tags, $request->department))>0 || count(array_diff($request->department, $old_tags))>0){
        //     $log = HomeDepartmentLog::where('home_content_id', $model->id);
        //     $log->delete();

        //     foreach($request->department as $value) { 
        //       $log = new HomeDepartmentLog();
        //       $log->home_content_id = $model->id;
        //       $log->department = $value;
        //       $log->save();
        //     }
        //   }
        // }
        //
      

        $model->status = $request->status;
        $model->save();

        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = HomeContent::find($data[0]);
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = HomeContent::count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {

        $model = HomeContent::find($request->id);

        $category = HomeTagLog::where('home_content_id', $model->id)->get();
        foreach($category as $key=>$val){
          $val->delete();
        }

        $speaker = HomeSpeakerLog::where('home_content_id', $model->id)->get();
        foreach($speaker as $key=>$val){
          $val->delete();
        }

        $level = HomeLevelLog::where('home_content_id', $model->id)->get();
        foreach($level as $key=>$val){
          $val->delete();
        }

        $learner = HomeLearnerLog::where('home_content_id', $model->id)->get();
        foreach($learner as $key=>$val){
          $val->delete();
        }

        $gender = HomeGenderLog::where('home_content_id', $model->id)->get();
        foreach($gender as $key=>$val){
          $val->delete();
        }

        $department = HomeDepartmentLog::where('home_content_id', $model->id)->get();
        foreach($department as $key=>$val){
          $val->delete();
        }

        HomeContent::where('position','>',$model->position)
        ->update(['position' => DB::raw('position - 1')]);
        
        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = HomeContent::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

}
