<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use App\Services\CourseEventService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\Datatables\Datatables;

class CourseEventController extends Controller
{
    protected $courseEventService;
    public $prefix = 'course_event';
    public $project_url = 'course_event';
    public $project_layout = 'admin.Lrk.course_event';

    public function __construct(CourseEventService $courseEventService)
    {
        $this->middleware('auth:admin');
        $this->courseEventService = $courseEventService;
    }

    public function index()
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $course_event_all = $this->courseEventService->getCount();
        $course_event_open = $this->courseEventService->getAll()->where('status', 1)->count();
        $course_event_closed = $this->courseEventService->getAll()->where('status', 2)->count();

        $obj = array(
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.index', compact(
            'obj', 
            'course_event_all', 
            'course_event_open', 
            'course_event_closed'
        ));
    }

    public function create()
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $data = $this->courseEventService->prepareDataForCreate();
        $model = $data['model'];

        $obj = array(
            'type' => 'create',
            'route' => route('course_event.store'),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', compact('model', 'obj'));
    }

    public function store(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $courseEvent = $this->courseEventService->createCourseEvent($request->all());

            if ($courseEvent) {
                return response()->json(['success' => true, 'message' => 'สร้างข้อมูลสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function show($id)
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $model = $this->courseEventService->findById($id);

        if (!$model) {
            return redirect()->route('admin.course_event')->with('error', 'ไม่พบข้อมูล');
        }

        $obj = array(
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.view', compact('model', 'obj'));
    }

    public function edit($id)
    {
        if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $model = $this->courseEventService->findById($id);

        if (!$model) {
            return redirect()->route('admin.course_event')->with('error', 'ไม่พบข้อมูล');
        }

        $obj = array(
            'type' => 'edit',
            'route' => route('course_event.update', $id),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', compact('model', 'obj'));
    }

    public function update(Request $request, $id)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $courseEvent = $this->courseEventService->updateCourseEvent($id, $request->all());

            if ($courseEvent) {
                return response()->json(['success' => true, 'message' => 'อัปเดตข้อมูลสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function destroy(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $id = $request->input('id');
            $result = $this->courseEventService->deleteCourseEvent($id);

            if ($result) {
                return response()->json(['success' => true, 'message' => 'ลบข้อมูลสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function status(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $id = $request->input('id');
            $result = $this->courseEventService->updateStatus($id);

            if ($result) {
                return response()->json(['success' => true, 'message' => 'อัปเดตสถานะสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถอัปเดตสถานะได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function position(Request $request)
    {
        try {
            if (!Auth::check() || (Auth::user()->level != '99' && Auth::user()->level != '88')) {
                return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
            }

            $id = $request->input('id');
            $position = $request->input('position');
            $result = $this->courseEventService->updatePosition($id, $position);

            if ($result) {
                return response()->json(['success' => true, 'message' => 'อัปเดตลำดับสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่สามารถอัปเดตลำดับได้']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
        $model = $this->courseEventService->getAll();

        return $datatables->of($model)
            ->addIndexColumn()
            ->addColumn('action', function ($model) {
                $editUrl = route('admin') . '/' . $this->prefix . '/' . $model->id . '/edit';

                return '
            <div class="btn-group">
                <a href="' . $editUrl . '" class="btn btn-sm btn-info" title="แก้ไข">
                    <i class="fa fa-edit"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteData(' . $model->id . ', \'/csisocietyadmin/' . $this->prefix . '/delete\', \'datatable_course_event\')" class="btn btn-sm btn-danger" title="ลบ">
                    <i class="fa fa-times"></i>
                </a>
            </div>';
            })
            ->addColumn('status', function ($model) {
                return $model->status;
            })
            ->addColumn('status_label', function ($model) {
                return $model->status == 1 ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
            })
            ->addColumn('category_display', function ($model) {
                return 'Course Event';
            })
            ->rawColumns(['action'])
            ->make(true);
    }
}