<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\CateSpeaker;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseSpeakerLog;
use App\Models\Core\SeminarSpeakerLog;
use Maatwebsite\Excel\Facades\Excel;


class CateSpeakerController extends Controller
{
    public $prefix = 'categories_speaker';
    public $project_url = 'categories_speaker';
    public $project_layout = 'admin.Lrk.categories_speaker';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
        $model = new CateSpeaker();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> route($this->prefix.'.store'),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
        $model = CateSpeaker::find($id);
        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>route($this->prefix.'.update',$model->id),
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['categories_speaker.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['categories_speaker.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['categories_speaker.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['categories_speaker.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      // Default ordering by position ascending (1, 2, 3, 4...)
      if ($orderby == 'position' || empty($orderby)) {
        $orderby = 'position';
        $order_sort = 'asc';
      }

      $query = DB::table('categories_speaker')
      ->select('categories_speaker.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('categories_speaker')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      $model = new CateSpeaker;
      $model->certified = isset($request->certified) ? AppHelper::instance()->convertDomHTML($request->certified) : null;
      $model->biography = isset($request->biography) ? AppHelper::instance()->convertDomHTML($request->biography) : null;

      $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;

      if ($request->hasFile('image')) {
        $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/avatar');
      }

      if ($request->hasFile('avatar')) {
        $model->avatar = AppHelper::instance()->saveImage($request->file('avatar'),'/upload/avatar');
      }

      $count = CateSpeaker::all()->count();
      $model->position = $count + 1;

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {

        $model = CateSpeaker::find($id);
        $model->certified = isset($request->certified) ? AppHelper::instance()->convertDomHTML($request->certified) : null;
        $model->biography = isset($request->biography) ? AppHelper::instance()->convertDomHTML($request->biography) : null;
  
        $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;

        if ($request->hasFile('image')) {
          if($model->image!=null&&$model->image!=''){
            AppHelper::instance()->removeImage($model->image);
          }
          $model->image = AppHelper::instance()->saveImage($request->file('image'),'/upload/avatar');
        }else{
          if(empty($request->source_image)){
            if($model->image!=null&&$model->image!=''){
              AppHelper::instance()->removeImage($model->image);
            }
            $model->image = null;
          }
        }

        if ($request->hasFile('avatar')) {
          if($model->avatar!=null&&$model->avatar!=''){
            AppHelper::instance()->removeImage($model->avatar);
          }
          $model->avatar = AppHelper::instance()->saveImage($request->file('avatar'),'/upload/avatar');
        }else{
          if(empty($request->source_avatar)){
            if($model->avatar!=null&&$model->avatar!=''){
              AppHelper::instance()->removeImage($model->avatar);
            }
            $model->avatar = null;
          }
        }

        $model->status = $request->status;
        $model->save();

        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {

        $model = CateSpeaker::find($request->id);

        $course = CourseSpeakerLog::where('speaker', $model->id)->get();
        foreach($course as $key_course => $val_course){
          $val_course->delete();
        }
      
        CateSpeaker::where('position','>',$model->position)
        ->update(['position' => DB::raw('position - 1')]);

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = CateSpeaker::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CateSpeaker::find($data[0]);
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;

            // Always arrange from least to most (1, 2, 3, 4...)
            $pos = ((int)$data[1]+1)+(($page)*$length);

            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function import(Request $request)
    {
      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;

      if(isset($request->excel)){
        $excels = json_decode($request->excel);
        if(count($excels)>0){
          foreach ($excels as &$excel) {
            $type = '';
            //แก้ไข key ที่ต้องการเช็คซ้ำ ตัวอย่าง $excel->id ซึ่งจะเป็น id , email หรืออะไรก็ได้ ถ้ามีซ้ำใน db ก็จะทำการอัพเดทข้อมูลแทน
            if(isset($excel->title_th)){
                if(!empty($excel->title_th)){
                  $result = CateSpeaker::where('title_th','=', $excel->title_th)->first();
                  //จบเรื่อง key ที่ต้องการเช็คซ้ำ

                  if($result){
                    $model_excel = CateSpeaker::find($result->id);
                    $type = 'update';
                  }else{
                    $model_excel = new CateSpeaker();
                    $type = 'store';
                  }
                }else{
                  $model_excel = new CateSpeaker();
                  $type = 'store';
                }
            }else{
              $model_excel = new CateSpeaker();
              $type = 'store';
            }

            // เพิ่มหรืออัพเดทข้อมูลตามชื่อหัวของ column ในไฟล์ excel

            $model_excel->name = isset($excel->name) ? AppHelper::instance()->cleanInput($excel->name) : '';
            $model_excel->title_th = isset($excel->title_th) ? AppHelper::instance()->cleanInput($excel->title_th) : '';
            $model_excel->status = 1;

            $response[$type]+=1;
            $model_excel->save();
            
          }
        }
      }
      return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);;
    }


    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='speaker_export') {
                $data = DB::table('categories_speaker')
                ->select('categories_speaker.*')
                ->orderBy('id', 'asc')
                ->get();

                $ex_array[] = array(
                  'id' => 'id',
                  'name' => 'name',
                  'title_th' => 'title_th',
                );

                foreach ($data as $e) {
                    $ex_array[]=array(
                    'id'=>$e->id,
                    'name'=>$e->name,
                    'title_th'=>$e->title_th,
                    );
                }
            }

            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    $export = new TempExport($ex_array);

                    return Excel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

}
