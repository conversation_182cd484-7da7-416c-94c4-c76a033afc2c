<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\CouponWebCode;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class CouponWebCodeController extends Controller
{
    public $prefix = 'coupon_web_code';
    public $project_url = 'coupon_web_code';
    public $project_layout = 'admin.Lrk.coupon_web_code';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return response()->json();
      }
      $model = CouponWebCode::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return response()->json();
      }
      $model = CouponWebCode::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['coupon_web_code.status', $value['search']['value']]);
          }else if($value['data']=='code'){
            array_push($where_search, ['coupon_web_code.code', $value['search']['value']]);
          }else{
            array_push($where_search, ['coupon_web_code.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('coupon_web_code')
      ->select('coupon_web_code.*')
      ->where('coupons_bulk_id',$request->coupons_bulk_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('coupon_web_code')
      ->where('coupons_bulk_id',$request->coupons_bulk_id)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new CouponWebCode; 

      $model->coupons_bulk_id = $request->coupons_bulk_id;
      $model->code = isset($request->gallery_modal_code) ? AppHelper::instance()->cleanInput($request->gallery_modal_code) : null;

      $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';

      $model->save();

      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $response = new Dummy();
      $response['status'] = 'false';


      $model = CouponWebCode::find($request->id);

      if($model){
  
        $model->code = isset($request->gallery_modal_code) ? AppHelper::instance()->cleanInput($request->gallery_modal_code) : null;
  
        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';
        $model->save();

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $response = new Dummy();
      $response['status'] = 'false';

      $model = CouponWebCode::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }
    
    public function import(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;

      if(isset($request->excel)){
        $excels = json_decode($request->excel);
        if(count($excels)>0){
          foreach ($excels as &$excel) {
            $type = '';
            
            if(isset($excel->code)){
              if(!empty($excel->code)){
                $code_dup = CouponWebCode::where('code', AppHelper::instance()->cleanInput($excel->code))->where('coupons_bulk_id', $request->coupons_bulk_id)->first();
                if(!$code_dup){
                  $model_excel = new CouponWebCode();
                  $type = 'store';
                  
                  $model_excel->code = isset($excel->code) ? AppHelper::instance()->cleanInput($excel->code) : null;
                  $model_excel->status = isset($excel->status) ? $excel->status : 1;
                  $model_excel->coupons_bulk_id = $request->coupons_bulk_id;
                      
                  $response[$type]+=1;
                  
                  $model_excel->save();
                }
              }
            }
          }
        }
      }
      return json_decode($response, true);;
    }

    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='code_web_export') {
                $data = DB::table('coupon_web_code')
                ->select('coupon_web_code.*')
                ->where('coupon_web_code.coupon_web_id', $request->coupon_web_id)
                ->orderBy('id', 'asc')
                ->get();

                $ex_array[]=array(
                  'code'=>'code',
                  'status'=>'status',
                );

                foreach ($data as $e) {

                  if($e->status == 1){
                    $e->status = 'เปิดใช้งาน';
                  }else{
                    $e->status = 'ใช้งานแล้ว';
                  }

                  $ex_array[]=array(
                      'code'=>$e->code,
                      'status'=>$e->status,
                  );
                }
            }



            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    // Excel::create($request->filename, function ($excel) use ($ex_array) {
                    //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
                    //         $sheet->fromArray($ex_array);
                    //     });
                    // })->download('xlsx');
                    $export = new TempExport($ex_array);
                    // AppHelper::instance()->consoleLog($ex_array);

                    return Excel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                    );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }
}
