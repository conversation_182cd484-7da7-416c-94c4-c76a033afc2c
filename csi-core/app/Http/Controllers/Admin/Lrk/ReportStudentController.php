<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\UsersExamPoint;
use App\Models\Core\UsersLearningLog;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class ReportStudentController extends Controller
{
    public $prefix = 'report_student';
    public $project_url = 'report_student';
    public $project_layout = 'admin.Lrk.report_student';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      $course_id = $request->course_id;
      $course = Course::where('id', $course_id)->select('title_th')->first();
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','course'));
    }

    public function create()
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_learning_log.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_learning_log.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_learning_log.id', $value['search']['value']]);
          }else if($value['data']=='u_name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_email'){
            array_push($where_search, ['user.email', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='u_e_id'){
            array_push($where_search, ['user.e_id', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='user_t'){
            array_push($where_search, ['categories_department.title_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_learning_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_learning_log')
      ->join('user', 'user.id', 'user_learning_log.user_id')
      ->join('categories_department', 'categories_department.id', 'user.dept_id')
      ->select('user.name as u_name', 'user.lastname as u_lastname', 'user.email as u_email', 'user.e_id as u_e_id', 'user_learning_log.id', 
                'user_learning_log.course_id', 'user_learning_log.user_id', 'categories_department.title_th as user_t')
      ->where('user_learning_log.course_id', $request->course_id)
      ->groupBy('user_learning_log.user_id')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = count(DB::table('user_learning_log')
      ->join('user', 'user.id', 'user_learning_log.user_id')
      ->join('categories_department', 'categories_department.id', 'user.dept_id')
      ->where('user_learning_log.course_id', $request->course_id)
      ->groupBy('user_learning_log.user_id')
      ->where($where_search)
      ->pluck('user_learning_log.user_id')->toArray());
            

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['fullname'] = $value->u_name.' '.$value->u_lastname;

        $learning_status = CourseLessonLog::where('course_id', $value->course_id)->sum(DB::raw('TIME_TO_SEC(duration)'));
        $student_learning = UsersLearningLog::where('user_id', $value->user_id)->where('course_id', $value->course_id)->sum(DB::raw('TIME_TO_SEC(watching_time)'));

        if($student_learning != 0){
          $percent = ($student_learning*100)/$learning_status;
          $percent = round($percent);
          if($percent){
            $obj['learning_status'] = $percent. ' %';
          }else{
            $obj['learning_status'] = '0 %';
          }
        }else{
          $obj['learning_status'] = '0 %';
        }
        

        $point = UsersExamPoint::where('user_id', $value->user_id)->where('course_id', $value->course_id)->sum(DB::raw('point'));
        $learning_pont = CourseLessonLog::where('course_id', $value->course_id)->sum(DB::raw('point'));
        if($learning_pont != 0){
          $point_per = ($point*100)/$learning_pont;

          if($point_per){
            $obj['point_per'] = $point_per. ' % จากคะแนนเต็ม';
          }else{
            $obj['point_per'] = '0 % จากคะแนนเต็ม';
          }
        }else{
          $obj['point_per'] = 'ไม่มีข้อสอบ';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/')->with('errors', 'Permission Deny');
    }

    public function update(Request $request, $id)
    {
      return redirect(route('admin').'/')->with('errors', 'Permission Deny');
    }

    public function filter(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }else{
        if ($request->name=='report_student') {
          
          $data = DB::table('user_learning_log');
          $data->join('user', 'user.id', 'user_learning_log.user_id');
          $data->join('categories_department', 'categories_department.id', 'user.dept_id');
          $data->select('user.name as u_name', 'user.lastname as u_lastname', 'user.email as u_email', 'user.e_id as u_e_id', 'user_learning_log.id', 
                    'user_learning_log.course_id', 'user_learning_log.user_id', 'categories_department.title_th as user_t');
          $data->where('user_learning_log.course_id', $request->course_id);
          $data->groupBy('user_learning_log.user_id');
          $data->get();
          
          if (isset($request->user_email)) {
            $data->where('user.email', 'like', '%'.$request->user_email.'%');
          }

          if (isset($request->created_at_start) && isset($request->created_at_to)) {
            $from = Carbon::parse($request->created_at_start)->toDateString();
            $to = Carbon::parse($request->created_at_to)->addDays(1)->toDateString();
            $data->whereBetween('user_learning_log.created_at', [$from, $to]);
          }
          $data = $data->get();

          $ex_array[]=array(
            'แผนก / หน่วยงาน'=>'แผนก / หน่วยงาน',
            'ชื่อ - นามสกุล'=>'ชื่อ - นามสกุล',
            'อีเมล์'=>'อีเมล์',
            'รหัสพนักงาน'=>'รหัสพนักงาน',
            'สถานะ'=>'สถานะ',
            'ผลการทดสอบ'=>'ผลการทดสอบ',
          );

          foreach ($data as $key=>$e) {


            $learning_status = CourseLessonLog::where('course_id', $e->course_id)->sum(DB::raw('TIME_TO_SEC(duration)'));
            $student_learning = UsersLearningLog::where('user_id', $e->user_id)->where('course_id', $e->course_id)->sum(DB::raw('TIME_TO_SEC(watching_time)'));
  
            if($student_learning != 0){
              $percent = ($student_learning*100)/$learning_status;
              $percent = round($percent, 2);
              if($percent){
                $percent = $percent. ' %';
              }else{
                $percent = '0 %';
              }
            }else{
              $percent = '0 %';
            }
            
  
            $point = UsersExamPoint::where('user_id', $e->user_id)->where('course_id', $e->course_id)->sum(DB::raw('point'));
            $learning_pont = CourseLessonLog::where('course_id', $e->course_id)->sum(DB::raw('point'));
            if($learning_pont != 0){
              $point_per = ($point*100)/$learning_pont;
  
              if($point_per){
                $point_per = $point_per. ' % จากคะแนนเต็ม';
              }else{
                $point_per = '0 % จากคะแนนเต็ม';
              }
            }else{
              $point_per = '0 % จากคะแนนเต็ม';
            }
            
            $ex_array[]=array(
            'แผนก / หน่วยงาน'=>$e->user_t,
            'รหัสพนักงาน'=>$e->u_e_id,
            'ชื่อ - นามสกุล'=>$e->u_name.' '.$e->u_lastname,
            'อีเมล์'=>$e->u_email,
            'สถานะ'=>$percent,
            'ผลการทดสอบ'=>$point_per,
            );
          }
        }

        $export_type = isset($request->export_type) ? $request->export_type : 'excel';
          if (isset($ex_array)) {
              if ($export_type=='excel') {
                  $export = new TempExport($ex_array);

                  return FacadesExcel::download($export, $request->filename.'.xlsx');
              } else {
                  $headers = array(
                  "Content-Encoding" => 'UTF-8',
                  "Content-type" => "text/csv; charset=UTF-8",
                  "Content-Disposition" => "attachment; ".$request->filename.".csv",
                  "Pragma" => "no-cache",
                  "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                  "Expires" => "0"
              );
                  echo "\xEF\xBB\xBF";

                  $keys = array_keys($ex_array[0]);
                  $callback = function () use ($ex_array,$keys) {
                      $fp= fopen('php://output', 'w');
                      fputcsv($fp, $keys);
                      foreach ($ex_array as $fields) {
                          fputcsv($fp, $fields);
                      }
                      fclose($fp);
                  };

                  return response()->stream($callback, 200, $headers);
              }
          }
      }

    }

}
