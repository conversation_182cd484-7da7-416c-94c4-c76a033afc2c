<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\Coupon;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CouponCourse;
use App\Models\Core\CouponSeminar;
use App\Models\Core\CouponSponsor;
use App\Models\Core\Course;
use Maatwebsite\Excel\Facades\Excel;


class CouponController extends Controller
{
    public $prefix = 'coupon';
    public $project_url = 'coupon';
    public $project_layout = 'admin.Lrk.coupon';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Coupon();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = Coupon::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['coupon.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['coupon.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['coupon.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['coupon.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('coupon')
      ->select('coupon.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('coupon')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Coupon;
      $model->code = isset($request->code) ? AppHelper::instance()->cleanInput($request->code) : null;
      $model->value = isset($request->value) ? AppHelper::instance()->cleanInput($request->value) : null;
      $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : null;

      $model->sponsor = is_array($request->sponsor) ? implode(",",$request->sponsor) : '';

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;

      $model->all_course = isset($request->all_course) ? AppHelper::instance()->cleanInput($request->all_course) : 1;

      $model->status = $request->status;

      $model->save();
  
      if(is_array($request->sponsor)){ //ถ้า tags มีค่า
        foreach($request->sponsor as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
          $log = new CouponSponsor();
          $log->coupon_id = $model->id;
          $log->sponsor_id = $value;
          $log->save();
        }
      }
      if($model->all_course == 2){
        return redirect(route('admin').'/coupon/'.$model->id.'/edit/')->with('success', 'Data has been update');
      }else{
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = Coupon::find($id);

      $old_tags = explode(',', $model->sponsor); //ตรวขสอบว่ามีข้อมูล sponsor ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
      $model->sponsor = is_array($request->sponsor) ? implode(",",$request->sponsor) : ''; //เก็บ sponsor ใหม่ที่มีมาอัพเดท
      if(is_array($request->sponsor)){
        if(!is_array($old_tags)){
          $old_tags = array();
        }

        $diff_tags = array_diff($old_tags, $request->sponsor); //หาว่า sponsor ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

        if(count(array_diff($old_tags, $request->sponsor))>0 || count(array_diff($request->sponsor, $old_tags))>0){
          $log = CouponSponsor::where('coupon_id', $model->id);
          $log->delete();

          foreach($request->sponsor as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new CouponSponsor();
            $log->coupon_id = $model->id;
            $log->sponsor_id = $value;
            $log->save();
          }
        }
      }

      $model->all_course = isset($request->all_course) ? AppHelper::instance()->cleanInput($request->all_course) : $model->all_course;

      $model->code = isset($request->code) ? AppHelper::instance()->cleanInput($request->code) : null;
      $model->value = isset($request->value) ? AppHelper::instance()->cleanInput($request->value) : null;
      $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : null;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y', $request->started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y', $request->end_date)->format('Y-m-d H:i:s') : null;

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return response()->json();
      }

      $model = Coupon::find($request->id);

      $coupon_course = CouponCourse::where('coupon_id', $model->id)->get();
      foreach($coupon_course as $key_course => $val_course){
        $val_course->delete();
      }

      $coupon_seminar = CouponSeminar::where('coupon_id', $model->id)->get();
      foreach($coupon_seminar as $key_seminar => $val_seminar){
        $val_seminar->delete();
      }

      $sponsor = CouponSponsor::where('coupon_id', $model->id)->get();
      foreach($sponsor as $key_sponsor => $val_sponsor){
        $val_sponsor->delete();
      }

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return response()->json();
      }
      $model = Coupon::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
