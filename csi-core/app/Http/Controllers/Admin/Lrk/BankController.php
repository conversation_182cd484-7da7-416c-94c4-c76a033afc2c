<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;
use Ya<PERSON>ra\Datatables\Datatables;
use App\Helpers\AppHelper;
use App\Models\Dummy;
use App\Models\Core\Bank;

class BankController extends Controller
{
    public $prefix = 'banks';
    public $project_url = 'banks';
    public $project_layout = 'admin.Lrk.bank';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $obj = array(
            'route' => route($this->prefix . '.store'),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.index', compact('obj'));
    }

    public function create()
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $model = new Bank();
        $old = session()->getOldInput();
        if ($old) {
            foreach ($old as $key => $value) {
                $model[$key] = $value;
            }
        }

        $obj = array(
            'type' => 'create',
            'route' => route($this->prefix . '.store'),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', compact('model', 'obj'));
    }

    public function edit($id)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $model = Bank::find($id);
        if ($model) {
            $obj = array(
                'type' => 'edit',
                'route' => route($this->prefix . '.update', $model->id),
                'prefix' => $this->prefix,
                'project_url' => $this->project_url,
                'project_layout' => $this->project_layout
            );
            AppHelper::instance()->consoleLog($model);
            return view($this->project_layout . '.form', compact('model', 'obj'));
        } else {
            return redirect(route('admin') . '/' . $this->project_url);
        }
    }

    public function store(Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $request->validate([
            'bank_name' => 'required|in:กสิกรไทย,กรุงเทพ,กรุงไทย,ทีเอ็มบีธนชาต,ไทยพาณิชย์,กรุงศรีอยุธยา,ออมสิน,อาคารสงเคราะห์,เกียรตินาคิน,ซีไอเอ็มบี,ทิสโก้,ยูโอบี,แลนด์ แอนด์ เฮาส์,ไอซีบีซี,อิสลามแห่งประเทศไทย,พร้อมเพย์',
            'account_name' => 'required|max:255',
            'type' => 'required|in:bank,promptpay',
            'qr_code' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // ถ้าเป็นธนาคารต้องมีเลขบัญชี ถ้าเป็นพร้อมเพย์ต้องมีเบอร์โทร
        if ($request->type == 'bank' && empty($request->account_number)) {
            return back()->withErrors(['account_number' => 'กรุณากรอกเลขบัญชีธนาคาร'])->withInput();
        }
        if ($request->type == 'promptpay' && empty($request->phone_number)) {
            return back()->withErrors(['phone_number' => 'กรุณากรอกหมายเลขโทรศัพท์'])->withInput();
        }

        $model = new Bank();
        $model->bank_name = AppHelper::instance()->cleanInput($request->bank_name);
        $model->account_name = AppHelper::instance()->cleanInput($request->account_name);
        $model->account_number = $request->type == 'bank' ? AppHelper::instance()->cleanInput($request->account_number) : null;
        $model->phone_number = $request->type == 'promptpay' ? AppHelper::instance()->cleanInput($request->phone_number) : null;
        $model->type = $request->type;
        $model->description = isset($request->description) ? AppHelper::instance()->cleanInput($request->description) : null;
        $model->status = isset($request->status) ? 1 : 0;
        $model->is_active = isset($request->is_active) ? 1 : 0;

        // จัดการ is_active - ให้มีได้แค่ 1 ตัวที่เป็น active
        if ($model->is_active == 1) {
            Bank::where('is_active', 1)->update(['is_active' => 0]);
        }

        // หา position สูงสุด
        $max_position = Bank::max('position');
        $model->position = $max_position ? $max_position + 1 : 1;

        // อัปโหลด QR Code
        if ($request->hasFile('qr_code')) {
            $model->qr_code = AppHelper::instance()->saveImage($request->file('qr_code'), '/upload/banks');
        }

        $model->save();

        return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been saved');
    }

    public function update(Request $request, $id)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            return redirect(route('admin') . '/')->with('errors', 'Permission Deny');
        }

        $model = Bank::find($id);
        if (!$model) {
            return redirect(route('admin') . '/' . $this->project_url);
        }

        $request->validate([
            'bank_name' => 'required|in:กสิกรไทย,กรุงเทพ,กรุงไทย,ทีเอ็มบีธนชาต,ไทยพาณิชย์,กรุงศรีอยุธยา,ออมสิน,อาคารสงเคราะห์,เกียรตินาคิน,ซีไอเอ็มบี,ทิสโก้,ยูโอบี,แลนด์ แอนด์ เฮาส์,ไอซีบีซี,อิสลามแห่งประเทศไทย,พร้อมเพย์',
            'account_name' => 'required|max:255',
            'type' => 'required|in:bank,promptpay',
            'qr_code' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // ถ้าเป็นธนาคารต้องมีเลขบัญชี ถ้าเป็นพร้อมเพย์ต้องมีเบอร์โทร
        if ($request->type == 'bank' && empty($request->account_number)) {
            return back()->withErrors(['account_number' => 'กรุณากรอกเลขบัญชีธนาคาร'])->withInput();
        }
        if ($request->type == 'promptpay' && empty($request->phone_number)) {
            return back()->withErrors(['phone_number' => 'กรุณากรอกหมายเลขโทรศัพท์'])->withInput();
        }

        $model->bank_name = AppHelper::instance()->cleanInput($request->bank_name);
        $model->account_name = AppHelper::instance()->cleanInput($request->account_name);
        $model->account_number = $request->type == 'bank' ? AppHelper::instance()->cleanInput($request->account_number) : null;
        $model->phone_number = $request->type == 'promptpay' ? AppHelper::instance()->cleanInput($request->phone_number) : null;
        $model->type = $request->type;
        $model->description = isset($request->description) ? AppHelper::instance()->cleanInput($request->description) : null;
        $model->status = 1;

        // อัปโหลด QR Code ใหม่
        if ($request->hasFile('qr_code')) {
            $model->qr_code = AppHelper::instance()->saveImage($request->file('qr_code'), '/upload/banks');
        }

        $model->save();

        return redirect(route('admin') . '/' . $this->project_url)->with('success', 'Data has been updated');
    }

    public function dblist(DataTables $datatables, Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            return new Dummy();
        }

        $draw = $request->get('draw');
        $start = $request->get('start');
        $length = $request->get('length');

        // Custom search
        $where_search = array();
        $columns = $request->get('columns');
        if ($columns) {
            foreach ($columns as $key => $value) {
                if (!empty($value['search']['value'])) {
                    if ($value['data'] == 'type') {
                        array_push($where_search, ['type', $value['search']['value']]);
                    } else if ($value['data'] == 'status') {
                        array_push($where_search, ['status', $value['search']['value']]);
                    } else if ($value['data'] == 'is_active') {
                        array_push($where_search, ['is_active', $value['search']['value']]);
                    } else if (in_array($value['data'], ['bank_name', 'account_name'])) {
                        array_push($where_search, [$value['data'], 'LIKE', '%' . $value['search']['value'] . '%']);
                    }
                }
            }
        }

        $order = $request->get('order');
        $columns = $request->get('columns');

        if ($order && $columns && isset($order[0]['column']) && isset($columns[$order[0]['column']])) {
            $index = $order[0]['column'];
            $orderby = $columns[$index]['data'];
            $order_sort = $order[0]['dir'];

            // Map display columns to actual database columns
            if ($orderby == 'position_display') {
                $orderby = 'position';
            } else if ($orderby == 'type_display') {
                $orderby = 'type';
            } else if ($orderby == 'status_display') {
                $orderby = 'status';
            } else if ($orderby == 'is_active_display') {
                $orderby = 'is_active';
            } else if (!in_array($orderby, ['bank_name', 'account_name', 'type', 'status', 'is_active', 'position', 'created_at'])) {
                $orderby = 'position';
            }
        } else {
            $orderby = 'position';
            $order_sort = 'asc';
        }

        $query = Bank::select('*')
            ->where($where_search)
            ->orderBy($orderby, $order_sort)
            ->offset($start)
            ->limit($length)
            ->get();

        $count_total = Bank::where($where_search)->count();

        // Number column
        if ($order_sort == 'desc') {
            $no_start = $start;
        } else {
            $no_start = $start + 1;
        }

        $query_array = json_decode(json_encode($query), true);
        foreach ($query_array as $key => $value) {
            if ($order_sort == 'desc') {
                $query_array[$key]['position_display'] = $count_total - $no_start;
                $no_start++;
            } else {
                $query_array[$key]['position_display'] = $no_start;
                $no_start++;
            }
        }
        $query = json_decode(json_encode($query_array));

        $data_query = array();
        foreach ($query as $key => $value) {
            $obj = new Dummy();
            foreach ($value as $key_data => $data) {
                $obj[$key_data] = $data;
            }

            // Format account info for display
            if ($obj['type'] == 'bank') {
                $obj['account_info'] = $obj['account_number'] ?? '-';
            } else {
                $obj['account_info'] = $obj['phone_number'] ?? '-';
            }

            // Format type
            $obj['type_display'] = $obj['type'] == 'bank' ? 'ธนาคาร' : 'พร้อมเพย์';

            // Format status
            $obj['status_display'] = $obj['status'] == 1 ? 'เปิดใช้งาน' : 'ปิดใช้งาน';

            // Format is_active
            $obj['is_active_display'] = $obj['is_active'] == 1 ? 'ใช้งานหลัก' : 'ไม่ใช้งาน';

            // QR Code
            $obj['qr_code_display'] = $obj['qr_code'] ? '<img src="' . $obj['qr_code'] . '" width="50" height="50" class="img-thumbnail">' : '-';

            $obj['action'] = '
                <div class="btn-group">
                    <a href="' . $this->prefix . '/' . $obj->id . '/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
                    <a href="javascript:void(0)" onclick="deleteDatatableItem(' . $obj->id . ',\'' . $this->prefix . '/delete\')" class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
                </div>';

            array_push($data_query, $obj);
        }

        $data = $datatables->of($data_query)
            ->with([
                'data' => $data_query,
                'draw' => $draw,
                "recordsTotal" => $count_total,
                "recordsFiltered" => $count_total,
            ])
            ->rawColumns(['qr_code_display', 'action']) // Allow HTML in these columns
            ->make(true);
        return $data;
    }

    public function destroy(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            $response['message'] = 'Permission Deny';
            return json_decode($response, true);
        }

        $model = Bank::find($request->id);
        if ($model) {
            $model->delete();
            $response['status'] = 'success';
        }

        return json_decode($response, true);
    }

    public function status(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            $response['message'] = 'Permission Deny';
            return json_decode($response, true);
        }

        $model = Bank::find($request->id);
        if ($model) {
            $model->status = $model->status == 1 ? 0 : 1;
            $model->save();
            $response['status'] = 'success';
            $response['new_status'] = $model->status;
        }

        return json_decode($response, true);
    }

    public function setActive(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            $response['message'] = 'Permission Deny';
            return json_decode($response, true);
        }

        $model = Bank::find($request->id);
        if ($model) {
            // Reset all to inactive
            Bank::where('is_active', 1)->update(['is_active' => 0]);

            // Set selected as active
            $model->is_active = 1;
            $model->save();

            $response['status'] = 'success';
        }

        return json_decode($response, true);
    }

    public function position(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        if (Auth::user()->level != '99' && Auth::user()->level != '66') {
            $response['message'] = 'Permission Deny';
            return json_decode($response, true);
        }

        $data_position = $request->position;
        foreach ($data_position as $key => $value) {
            $model = Bank::find($value['id']);
            if ($model) {
                $model->position = $value['position'];
                $model->save();
            }
        }

        $response['status'] = 'success';
        return json_decode($response, true);
    }

    public function show($id)
    {
        return redirect(route('admin') . '/' . $this->project_url);
    }
}
