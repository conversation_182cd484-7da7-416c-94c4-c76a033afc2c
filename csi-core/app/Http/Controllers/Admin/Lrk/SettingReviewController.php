<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\SettingCenterReview;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Models\Core\ReviewVideoLog;
use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class SettingReviewController extends Controller
{
    public $prefix = 'setting_review';
    public $project_url = 'setting_review';
    public $project_layout = 'admin.Lrk.setting_review';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new SettingCenterReview();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = SettingCenterReview::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '66'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['setting_center_review.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['setting_center_review.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['setting_center_review.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['setting_center_review.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('setting_center_review')
      ->select('setting_center_review.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('setting_center_review')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem(' . $obj->id . ',\'' . $this->prefix . '/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = new SettingCenterReview;
      $model->value = isset($request->value) ? AppHelper::instance()->cleanInput($request->value) : null;
        
      if ($request->hasFile('thumb')) {
        if($model->thumb!=null&&$model->thumb!=''){
          AppHelper::instance()->removeImage($model->thumb);
        }
        $model->thumb = AppHelper::instance()->saveImage($request->file('thumb'),'setting_content');
      }else{
        if(empty($request->source_thumb)){
          if($model->thumb!=null&&$model->thumb!=''){
            AppHelper::instance()->removeImage($model->thumb);
          }
          $model->thumb = null;
        }
      }

      $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
      
      $model->start = isset($request->start) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->start)->format('Y-m-d H:i:s') : null;
      $model->end = isset($request->end) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end)->format('Y-m-d H:i:s') : null;

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = SettingCenterReview::find($id);

      if($model){

        $model->value = isset($request->value) ? AppHelper::instance()->cleanInput($request->value) : null;
        
        if ($request->hasFile('thumb')) {
          if($model->thumb!=null&&$model->thumb!=''){
            AppHelper::instance()->removeImage($model->thumb);
          }
          $model->thumb = AppHelper::instance()->saveImage($request->file('thumb'),'setting_content');
        }else{
          if(empty($request->source_thumb)){
            if($model->thumb!=null&&$model->thumb!=''){
              AppHelper::instance()->removeImage($model->thumb);
            }
            $model->thumb = null;
          }
        }

        $model->title = isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null;
        
        $model->start = isset($request->start) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->start)->format('Y-m-d H:i:s') : null;
        $model->end = isset($request->end) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end)->format('Y-m-d H:i:s') : null;

        $model->status = $request->status;
        $model->save();
  
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = SettingCenterReview::find($request->id);
      if($model){
        $log = ReviewVideoLog::where('video_id', $model->id)->get();
        foreach($log as $key_log => $val_log){
          $val_log->delete();
        }
        $model->delete();
  
        return response()->json();
      }
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = SettingCenterReview::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
