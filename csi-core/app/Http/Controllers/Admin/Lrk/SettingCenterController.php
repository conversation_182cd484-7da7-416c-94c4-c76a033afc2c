<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\Article;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\ArticleCateLog;
use App\Models\Core\ArticleLog;
use App\Models\Core\ArticleSubCateLog;
use App\Models\Core\ArticleTagLog;
use App\Models\Core\Tags;
use App\Models\Core\UsersActivityLog;
use App\Models\Core\UsersFavoriteLog;
use Maatwebsite\Excel\Facades\Excel;


class SettingCenterController extends Controller
{
    public $prefix = 'setting_center';
    public $project_url = 'setting_center';
    public $project_layout = 'admin.Lrk.setting_center';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '88'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

}
