<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\CouponWeb;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CouponWebCode;
use App\Models\Core\CouponWebExclude;
use App\Models\Core\CouponWebInclude;
use App\Models\Core\CouponWebSponsor;

class CouponWebController extends Controller
{
    public $prefix = 'coupon_web';
    public $project_url = 'coupon_web';
    public $project_layout = 'admin.Lrk.coupon_web';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new CouponWeb();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = CouponWeb::find($id);
      $article_id = $model->id;
      if($model){
        $obj = array(
          'type'=>'view',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.view', compact('model','obj','article_id'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = CouponWeb::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect(route('admin').'/'.$this->project_url);
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['coupon_web.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['coupon_web.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['coupon_web.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['coupon_web.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('coupon_web')
      ->select('coupon_web.*')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();
      $count_total = DB::table('coupon_web')
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column


      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;

    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new CouponWeb;


      $model->no = isset($request->no) ? AppHelper::instance()->cleanInput($request->no) : 0;
      $model->discount_type = isset($request->discount_type) ? $request->discount_type : null;
      $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : null;
      $model->prefix = isset($request->prefix) ? AppHelper::instance()->cleanInput($request->prefix) : null;
      
      $model->min_spend = isset($request->min_spend) ? AppHelper::instance()->cleanInput($request->min_spend) : null;
      $model->max_spend = isset($request->max_spend) ? AppHelper::instance()->cleanInput($request->max_spend) : null;

      $model->allow_email = isset($request->allow_email) ? AppHelper::instance()->cleanInput($request->allow_email) : null;
      
      $model->limitation_coupon = isset($request->limitation_coupon) ? AppHelper::instance()->cleanInput($request->limitation_coupon) : null;
      $model->limitation_user = isset($request->limitation_user) ? AppHelper::instance()->cleanInput($request->limitation_user) : null;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_date)->format('Y-m-d H:i:s') : null;

      $model->status = $request->status;

      $model->condition_type = $request->condition_type;

      if($model->condition_type == 2){
        $model->include_course = is_array($request->include_course) ? implode(",",$request->include_course) : null;
        $model->exclude_course = null;
      }else if($model->condition_type == 3){
        $model->exclude_course = is_array($request->exclude_course) ? implode(",",$request->exclude_course) : null;
        $model->include_course = null;
      }else{
        $model->include_course = null;
        $model->exclude_course = null;
      }

      $model->save();

      if(is_array($request->include_course)){
        foreach($request->include_course as $value) {
          $log = new CouponWebInclude();
          $log->coupon_web_id = $model->id;
          $log->course_id = $value;
          $log->save();
        }
      }

      if(is_array($request->exclude_course)){
        foreach($request->exclude_course as $value) {
          $log = new CouponWebExclude();
          $log->coupon_web_id = $model->id;
          $log->course_id = $value;
          $log->save();
        }
      }

      if($model->no != 0 && $model->prefix != '' && $model->prefix != null){
        for($i = 0; $i < $model->no; $i++){
          $code_log = new CouponWebCode();
          $code_log->coupons_bulk_id = $model->id;
          $code_log->code = $model->prefix.AppHelper::instance()->generateRandomString(6);
          $code_log->status = 1;
          $code_log->save();
        }
      }

      if($model->no > 0){
        return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');
      }else{
        return redirect(route('admin').'/coupon_web/'.$model->id.'/edit/')->with('success', 'Data has been update');
      }
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = CouponWeb::find($id);

      $model->condition_type = $request->condition_type;

      if($model->condition_type == 1){
        $model->include_course = null;

        $log_include = CouponWebInclude::where('coupon_web_id', $model->id);
        $log_include->delete();

        $model->exclude_course = null;

        $log_exclude = CouponWebExclude::where('coupon_web_id', $model->id);
        $log_exclude->delete();
      }else if($model->condition_type == 2){

        $model->exclude_course = null;

        $log_exclude = CouponWebExclude::where('coupon_web_id', $model->id);
        $log_exclude->delete();

        $old_tags = explode(',', $model->include_course);
        $model->include_course = is_array($request->include_course) ? implode(",",$request->include_course) : null;
        if(is_array($request->include_course)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }
  
          $diff_tags = array_diff($old_tags, $request->include_course);
  
          if(count(array_diff($old_tags, $request->include_course))>0 || count(array_diff($request->include_course, $old_tags))>0){
            $log = CouponWebInclude::where('coupon_web_id', $model->id);
            $log->delete();
  
            foreach($request->include_course as $value) {
              $log = new CouponWebInclude();
              $log->coupon_web_id = $model->id;
              $log->course_id = $value;
              $log->save();
            }
          }
        }else{
          $log = CouponWebInclude::where('coupon_web_id', $model->id);
          $log->delete();
        }
      }else if($model->condition_type == 3){

        $model->include_course = null;

        $log_include = CouponWebInclude::where('coupon_web_id', $model->id);
        $log_include->delete();

        $old_tags = explode(',', $model->exclude_course);
        $model->exclude_course = is_array($request->exclude_course) ? implode(",",$request->exclude_course) : null;
        if(is_array($request->exclude_course)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }
  
          $diff_tags = array_diff($old_tags, $request->exclude_course);
  
          if(count(array_diff($old_tags, $request->exclude_course))>0 || count(array_diff($request->exclude_course, $old_tags))>0){
            $log = CouponWebExclude::where('coupon_web_id', $model->id);
            $log->delete();
  
            foreach($request->exclude_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
              $log = new CouponWebExclude();
              $log->coupon_web_id = $model->id;
              $log->course_id = $value;
              $log->save();
            }
          }
        }else{
          $log = CouponWebExclude::where('coupon_web_id', $model->id);
          $log->delete();
        }
      }

      $model->discount_type = isset($request->discount_type) ? $request->discount_type : null;
      $model->amount = isset($request->amount) ? AppHelper::instance()->cleanInput($request->amount) : null;
      
      $model->min_spend = isset($request->min_spend) ? AppHelper::instance()->cleanInput($request->min_spend) : null;
      $model->max_spend = isset($request->max_spend) ? AppHelper::instance()->cleanInput($request->max_spend) : null;

      $model->allow_email = isset($request->allow_email) ? AppHelper::instance()->cleanInput($request->allow_email) : null;
      
      $model->limitation_coupon = isset($request->limitation_coupon) ? AppHelper::instance()->cleanInput($request->limitation_coupon) : null;
      $model->limitation_user = isset($request->limitation_user) ? AppHelper::instance()->cleanInput($request->limitation_user) : null;

      $model->started_date = isset($request->started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->started_date)->format('Y-m-d H:i:s') : null;
      $model->end_date = isset($request->end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->end_date)->format('Y-m-d H:i:s') : null;  

      $model->status = $request->status;
      $model->save();

      return redirect(route('admin').'/'.$this->project_url)->with('success', 'Data has been update');

    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }

      $model = CouponWeb::find($request->id);

      $code_log = CouponWebCode::where('coupons_bulk_id', $model->id)->delete();

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99'){
        return response()->json();
      }
      $model = CouponWeb::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
