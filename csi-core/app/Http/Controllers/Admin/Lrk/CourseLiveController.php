<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;
use App\Services\CourseLiveService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\Datatables\Datatables;

class CourseLiveController extends Controller
{
    protected $courseLiveService;
    public $prefix = 'course_live';
    public $project_url = 'course_live';
    public $project_layout = 'admin.Lrk.course_live';

    public function __construct(CourseLiveService $courseLiveService)
    {
        $this->middleware('auth:admin');
        $this->courseLiveService = $courseLiveService;
    }

    public function index()
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $course_live_all = $this->courseLiveService->getCount();
        $course_live_open = $this->courseLiveService->getAll()->where('status', 1)->count();
        $course_live_closed = $this->courseLiveService->getAll()->where('status', 2)->count();

        $obj = array(
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.index', compact(
            'obj', 
            'course_live_all', 
            'course_live_open', 
            'course_live_closed'
        ));
    }

    public function create()
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $data = $this->courseLiveService->prepareDataForCreate();
        $model = $data['model'];

        $obj = array(
            'type' => 'create',
            'route' => route('course_live.store'),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', compact('model', 'obj'));
    }

    public function store(Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        try {
            $this->courseLiveService->createCourseLive($request);
            return redirect()->route('admin.course_live')->with('success', 'สร้างข้อมูล Course Live สำเร็จ');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'เกิดข้อผิดพลาด: ' . $e->getMessage())->withInput();
        }
    }

    public function show($id)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $model = $this->courseLiveService->findById($id);
        
        if (!$model) {
            return redirect()->route('admin.course_live')->with('error', 'ไม่พบข้อมูล');
        }

        $obj = array(
            'type' => 'view',
            'route' => '',
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.view', compact('model', 'obj'));
    }

    public function edit($id)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        $data = $this->courseLiveService->prepareDataForEdit($id);
        
        if (!$data) {
            return redirect()->route('admin.course_live')->with('error', 'ไม่พบข้อมูล');
        }

        $model = $data['model'];

        $obj = array(
            'type' => 'edit',
            'route' => route('course_live.update', $id),
            'prefix' => $this->prefix,
            'project_url' => $this->project_url,
            'project_layout' => $this->project_layout
        );

        return view($this->project_layout . '.form', compact('model', 'obj'));
    }

    public function update(Request $request, $id)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return redirect()->route('admin')->with('error', 'ไม่มีสิทธิ์เข้าถึง');
        }

        try {
            $result = $this->courseLiveService->updateCourseLive($request, $id);
            
            if ($result) {
                return redirect()->route('admin.course_live')->with('success', 'อัพเดทข้อมูล Course Live สำเร็จ');
            } else {
                return redirect()->back()->with('error', 'ไม่พบข้อมูลที่ต้องการแก้ไข')->withInput();
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'เกิดข้อผิดพลาด: ' . $e->getMessage())->withInput();
        }
    }

    public function destroy(Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
        }

        try {
            $result = $this->courseLiveService->deleteCourseLive($request->id);
            
            if ($result) {
                return response()->json(['success' => true, 'message' => 'ลบข้อมูลสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่พบข้อมูลที่ต้องการลบ']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function status(Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
        }

        try {
            $result = $this->courseLiveService->toggleStatus($request->id);
            
            if ($result) {
                return response()->json(['success' => true, 'message' => 'เปลี่ยนสถานะสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่พบข้อมูล']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function position(Request $request)
    {
        if (Auth::user()->level != '99' && Auth::user()->level != '88') {
            return response()->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
        }

        try {
            $result = $this->courseLiveService->updatePosition($request->id, $request->position);
            
            if ($result) {
                return response()->json(['success' => true, 'message' => 'เปลี่ยนตำแหน่งสำเร็จ']);
            } else {
                return response()->json(['success' => false, 'message' => 'ไม่พบข้อมูล']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
        $model = $this->courseLiveService->getAll();

        return $datatables->of($model)
            ->addIndexColumn()
            ->addColumn('action', function ($model) {
                $editUrl = route('admin') . '/' . $this->prefix . '/' . $model->id . '/edit';

                return '
            <div class="btn-group">
                <a href="' . $editUrl . '" class="btn btn-sm btn-info" title="แก้ไข">
                    <i class="fa fa-edit"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteData(' . $model->id . ', \'/csisocietyadmin/' . $this->prefix . '/delete\', \'datatable_course_live\')" class="btn btn-sm btn-danger" title="ลบ">
                    <i class="fa fa-times"></i>
                </a>
            </div>';
            })
            ->addColumn('status', function ($model) {
                return $model->status;
            })
            ->addColumn('status_label', function ($model) {
                return $model->status == 1 ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
            })
            ->addColumn('category_display', function ($model) {
                return 'Course Live';
            })
            ->rawColumns(['action'])
            ->make(true);
    }
}
