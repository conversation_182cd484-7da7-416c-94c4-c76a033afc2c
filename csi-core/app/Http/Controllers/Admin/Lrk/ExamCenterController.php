<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\CourseExamLog;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\CourseLessonLog;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class ExamCenterController extends Controller
{
    public $prefix = 'exam_center';
    public $project_url = 'exam_center';
    public $project_layout = 'admin.Lrk.exam_center';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create(Request $request)
    {      
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {

        $model = CourseExamLog::find($request->id);

        if($model){
          $lesson_id = CourseLessonLog::where('id', $model->lesson_id)->first();
          $course_id = Course::where('id', $model->course_id)->first();

          $obj = array(
            'type'=>'edit',
            'route'=>'/csisocietyadmin/exam_log/'.$request->course_id.'/'.$request->lesson_id.'/edit/'.$request->id,
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj','lesson_id','course_id'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='question_th'){
            array_push($where_search, ['course_exam.question_th', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='point'){
            array_push($where_search, ['course_exam.point', $value['search']['value']]);
          }else{
            array_push($where_search, ['course_exam.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('course_exam')
      ->join('course', 'course.id', 'course_exam.course_id')
      ->join('course_lesson', 'course_lesson.id', 'course_exam.lesson_id')
      ->select('course_exam.*', 'course.title_th as c_title', 'course_lesson.title_th as l_title')
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('course_exam')
      ->join('course', 'course.id', 'course_exam.course_id')
      ->join('course_lesson', 'course_lesson.id', 'course_exam.lesson_id')
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="/csisocietyadmin/exam_log/'.$obj->course_id.'/'.$obj->lesson_id.'/edit/'.$obj->id.'?type=setting_center" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
        </div>';
        $obj['question_th'] = AppHelper::instance()->limitCharUTF8($obj['question_th'],25);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {

    }

    public function update(Request $request)
    {

        $model = CourseExamLog::find($request->id);
        if($model){
          $model->question_th = isset($request->question_th) ? AppHelper::instance()->cleanInput($request->question_th) : null;
          $model->point = isset($request->point) ? AppHelper::instance()->cleanInput($request->point) : 0;
          $model->exam_type = isset($request->exam_type) ? AppHelper::instance()->cleanInput($request->exam_type) : 1;
          $model->answer_type = isset($request->answer_type) ? AppHelper::instance()->cleanInput($request->answer_type) : 1;
          $model->file_pptx = isset($request->file_pptx) ? AppHelper::instance()->cleanInput($request->file_pptx) : null;
          $model->file_excel = isset($request->file_excel) ? AppHelper::instance()->cleanInput($request->file_excel) : null;
          $model->file_word = isset($request->file_word) ? AppHelper::instance()->cleanInput($request->file_word) : null;

          $model->status = $request->status;
          $model->save();

          return redirect(route('admin').'/exam_log/'.$request->course_id.'/'.$request->lesson_id)->with('success', 'Data has been update');
        }
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = CourseExamLog::where('lesson_id',$request->lesson_id)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = CourseExamLog::where('lesson_id',$request->lesson_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {
        $model = CourseExamLog::find($request->id);

        $answer = CourseExamAnswer::where('exam_id', $model->id)->get();
        foreach($answer as $key_ans=>$value_ans){
          $value_ans->delete();
        }

        CourseExamLog::where('lesson_id', $model->lesson_id)->where('position','>',$model->position)
          ->update(['position' => DB::raw('position - 1')]);
        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = CourseExamLog::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        // $model->timestamps = false;
        $model->save();

        return response()->json();
    }

  }
