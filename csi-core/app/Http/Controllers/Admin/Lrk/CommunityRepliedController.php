<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\UserCommunityRepliedLog;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CommunityRepliedLike;
use App\Models\Core\CourseExamAnswer;
use App\Models\VNE\Project;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class CommunityRepliedController extends Controller
{
    public $prefix = 'community_replied';
    public $project_url = 'community_replied';
    public $project_layout = 'admin.Lrk.community_replied';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      
      $feed_id = $request->feed_id;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','feed_id'));
    }

    public function create(Request $request)
    {      
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function show(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='comment'){
            array_push($where_search, ['user_community_replied_log.comment', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='status'){
            array_push($where_search, ['user_community_replied_log.status', $value['search']['value']]);
          }else{
            array_push($where_search, ['user_community_replied_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('user_community_replied_log')
      ->select('user_community_replied_log.*')
      ->where('user_community_replied_log.feed_id', $request->feed_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_community_replied_log')
      ->where('user_community_replied_log.feed_id', $request->feed_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $count_like = CommunityRepliedLike::where('replied_id', $obj->id)->count();
        if($count_like){
          $obj['count_like'] = $count_like;
        }else{
          $obj['count_like'] = 'ไม่มี';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        $obj['text_area'] = AppHelper::instance()->limitCharUTF8($obj['text_area'],30);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function update(Request $request)
    {
      return redirect(route('admin').'/'.$this->project_url);
    }

    public function position(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = UserCommunityRepliedLog::where('feed_id',$request->feed_id)
                  ->where('id',$data[0])
                  ->first();
          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = UserCommunityRepliedLog::where('feed_id',$request->feed_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }

    public function destroy(Request $request)
    {
        $model = UserCommunityRepliedLog::find($request->id);

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = UserCommunityRepliedLog::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }
        $model->save();

        return response()->json();
    }

  }
