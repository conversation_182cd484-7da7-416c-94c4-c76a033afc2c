<?php

namespace App\Http\Controllers\Admin\Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\EventCoTraining;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class EventCoTrainingLogController extends Controller
{
    public $prefix = 'event_co_training';
    public $project_url = 'event_co_training';
    public $project_layout = 'admin.Lrk.event_co_training';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {
      $model = EventCoTraining::find($request->id);
      
      EventCoTraining::where('event_id',$model->event_id)->where('position','>',$model->position)
      ->update(['position' => DB::raw('position - 1')]);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      $model = EventCoTraining::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='name'){
            array_push($where_search, ['event_co_training.name', $value['search']['value']]);
          }else if($value['data']=='link'){
            array_push($where_search, ['event_co_training.link', $value['search']['value']]);
          }else{
            array_push($where_search, ['event_co_training.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('event_co_training')
      ->select('event_co_training.*')
      ->where('event_id',$request->event_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('event_co_training')
      ->where('event_id',$request->event_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.route('admin').'/event_co_training/delete\',\'datatable_gal\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

      return $data;
    }

    public function store_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = new EventCoTraining; 

      $model->event_id = $request->event_id;
  
      $model->name = isset($request->gallery_modal_name) ? AppHelper::instance()->cleanInput($request->gallery_modal_name) : null;
      $model->link = isset($request->gallery_modal_link) ? AppHelper::instance()->cleanInput($request->gallery_modal_link) : null;

      $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';

      $count = EventCoTraining::where('event_id',$request->event_id)
      ->count();
      $model->position = $count + 1;

      $model->save();
      $response['status'] = 'success';
      $response['model'] = $model;
      return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = EventCoTraining::find($request->id);

      if($model){
  
        $model->name = isset($request->gallery_modal_name) ? AppHelper::instance()->cleanInput($request->gallery_modal_name) : null;
        $model->link = isset($request->gallery_modal_link) ? AppHelper::instance()->cleanInput($request->gallery_modal_link) : null;
  
        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '1';
        $model->save();

        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = EventCoTraining::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }

    public function position_page(Request $request)
    {
      $lists = explode(',', $request->list);
      if(count($lists)>0){
        foreach ($lists as $key=>$value) {
          $data = explode(':', $value);
          $model = EventCoTraining::where('event_id',$request->event_id)
                  ->where('id',$data[0])
                  ->first();

          if($model){
            $page = (int)$request->page;
            $length = (int)$request->length;
            if($request->order=='asc'){
              $pos = ((int)$data[1]+1)+(($page)*$length);
            }else if($request->order=='desc'){
              $top = 0;
              $alldata = EventCoTraining::where('event_id',$request->event_id)->count();
              $top = $alldata-($length*$page);
              $pos = ($top-((int)$data[1]));
            }
            $model->position = $pos;
            $model->save();
          }
        }
      }
    }
}
