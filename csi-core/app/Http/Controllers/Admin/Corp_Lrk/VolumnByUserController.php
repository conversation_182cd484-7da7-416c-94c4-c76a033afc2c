<?php

namespace App\Http\Controllers\Admin\Corp_Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\VolumnByUser;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\CourseExamAnswer;
use App\Models\Core\UserHistory;
use App\Models\Core\VolumnByLot;
use App\Models\User;
use App\Models\VNE\Project;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;


class VolumnByUserController extends Controller
{
    public $prefix = 'volumn_by_user';
    public $project_url = 'Corp_Lrk/volumn_by_user';
    public $project_layout = 'admin.Corp_Lrk.volumn_by_user';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      
      $company_id = $request->company_id;
      $lot_id = $request->lot_id;

      // $limit = VolumnByLot::where('id', $lot_id)->select('limit')->first();
      $user = VolumnByUser::where('company_id', $company_id)
      ->where('lot_id', $lot_id)->count();

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','company_id','lot_id','user'));
    }

    public function create(Request $request)
    {      
        $lot_id = $request->lot_id;
        $company_id = $request->company_id;

        $model = new VolumnByUser();
        $old = session()->getOldInput();
        if($old)
        {
          foreach ($old as $key => $value) {
            $model[$key] = $value;
          }
        }
        $obj = array(
          'type'=>'create',
          'route'=> url('csisocietyadmin/volumn_by_user/'.$company_id.'/'.$lot_id.'/create'),
          'btn'=>'Add',
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        return view($this->project_layout.'.form', compact('model','obj','company_id','lot_id'));
    }

    public function show(Request $request, $id)
    {
        return redirect(route('admin').'/'.$this->project_url);
    }

    public function edit(Request $request, $id)
    {
        $lot_id = $request->lot_id;
        $company_id = $request->company_id;

        $model = VolumnByUser::find($request->id);

        if($model){
          $obj = array(
            'type'=>'edit',
            'route'=>'/csisocietyadmin/volumn_by_user/'.$request->company_id.'/'.$request->lot_id.'/edit/'.$request->id,
            'prefix'=>$this->prefix,
            'project_url'=>$this->project_url,
            'project_layout'=>$this->project_layout
          );
          AppHelper::instance()->consoleLog($model);
          return view($this->project_layout.'.form', compact('model','obj','lot_id','company_id'));
        }else{
          return redirect(route('admin').'/'.$this->project_url);
        }
    }

    public function dblist(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='email'){
            array_push($where_search, ['volumn_user_log.email', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['volumn_user_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('volumn_user_log')
      ->select('volumn_user_log.*')
      ->where('volumn_user_log.company_id', $request->company_id)
      ->where('volumn_user_log.lot_id', $request->lot_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();
      $count_total = DB::table('volumn_user_log')
      ->where('volumn_user_log.company_id', $request->company_id)
      ->where('volumn_user_log.lot_id', $request->lot_id)
      ->where($where_search)
      ->count();

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;

    }

    public function store(Request $request)
    {

        $model = new VolumnByUser;
        $limit = VolumnByLot::where('id', $request->lot_id)
        ->select('limit')
        ->first();

        $count = VolumnByUser::where('company_id',$request->company_id)
        ->where('lot_id',$request->lot_id)->count();
        if($count < $limit->limit){
          $model->company_id = $request->company_id;
          $model->lot_id = $request->lot_id;
  
          $model->email = isset($request->email) ? AppHelper::instance()->cleanInput($request->email) : null;
          
          $model->save();
          return redirect('/Corporation/Corp_Lrk/volumn_by_user/'.$model->company_id.'/'.$model->lot_id)->with('success', 'Data has been update');  
        }else{
          return redirect('/Corporation/Corp_Lrk/volumn_by_user/'.$request->company_id.'/'.$request->lot_id)->with('errors', 'Data has been limited');  
        }
        
    }

    public function update(Request $request)
    {

        $model = VolumnByUser::find($request->id);
        if($model){

          $model->company_id = $request->company_id;
          $model->lot_id = $request->lot_id;
          $model->email = isset($request->email) ? AppHelper::instance()->cleanInput($request->email) : null;

          $model->save();

          return redirect('/Corporation/Corp_Lrk/volumn_by_user/'.$request->company_id.'/'.$request->lot_id)->with('success', 'Data has been update');
        }
    }

    public function destroy(Request $request)
    {
        $model = VolumnByUser::find($request->id);

        if($model){
            $user = User::where(function ($user) use ($model) {
                    $user->where('internal_email',$model->email);
                    $user->orWhere('email',$model->email);
                    });
            $user = $user->first();

            if($user){
                $history = UserHistory::where('user_id',$user->id)
                ->where('get_type',6)
                ->where('company_id', $model->company_id)
                ->where('company_lot_id', $model->lot_id)
                ->where('expired','>=',Carbon::now())
                ->get();
                foreach($history as $val_his){
                  $val_his->expired = Carbon::now()->subdays(1);
                  $val_his->save();
                }
            }
          $model->delete();
        }

        return response()->json();
    }
    
    public function import(Request $request)
    {
      if(Auth::user()->level != '55'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $response = new Dummy();
      $response['store'] = 0;
      $response['update'] = 0;

      $limit = VolumnByLot::where('id', $request->lot_id)->select('limit')
      ->first();

      $count = VolumnByUser::where('company_id', $request->company_id)
      ->where('lot_id', $request->lot_id)
      ->count();

      if($limit){
        if(isset($request->excel)){
          $excels = json_decode($request->excel);

          if(count($excels)>0){

            $total = count($excels) + $count;
            if($total <= $limit->limit){

              foreach ($excels as &$excel) {
                $type = '';
                if(isset($excel->email)){
                  if(!empty($excel->email)){
                    $email_dup = VolumnByUser::where('email', AppHelper::instance()->cleanInput($excel->email))
                    ->where('company_id', $request->company_id)->where('lot_id', $request->lot_id)->first();
                    if(!$email_dup){
                      $model_excel = new VolumnByUser();
                      $type = 'store';
                      
                      $model_excel->email = isset($excel->email) ? AppHelper::instance()->cleanInput($excel->email) : null;
                      $model_excel->company_id = $request->company_id;
                      $model_excel->lot_id = $request->lot_id;
                          
                      $response[$type]+=1;
                      
                      $model_excel->save();
                    }
                  }
                }
              }
            }
          }
        }
      }
      return json_decode($response, true);;
      // return redirect('/Corporation/Corp_Lrk/volumn_by_user/'.$request->company_id.'/'.$request->lot_id)->with('success', 'Data has been update');
    }

  }
