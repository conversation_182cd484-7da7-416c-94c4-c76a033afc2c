<?php

namespace App\Http\Controllers\Admin\Corp_Lrk;

use App\Exports\TempExport;
use App\Http\Controllers\Controller;

use App\Models\Core\VolumnBy;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use App\Models\Admin;
use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\ArticleCateLog;
use App\Models\Core\ArticleLog;
use App\Models\Core\ArticleSubCateLog;
use App\Models\Core\ArticleTagLog;
use App\Models\Core\Tags;
use App\Models\Core\UserHistory;
use App\Models\Core\UsersActivityLog;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\VolumnByCourse;
use App\Models\Core\VolumnByLot;
use App\Models\Core\VolumnByUser;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel;


class UserCorpCourseController extends Controller
{
    public $prefix = 'dashboard_user_course';
    public $project_url = 'Corp_Lrk/dashboard_user_course';
    public $project_layout = 'admin.Corp_Lrk.dashboard_user_course';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index(Request $request)
    {
      if(Auth::user()->level != '55'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $course_id = $request->course_id;
      $lot_id = $request->lot;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj','course_id','lot_id'));
    }

    public function create()
    {
      return redirect('/Corporation');
    }

    public function show($id)
    {
      return redirect('/Corporation');
    }

    public function edit($id)
    {
      return redirect('/Corporation');
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '55'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['user_history.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['user_history.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['user_history.id', $value['search']['value']]);
          }else if($value['data']=='name'){
            array_push($where_search, ['user.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='lastname'){
            array_push($where_search, ['user.lastname', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='percent'){
            array_push($where_search, ['user_learning_log.watch_percent', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='learning_status'){
            array_push($where_search, ['user_learning_log.status', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['user_history.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('user_history')
      ->join('user', 'user.id', 'user_history.user_id')
      ->join('user_learning_log', 'user_learning_log.user_id', 'user_history.user_id')
      ->select('user_history.*', 'user.name', 'user.lastname', 'user.email', 'user_learning_log.watch_percent as percent', 'user_learning_log.status as learning_status')
      ->where('user_history.course_id', $request->course_id)
      ->where('user_learning_log.course_id', $request->course_id)
      ->where('user_history.company_lot_id', $request->lot)
      ->where('user_history.get_type', 6)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('user_history')
      ->join('user', 'user.id', 'user_history.user_id')
      ->join('user_learning_log', 'user_learning_log.user_id', 'user_history.user_id')
      ->where('user_history.course_id', $request->course_id)
      ->where('user_learning_log.course_id', $request->course_id)
      ->where('user_history.company_lot_id', $request->lot)
      ->where('user_history.get_type', 6)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      return redirect('/Corporation')->with('errors', 'Username Cannot be used');
    }

    public function update(Request $request, $id)
    {
      return redirect('/Corporation')->with('errors', 'Username Cannot be used');
    }

    public function destroy(Request $request)
    {
      return response()->json();
    }

    public function status(Request $request)
    {
      return response()->json();
    }

    public function export(Request $request)
    {
        if (isset($request->name)) {
            if ($request->name=='attendant_export') {
                $data = DB::table('user_history')
                ->join('user', 'user.id', 'user_history.user_id')
                ->join('user_learning_log', 'user_learning_log.user_id', 'user_history.user_id')
                ->join('course', 'course.id', 'user_history.course_id')
                ->select('user_history.*', 'user.name', 'user.lastname', 'user.email', 'user_learning_log.watch_percent as percent', 'user_learning_log.status as learning_status',
                          'course.title_th as c_title')
                ->where('user_history.course_id', $request->course_id)
                ->where('user_learning_log.course_id', $request->course_id)
                ->where('user_history.company_lot_id', $request->lot)
                ->where('user_history.get_type', 6)
                ->orderBy('user_history.user_id', 'asc')
                ->get();

                $ex_array[]=array(
                  'คอร์ส'=>'คอร์ส',
                  'ชื่อ'=>'ชื่อ',
                  'นามสกุล'=>'นามสกุล',
                  'อีเมล์'=>'อีเมล์',
                  'สถานะ'=>'สถานะ',
                  'เปอร์เซนต์การเรียน'=>'เปอร์เซนต์การเรียน',
                );

                foreach ($data as $e) {

                  if($e->learning_status == 1){
                    $e->learning_status = 'Incomplete';
                  }else{
                    $e->learning_status = 'Complete';
                  }

                  $ex_array[]=array(
                      'คอร์ส'=>$e->c_title,
                      'ชื่อ'=>$e->name,
                      'นามสกุล'=>$e->lastname,
                      'อีเมล์'=>$e->email,
                      'สถานะ'=>$e->learning_status,
                      'เปอร์เซนต์การเรียน'=>$e->percent,
                  );
                }
            }



            // ไม่ต้องแก้ตรงนี้ก็ได้
            $export_type = isset($request->export_type) ? $request->export_type : 'excel';
            if (isset($ex_array)) {
                if ($export_type=='excel') {
                    // Excel::create($request->filename, function ($excel) use ($ex_array) {
                    //     $excel->sheet('Data', function ($sheet) use ($ex_array) {
                    //         $sheet->fromArray($ex_array);
                    //     });
                    // })->download('xlsx');
                    $export = new TempExport($ex_array);
                    // AppHelper::instance()->consoleLog($ex_array);

                    return Excel::download($export, $request->filename.'.xlsx');
                } else {
                    $headers = array(
                    "Content-Encoding" => 'UTF-8',
                    "Content-type" => "text/csv; charset=UTF-8",
                    "Content-Disposition" => "attachment; ".$request->filename.".csv",
                    "Pragma" => "no-cache",
                    "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                    "Expires" => "0"
                    );
                    echo "\xEF\xBB\xBF";

                    $keys = array_keys($ex_array[0]);
                    $callback = function () use ($ex_array,$keys) {
                        $fp= fopen('php://output', 'w');
                        fputcsv($fp, $keys);
                        foreach ($ex_array as $fields) {
                            fputcsv($fp, $fields);
                        }
                        fclose($fp);
                    };

                    return response()->stream($callback, 200, $headers);
                }
            }
        }
    }

}
