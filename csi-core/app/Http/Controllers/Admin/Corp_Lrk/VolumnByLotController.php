<?php

namespace App\Http\Controllers\Admin\Corp_Lrk;

use App\Http\Controllers\Controller;

use App\Models\Core\VolumnByLot;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\VolumnBy;
use App\Models\Core\VolumnByCourse;
use App\Models\Core\VolumnByExCate;
use App\Models\Core\VolumnByInCate;
use App\Models\Core\VolumnByUser;
use Maatwebsite\Excel\Facades\Excel;


class VolumnByLotController extends Controller
{
    public $prefix = 'volumn_by_lot';
    public $project_url = 'Corp_Lrk/volumn_by_lot';
    public $project_layout = 'admin.Corp_Lrk.volumn_by_lot';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function destroy(Request $request)
    {

        $model = VolumnByLot::find($request->id);

        $model->delete();

        return response()->json();
    }

    public function status(Request $request)
    {
        $model = VolumnByLot::find($request->id);

        if($model->status=='1'){
          $model->status = '2';
        }
        else{
          $model->status = '1';
        }

        $model->save();

        return response()->json();
    }

    public function dblist_page(DataTables $datatables, Request $request)
    {
      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='lot_name'){
            array_push($where_search, ['volumn_lot_log.lot_name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='status'){
            array_push($where_search, ['volumn_lot_log.status', $value['search']['value']]);
          }else if($value['data']=='limit'){
            array_push($where_search, ['volumn_lot_log.limit', $value['search']['value']]);
          }else{
            array_push($where_search, ['volumn_lot_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      $query = DB::table('volumn_lot_log')
      ->select('volumn_lot_log.*')
      ->where('company_id',$request->company_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('volumn_lot_log')
      ->where('company_id',$request->company_id)
      ->where($where_search)
      ->count();
      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }

        $user = VolumnByUser::where('company_id', $obj->company_id)
        ->where('lot_id', $obj->id)->count();

        if($user > 0){
          $obj['user'] = '
          <a href="'.url('/Corporation').'/Corp_Lrk/volumn_by_user/'.$obj->company_id.'/'.$obj->id.'">
            จำนวน '. $user .' ราย
          </a>';
        }else{
          $obj['user'] = '
          <a href="'.url('/Corporation').'/Corp_Lrk/volumn_by_user/'.$obj->company_id.'/'.$obj->id.'">
            ยังไม่พบผู้ลงทะเบียน
          </a>';
        }

        $obj['action'] = '
        <div class="btn-group">
          <a href="javascript:void(0)" onclick="viewDatatableItem('.$obj->id.')" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.route('admin').'/Unif/product_log/delete\',\'datatable_gal\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
        </div>';

        $obj['text_area'] = AppHelper::instance()->limitCharUTF8($obj['text_area'],30);
        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);

        return $data;
    }

    public function store_page(Request $request)
    {
        $response = new Dummy();
        $response['status'] = 'false';

        $model = new VolumnByLot; 

        $model->company_id = $request->company_id;

        $lot_count = VolumnByLot::where('company_id', $request->company_id)->count();
        
        $model->lot = $lot_count + 1;

        $model->lot_name = isset($request->gallery_modal_lot_name) ? AppHelper::instance()->cleanInput($request->gallery_modal_lot_name) : '';
        $model->limit = isset($request->gallery_modal_limit) ? AppHelper::instance()->cleanInput($request->gallery_modal_limit) : '';
        
        $model->started_date = isset($request->gallery_modal_started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
        $model->end_date = isset($request->gallery_modal_end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_end_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
                
        $model->type = isset($request->gallery_modal_type) ? $request->gallery_modal_type : '';
        
        $model->ex_course = is_array($request->gallery_modal_ex_course) ? implode(",",$request->gallery_modal_ex_course) : '';
        $model->ex_cate = is_array($request->gallery_modal_ex_cate) ? implode(",",$request->gallery_modal_ex_cate) : '';
        $model->in_cate = is_array($request->gallery_modal_in_cate) ? implode(",",$request->gallery_modal_in_cate) : '';

        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';

        $model->save();

        if(is_array($request->gallery_modal_ex_course)){ //ถ้า tags มีค่า
          foreach($request->gallery_modal_ex_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new VolumnByCourse();
            $log->volumn_id = $model->id;
            $log->course_id = $value;
            $log->save();
          }
        }

        if(is_array($request->gallery_modal_ex_cate)){ //ถ้า tags มีค่า
          foreach($request->gallery_modal_ex_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new VolumnByExCate();
            $log->volumn_id = $model->id;
            $log->cate_id = $value;
            $log->save();  
          }
        }

        if(is_array($request->gallery_modal_in_cate)){ //ถ้า tags มีค่า
          foreach($request->gallery_modal_in_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
            $log = new VolumnByInCate();
            $log->volumn_id = $model->id;
            $log->cate_id = $value;
            $log->save();
          }
        }

        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
    }

    public function edit_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';


      $model = VolumnByLot::find($request->id);

      if($model){
  
        $model->lot_name = isset($request->gallery_modal_lot_name) ? AppHelper::instance()->cleanInput($request->gallery_modal_lot_name) : '';
        $model->limit = isset($request->gallery_modal_limit) ? AppHelper::instance()->cleanInput($request->gallery_modal_limit) : '';
        
        $model->started_date = isset($request->gallery_modal_started_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_started_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
        $model->end_date = isset($request->gallery_modal_end_date) ? Carbon::createFromFormat('d-m-Y, H:i:s', $request->gallery_modal_end_date)->format('Y-m-d H:i:s') : Carbon::now('Asia/Bangkok')->format('Y-m-d H:i:s');
                
        $model->type = isset($request->gallery_modal_type) ? $request->gallery_modal_type : '';
        if($model->type == 1){

          $old_tags = explode(',', $model->in_cate); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
          $model->in_cate = is_array($request->gallery_modal_in_cate) ? implode(",",$request->gallery_modal_in_cate) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
          if(is_array($request->gallery_modal_in_cate)){
            if(!is_array($old_tags)){
              $old_tags = array();
            }
  
            $diff_tags = array_diff($old_tags, $request->gallery_modal_in_cate); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร
  
            if(count(array_diff($old_tags, $request->gallery_modal_in_cate))>0 || count(array_diff($request->gallery_modal_in_cate, $old_tags))>0){
              $log = VolumnByInCate::where('volumn_id', $model->id);
              $log->delete();
  
              foreach($request->gallery_modal_in_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
                $log = new VolumnByInCate();
                $log->volumn_id = $model->id;
                $log->cate_id = $value;
                $log->save();
              }
            }
          }else{
            $log = VolumnByInCate::where('volumn_id', $model->id);
            $log->delete();
          }

          $log = VolumnByExCate::where('volumn_id', $model->id)->delete();
          $model->ex_cate = null;
        }else{

          $old_tags = explode(',', $model->ex_cate); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
          $model->ex_cate = is_array($request->gallery_modal_ex_cate) ? implode(",",$request->gallery_modal_ex_cate) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
          if(is_array($request->gallery_modal_ex_cate)){
            if(!is_array($old_tags)){
              $old_tags = array();
            }
  
            $diff_tags = array_diff($old_tags, $request->gallery_modal_ex_cate); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร
  
            if(count(array_diff($old_tags, $request->gallery_modal_ex_cate))>0 || count(array_diff($request->gallery_modal_ex_cate, $old_tags))>0){
              $log = VolumnByExCate::where('volumn_id', $model->id);
              $log->delete();
  
              foreach($request->gallery_modal_ex_cate as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
                $log = new VolumnByExCate();
                $log->volumn_id = $model->id;
                $log->cate_id = $value;
                $log->save();
              }
            }
          }else{
            $log = VolumnByExCate::where('volumn_id', $model->id);
            $log->delete();
          }

          $log = VolumnByInCate::where('volumn_id', $model->id)->delete();
          $model->in_cate = null;
        }

        $old_tags = explode(',', $model->ex_course); //ตรวขสอบว่ามีข้อมูล tags ก่อนหน้าหรือไม่ โดยการแตกออกมาเป็น array
        $model->ex_course = is_array($request->gallery_modal_ex_course) ? implode(",",$request->gallery_modal_ex_course) : ''; //เก็บ tags ใหม่ที่มีมาอัพเดท
        if(is_array($request->gallery_modal_ex_course)){
          if(!is_array($old_tags)){
            $old_tags = array();
          }

          $diff_tags = array_diff($old_tags, $request->gallery_modal_ex_course); //หาว่า tags ก่อนหน้าที่มีแตกต่างกับของใหม่หรือไม่ ถ้ามี เราจะทำการลบออกเพื่อสร้างใหม่ แต่หากไม่ต่างก็จะไม่ทำอะไร

          if(count(array_diff($old_tags, $request->gallery_modal_ex_course))>0 || count(array_diff($request->gallery_modal_ex_course, $old_tags))>0){
            $log = VolumnByCourse::where('volumn_id', $model->id);
            $log->delete();

            foreach($request->gallery_modal_ex_course as $value) { //วนค่าใน Array ออกมาทีละตัวโดยค่าที่ได้ให้ชื่อตัวแปรว่า $value
              $log = new VolumnByCourse();
              $log->volumn_id = $model->id;
              $log->course_id = $value;
              $log->save();
            }
          }
        }else{
          $log = VolumnByCourse::where('volumn_id', $model->id);
          $log->delete();
        }

        $model->status = isset($request->gallery_modal_status) ? $request->gallery_modal_status : '';
        $model->save();
        $response['status'] = 'success';
        $response['model'] = $model;
      }
      return json_decode($response, true);

    }

    public function view_page(Request $request)
    {
      $response = new Dummy();
      $response['status'] = 'false';

      $model = VolumnByLot::find($request->id);
      if($model){
        $response['status'] = 'success';
        $response['model'] = $model;
        return json_decode($response, true);
      }

      return json_decode($response, true);

    }
}
