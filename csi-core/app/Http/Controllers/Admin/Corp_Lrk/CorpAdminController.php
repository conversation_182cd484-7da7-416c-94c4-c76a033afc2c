<?php

namespace App\Http\Controllers\Admin\Corp_Lrk;

use App\Http\Controllers\Controller;

use App\Models\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Helpers\AppHelper;
use Ya<PERSON>ra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;

use Carbon\Carbon;
use App\Models\Dummy;
use Maatwebsite\Excel\Facades\Excel;


class CorpAdminController extends Controller
{
    public $prefix = 'corp_admin';
    public $project_url = 'Corp_Lrk/corp_admin';
    public $project_layout = 'admin.Corp_Lrk.corp_admin';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.index', compact('obj'));
    }

    public function create()
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Admin();
      $old = session()->getOldInput();
      if($old)
      {
        foreach ($old as $key => $value) {
          $model[$key] = $value;
        }
      }
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      return view($this->project_layout.'.form', compact('model','obj'));
    }

    public function show($id)
    {
      return redirect('/Corporation')->with('errors', 'Permission Deny');
 
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return redirect('/Corporation')->with('errors', 'Permission Deny');
      }
      $model = Admin::find($id);
      if($model){
        $obj = array(
          'type'=>'edit',
          'route'=>route($this->prefix.'.update',$model->id),
          'prefix'=>$this->prefix,
          'project_url'=>$this->project_url,
          'project_layout'=>$this->project_layout
        );
        AppHelper::instance()->consoleLog($model);
        return view($this->project_layout.'.form', compact('model','obj'));
      }else{
        return redirect('/Corporation');
      }
    }

    public function dblist(DataTables $datatables, Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return new Dummy();
      }

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      //custom search
      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='status'){
            array_push($where_search, ['admin.status', $value['search']['value']]);
          }else if($value['data']=='position'){
            array_push($where_search, ['admin.position', $value['search']['value']]);
          }else if($value['data']=='id'){
            array_push($where_search, ['admin.id', $value['search']['value']]);
          }else{
            array_push($where_search, ['admin.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }

      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      //number column
      if($orderby=='position'){
        $orderby = 'id';
      }
      //number column

      $query = DB::table('admin')
      ->select('admin.*')
      ->where('company_id', Auth::user()->company_id)
      ->where($where_search)
      ->orderBy($orderby, $order_sort)
      ->offset($start)
      ->limit($length)
      ->get();

      $count_total = DB::table('admin')
      ->where('company_id', Auth::user()->company_id)
      ->where($where_search)
      ->count();

      //number column
      if($order_sort=='desc'){
        $no_start = $start;
      }else{
        $no_start = $start+1;
      }
      $query_array = json_decode(json_encode($query), true);
      foreach ($query_array as $key => $value) {
        if($order_sort=='desc'){
          $query_array[$key]['position'] = $count_total - $no_start;
          $no_start++;
        }else{
          $query_array[$key]['position'] = $no_start;
          $no_start++;
        }
      }
      $query = json_decode(json_encode($query_array));
      //number column

      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        if(Auth::user()->level == '99'){
          $obj['action'] = '
          <div class="btn-group">
            <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
            <a href="javascript:void(0)" onclick="deleteDatatableItem('.$obj->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
          </div>';
        }else{
          $obj['action'] = '
          <div class="btn-group">
            <a href="'.$this->prefix.'/'.$obj->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
          </div>';
        }
        
        array_push($data_query, $obj);
      }
      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;
    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Admin;

      $admin_username = Admin::where('username', $request->username)->first();
      if(!$admin_username){
          $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
          $model->username = isset($request->username) ? AppHelper::instance()->cleanInput($request->username) : null;
          $model->password = isset($request->password) ? Hash::make($request->password) : null;
          
          if ($request->hasFile('avatar')) {
            $model->avatar = AppHelper::instance()->saveImage($request->file('avatar'),'/upload/company');
          }
    
          $model->status = $request->status;
          $model->company_id = Auth::user()->company_id;
          $model->level = 55;
          $model->company_level = 2;
          $model->save();

          return redirect('/Corporation/Corp_Lrk/corp_admin')->with('success', 'Data has been update');
      }else{
        return redirect('/Corporation/Corp_Lrk/corp_admin')->with('errors', 'Username Cannot be used');
      }
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }

      $model = Admin::find($id);
      if($model){

        $admin_username = Admin::where('username', $request->username)->where('id', $model->id)->first();
        if(!$admin_username){
          $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
          $model->username = isset($request->username) ? AppHelper::instance()->cleanInput($request->username) : null;
          
          if (Auth::user()->id ==$model->id){
            $model->password = isset($request->password) ? Hash::make($request->password) : null;
          }
            
          if ($request->hasFile('avatar')) {
            if($model->avatar!=null&&$model->avatar!=''){
              AppHelper::instance()->removeImage($model->avatar);
            }
            $model->avatar = AppHelper::instance()->saveImage($request->file('avatar'),'/upload/company');
          }else{
            if(empty($request->source_avatar)){
              if($model->avatar!=null&&$model->avatar!=''){
                AppHelper::instance()->removeImage($model->avatar);
              }
              $model->avatar = null;
            }
          }
  
          $model->status = $request->status;
    
          $model->save();
          return redirect('/Corporation/Corp_Lrk/corp_admin')->with('success', 'Data has been update');
        }else{
          return redirect('/Corporation/Corp_Lrk/corp_admin')->with('errors', 'Username has been used');
        }
      }else{
        return redirect('/Corporation/Corp_Lrk/corp_admin')->with('errors', 'Data Not Found');
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return response()->json();
      }

      $model = Admin::find($request->id);

      $model->delete();

      return response()->json();
    }

    public function status(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '55'){
        return response()->json();
      }
      $model = Admin::find($request->id);

      if($model->status=='1'){
        $model->status = '2';
      }
      else{
        $model->status = '1';
      }

      $model->save();

      return response()->json();
    }

}
