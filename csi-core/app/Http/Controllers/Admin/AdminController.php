<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\Admin;

use App\Helpers\AppHelper;
use App\Helpers\InputHelper;

use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;


use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema as Schema;
use Illuminate\Database\Migrations\Migration;

use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;

use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\Dummy;

class AdminController extends Controller
{
    public $prefix = 'admin';

    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $obj = array(
        'prefix'=>$this->prefix
      );
      return view('admin.'.$this->prefix.'.index', compact('obj'));
    }

    public function dblist(DataTables $datatables)
    {
      if(Auth::user()->level != '99'){
        return new Dummy();
      }
      $query = DB::table('admin')
      ->select('admin.id', 'admin.name', 'admin.level', 'admin.username', 'admin.avatar')
      ->orderBy('id', 'desc')
      ->get();

      if(Auth::user()->level =='99'){
        $data = $datatables->of($query)
        ->addColumn('action', function ($model) {
            return '
            <div class="btn-group">
              <a href="'.$this->prefix.'/'.$model->id.'/edit" class="btn btn-info" data-original-title="แก้ไข" title="แก้ไข"><i class="fa fa-edit"></i></a>
              <a href="javascript:void(0)" onclick="deleteDatatableItem('.$model->id.',\''.$this->prefix.'/delete\')"class="btn btn-danger" data-original-title="ลบ" title="ลบ"><i class="fa fa-times"></i></a>
            </div>
            ';
        })
        ->rawColumns(['action'])
        ->editColumn('name', '{{ Str::limit($name, 60) }}')
        ->make(true);
      }else{
        $data = $datatables->of($query)
              ->make(true);
      }

      return $data;

    }

    public function create()
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = new Admin();
      $obj = array(
        'type'=>'create',
        'route'=> route($this->prefix.'.store'),
        'prefix'=>$this->prefix
      );
      return view('admin.'.$this->prefix.'.form', compact('model','obj'));
    }

    public function show($id)
    {
        return view('admin.'.$this->prefix.'.view', compact('model','obj'));
    }

    public function edit($id)
    {
      if(Auth::user()->level != '99' && Auth::user()->id != $id){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $model = Admin::find($id);
      $obj = array(
        'type'=>'edit',
        'route'=>route($this->prefix.'.update',$model->id),
        'prefix'=>$this->prefix
      );
      // AppHelper::instance()->consoleLog($model);
      return view('admin.'.$this->prefix.'.form', compact('model','obj'));

    }

    public function store(Request $request)
    {
      if(Auth::user()->level != '99'){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $request = AppHelper::instance()->purifier($request);
      $model = new Admin();
      
      $username = Admin::where('username', $request->username)->first();
      if(!$username){

        $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
        $model->username = isset($request->username) ? AppHelper::instance()->cleanInput($request->username) : null;
        $model->password = isset($request->password) ? Hash::make($request->password) : null;
        $model->level = $request->level;

        if ($request->hasFile('avatar')) {
          $model->avatar = AppHelper::instance()->saveImage($request->file('avatar'),'upload/avatar',300);
        }

        $model->status = $request->status;

        $model->save();

        return redirect(route('admin').'/'.$this->prefix)->with('success', 'Account has been added');
      }else{
        return redirect(route('admin').'/'.$this->prefix)->with('errors', 'Username has been in system');
      }
    }

    public function update(Request $request, $id)
    {
      if(Auth::user()->level != '99' && Auth::user()->id != $id){
        return redirect(route('admin').'/')->with('errors', 'Permission Deny');
      }
      $request = AppHelper::instance()->purifier($request);
      $model = Admin::find($id);
      
      $username = Admin::where('username', $request->username)->where('admin.id','!=', $id)->first();
      if(!$username){
        $model->name = isset($request->name) ? AppHelper::instance()->cleanInput($request->name) : null;
        $model->username = isset($request->username) ? AppHelper::instance()->cleanInput($request->username) : null;
        
        if (Auth::user()->id ==$model->id) {
          $model->password = isset($request->password) ? Hash::make($request->password) : $model->password;
        }

        if ($request->hasFile('avatar')) {
          if($model->avatar!=null&&$model->avatar!=''){
            AppHelper::instance()->removeImage($model->avatar);
          }
          $model->avatar = AppHelper::instance()->saveImage($request->file('avatar'),'upload/avatar',300);
        }else{
          if(empty($request->source_avatar)){
            if($model->avatar!=null&&$model->avatar!=''){
              AppHelper::instance()->removeImage($model->avatar);
            }
            $model->avatar = null;
          }
        }

        if ($request->get('level')) {
            $model->level = $request->get('level');
        }

        $model->status = $request->status;

        $model->save();

        return redirect(route('admin').'/'.$this->prefix)->with('success', 'Account has been updated');
      }else{
        return redirect(route('admin').'/'.$this->prefix)->with('errors', 'Username has been in system');
      }
    }

    public function destroy(Request $request)
    {
      if(Auth::user()->level != '99' && Auth::user()->level != '77'){
        return new Dummy();
      }
      Admin::find($request->id)->delete();
      return response()->json();
    }

}
