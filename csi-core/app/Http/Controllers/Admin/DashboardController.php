<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\Core\ActivityLearner;
use App\Models\Core\Article;
use App\Models\Core\CateSpeaker;
use App\Models\Core\Course;
use App\Models\Core\CourseCheckerLog;
use App\Models\Core\Infographic;
use App\Models\Core\Seminar;
use App\Models\Core\UserGraduateLog;
use App\Models\Core\UsersLearningLog;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $count_user = User::where('status', 1)->count();

        $count_course = Course::where('status', 1)->count();

        if(Auth::user()->level != 88){
            $order = UsersOrderLog::where('is_free', 2)->count();
            $order_success = UsersOrderLog::where('is_free', 2)->where('status', 2)->count();
    
            $sale = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->where('user_order_log.status', 2)
            ->select('user_order_list.price')
            ->get();
    
            $price_total = $sale->sum('price');
    
            $order_discount = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->where('user_order_log.status', 2)
            ->where('user_order_list.discount_value', '!=', null)
            ->select('user_order_list.discount_value', 'user_order_log.web_price')
            ->get();
      
            $discount = $order_discount->sum('discount_value');
            $discount_etc = $order_discount->sum('web_price');
      
            $discount_all = $discount+$discount_etc;
    
            
    
            $order_total = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->where('user_order_log.status', 2)
            ->select('user_order_list.price')->get();
    
            $total = $order_total->sum('price');
    
            
    
            $net_price = $price_total-$discount_all;
            
    
            if($net_price != 0){
                $percent = ($net_price*100)/$total;
            }else{
                $percent = 0;
            }
    
            $net_percent = intval(number_format($percent,2));
    
            $course = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
            ->join('course', 'course.id', 'user_order_list.course_id')
            ->where('user_order_log.status', 2)
            ->where('course.trailer_media', '!=', 5)
            ->select('user_order_list.course_id', 'course.title_th', 'course.image_th', DB::raw('COUNT(user_order_list.course_id) as max_course'))
            ->groupBy('user_order_list.course_id')
            ->orderBy('max_course', 'desc')
            ->limit(10)
            ->get();
    
            $learning = UsersLearningLog::join('course', 'course.id', 'user_learning_log.course_id')
            ->where('course.speaker', '!=', null)
            ->where('course.speaker', '!=', '')
            ->select('course.speaker as course_speaker', DB::raw('COUNT(user_learning_log.course_id) as max_course'))
            ->groupBy('user_learning_log.course_id')
            ->limit(10)
            ->orderBy('max_course', 'desc')
            ->get();
            foreach($learning as $key_learning=>$val_learning){
                if($val_learning != '' && $val_learning != null){
                    $speaker = explode(",",$val_learning['course_speaker']);
                    $speaker_name = '';
                    foreach($speaker as $key_speaker=>$val_speaker){
                        $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
                        if($speaker_q){
                          if($speaker_name != ''){
                            $speaker_name .= ', ';
                          }
                          $speaker_name .= $speaker_q->title_th;
                        }
                      }
                      $learning[$key_learning]['course_speaker'] = $speaker_name;
                }else{
                    $learning[$key_learning]['course_speaker'] = 'ไม่มีผู้สอน';
                }
            }
    
            $user_rank = UserGraduateLog::join('user', 'user.id', 'user_graduate_log.user_id')
            ->select('user_graduate_log.user_id', 'user.name', 'user.lastname', 'user.email', 'user.mobile', DB::raw('COUNT(user_graduate_log.user_id) as max_learning'))
            ->groupBy('user_graduate_log.user_id')
            ->orderBy('max_learning', 'desc')
            ->limit(10)
            ->get();
    
            $school_rank = UsersOrderLog::join('user', 'user.id', 'user_order_log.user_id')
            ->select('user_order_log.user_id', 'user.school', DB::raw('COUNT(user_order_log.user_id) as max_schooling'))
            ->groupBy('user.school')
            ->orderBy('max_schooling', 'desc')
            ->limit(20)
            ->get();
    
            //Graph
    
            $today = Carbon::now()->toDateString();
            $month = Carbon::now()->month;
            $year = Carbon::now()->year;
    
            //DATE
            $sum_today = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', $today)
            ->where('user_order_log.status', 2)
            ->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
    
            //COUNT
            
            $convert_date = array();
            $arr_date = array();
    
            $arr_date_10 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(10))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_10);
            array_push($convert_date,Carbon::now()->subdays(10)->format('Y/m/d'));
    
            $arr_date_9 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(9))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_9);
            array_push($convert_date,Carbon::now()->subdays(9)->format('Y/m/d'));
    
            $arr_date_8 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(8))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_8);
            array_push($convert_date,Carbon::now()->subdays(8)->format('Y/m/d'));
    
            $arr_date_7 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(7))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_7);
            array_push($convert_date,Carbon::now()->subdays(7)->format('Y/m/d'));
    
            $arr_date_6 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(6))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_6);
            array_push($convert_date,Carbon::now()->subdays(6)->format('Y/m/d'));
    
            $arr_date_5 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(5))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_5);
            array_push($convert_date,Carbon::now()->subdays(5)->format('Y/m/d'));
    
            $arr_date_4 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(4))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_4);
            array_push($convert_date,Carbon::now()->subdays(4)->format('Y/m/d'));
    
            $arr_date_3 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(3))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_3);
            array_push($convert_date,Carbon::now()->subdays(3)->format('Y/m/d'));
    
            $arr_date_2 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(2))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_2);
            array_push($convert_date,Carbon::now()->subdays(2)->format('Y/m/d'));
    
            $arr_date_1 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', Carbon::now()->subdays(1))
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_1);  
            array_push($convert_date,Carbon::now()->subdays(1)->format('Y/m/d')); 
    
            $arr_date_0 = UsersOrderList::join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->wheredate('user_order_log.created_at', $today)
            ->where('user_order_log.status', 2)->where('user_order_log.is_free', 2)
            ->sum('user_order_list.price');
            array_push($arr_date,$arr_date_0);  
            array_push($convert_date,Carbon::now()->format('Y/m/d')); 
            //COUNT
    
            //DATE
    
            //MONTH
            $sum_month = UsersOrderLog::whereMonth('user_order_log.created_at', $month)
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('user_order_log.total_price');
    
            $convert_month = array();
            $arr_month = array();
    
            $arr_month_6 = UsersOrderLog::whereMonth('created_at', Carbon::now()->subMonths(6))
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_month,$arr_month_6);
            array_push($convert_month,Carbon::now()->subMonths(6)->format('M')); 
    
            $arr_month_5 = UsersOrderLog::whereMonth('created_at', Carbon::now()->subMonths(5))
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_month,$arr_month_5);
            array_push($convert_month,Carbon::now()->subMonths(5)->format('M')); 
    
            $arr_month_4 = UsersOrderLog::whereMonth('created_at', Carbon::now()->subMonths(4))
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_month,$arr_month_4);
            array_push($convert_month,Carbon::now()->subMonths(4)->format('M')); 
    
            $arr_month_3 = UsersOrderLog::whereMonth('created_at', Carbon::now()->subMonths(3))
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_month,$arr_month_3);
            array_push($convert_month,Carbon::now()->subMonths(3)->format('M')); 
    
            $arr_month_2 = UsersOrderLog::whereMonth('created_at', Carbon::now()->subMonths(2))
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_month,$arr_month_2);
            array_push($convert_month,Carbon::now()->subMonths(2)->format('M')); 
    
            $arr_month_1 = UsersOrderLog::whereMonth('created_at', Carbon::now()->subMonths(1))
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_month,$arr_month_1);
            array_push($convert_month,Carbon::now()->subMonths(1)->format('M')); 
    
            $arr_month_0 = UsersOrderLog::whereMonth('created_at', $month)
            ->whereYear('user_order_log.created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_month,$arr_month_0);
            array_push($convert_month,Carbon::now()->format('M')); 
            //MONTH
    
            //YEAR
            $sum_year = UsersOrderLog::whereYear('user_order_log.created_at', $year)
            ->sum('user_order_log.total_price');
    
            $convert_year = array();
            $arr_year = array();
            
            $arr_year_6 = UsersOrderLog::whereYear('created_at', Carbon::now()->subYears(6))
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_year,$arr_year_6);
            array_push($convert_year,Carbon::now()->subYears(6)->format('Y')); 
    
            $arr_year_5 = UsersOrderLog::whereYear('created_at', Carbon::now()->subYears(5))
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_year,$arr_year_5);
            array_push($convert_year,Carbon::now()->subYears(5)->format('Y')); 
    
            $arr_year_4 = UsersOrderLog::whereYear('created_at', Carbon::now()->subYears(4))
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_year,$arr_year_4);
            array_push($convert_year,Carbon::now()->subYears(4)->format('Y')); 
    
            $arr_year_3 = UsersOrderLog::whereYear('created_at', Carbon::now()->subYears(3))
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_year,$arr_year_3);
            array_push($convert_year,Carbon::now()->subYears(3)->format('Y')); 
    
            $arr_year_2 = UsersOrderLog::whereYear('created_at', Carbon::now()->subYears(2))
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_year,$arr_year_2);
            array_push($convert_year,Carbon::now()->subYears(2)->format('Y')); 
    
            $arr_year_1 = UsersOrderLog::whereYear('created_at', Carbon::now()->subYears(1))
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_year,$arr_year_1);
            array_push($convert_year,Carbon::now()->subYears(1)->format('Y')); 
    
            $arr_year_0 = UsersOrderLog::whereYear('created_at', $year)
            ->where('status', 2)->where('is_free', 2)
            ->sum('total_price');
            array_push($arr_year,$arr_year_0);
            array_push($convert_year,Carbon::now()->format('Y')); 
            //YEAR
    
            // if($sum_today){
            //     $sum_today = number_format($sum_today->total_price);
            // }else{
            //     $sum_today = 0;
            // }
    
            if($sum_month){
                $sum_month = number_format($sum_month);
            }else{
                $sum_month = 0;
            }
    
            if($sum_year){
                $sum_year = number_format($sum_year);
            }else{
                $sum_year = 0;
            }
    
            AppHelper::instance()->consoleLog($discount_etc);
            AppHelper::instance()->consoleLog($today);
            AppHelper::instance()->consoleLog($sum_today);
            AppHelper::instance()->echoData('arr_date',$arr_date);
            AppHelper::instance()->echoData('convert_date',$convert_date);

            AppHelper::instance()->consoleLog($month);
            AppHelper::instance()->consoleLog($sum_month);
            AppHelper::instance()->echoData('arr_month',$arr_month);
            AppHelper::instance()->echoData('convert_month',$convert_month);

            AppHelper::instance()->consoleLog($year);
            AppHelper::instance()->consoleLog($sum_year);
            AppHelper::instance()->echoData('arr_year',$arr_year);
            AppHelper::instance()->echoData('convert_year',$convert_year);
            
            // AppHelper::instance()->consoleLog($school_rank);

    
            //Graph
        }else{
            $admin_log = CourseCheckerLog::where('admin_id', Auth::user()->id)->get();
        
            $arr_id = array();
            foreach ($admin_log as $key_data => $data) { 
            array_push($arr_id, $data->admin_id);
            }

            $order = UsersOrderLog::join('course', 'course.id', 'user_order_log.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->where('user_order_log.is_free', 2)
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->count();

            $order_success = UsersOrderLog::join('course', 'course.id', 'user_order_log.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->where('user_order_log.is_free', 2)
            ->where('user_order_log.status', 2)
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->count();
    
            $sale = UsersOrderList::join('course', 'course.id', 'user_order_list.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->where('user_order_log.status', 2)
            ->select('user_order_list.price')
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->get();
    
            $price_total = $sale->sum('price');
    
            $order_discount = UsersOrderList::join('course', 'course.id', 'user_order_list.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->where('user_order_log.status', 2)
            ->where('user_order_list.discount_value', '!=', null)
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->select('user_order_list.discount_value', 'user_order_log.web_price')
            ->get();
      
            $discount = $order_discount->sum('discount_value');
            $discount_etc = $order_discount->sum('web_price');
      
            $discount_all = $discount+$discount_etc;
    
            
    
            $order_total = UsersOrderList::join('course', 'course.id', 'user_order_list.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->join('user_order_log', 'user_order_log.id','user_order_list.order_id')
            ->where('user_order_log.status', 2)
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->select('user_order_list.price')
            ->get();
    
            $total = $order_total->sum('price');
    
            
    
            $net_price = $price_total-$discount_all;
            
    
            if($net_price != 0){
                $percent = ($net_price*100)/$total;
            }else{
                $percent = 0;
            }
    
            $net_percent = intval(number_format($percent,2));
    
            $course = UsersOrderList::join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
            ->join('course', 'course.id', 'user_order_list.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->where('user_order_log.status', 2)
            ->where('course.trailer_media', '!=', 5)
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->select('user_order_list.course_id', 'course.title_th', 'course.image_th', DB::raw('COUNT(user_order_list.course_id) as max_course'))
            ->groupBy('user_order_list.course_id')
            ->orderBy('max_course', 'desc')
            ->limit(10)
            ->get();
    
            $learning = UsersLearningLog::join('course', 'course.id', 'user_learning_log.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->where('course.speaker', '!=', null)
            ->where('course.speaker', '!=', '')
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->select('course.speaker as course_speaker', DB::raw('COUNT(user_learning_log.course_id) as max_course'))
            ->groupBy('user_learning_log.course_id')
            ->limit(10)
            ->orderBy('max_course', 'desc')
            ->get();
            foreach($learning as $key_learning=>$val_learning){
                if($val_learning != '' && $val_learning != null){
                    $speaker = explode(",",$val_learning['course_speaker']);
                    $speaker_name = '';
                    foreach($speaker as $key_speaker=>$val_speaker){
                        $speaker_q = CateSpeaker::where('id', $val_speaker)->first();
                        if($speaker_q){
                          if($speaker_name != ''){
                            $speaker_name .= ', ';
                          }
                          $speaker_name .= $speaker_q->title_th;
                        }
                      }
                      $learning[$key_learning]['course_speaker'] = $speaker_name;
                }else{
                    $learning[$key_learning]['course_speaker'] = 'ไม่มีผู้สอน';
                }
            }
    
            $user_rank = UserGraduateLog::join('user', 'user.id', 'user_graduate_log.user_id')
            ->join('course', 'course.id', 'user_graduate_log.course_id')
            ->join('course_checker_log', 'course_checker_log.course_id', 'course.id')
            ->whereIn('course_checker_log.admin_id', $arr_id)
            ->select('user_graduate_log.user_id', 'user.name', 'user.lastname', 'user.email', 'user.mobile', DB::raw('COUNT(user_graduate_log.user_id) as max_learning'))
            ->groupBy('user_graduate_log.user_id')
            ->orderBy('max_learning', 'desc')
            ->limit(10)
            ->get();
    
            //Graph
    
            $today = Carbon::now()->toDateString();
            $month = Carbon::now()->month;
            $year = Carbon::now()->year;
    
            //DATE
            $sum_today = ActivityLearner::wheredate('activity_learning.created_at', $today)
            ->first();
    
            //COUNT
            
            $convert_date = array();
            $arr_date = array();
    
            $arr_date_10 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(10))
            ->sum('amount');
            array_push($arr_date,$arr_date_10);
            array_push($convert_date,Carbon::now()->subdays(10)->format('Y/m/d'));
    
            $arr_date_9 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(9))
            ->sum('amount');
            array_push($arr_date,$arr_date_9);
            array_push($convert_date,Carbon::now()->subdays(9)->format('Y/m/d'));
    
            $arr_date_8 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(8))
            ->sum('amount');
            array_push($arr_date,$arr_date_8);
            array_push($convert_date,Carbon::now()->subdays(8)->format('Y/m/d'));
    
            $arr_date_7 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(7))
            ->sum('amount');
            array_push($arr_date,$arr_date_7);
            array_push($convert_date,Carbon::now()->subdays(7)->format('Y/m/d'));
    
            $arr_date_6 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(6))
            ->sum('amount');
            array_push($arr_date,$arr_date_6);
            array_push($convert_date,Carbon::now()->subdays(6)->format('Y/m/d'));
    
            $arr_date_5 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(5))
            ->sum('amount');
            array_push($arr_date,$arr_date_5);
            array_push($convert_date,Carbon::now()->subdays(5)->format('Y/m/d'));
    
            $arr_date_4 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(4))
            ->sum('amount');
            array_push($arr_date,$arr_date_4);
            array_push($convert_date,Carbon::now()->subdays(4)->format('Y/m/d'));
    
            $arr_date_3 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(3))
            ->sum('amount');
            array_push($arr_date,$arr_date_3);
            array_push($convert_date,Carbon::now()->subdays(3)->format('Y/m/d'));
    
            $arr_date_2 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(2))
            ->sum('amount');
            array_push($arr_date,$arr_date_2);
            array_push($convert_date,Carbon::now()->subdays(2)->format('Y/m/d'));
    
            $arr_date_1 = ActivityLearner::wheredate('created_at', Carbon::now()->subdays(1))
            ->sum('amount');  
            array_push($arr_date,$arr_date_1);  
            array_push($convert_date,Carbon::now()->subdays(1)->format('Y/m/d')); 
    
            $arr_date_0 = ActivityLearner::wheredate('created_at', $today)
            ->sum('amount');
            array_push($arr_date,$arr_date_0);  
            array_push($convert_date,Carbon::now()->format('Y/m/d')); 
            //COUNT
    
            //DATE
    
            //MONTH
            $sum_month = ActivityLearner::whereMonth('activity_learning.created_at', $month)
            ->whereYear('activity_learning.created_at', $year)
            ->sum('activity_learning.total_price');
    
    
            // $sum_month_order = ActivityLearner::whereMonth('created_at', $month)
            // ->whereYear('activity_learning.created_at', $year)->sum('amount');
    
            $convert_month = array();
            $arr_month = array();
    
            $arr_month_6 = ActivityLearner::whereMonth('created_at', Carbon::now()->subMonths(6))
            ->whereYear('activity_learning.created_at', $year)
            ->sum('amount');
            array_push($arr_month,$arr_month_6);  
            array_push($convert_month,Carbon::now()->subMonths(6)->format('M')); 
    
            $arr_month_5 = ActivityLearner::whereMonth('created_at', Carbon::now()->subMonths(5))
            ->whereYear('activity_learning.created_at', $year)
            ->sum('amount');
            array_push($arr_month,$arr_month_5);
            array_push($convert_month,Carbon::now()->subMonths(5)->format('M')); 
    
            $arr_month_4 = ActivityLearner::whereMonth('created_at', Carbon::now()->subMonths(4))
            ->whereYear('activity_learning.created_at', $year)
            ->sum('amount');
            array_push($arr_month,$arr_month_4);
            array_push($convert_month,Carbon::now()->subMonths(4)->format('M')); 
    
            $arr_month_3 = ActivityLearner::whereMonth('created_at', Carbon::now()->subMonths(3))
            ->whereYear('activity_learning.created_at', $year)
            ->sum('amount');
            array_push($arr_month,$arr_month_3);
            array_push($convert_month,Carbon::now()->subMonths(3)->format('M')); 
    
            $arr_month_2 = ActivityLearner::whereMonth('created_at', Carbon::now()->subMonths(2))
            ->whereYear('activity_learning.created_at', $year)
            ->sum('amount');
            array_push($arr_month,$arr_month_2);
            array_push($convert_month,Carbon::now()->subMonths(2)->format('M')); 
    
            $arr_month_1 = ActivityLearner::whereMonth('created_at', Carbon::now()->subMonths(1))
            ->whereYear('activity_learning.created_at', $year)
            ->sum('amount');
            array_push($arr_month,$arr_month_1);
            array_push($convert_month,Carbon::now()->subMonths(1)->format('M')); 
    
            $arr_month_0 = ActivityLearner::whereMonth('created_at', $month)
            ->whereYear('activity_learning.created_at', $year)
            ->sum('amount');
            array_push($arr_month,$arr_month_0);
            array_push($convert_month,Carbon::now()->format('M')); 
            //MONTH
    
            //YEAR
            $sum_year = ActivityLearner::whereYear('activity_learning.created_at', $year)
            ->first();
    
            // $sum_year_order = ActivityLearner::whereYear('created_at', $year)->where('status', 2)->count();
    
            $convert_year = array();
            $arr_year = array();
            
            $arr_year_6 = ActivityLearner::whereYear('created_at', Carbon::now()->subYears(6))
            ->sum('amount');
            array_push($arr_year,$arr_year_6);
            array_push($convert_year,Carbon::now()->subYears(6)->format('Y')); 
    
            $arr_year_5 = ActivityLearner::whereYear('created_at', Carbon::now()->subYears(5))
            ->sum('amount');
            array_push($arr_year,$arr_year_5);
            array_push($convert_year,Carbon::now()->subYears(5)->format('Y')); 
    
            $arr_year_4 = ActivityLearner::whereYear('created_at', Carbon::now()->subYears(4))
            ->sum('amount');
            array_push($arr_year,$arr_year_4);
            array_push($convert_year,Carbon::now()->subYears(4)->format('Y')); 
    
            $arr_year_3 = ActivityLearner::whereYear('created_at', Carbon::now()->subYears(3))
            ->sum('amount');
            array_push($arr_year,$arr_year_3);
            array_push($convert_year,Carbon::now()->subYears(3)->format('Y')); 
    
            $arr_year_2 = ActivityLearner::whereYear('created_at', Carbon::now()->subYears(2))
            ->sum('amount');
            array_push($arr_year,$arr_year_2);
            array_push($convert_year,Carbon::now()->subYears(2)->format('Y')); 
    
            $arr_year_1 = ActivityLearner::whereYear('created_at', Carbon::now()->subYears(1))
            ->sum('amount');
            array_push($arr_year,$arr_year_1);
            array_push($convert_year,Carbon::now()->subYears(1)->format('Y')); 
    
            $arr_year_0 = ActivityLearner::whereYear('created_at', $year)
            ->sum('amount');
            array_push($arr_year,$arr_year_0);
            array_push($convert_year,Carbon::now()->format('Y')); 
            //YEAR
    
            if($sum_today){
                $sum_today = number_format($sum_today->amount);
            }else{
                $sum_today = 0;
            }
    
            if($sum_month){
                $sum_month = number_format($sum_month->amount);
            }else{
                $sum_month = 0;
            }
    
            if($sum_year){
                $sum_year = number_format($sum_year->amount);
            }else{
                $sum_year = 0;
            }
    
            AppHelper::instance()->consoleLog($discount_etc);
            AppHelper::instance()->consoleLog($today);
            AppHelper::instance()->consoleLog($sum_today);
            AppHelper::instance()->echoData('arr_date',$arr_date);
            AppHelper::instance()->echoData('convert_date',$convert_date);

            AppHelper::instance()->consoleLog($month);
            AppHelper::instance()->consoleLog($sum_month);
            AppHelper::instance()->echoData('arr_month',$arr_month);
            AppHelper::instance()->echoData('convert_month',$convert_month);

            AppHelper::instance()->consoleLog($year);
            AppHelper::instance()->consoleLog($sum_year);
            AppHelper::instance()->echoData('arr_year',$arr_year);
            AppHelper::instance()->echoData('convert_year',$convert_year);
            
    
            //Graph
        }
        

        $obj = array(
            'count_user' => $count_user,
            'count_course' => $count_course,

            'order' => $order,
            'order_success' => $order_success,

            'price_total' => $price_total,
            'discount_all' => $discount_all,
            'net_price' => $net_price,
            'net_percent' => $net_percent,
            'school_rank' => $school_rank,
        );

        AppHelper::instance()->consoleLog($learning);
        AppHelper::instance()->consoleLog($school_rank);
        return view('admin.dashboard', compact('obj', 'course','user_rank', 'learning','sum_today','sum_month','sum_year','school_rank'));
    }
    public function sample()
    {
        return view('admin.page_builder');
    }
    public function error400()
    {
        return view('admin.400');
    }
    public function error500()
    {
        return view('admin.500');
    }
}
