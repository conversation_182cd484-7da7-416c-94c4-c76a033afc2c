<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\Dummy;
use App\Models\Core\UserHistory;
use App\Models\Core\VolumnBy;
use App\Models\Core\VolumnByCourse;


use Illuminate\Http\Request;

use Yajra\Datatables\Datatables;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class DashboardCorpController extends Controller
{
    public $prefix = 'dashboarb_course';
    public $project_url = 'dashboarb_course';
    public $project_layout = 'admin.volumn_by';
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $obj = array(
            'company_name' => '',
            'company_id' => '',
            'lot_id' => '',
        );
        $company = VolumnBy::where('id', Auth::user()->company_id)->select('company_name', 'id')->first(); 

        if($company){
            if(isset($request->lot)){
                $obj = array(
                    'company_name' => $company->company_name,
                    'company_id' => $company->id,
                    'lot_id' => $request->lot,
                );
            }else{
                $obj = array(
                    'company_name' => $company->company_name,
                    'company_id' => $company->id,
                    'lot_id' => '',
                );
            }
           
        }
        // AppHelper::instance()->consoleLog($lot);

        return view('admin.dashboard_corp', compact('obj'));
    }

    public function index_course(Request $request)
    {
      
      $lot = $request->lot;

      $obj = array(
        'prefix'=>$this->prefix,
        'project_url'=>$this->project_url,
        'project_layout'=>$this->project_layout
      );
      
      AppHelper::instance()->consoleLog($lot);
      return view($this->project_layout.'.index', compact('obj','lot'));
    }

    public function dblist_course(DataTables $datatables, Request $request)
    {

      $draw = $request->get('draw');
      $start = $request->get('start');
      $length = $request->get('length');

      $where_search = array();
      foreach ($request->get('columns') as $key => $value) {
        if(!empty($value['search']['value'])){
          if($value['data']=='course_name'){
            array_push($where_search, ['course.name', 'LIKE','%'.$value['search']['value'].'%']);
          }else if($value['data']=='c_key'){
            array_push($where_search, ['course.course_key', 'LIKE','%'.$value['search']['value'].'%']);
          }else{
            array_push($where_search, ['volumn_course_log.'.$value['data'], 'LIKE','%'.$value['search']['value'].'%']);
          }
        }
      }
      $index = $request->get('order')[0]['column'];
      $orderby = $request->get('columns')[$index]['data'];
      $order_sort = $request->get('order')[0]['dir'];

      if(isset($request->lot)){
        $query = DB::table('volumn_course_log')
        ->join('course', 'course.id', 'volumn_course_log.course_id')
        ->select('volumn_course_log.*', 'course.title_th as c_title', 'course.course_key as c_key', 'course.image_th', 'course.price', 'course.is_cert', 'course.status')
        ->where('volumn_course_log.volumn_id', $request->lot)
        ->where($where_search)
        ->orderBy($orderby, $order_sort)
        ->offset($start)
        ->limit($length)
        ->get();
        $count_total = DB::table('volumn_course_log')
        ->join('course', 'course.id', 'volumn_course_log.course_id')
        ->where('volumn_course_log.volumn_id', $request->lot)
        ->where($where_search)
        ->count();
      }else{
        $query = array();
        $count_total = 0;
      }
      $data_query = array();
      foreach ($query as $key => $value) {
        $obj = new Dummy();
        foreach ($value as $key_data => $data) {
          $obj[$key_data] = $data;
        }
        

        $count_student = UserHistory::where('course_id', $value->course_id)->where('company_lot_id', $value->volumn_id)->where('get_type', 6)->groupBy('user_id')->get();
        $count_student = Count($count_student);
        if($count_student){
          $obj['count_student'] = '
          <a href="'.url('/Corporation').'/Corp_Lrk/dashboard_user_course/'.$value->course_id.'/'.$value->volumn_id.'">
            
            จำนวน '. $count_student .' ราย
          </a>';
        }else{
          $obj['count_student'] = '0 ราย';
        }

        array_push($data_query, $obj);
      }

      $data = $datatables->of($data_query)
            ->with([
              'data' => $data_query,
              'draw' => $draw,
              "recordsTotal" => $count_total,
              "recordsFiltered" => $count_total,
            ])
            ->make(true);
      return $data;

    }


    public function sample()
    {
        return view('admin.page_builder');
    }
    public function error400()
    {
        return view('admin.400');
    }
    public function error500()
    {
        return view('admin.500');
    }
}
