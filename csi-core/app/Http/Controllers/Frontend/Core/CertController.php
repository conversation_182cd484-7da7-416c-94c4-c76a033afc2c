<?php

namespace App\Http\Controllers\Frontend\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\User;

class CertController extends Controller
{

    public function __construct()
    {

    }

    public  function  add(Request $request){
        $response = new Dummy();
        if(!isset($request->page)){
            $request->page = 1;
        }
        if(!isset($request->limit)){
            $request->limit = 100;
        }
        $offset = (intval($request->page)-1)*intval($request->limit);
        $user = User::join('user_graduate_log','user_graduate_log.user_id','user.id')->select('user_graduate_log.*')
        ->where('user.status',1)->limit($request->limit)->offset($offset)->get();
        foreach($user as $val){
            AppHelper::instance()->addCertificateLog($val);
        }
        $response['user_all'] = User::join('user_graduate_log','user_graduate_log.user_id','user.id')->select('user_graduate_log.*')
        ->where('user.status',1)->count();
        if(count($user)>0){
            $response['status'] = 'success';
        }else{
            $response['status'] = 'finished';
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

}
