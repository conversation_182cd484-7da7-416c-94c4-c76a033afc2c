<?php

namespace App\Http\Controllers\Frontend\Core;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

use App\Helpers\AppHelper;
use App\Helpers\SimpleEnDeHelper;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Models\Dummy;
use App\Models\Core\Course;
use App\Models\Core\CourseGroup;
use App\Models\Core\EmailDynamic;
use App\Models\Core\NotiAutoLog;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\UserGiftLog;
use App\Models\Core\UserHistory;
use App\Models\Core\UserLearnedTime;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\User;

class KpaymentController extends Controller
{

    public function __construct()
    {

    }

    public  function  card(Request $request){
        $response = new Dummy();
        $response['status'] = '';
        // $response['return'] = $request->all();
        // $response['PAYMENT_SDK'] = env('PAYMENT_SDK').'/card/v2/charge';

        $order = UsersOrderLog::where('id',SimpleEnDeHelper::instance()->decryptString($request->order_id_encrypt))->where('status',1)->first();
        // $response['order'] = $order;

        if($order){
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL =>  env('PAYMENT_SDK').'/card/v2/charge'.'?amount='.$order->total_price.'&currency=THB&description=MDCU%20MEDNOVATION&source_type=card&mode=token&reference_order='.$order->order_no.'_'.AppHelper::instance()->generateRandomString(6).'&token='.$request->token.'',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                'x-api-key: '.env('PAYMENT_SKEY')
            ),
            ));
    
            $resp = curl_exec($curl);
            $resp = json_decode($resp,TRUE);
    
            curl_close($curl);
            // echo $response;
            $response['response'] = $resp;
            if($resp['object']=='charge' && $resp['status']=='success'){
                $response['status'] = 'success';
                
                $order->reference = $resp['id'];
                $order->status = 4;
                $order->save();
    
                // $payment_log = new UsersPaymentLog;
                // $payment_log->order_id = $order->id;
                // $payment_log->reference = $resp['id'];
                // $payment_log->save();
                
    
                return redirect($resp['redirect_url']);
            }else{
                return redirect('https://csisociety.com/dashboard/history');
            }
            
    
        }else{
            $response['status'] = 'not found';
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

 
    public function checkCallback(Request $request){
        $response = new Dummy();
        $response['status'] = '';
        $response['request'] = $request->all();
        
        if($request->objectId){
            $order = UsersOrderLog::where('reference',$request->objectId)->first();
            if($order){
                $curl = curl_init();

                curl_setopt_array($curl, array(
                CURLOPT_URL => env('PAYMENT_SDK').'/card/v2/charge/'.$request->objectId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'x-api-key: '.env('PAYMENT_SKEY')
                ),
                ));
        
        
                $resp = curl_exec($curl);
                $resp = json_decode($resp,TRUE);
        
                curl_close($curl);
                // echo $response;
                if($resp && isset($resp['transaction_state']) && $resp['transaction_state']=='Authorized'){
                    $order->status = 2;
                    $order->created_receipt_date = Carbon::now('Asia/Bangkok');
                    $order->receipt_path = env('APP_URL').'/receipt/'.SimpleEnDeHelper::instance()->encryptString($order->id);
                    $order->save();
                    $order_history_arr = UsersOrderLog::join('user_order_list','user_order_list.order_id','user_order_log.id')
                    ->select('user_order_log.id','user_order_log.receive_email','user_order_log.receive_message','user_order_log.user_id','user_order_log.buy_type','user_order_log.created_receipt_date'
                    ,'user_order_list.type','user_order_list.course_id','user_order_list.group_id','user_order_list.subscription_id','user_order_list.id as list_id')
                    ->where('user_order_log.status',2)->where('user_order_log.id',$order->id)->get();
                    foreach($order_history_arr as $order_history){
                        if($order_history && $order_history->buy_type==1){
                            if($order_history->type==1){
                                $course = Course::where('id',$order_history->course_id)->first();
                                if($course){
                                    $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$order_history->course_id)
                                    ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                                    if(!$check){
                                    $history_log = new UserHistory();
                                    if($course->trailer_media==2){
                                        $history_log->zoom_join_url = $course->zoom_join_url;
                                        $response['zoom_join_url'] = $history_log->zoom_join_url;
                                      }
                                    $history_log->get_type = 2;
                                    $history_log->order_list_id = $order_history->list_id;
                                    $history_log->user_id = $order_history->user_id;
                                    $history_log->course_id = $order_history->course_id;
                                    $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                                    $history_log->save();
                                    $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                    if($del_limit){
                                        $del_limit->delete();
                                    }
                                    $response['status'] = 'success';
                                    }
                                }
                            }else if($order_history->type==2){
                                $group = CourseGroup::where('id',$order_history->group_id)->first();
                                if($group){
                                    $course = Course::join('group_log','group_log.course_id','course.id')->select('course.id','course.duration_time','course.trailer_media','course.zoom_join_url')->where('group_id',$group->id)->get();
                                    foreach($course as $val_course){
                                    $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$val_course->id)
                                    ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                                    if(!$check){
                                        $history_log = new UserHistory();
                                        if($val_course->trailer_media==2){
                                            $history_log->zoom_join_url = $val_course->zoom_join_url;
                                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                                          }
                                        $history_log->get_type = 2;
                                        $history_log->order_list_id = $order_history->list_id;
                                        $history_log->user_id = $order_history->user_id;
                                        $history_log->course_id = $val_course->id;
                                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                                        $history_log->save();
                                        $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                        if($del_limit){
                                            $del_limit->delete();
                                        }
                                        $response['status'] = 'success';
                                    }
                                    }
                                }
                            }else if($order_history->type==3){
                                $subscription = Subscription::where('id',$order_history->subscription_id)->first();
                                if($subscription){
                                    $last_sub = SubscriptionLog::where('expired','>=',Carbon::now())->where('user_id',$order_history->user_id)->where('subscription_id',$subscription->id)->orderby('expired','desc')->first();
                                    $subscription_log = new SubscriptionLog();
                                    $subscription_log->order_list_id = $order_history->list_id;
                                    $subscription_log->user_id = $order_history->user_id;
                                    $subscription_log->subscription_id = $subscription->id;
                                    if($last_sub){
                                    $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                                    }else{
                                    $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                                    }
                                    $subscription_log->save();
                                }
                            }
                        }else if($order_history && $order_history->buy_type==2){
                            $gift_log = new UserGiftLog();
                            $gift_log->user_id = $order_history->user_id;
                            $gift_log->order_id = $order_history->id;
                            $gift_log->receive_email = $order_history->receive_email;
                            $gift_log->save();
                
                            $email_dynamic = EmailDynamic::where('id',10)->where('status',1)->first();
                            if($email_dynamic){
                                $replace_detail = $email_dynamic->details;
                                $replace_detail = str_replace("{{sender}}",$order_history->name.' '.$order_history->lastname,$replace_detail);
                                if(isset($order_history->receive_message)&&$order_history->receive_message!=null&&$order_history->receive_message!=''&&$order_history->receive_message!='null'){
                                    $replace_detail = str_replace("{{message}}",'ข้อความ : '.$order_history->receive_message,$replace_detail);
                                }else{
                                    $replace_detail = str_replace("{{message}}",'',$replace_detail);
                                }
                                $order_gift = UsersOrderList::where('user_order_list.order_id', $order_history->id)->get();
                                foreach($order_gift as $key=>$value){
                                $order_gift[$key]['title_th'] = null;
                                $order_gift[$key]['subtitle_th'] = null;
                                $order_gift[$key]['image_th'] = null;
                                $order_gift[$key]['course_link'] = null;
                                if($value->type==1){
                                    $course_log = Course::where('id',$value->course_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title_th;
                                        $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                                        $order_gift[$key]['image_th'] = $course_log->image_th;
                                        $order_gift[$key]['course_link'] = 'https://csisociety.com/course/'.$course_log->slug;
                                    }
                                }else if($value->type==2){
                                    $course_log = CourseGroup::where('id',$value->group_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title;
                                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                                        $order_gift[$key]['image_th'] = $course_log->thumb;
                                    }
                                }else if($value->type==3){
                                    $course_log = Subscription::where('id',$value->subscription_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title;
                                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                                        $order_gift[$key]['image_th'] = $course_log->thumb;
                                    }
                                }
                                }
                                $order_replace = '';
                                foreach($order_gift as $value){
                                    if($value->type==1){
                                        $order_replace.=
                                        '<tr>
                                            <td style="width:30px;"></td>
                                            <td style="width: 140px">
                                                <a href="'.$value->course_link.'">
                                                    <img src="'.$value->image_th.'" style="width: 140px;">
                                                </a>
                                            </td>
                                            <td style="padding: 10px;">
                                                <a href="'.$value->course_link.'">
                                                <b>'.$value->title_th.'</b><br>'.$value->subtitle_th.'
                                                </a>
                                            </td>
                                        </tr>';
                                    }else{
                                        $order_replace.=
                                        '<tr>
                                            <td style="width:30px;"></td>
                                            <td style="width: 140px">
                                                <img src="'.$value->image_th.'" style="width: 140px;">
                                            </td>
                                            <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
                                            '.$value->subtitle_th.'
                                            </td>
                                        </tr>';
                                    }
                                }
                                $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
                                $replace_detail = str_replace("{{link}}","https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id),$replace_detail);
                                $obj = new Dummy();
                                $obj['subject'] = $email_dynamic->subject;
                                $obj['code'] = $replace_detail;
                                $obj['email'] = $order_history->receive_email;
                                $obj['cc'] = [];
                                $obj['bcc'] = [];
                                AppHelper::instance()->mailTemplate($obj);
                            }
                            $receive_user = User::where('email',$order_history->receive_email)->first();
                            if($receive_user){
                                $noti_log = new NotiAutoLog();
                                $noti_log->user_id = $receive_user->id;
                                $noti_log->image = '/assets/images/qr.png';
                                $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
                                $noti_log->description = 'จากคุณ'.$order_history->name.' '.$order_history->lastname;
                                $noti_log->link = "https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id);
                                $noti_log->save();
                            }
                        }
                    }
                    $response['status'] = 'success';
                }
            }
            $response['status'] = 'notfound : '.$request->objectId;
        }
        else if($request->chargeId){
            if(isset($request->order_id_encrypt))
            {
                $order = UsersOrderLog::where('id',SimpleEnDeHelper::instance()->decryptString($request->order_id_encrypt))->first();
                if($order){
                    $order->reference = $request->chargeId;
                    $order->save();

                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                    CURLOPT_URL => env('PAYMENT_SDK').'/qr/v2/qr/'.$request->chargeId,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'x-api-key: '.env('PAYMENT_SKEY')
                    ),
                    ));
            
            
                    $resp = curl_exec($curl);
                    $resp = json_decode($resp,TRUE);
            
                    curl_close($curl);
                    // echo $response;
                    if($resp && isset($resp['transaction_state']) && $resp['transaction_state']=='Authorized'){
                        $order->status = 2;
                        $order->created_receipt_date = Carbon::now('Asia/Bangkok');
                        $order->receipt_path = env('APP_URL').'/receipt/'.SimpleEnDeHelper::instance()->encryptString($order->id);
                        $order->save();
                        $order_history_arr = UsersOrderLog::join('user_order_list','user_order_list.order_id','user_order_log.id')
                        ->select('user_order_log.id','user_order_log.receive_email','user_order_log.receive_message','user_order_log.user_id','user_order_log.buy_type','user_order_log.created_receipt_date'
                        ,'user_order_list.type','user_order_list.course_id','user_order_list.group_id','user_order_list.subscription_id','user_order_list.id as list_id')
                        ->where('user_order_log.status',2)->where('user_order_log.id',$order->id)->get();
                        foreach($order_history_arr as $order_history){
                            if($order_history && $order_history->buy_type==1){
                                if($order_history->type==1){
                                    $course = Course::where('id',$order_history->course_id)->first();
                                    if($course){
                                        $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$order_history->course_id)
                                        ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                                        if(!$check){
                                        $history_log = new UserHistory();
                                        if($course->trailer_media==2){
                                            $history_log->zoom_join_url = $course->zoom_join_url;
                                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                                          }
                                        $history_log->get_type = 2;
                                        $history_log->order_list_id = $order_history->list_id;
                                        $history_log->user_id = $order_history->user_id;
                                        $history_log->course_id = $order_history->course_id;
                                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                                        $history_log->save();
                                        $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                        if($del_limit){
                                            $del_limit->delete();
                                        }
                                        $response['status'] = 'success';
                                        }
                                    }
                                }else if($order_history->type==2){
                                    $group = CourseGroup::where('id',$order_history->group_id)->first();
                                    if($group){
                                        $course = Course::join('group_log','group_log.course_id','course.id')->select('course.id','course.duration_time','course.trailer_media','course.zoom_join_url')->where('group_id',$group->id)->get();
                                        foreach($course as $val_course){
                                        $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$val_course->id)
                                        ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                                        if(!$check){
                                            $history_log = new UserHistory();
                                            if($val_course->trailer_media==2){
                                                $history_log->zoom_join_url = $val_course->zoom_join_url;
                                                $response['zoom_join_url'] = $history_log->zoom_join_url;
                                              }
                                            $history_log->get_type = 2;
                                            $history_log->order_list_id = $order_history->list_id;
                                            $history_log->user_id = $order_history->user_id;
                                            $history_log->course_id = $val_course->id;
                                            $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                                            $history_log->save();
                                            $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                            if($del_limit){
                                                $del_limit->delete();
                                            }
                                            $response['status'] = 'success';
                                        }
                                        }
                                    }
                                }else if($order_history->type==3){
                                    $subscription = Subscription::where('id',$order_history->subscription_id)->first();
                                    if($subscription){
                                        $last_sub = SubscriptionLog::where('expired','>=',Carbon::now())->where('user_id',$order_history->user_id)->where('subscription_id',$subscription->id)->orderby('expired','desc')->first();
                                        $subscription_log = new SubscriptionLog();
                                        $subscription_log->order_list_id = $order_history->list_id;
                                        $subscription_log->user_id = $order_history->user_id;
                                        $subscription_log->subscription_id = $subscription->id;
                                        if($last_sub){
                                        $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                                        }else{
                                        $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                                        }
                                        $subscription_log->save();
                                    }
                                }
                            }else if($order_history && $order_history->buy_type==2){
                                $gift_log = new UserGiftLog();
                                $gift_log->user_id = $order_history->user_id;
                                $gift_log->order_id = $order_history->id;
                                $gift_log->receive_email = $order_history->receive_email;
                                $gift_log->save();
                    
                                $email_dynamic = EmailDynamic::where('id',10)->where('status',1)->first();
                                if($email_dynamic){
                                    $replace_detail = $email_dynamic->details;
                                    $replace_detail = str_replace("{{sender}}",$order_history->name.' '.$order_history->lastname,$replace_detail);
                                    if(isset($order_history->receive_message)&&$order_history->receive_message!=null&&$order_history->receive_message!=''&&$order_history->receive_message!='null'){
                                        $replace_detail = str_replace("{{message}}",'ข้อความ : '.$order_history->receive_message,$replace_detail);
                                    }else{
                                        $replace_detail = str_replace("{{message}}",'',$replace_detail);
                                    }
                                    $order_gift = UsersOrderList::where('user_order_list.order_id', $order_history->id)->get();
                                    foreach($order_gift as $key=>$value){
                                    $order_gift[$key]['title_th'] = null;
                                    $order_gift[$key]['subtitle_th'] = null;
                                    $order_gift[$key]['image_th'] = null;
                                    $order_gift[$key]['course_link'] = null;
                                    if($value->type==1){
                                        $course_log = Course::where('id',$value->course_id)->first();
                                        if($course_log){
                                            $order_gift[$key]['title_th'] = $course_log->title_th;
                                            $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                                            $order_gift[$key]['image_th'] = $course_log->image_th;
                                            $order_gift[$key]['course_link'] = 'https://csisociety.com/course/'.$course_log->slug;
                                        }
                                    }else if($value->type==2){
                                        $course_log = CourseGroup::where('id',$value->group_id)->first();
                                        if($course_log){
                                            $order_gift[$key]['title_th'] = $course_log->title;
                                            $order_gift[$key]['subtitle_th'] = $course_log->details;
                                            $order_gift[$key]['image_th'] = $course_log->thumb;
                                        }
                                    }else if($value->type==3){
                                        $course_log = Subscription::where('id',$value->subscription_id)->first();
                                        if($course_log){
                                            $order_gift[$key]['title_th'] = $course_log->title;
                                            $order_gift[$key]['subtitle_th'] = $course_log->details;
                                            $order_gift[$key]['image_th'] = $course_log->thumb;
                                        }
                                    }
                                    }
                                    $order_replace = '';
                                    foreach($order_gift as $value){
                                        if($value->type==1){
                                            $order_replace.=
                                            '<tr>
                                                <td style="width:30px;"></td>
                                                <td style="width: 140px">
                                                    <a href="'.$value->course_link.'">
                                                      <img src="'.$value->image_th.'" style="width: 140px;">
                                                    </a>
                                                </td>
                                                <td style="padding: 10px;">
                                                  <a href="'.$value->course_link.'">
                                                    <b>'.$value->title_th.'</b><br>'.$value->subtitle_th.'
                                                  </a>
                                                </td>
                                            </tr>';
                                        }else{
                                            $order_replace.=
                                            '<tr>
                                                <td style="width:30px;"></td>
                                                <td style="width: 140px">
                                                    <img src="'.$value->image_th.'" style="width: 140px;">
                                                </td>
                                                <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
                                                '.$value->subtitle_th.'
                                                </td>
                                            </tr>';
                                        }
                                    }
                                    $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
                                    $replace_detail = str_replace("{{link}}","https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id),$replace_detail);
                                    $obj = new Dummy();
                                    $obj['subject'] = $email_dynamic->subject;
                                    $obj['code'] = $replace_detail;
                                    $obj['email'] = $order_history->receive_email;
                                    $obj['cc'] = [];
                                    $obj['bcc'] = [];
                                    AppHelper::instance()->mailTemplate($obj);
                                }
                                $receive_user = User::where('email',$order_history->receive_email)->first();
                                if($receive_user){
                                    $noti_log = new NotiAutoLog();
                                    $noti_log->user_id = $receive_user->id;
                                    $noti_log->image = '/assets/images/qr.png';
                                    $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
                                    $noti_log->description = 'จากคุณ'.$order_history->name.' '.$order_history->lastname;
                                    $noti_log->link = "https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id);
                                    $noti_log->save();
                                }
                            }
                        }
                        $response['status'] = 'success';
                    }
                    $response['resp'] = $resp;
                }else{
                    $response['status'] = 'notfound : order';
                }
                    
            }
        }
        return redirect('https://csisociety.com');
        // return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }

    public  function  cronjob(Request $request){
        $response = new Dummy();
        $response['status'] = '';
        $arr = array();

        $orders = UsersOrderLog::select('id','reference')->where('updated_at','>=',Carbon::now()->subdays(1))->where('payment_type',2)->where('status',4)->whereNotNull('reference')->get();
        foreach ($orders as $key => $order) {
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => env('PAYMENT_SDK').'/card/v2/charge/'.$order->reference,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'x-api-key: '.env('PAYMENT_SKEY')
            ),
            ));


            $resp = curl_exec($curl);
            $resp = json_decode($resp,TRUE);
    
            curl_close($curl);
            // echo $response;
            array_push($arr,$resp);
            if($resp && isset($resp['transaction_state']) && $resp['transaction_state']=='Authorized'){
                $order_model = UsersOrderLog::find($order->id);
                if($order_model){
                    $order_model->created_receipt_date = Carbon::now('Asia/Bangkok');
                    $order_model->status = 2;
                    $order_model->receipt_path = env('APP_URL').'/receipt/'.SimpleEnDeHelper::instance()->encryptString($order_model->id);
                    $order_model->save();
                    $order_history_arr = UsersOrderLog::join('user_order_list','user_order_list.order_id','user_order_log.id')
                    ->select('user_order_log.id','user_order_log.receive_email','user_order_log.receive_message','user_order_log.user_id','user_order_log.buy_type','user_order_log.created_receipt_date'
                    ,'user_order_list.type','user_order_list.course_id','user_order_list.group_id','user_order_list.subscription_id','user_order_list.id as list_id')
                    ->where('user_order_log.status',2)->where('user_order_log.id',$order_model->id)->get();
                    foreach($order_history_arr as $order_history){
                        if($order_history && $order_history->buy_type==1){
                            if($order_history->type==1){
                                $course = Course::where('id',$order_history->course_id)->first();
                                if($course){
                                    $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$order_history->course_id)
                                    ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                                    if(!$check){
                                    $history_log = new UserHistory();
                                    if($course->trailer_media==2){
                                        $history_log->zoom_join_url = $course->zoom_join_url;
                                        $response['zoom_join_url'] = $history_log->zoom_join_url;
                                      }
                                    $history_log->get_type = 2;
                                    $history_log->order_list_id = $order_history->list_id;
                                    $history_log->user_id = $order_history->user_id;
                                    $history_log->course_id = $order_history->course_id;
                                    $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                                    $history_log->save();
                                    $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                    if($del_limit){
                                        $del_limit->delete();
                                    }
                                    $response['status'] = 'success';
                                    }
                                }
                            }else if($order_history->type==2){
                                $group = CourseGroup::where('id',$order_history->group_id)->first();
                                if($group){
                                    $course = Course::join('group_log','group_log.course_id','course.id')->select('course.id','course.duration_time','course.trailer_media','course.zoom_join_url')->where('group_id',$group->id)->get();
                                    foreach($course as $val_course){
                                    $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$val_course->id)
                                    ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                                    if(!$check){
                                        $history_log = new UserHistory();
                                        if($val_course->trailer_media==2){
                                            $history_log->zoom_join_url = $val_course->zoom_join_url;
                                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                                          }
                                        $history_log->get_type = 2;
                                        $history_log->order_list_id = $order_history->list_id;
                                        $history_log->user_id = $order_history->user_id;
                                        $history_log->course_id = $val_course->id;
                                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                                        $history_log->save();
                                        $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                        if($del_limit){
                                            $del_limit->delete();
                                        }
                                        $response['status'] = 'success';
                                    }
                                    }
                                }
                            }else if($order_history->type==3){
                                $subscription = Subscription::where('id',$order_history->subscription_id)->first();
                                if($subscription){
                                    $last_sub = SubscriptionLog::where('expired','>=',Carbon::now())->where('user_id',$order_history->user_id)->where('subscription_id',$subscription->id)->orderby('expired','desc')->first();
                                    $subscription_log = new SubscriptionLog();
                                    $subscription_log->order_list_id = $order_history->list_id;
                                    $subscription_log->user_id = $order_history->user_id;
                                    $subscription_log->subscription_id = $subscription->id;
                                    if($last_sub){
                                    $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                                    }else{
                                    $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                                    }
                                    $subscription_log->save();
                                }
                            }
                        }else if($order_history && $order_history->buy_type==2){
                            $gift_log = new UserGiftLog();
                            $gift_log->user_id = $order_history->user_id;
                            $gift_log->order_id = $order_history->id;
                            $gift_log->receive_email = $order_history->receive_email;
                            $gift_log->save();
                
                            $email_dynamic = EmailDynamic::where('id',10)->where('status',1)->first();
                            if($email_dynamic){
                                $replace_detail = $email_dynamic->details;
                                $replace_detail = str_replace("{{sender}}",$order_history->name.' '.$order_history->lastname,$replace_detail);
                                if(isset($order_history->receive_message)&&$order_history->receive_message!=null&&$order_history->receive_message!=''&&$order_history->receive_message!='null'){
                                    $replace_detail = str_replace("{{message}}",'ข้อความ : '.$order_history->receive_message,$replace_detail);
                                }else{
                                    $replace_detail = str_replace("{{message}}",'',$replace_detail);
                                }
                                $order_gift = UsersOrderList::where('user_order_list.order_id', $order_history->id)->get();
                                foreach($order_gift as $key=>$value){
                                $order_gift[$key]['title_th'] = null;
                                $order_gift[$key]['subtitle_th'] = null;
                                $order_gift[$key]['image_th'] = null;
                                $order_gift[$key]['course_link'] = null;
                                if($value->type==1){
                                    $course_log = Course::where('id',$value->course_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title_th;
                                        $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                                        $order_gift[$key]['image_th'] = $course_log->image_th;
                                        $order_gift[$key]['course_link'] = 'https://csisociety.com/course/'.$course_log->slug;
                                    }
                                }else if($value->type==2){
                                    $course_log = CourseGroup::where('id',$value->group_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title;
                                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                                        $order_gift[$key]['image_th'] = $course_log->thumb;
                                    }
                                }else if($value->type==3){
                                    $course_log = Subscription::where('id',$value->subscription_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title;
                                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                                        $order_gift[$key]['image_th'] = $course_log->thumb;
                                    }
                                }
                                }
                                $order_replace = '';
                                foreach($order_gift as $value){
                                    if($value->type==1){
                                        $order_replace.=
                                        '<tr>
                                            <td style="width:30px;"></td>
                                            <td style="width: 140px">
                                                <a href="'.$value->course_link.'">
                                                  <img src="'.$value->image_th.'" style="width: 140px;">
                                                </a>
                                            </td>
                                            <td style="padding: 10px;">
                                              <a href="'.$value->course_link.'">
                                                <b>'.$value->title_th.'</b><br>'.$value->subtitle_th.'
                                              </a>
                                            </td>
                                        </tr>';
                                    }else{
                                        $order_replace.=
                                        '<tr>
                                            <td style="width:30px;"></td>
                                            <td style="width: 140px">
                                                <img src="'.$value->image_th.'" style="width: 140px;">
                                            </td>
                                            <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
                                            '.$value->subtitle_th.'
                                            </td>
                                        </tr>';
                                    }
                                }
                                $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
                                $replace_detail = str_replace("{{link}}","https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id),$replace_detail);
                                $obj = new Dummy();
                                $obj['subject'] = $email_dynamic->subject;
                                $obj['code'] = $replace_detail;
                                $obj['email'] = $order_history->receive_email;
                                $obj['cc'] = [];
                                $obj['bcc'] = [];
                                AppHelper::instance()->mailTemplate($obj);
                            }
                            $receive_user = User::where('email',$order_history->receive_email)->first();
                            if($receive_user){
                                $noti_log = new NotiAutoLog();
                                $noti_log->user_id = $receive_user->id;
                                $noti_log->image = '/assets/images/qr.png';
                                $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
                                $noti_log->description = 'จากคุณ'.$order_history->name.' '.$order_history->lastname;
                                $noti_log->link = "https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id);
                                $noti_log->save();
                            }
                        }
                    }
                }
            }else{
                // $order_model = UsersOrderLog::find($order->id);
                // $order_model->status = 3;
                // $order_model->save();
            }
        }

        $orders = UsersOrderLog::select('id','reference')->where('updated_at','>=',Carbon::now()->subdays(1))->where('payment_type',1)->where('status',4)->whereNotNull('reference')->get();
        foreach ($orders as $key => $order) {
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => env('PAYMENT_SDK').'/qr/v2/qr/'.$order->reference,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'x-api-key: '.env('PAYMENT_SKEY')
            ),
            ));


            $resp = curl_exec($curl);
            $resp = json_decode($resp,TRUE);
    
            curl_close($curl);
            // echo $response;
            array_push($arr,$resp);
            if($resp && isset($resp['transaction_state']) && $resp['transaction_state']=='Authorized'){
                $order_model = UsersOrderLog::find($order->id);
                if($order_model){
                    $order_model->status = 2;
                    $order_model->created_receipt_date = Carbon::now('Asia/Bangkok');
                    $order_model->receipt_path = env('APP_URL').'/receipt/'.SimpleEnDeHelper::instance()->encryptString($order_model->id);
                    $order_model->save();
                    $order_history_arr = UsersOrderLog::join('user_order_list','user_order_list.order_id','user_order_log.id')
                    ->select('user_order_log.id','user_order_log.receive_email','user_order_log.receive_message','user_order_log.user_id','user_order_log.buy_type','user_order_log.created_receipt_date'
                    ,'user_order_list.type','user_order_list.course_id','user_order_list.group_id','user_order_list.subscription_id','user_order_list.id as list_id')
                    ->where('user_order_log.status',2)->where('user_order_log.id',$order_model->id)->get();
                    foreach($order_history_arr as $order_history){
                        if($order_history && $order_history->buy_type==1){
                            if($order_history->type==1){
                                $course = Course::where('id',$order_history->course_id)->first();
                                if($course){
                                    $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$order_history->course_id)
                                    ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time))->first();
                                    if(!$check){
                                    $history_log = new UserHistory();
                                    if($course->trailer_media==2){
                                        $history_log->zoom_join_url = $course->zoom_join_url;
                                        $response['zoom_join_url'] = $history_log->zoom_join_url;
                                      }
                                    $history_log->get_type = 2;
                                    $history_log->order_list_id = $order_history->list_id;
                                    $history_log->user_id = $order_history->user_id;
                                    $history_log->course_id = $order_history->course_id;
                                    $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($course->duration_time);
                                    $history_log->save();
                                    $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                    if($del_limit){
                                        $del_limit->delete();
                                    }
                                    $response['status'] = 'success';
                                    }
                                }
                            }else if($order_history->type==2){
                                $group = CourseGroup::where('id',$order_history->group_id)->first();
                                if($group){
                                    $course = Course::join('group_log','group_log.course_id','course.id')->select('course.id','course.duration_time','course.trailer_media','course.zoom_join_url')->where('group_id',$group->id)->get();
                                    foreach($course as $val_course){
                                    $check = UserHistory::where('order_list_id',$order_history->list_id)->where('user_id',$order_history->user_id)->where('course_id',$val_course->id)
                                    ->where('expired',Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time))->first();
                                    if(!$check){
                                        $history_log = new UserHistory();
                                        if($val_course->trailer_media==2){
                                            $history_log->zoom_join_url = $val_course->zoom_join_url;
                                            $response['zoom_join_url'] = $history_log->zoom_join_url;
                                          }
                                        $history_log->get_type = 2;
                                        $history_log->order_list_id = $order_history->list_id;
                                        $history_log->user_id = $order_history->user_id;
                                        $history_log->course_id = $val_course->id;
                                        $history_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($val_course->duration_time);
                                        $history_log->save();
                                        $del_limit = UserLearnedTime::where('user_id',$history_log->user_id)->where('course_id',$history_log->course_id)->first();
                                        if($del_limit){
                                            $del_limit->delete();
                                        }
                                        $response['status'] = 'success';
                                    }
                                    }
                                }
                            }else if($order_history->type==3){
                                $subscription = Subscription::where('id',$order_history->subscription_id)->first();
                                if($subscription){
                                    $last_sub = SubscriptionLog::where('expired','>=',Carbon::now())->where('user_id',$order_history->user_id)->where('subscription_id',$subscription->id)->orderby('expired','desc')->first();
                                    $subscription_log = new SubscriptionLog();
                                    $subscription_log->order_list_id = $order_history->list_id;
                                    $subscription_log->user_id = $order_history->user_id;
                                    $subscription_log->subscription_id = $subscription->id;
                                    if($last_sub){
                                    $subscription_log->expired = Carbon::parse($last_sub->expired)->addDays($subscription->period);
                                    }else{
                                    $subscription_log->expired = Carbon::parse($order_history->created_receipt_date)->addDays($subscription->period);
                                    }
                                    $subscription_log->save();
                                }
                            }
                        }else if($order_history && $order_history->buy_type==2){
                            $gift_log = new UserGiftLog();
                            $gift_log->user_id = $order_history->user_id;
                            $gift_log->order_id = $order_history->id;
                            $gift_log->receive_email = $order_history->receive_email;
                            $gift_log->save();
                
                            $email_dynamic = EmailDynamic::where('id',10)->where('status',1)->first();
                            if($email_dynamic){
                                $replace_detail = $email_dynamic->details;
                                $replace_detail = str_replace("{{sender}}",$order_history->name.' '.$order_history->lastname,$replace_detail);
                                if(isset($order_history->receive_message)&&$order_history->receive_message!=null&&$order_history->receive_message!=''&&$order_history->receive_message!='null'){
                                    $replace_detail = str_replace("{{message}}",'ข้อความ : '.$order_history->receive_message,$replace_detail);
                                }else{
                                    $replace_detail = str_replace("{{message}}",'',$replace_detail);
                                }
                                $order_gift = UsersOrderList::where('user_order_list.order_id', $order_history->id)->get();
                                foreach($order_gift as $key=>$value){
                                $order_gift[$key]['title_th'] = null;
                                $order_gift[$key]['subtitle_th'] = null;
                                $order_gift[$key]['image_th'] = null;
                                $order_gift[$key]['course_link'] = null;
                                if($value->type==1){
                                    $course_log = Course::where('id',$value->course_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title_th;
                                        $order_gift[$key]['subtitle_th'] = $course_log->subtitle_th;
                                        $order_gift[$key]['image_th'] = $course_log->image_th;
                                        $order_gift[$key]['course_link'] = 'https://csisociety.com/course/'.$course_log->slug;
                                    }
                                }else if($value->type==2){
                                    $course_log = CourseGroup::where('id',$value->group_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title;
                                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                                        $order_gift[$key]['image_th'] = $course_log->thumb;
                                    }
                                }else if($value->type==3){
                                    $course_log = Subscription::where('id',$value->subscription_id)->first();
                                    if($course_log){
                                        $order_gift[$key]['title_th'] = $course_log->title;
                                        $order_gift[$key]['subtitle_th'] = $course_log->details;
                                        $order_gift[$key]['image_th'] = $course_log->thumb;
                                    }
                                }
                                }
                                $order_replace = '';
                                foreach($order_gift as $value){
                                    if($value->type==1){
                                        $order_replace.=
                                        '<tr>
                                            <td style="width:30px;"></td>
                                            <td style="width: 140px">
                                                <a href="'.$value->course_link.'">
                                                  <img src="'.$value->image_th.'" style="width: 140px;">
                                                </a>
                                            </td>
                                            <td style="padding: 10px;">
                                              <a href="'.$value->course_link.'">
                                                <b>'.$value->title_th.'</b><br>'.$value->subtitle_th.'
                                              </a>
                                            </td>
                                        </tr>';
                                    }else{
                                        $order_replace.=
                                        '<tr>
                                            <td style="width:30px;"></td>
                                            <td style="width: 140px">
                                                <img src="'.$value->image_th.'" style="width: 140px;">
                                            </td>
                                            <td style="padding: 10px;"><b>'.$value->title_th.'</b><br>
                                            '.$value->subtitle_th.'
                                            </td>
                                        </tr>';
                                    }
                                }
                                $replace_detail = str_replace("{{order_list}}",$order_replace,$replace_detail);
                                $replace_detail = str_replace("{{link}}","https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id),$replace_detail);
                                $obj = new Dummy();
                                $obj['subject'] = $email_dynamic->subject;
                                $obj['code'] = $replace_detail;
                                $obj['email'] = $order_history->receive_email;
                                $obj['cc'] = [];
                                $obj['bcc'] = [];
                                AppHelper::instance()->mailTemplate($obj);
                            }
                            $receive_user = User::where('email',$order_history->receive_email)->first();
                            if($receive_user){
                                $noti_log = new NotiAutoLog();
                                $noti_log->user_id = $receive_user->id;
                                $noti_log->image = '/assets/images/qr.png';
                                $noti_log->title = 'ยินดีด้วย! คุณได้รับของขวัญ';
                                $noti_log->description = 'จากคุณ'.$order_history->name.' '.$order_history->lastname;
                                $noti_log->link = "https://csisociety.com/gift/".SimpleEnDeHelper::instance()->encryptString($gift_log->id);
                                $noti_log->save();
                            }
                        }
                    }
                }
            }else{
                // $order_model = UsersOrderLog::find($order->id);
                // $order_model->status = 3;
                // $order_model->save();
            }
        }

        $response['arr'] = $arr;

        $orders_cancel = UsersOrderLog::select('id')->where('updated_at','<',Carbon::now()->subdays(1))->where('payment_type',2)->where('status',4)->whereNotNull('reference')->get();
        foreach($orders_cancel as $val_cancel){
            $cancel_model = UsersOrderLog::find($val_cancel->id);
            $cancel_model->status = 3;
            $cancel_model->save();
        }

        $orders_cancel = UsersOrderLog::select('id')->where('updated_at','<',Carbon::now()->subdays(1))->where('payment_type',1)->where('status',4)->whereNotNull('reference')->get();
        foreach($orders_cancel as $val_cancel){
            $cancel_model = UsersOrderLog::find($val_cancel->id);
            $cancel_model->status = 3;
            $cancel_model->save();
        }
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    

    public function notify(Request $request){
        $response = new Dummy();
        $response['status'] = '';
        $response['status'] = 'success';
        // return redirect('https://www.medumore.org');
        return response()->json($response, 200, ['Content-Type' => 'application/json;charset=UTF-8', 'Charset' => 'utf-8'],JSON_UNESCAPED_UNICODE);
    }
    

}
