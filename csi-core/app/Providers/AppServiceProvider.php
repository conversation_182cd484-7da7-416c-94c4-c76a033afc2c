<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(
            \App\Repositories\Interfaces\CategoriesSpeakerRepositoryInterface::class,
            \App\Repositories\Eloquent\CategoriesSpeakerRepository::class
        );
        
        $this->app->bind(
            \App\Repositories\Interfaces\CourseLiveRepositoryInterface::class,
            \App\Repositories\Eloquent\CourseLiveRepository::class
        );
        
        $this->app->bind(
            \App\Repositories\Interfaces\CourseEventRepositoryInterface::class,
            \App\Repositories\Eloquent\CourseEventRepository::class
        );
        
        $this->app->bind(
            \App\Repositories\Interfaces\CompanyVisitRepositoryInterface::class,
            \App\Repositories\Eloquent\CompanyVisitRepository::class
        );

        $this->app->bind(
            \App\Repositories\HomeContentRepository::class,
            \App\Repositories\HomeContentRepository::class
        );

         $this->app->bind(
            \App\Services\HomeContent\CourseContentService::class,
            \App\Services\HomeContent\CourseContentService::class
        );
        
        $this->app->bind(
            \App\Services\HomeContent\DataContentService::class,
            \App\Services\HomeContent\DataContentService::class
        );
        
        $this->app->bind(
            \App\Services\HomeContent\HomeContentService::class,
            \App\Services\HomeContent\HomeContentService::class
        );
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        Schema::defaultStringLength(191); 
        if(env('APP_URL') === 'https://forwardengcore.forwardenglish-online.com') {
            \URL::forceScheme('https');
        }
    }
}
