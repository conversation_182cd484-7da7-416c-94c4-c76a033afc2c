<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\Interfaces\SubscriptionMainRepositoryInterface;
use App\Repositories\Eloquent\SubscriptionMainRepository;
use App\Repositories\Interfaces\SubscriptionRepositoryInterface;
use App\Repositories\Eloquent\SubscriptionRepository;

use App\Repositories\Interfaces\CourseRepositoryInterface;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Repositories\Interfaces\ContentSubscriptionRepositoryInterface;
use App\Repositories\Interfaces\CourseGroupRepositoryInterface;
use App\Repositories\Eloquent\CourseRepository;
use App\Repositories\Eloquent\UserRepository;
use App\Repositories\Eloquent\ContentSubscriptionRepository;
use App\Repositories\Eloquent\CourseGroupRepository;
use App\Repositories\Interfaces\SubscriptionLogRepositoryInterface;
use App\Repositories\Eloquent\SubscriptionLogRepository;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Repository bindings
        $this->app->bind(
            SubscriptionMainRepositoryInterface::class,
            SubscriptionMainRepository::class
        );

        $this->app->bind(
            SubscriptionRepositoryInterface::class,
            SubscriptionRepository::class
        );

        // New Repository bindings for CourseContentService
        $this->app->bind(
            CourseRepositoryInterface::class,
            CourseRepository::class
        );

        $this->app->bind(
            UserRepositoryInterface::class,
            UserRepository::class
        );

        $this->app->bind(
            ContentSubscriptionRepositoryInterface::class,
            ContentSubscriptionRepository::class
        );

        $this->app->bind(
            CourseGroupRepositoryInterface::class,
            CourseGroupRepository::class
        );

        $this->app->bind(
            SubscriptionLogRepositoryInterface::class,
            SubscriptionLogRepository::class
        );
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}