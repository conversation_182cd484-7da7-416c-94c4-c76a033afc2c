<?php

namespace App\Services;

use App\Models\Core\UserBillingHistory;
use App\Models\Core\Users;
use App\Models\Core\SubDistrict;
use App\Models\Core\District;
use App\Models\Core\Province;
use Exception;

class BillingHistoryService
{
    /**
     * Save billing history from order request
     *
     * @param int $userId
     * @param object $request
     * @return bool
     */
    public function saveBillingHistory($userId, $request)
    {
        try {
            $billingData = [
                'user_id' => $userId,
                'name' => $request->name,
                'lastname' => $request->lastname,
                'email' => $request->email,
                'tel' => $request->tel,
                'address' => $request->address,
                'address_type' => $request->address_type,
                'iden_no' => $request->iden_no,
                'postcode' => $request->postcode,
            ];

            // เพิ่มข้อมูลที่อยู่
            $billingData = $this->addAddressDetails($billingData, $request);

            // เพิ่มข้อมูลบริษัท (สำหรับนิติบุคคล)
            if ($request->address_type == 2) {
                $billingData['company_type'] = $request->company_type ?? null;
                $billingData['company_branch'] = $request->company_branch ?? null;
            }

            UserBillingHistory::create($billingData);
            
            return true;
        } catch (Exception $e) {
            // Log error but don't fail the order
            error_log('Failed to save billing history: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get latest billing info for user
     *
     * @param int $userId
     * @return array|null
     */
    public function getLastBillingInfo($userId)
    {
        $lastBilling = UserBillingHistory::getLatestByUserId($userId);
        
        if ($lastBilling) {
            return [
                'name' => $lastBilling->name,
                'lastname' => $lastBilling->lastname,
                'email' => $lastBilling->email,
                'tel' => $lastBilling->tel,
                'address' => $lastBilling->address,
                'subdistrict' => $lastBilling->subdistrict,
                'district' => $lastBilling->district,
                'province' => $lastBilling->province,
                'postcode' => $lastBilling->postcode,
                'address_type' => $lastBilling->address_type,
                'iden_no' => $lastBilling->iden_no,
                'company_type' => $lastBilling->company_type,
                'company_branch' => $lastBilling->company_branch,
            ];
        }

        return null;
    }

    /**
     * Get fallback billing info from user profile
     *
     * @param object $user
     * @return array
     */
    public function getFallbackBillingInfo($user)
    {
        return [
            'name' => $user->name,
            'lastname' => $user->lastname,
            'email' => $user->email,
            'tel' => $user->mobile ?? $user->tel,
            'address' => $user->address,
            'subdistrict' => $user->subdistrict_name,
            'district' => $user->district_name,
            'province' => $user->province_name,
            'postcode' => $user->postcode,
            'address_type' => 1, // default บุคคลธรรมดา
            'iden_no' => null,
            'company_type' => null,
            'company_branch' => null,
        ];
    }

    /**
     * Add address details to billing data
     *
     * @param array $billingData
     * @param object $request
     * @return array
     */
    private function addAddressDetails($billingData, $request)
    {
        // เพิ่มข้อมูลตำบล
        if (isset($request->subdistrict)) {
            $subdistrict = SubDistrict::where('id', $request->subdistrict)->first();
            if ($subdistrict) {
                $billingData['subdistrict'] = $subdistrict->name;
            }
        }

        // เพิ่มข้อมูลอำเภอ
        if (isset($request->district)) {
            $district = District::where('id', $request->district)->first();
            if ($district) {
                $billingData['district'] = $district->name;
            }
        }

        // เพิ่มข้อมูลจังหวัด
        if (isset($request->province)) {
            $province = Province::where('id', $request->province)->first();
            if ($province) {
                $billingData['province'] = $province->name;
            }
        }

        return $billingData;
    }

    /**
     * Get billing history list for user
     *
     * @param int $userId
     * @param int $limit
     * @return array
     */
    public function getBillingHistoryList($userId, $limit = 10)
    {
        $histories = UserBillingHistory::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return $histories->map(function ($history) {
            return [
                'id' => $history->id,
                'name' => $history->name,
                'lastname' => $history->lastname,
                'address_type' => $history->address_type,
                'address_type_text' => $history->address_type == 1 ? 'บุคคลธรรมดา' : 'นิติบุคคล',
                'created_at' => $history->created_at->format('d/m/Y H:i'),
                'created_at_thai' => $this->formatDateThai($history->created_at),
            ];
        })->toArray();
    }

    /**
     * Format date to Thai format
     *
     * @param \Carbon\Carbon $date
     * @return string
     */
    private function formatDateThai($date)
    {
        $thaiMonths = [
            1 => 'มกราคม', 2 => 'กุมภาพันธ์', 3 => 'มีนาคม', 4 => 'เมษายน',
            5 => 'พฤษภาคม', 6 => 'มิถุนายน', 7 => 'กรกฎาคม', 8 => 'สิงหาคม',
            9 => 'กันยายน', 10 => 'ตุลาคม', 11 => 'พฤศจิกายน', 12 => 'ธันวาคม'
        ];

        $day = $date->day;
        $month = $thaiMonths[$date->month];
        $year = $date->year + 543;
        $time = $date->format('H:i');

        return "{$day} {$month} {$year} เวลา {$time} น.";
    }
}
