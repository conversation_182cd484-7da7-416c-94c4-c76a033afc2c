<?php

namespace App\Services\HomeContent;

use App\Repositories\HomeContentRepository;
use App\Services\HomeContent\CourseContentService;
use App\Services\HomeContent\DataContentService;
use Illuminate\Support\Facades\Cache;

class HomeContentService
{
    protected $repository;
    protected $courseService;
    protected $dataService;

    public function __construct(
        HomeContentRepository $repository,
        CourseContentService $courseService,
        DataContentService $dataService
    ) {
        $this->repository = $repository;
        $this->courseService = $courseService;
        $this->dataService = $dataService;
    }

    public function getSuggestionList($utoken)
    {
            $homeContents = $this->repository->getActiveContents();
            $suggestions = [];

            foreach ($homeContents as $content) {
                $data = $this->buildContentData($content, $utoken);
                
                if (!empty($data)) {
                    $suggestions[] = [
                        'id' => $content->id,
                        'type' => $content->type,
                        'name' => $content->name,
                        'color' => $content->color,
                        'size' => $content->size,
                        'background' => $content->background,
                        'title' => $content->title,
                        'subtitle' => $content->subtitle,
                        'cta' => $content->cta,
                        'link' => $content->link,
                        'playlist' => $content->playlist,
                        'popular_all' => $content->popular_all,
                        'learner' => $content->learner,
                        'level' => $content->level,
                        'category' => $content->category,
                        'gender' => $content->gender,
                        'department' => $content->department,
                        'speaker' => $content->speaker,
                        'tag' => $content->tag,
                        'group_id' => $content->group_id,
                        'start_date' => $content->start_date,
                        'end_date' => $content->end_date,
                        'sub_day' => $content->sub_day,
                        'show_video_details' => $content->show_video_details,
                        'position' => $content->position,
                        'status' => $content->status,
                        'created_at' => $content->created_at,
                        'updated_at' => $content->updated_at,
                        'section' => $content->section,
                        'title_th' => $content->title_th,
                        'title_en' => $content->title_en,
                        'data' => $data
                    ];
                }
            }

            return $suggestions;
        // });
    }

    public function buildContentData($content, $utoken)
    {
        $value = $content->value ? json_decode($content->value) : null;
        
        if ($content->type == 31 && $content->subscription_main_id) {
            if (!$value) {
                $value = new \stdClass();
            }
            $value->subscription_main_id = $content->subscription_main_id;
        }
        
        switch ($content->type) {
            case 1: // หลักสูตรตามเงื่อนไข
                return $this->courseService->getSelectedCourses($value, $utoken);
                
            case 2: // หลักสูตรฟรี
                return $this->courseService->getFreeCourses($utoken);
                
            case 3: // หลักสูตรแนะนำ
                return $this->courseService->getSuggestedCourses($utoken);
                
            case 4: // หลักสูตรใหม่
                return $this->courseService->getLatestCourses($utoken);
                
            case 5: // หลักสูตรที่กำลังจะเปิด
                return $this->courseService->getUpcomingCourses($utoken);
                
            case 6: // บทความตามเงื่อนไข
                return $this->dataService->getSelectedArticles($value);
                
            case 7: // บทความใหม่
                return $this->dataService->getLatestArticles();
                
            case 8: // หลักสูตรแนะนำตาม user_type
                return $this->courseService->getUserRecommendedCourses($utoken);
                
            case 9: // หลักสูตรที่ผู้ใช้ชื่นชอบ
                return $this->courseService->getUserFavoriteCourses($utoken);
                
            case 10: // หลักสูตรยอดนิยม
                return $this->courseService->getPopularCourses($content, $utoken);
                
            case 11: // หลักสูตรแนะนำตาม Level ที่ซื้อ
                return $this->courseService->getUserLevelBasedCourses($utoken);
                
            case 12: // กลุ่มเป้าหมายตามเงื่อนไข
                return $this->dataService->getSelectedLearners($value);
                
            case 13: // หลักสูตรตาม Category ที่สนใจ
                return $this->courseService->getUserInterestCourses($utoken);
                
            case 14: // หลักสูตรตามเงื่อนไข (ซ้ำกับ type 1)
                return $this->courseService->getSelectedCourses($value, $utoken);
                
            case 15: // หลักสูตรแนะนำ (ซ้ำกับ type 3)
                return $this->courseService->getSuggestedCourses($utoken);
                
            case 16: // หลักสูตรใหม่ (ซ้ำกับ type 4)
                return $this->courseService->getNewCourses($utoken);
                
            case 17: // Course Group
                return $this->courseService->getCourseGroup($content->group_id ?? null, $utoken);
                
            case 18: // หลักสูตรที่กำลังจะเปิด (ซ้ำกับ type 5)
                return $this->courseService->getUpcomingCourses($utoken);
                
            case 19: // หลักสูตรตามเงื่อนไข (ซ้ำกับ type 1)
                return $this->courseService->getSelectedCourses($value, $utoken);
                
            case 20: // วิทยากรตามเงื่อนไข (ซ้ำกับ type 9)
                return $this->dataService->getSelectedSpeakers($value);
                
            case 21: // หมวดหมู่ตามเงื่อนไข (ซ้ำกับ type 13)
                return $this->dataService->getSelectedCategories($value);
                
            case 22: // กลุ่มเป้าหมายตามเงื่อนไข (ซ้ำกับ type 12)
                return $this->dataService->getSelectedLearners($value);
                
            case 23: // Point Ranking
                return $this->dataService->getPointRanking();
                
            case 24: // Knowledge Management Center
                return $this->dataService->getKnowledgeCenter($content->id);
                
            case 25: // หลักสูตรปฏิทิน
                return $this->dataService->getSelectedCurriculums($value);
                
            case 26: // หลักสูตรใหม่ (ซ้ำกับ type 4)
                return $this->courseService->getLatestCourses($utoken);
                
            case 27: // หลักสูตรที่กำลังจะเปิด (ซ้ำกับ type 5)
                return $this->courseService->getUpcomingCourses($utoken);
                
            case 28: // หลักสูตรฟรี (ซ้ำกับ type 2)
                return $this->courseService->getFreeCourses($utoken);
                
            case 29: // Home Gallery
                return $this->dataService->getHomeGallery($content->id);
                
            case 30: // Home Video
                return $this->dataService->getHomeVideo($content->id);
                
            case 31: // หลักสูตร Subscription
                return $this->courseService->getSubscriptionCourses($value, $utoken);
                
            case 32: // หลักสูตร Curriculum
                return $this->courseService->getCurriculumCourses($value, $utoken);
                
            case 33: // หลักสูตรโปรโมชั่น
                return $this->courseService->getPromotionCourses($value, $utoken);
                
            case 34: // วิทยากรพิเศษ
                return $this->dataService->getSelectedSpeakers($value);
                
            case 35: // คอร์ส Live (trailer_media = 2)
                return $this->courseService->getLiveCourses($value, $utoken);
                
            case 36: // คอร์ส Event (trailer_media = 5)
                return $this->courseService->getEventCourses($value, $utoken);
                
            case 37: // Company Visit (trailer_media = 4)
                return $this->courseService->getCompanyVisitCourses($value, $utoken);
                
            default:
                return [];
        }
    }
}
