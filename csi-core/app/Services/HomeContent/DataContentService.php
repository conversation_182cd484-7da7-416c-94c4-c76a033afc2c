<?php

namespace App\Services\HomeContent;

use App\Models\Core\Article;
use App\Models\Core\ArticlePin;
use App\Models\Core\Department;
use App\Models\Core\CateSpeaker;
use App\Models\Core\Learner;
use App\Models\Core\Categories;
use App\Models\Core\Curriculum;
use App\Models\Core\HomeGallery;
use App\Models\Core\HomeVideo;
use App\Models\Core\HomeGMCenter;
use App\Models\Core\TopSpeakerLog;
use App\Helpers\AppHelper;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DataContentService
{
    public function getSelectedArticles($value)
    {
        $news_pin = Article::join('news_pin', 'news_pin.news_id', 'article.id')
            ->select('article.id', 'article.slug', 'article.image', 'article.title_th', 'article.subtitle_th', 'article.created_at as date')
            ->where('article.status', 1)
            ->where('news_pin.status', 1)
            ->orderBy('news_pin.position', 'asc')
            ->get();

        $news_pin_id = $news_pin->pluck('id')->toArray();

        $news_other = Article::select('article.id', 'article.slug', 'article.image', 'article.title_th', 'article.subtitle_th', 'article.created_at as date')
            ->where('article.status', 1)
            ->whereNotIn('article.id', $news_pin_id)
            ->orderBy('article.created_at', 'desc')
            ->limit(8)
            ->get();

        $news_list = array_merge(
            json_decode(json_encode($news_pin), true), 
            json_decode(json_encode($news_other), true)
        );

        foreach ($news_list as $key => $value_news) {
            $news_list[$key]['date'] = \App\Helpers\AppHelper::instance()->DateThaiOnly($value_news['date']);
        }

        return $news_list;
    }

    public function getSelectedSpeakers($value)
    {
        // Type 34: แสดงรายการอาจารย์ - ส่งข้อมูลครบทุก field
        return CateSpeaker::select([
            'categories_speaker.id', 
            'categories_speaker.name', 
            'categories_speaker.name_en', 
            'categories_speaker.title_th', 
            'categories_speaker.title_en', 
            'categories_speaker.certified', 
            'categories_speaker.biography', 
            'categories_speaker.image as avatar', 
            'categories_speaker.image', 
            'categories_speaker.position', 
            'categories_speaker.status', 
            'categories_speaker.created_at', 
            'categories_speaker.updated_at'
        ])
        ->where('categories_speaker.status', 1)
        ->orderBy('categories_speaker.position', 'asc')
        ->limit(12)
        ->get();
    }

    public function getPopularSpeakers()
    {
        // Type 10 variation: Popular speakers
        return CateSpeaker::join('top_speaker_log', 'top_speaker_log.speaker_id', 'categories_speaker.id')
            ->select([
                'categories_speaker.id', 
                'categories_speaker.name', 
                'categories_speaker.title_th', 
                'categories_speaker.image as avatar', 
                'categories_speaker.biography as description',
                DB::raw('SUM(top_speaker_log.count) as top_view')
            ])
            ->where('categories_speaker.status', 1)
            ->groupBy('categories_speaker.id')
            ->orderBy('top_view', 'desc')
            ->limit(8)
            ->get();
    }

    public function getSelectedDepartments($value)
    {
        // Basic department data
        return [];
    }

    public function getSelectedLearners($value)
    {
        // Basic learner data  
        return [];
    }

    public function getSelectedCategories($value)
    {
        // Basic category data
        return Categories::select(['id', 'title_th', 'image_th', 'position'])
            ->where('status', 1)
            ->orderBy('position', 'asc')
            ->limit(8)
            ->get();
    }

    public function getSelectedCurriculums($value)
    {
        // Type 25: Curriculum data
        return [];
    }

    public function getHomeGallery($content_id)
    {
        // Type 29: Home Gallery
        return HomeGallery::where('home_content_id', $content_id)
            ->where('status', 1)
            ->orderBy('position', 'asc')
            ->get();
    }

    public function getHomeVideo($content_id)
    {
        // Type 30: Home Video  
        return HomeVideo::where('home_content_id', $content_id)
            ->where('status', 1)
            ->orderBy('position', 'asc')
            ->get();
    }

    public function getPointRanking()
    {
        // Type 23: Point ranking
        $data_month = \App\Models\User::join('user_point_log', 'user_point_log.user_id', 'user.id')
            ->select('user.avatar', 'user.nickname', DB::raw('SUM(user_point_log.point) as point'))
            ->where('user.status', 1)
            ->whereMonth('user_point_log.created_at', Carbon::now()->month)
            ->orderBy('point', 'desc')
            ->groupBy('user.id')
            ->limit(8)
            ->get();
            
        $data_all = \App\Models\User::select('avatar', 'nickname', 'point')
            ->where('status', 1)
            ->orderBy('point', 'desc')
            ->limit(8)
            ->get();

        return [
            'data' => $data_month,
            'data_month' => $data_month,
            'data_all' => $data_all
        ];
    }

    public function getKnowledgeCenter($content_id)
    {
        // Type 24: Knowledge management center - ตามไฟล์เดิมแบบสมบูรณ์
        $km_log = HomeGMCenter::where('home_content_id', $content_id)
            ->where('status', 1)
            ->orderBy('position', 'asc')
            ->get();

        foreach ($km_log as $key => $val) {
            $km_log[$key]['count'] = 0;
            if ($val->type == 1) {
                $count_course = \App\Models\Core\Course::where('started_date', '<=', Carbon::now())
                    ->where('trailer_media', 1)
                    ->where('status', 1)
                    ->where(function ($query) {
                        $query->where('end_date', '>=', Carbon::now())
                              ->orWhere('end_date', null);
                    })
                    ->count();
                $km_log[$key]['count'] = $count_course;
            } else if ($val->type == 2) {
                $count_course = \App\Models\Core\Course::where('started_date', '<=', Carbon::now())
                    ->where('trailer_media', 3)
                    ->where('status', 1)
                    ->where(function ($query) {
                        $query->where('end_date', '>=', Carbon::now())
                              ->orWhere('end_date', null);
                    })
                    ->count();
                $km_log[$key]['count'] = $count_course;
            } else if ($val->type == 3) {
                $count_course = \App\Models\Core\Course::join('course_file', 'course_file.course_id', 'course.id')
                    ->where('course.started_date', '<=', Carbon::now())
                    ->where('course.status', 1)
                    ->where(function ($query) {
                        $query->where('course.end_date', '>=', Carbon::now())
                              ->orWhere('course.end_date', null);
                    })
                    ->pluck('course.id')
                    ->unique()
                    ->count();
                $km_log[$key]['count'] = $count_course;
            }
        }
        
        return $km_log;
    }

    public function getLatestArticles()
    {
        return Article::select([
            'id', 'title_th', 'subtitle_th', 'description_th', 'image_th',
            'created_at', 'type_id', 'slug'
        ])
        ->where('status', 1)
        ->orderBy('created_at', 'desc')
        ->limit(8)
        ->get();
    }

    public function getPopularArticles()
    {
        return Article::select([
            'id', 'title_th', 'subtitle_th', 'description_th', 'image_th',
            'created_at', 'type_id', 'slug'
        ])
        ->where('status', 1)
        ->orderBy('view', 'desc')
        ->limit(8)
        ->get();
    }
}
