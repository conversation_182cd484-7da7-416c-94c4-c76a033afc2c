<?php
namespace App\Services\HomeContent;

use App\Helpers\AppHelper;
use App\Repositories\Interfaces\CourseRepositoryInterface;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Repositories\Interfaces\ContentSubscriptionRepositoryInterface;
use App\Repositories\Interfaces\CourseGroupRepositoryInterface;
use Carbon\Carbon;

class CourseContentService
{
    protected $courseRepository;
    protected $userRepository;
    protected $subscriptionRepository;
    protected $courseGroupRepository;

    public function __construct(
        CourseRepositoryInterface $courseRepository,
        UserRepositoryInterface $userRepository,
        ContentSubscriptionRepositoryInterface $subscriptionRepository,
        CourseGroupRepositoryInterface $courseGroupRepository
    ) {
        $this->courseRepository = $courseRepository;
        $this->userRepository = $userRepository;
        $this->subscriptionRepository = $subscriptionRepository;
        $this->courseGroupRepository = $courseGroupRepository;
    }

    protected function addSubscriptionInfo($courses)
    {
        foreach ($courses as $key => $course) {
            $subscriptions = \App\Models\Core\Subscription::where('status', 1)
                ->whereNotNull('subscription_main_id')
                ->with('subscriptionMain')
                ->get();
            
            $course_subscription_mains = [];
            $seen_main_ids = [];
            
            foreach ($subscriptions as $sub) {
                if (!$sub->subscriptionMain || in_array($sub->subscription_main_id, $seen_main_ids)) {
                    continue;
                }
                
                $isInSubscription = false;

                if ($sub->select_type == 1) {
                    $in_cate_count = \App\Models\Core\SubscriptionInCate::where('subscription_id', $sub->id)->count();
                    if ($in_cate_count == 0) {
                        $isInSubscription = true;
                    } else {
                        $in_cate = \App\Models\Core\SubscriptionInCate::join('course_categories_log', 'course_categories_log.cate_id', 'subscription_in_cate.cate_id')
                            ->where('subscription_in_cate.subscription_id', $sub->id)
                            ->where('course_categories_log.course_id', $course->id)
                            ->first();
                        if ($in_cate) {
                            $isInSubscription = true;
                        }
                    }
                } else {
                    $ex_cate_count = \App\Models\Core\SubscriptionExCate::where('subscription_id', $sub->id)->count();
                    if ($ex_cate_count == 0) {
                        $isInSubscription = true;
                    } else {
                        $ex_cate = \App\Models\Core\SubscriptionExCate::join('course_categories_log', 'course_categories_log.cate_id', 'subscription_ex_cate.cate_id')
                            ->where('subscription_ex_cate.subscription_id', $sub->id)
                            ->where('course_categories_log.course_id', $course->id)
                            ->first();
                        if (!$ex_cate) {
                            $isInSubscription = true;
                        }
                    }
                }

                $ex_course = \App\Models\Core\SubscriptionExCourse::where('course_id', $course->id)
                    ->where('subscription_id', $sub->id)
                    ->first();
                if ($ex_course) {
                    $isInSubscription = false;
                }
                
                if ($isInSubscription && $sub->subscriptionMain) {
                    $course_subscription_mains[] = [
                        'id' => $sub->subscriptionMain->id,
                        'title' => $sub->subscriptionMain->title,
                        'slug' => $sub->subscriptionMain->slug
                    ];
                    
                    $seen_main_ids[] = $sub->subscription_main_id;
                }
            }
            
            $courses[$key]->subscription_mains = $course_subscription_mains;
        }
        
        return $courses;
    }

    public function getSelectedCourses($value, $utoken)
    {
        $filters = $this->prepareFilters($value);
        $courses = $this->courseRepository->findCoursesWithFilters($filters);
        $courses = $this->addSubscriptionInfo($courses);
        
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    private function prepareFilters($value): array
    {
        return [
            'department' => $value->department ?? null,
            'learner' => $value->learner ?? null,
            'speaker' => $value->speaker ?? null,
            'category' => $value->category ?? null,
            'level' => $value->level ?? null,
        ];
    }

    public function getFreeCourses($utoken)
    {
        $courses = $this->courseRepository->findFreeCourses();
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getSuggestedCourses($utoken)
    {
        $courses = $this->courseRepository->findSuggestedCourses();
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getLatestCourses($utoken)
    {
        $courses = $this->courseRepository->findLatestCourses();
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getNewCourses($utoken)
    {
        $courses = $this->courseRepository->findNewCourses();
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getUpcomingCourses($utoken)
    {
        $courses = $this->courseRepository->findUpcomingCourses();
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getUserRecommendedCourses($utoken)
    {
        if (!$utoken) {
            return [];
        }
        
        $user = $this->userRepository->findByToken($utoken);
        if (!$user) {
            return [];
        }

        $courses = $this->courseRepository->findUserRecommendedCourses($user->id);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getUserFavoriteCourses($utoken)
    {
        if (!$utoken) {
            return [];
        }
        
        $user = $this->userRepository->findByToken($utoken);
        if (!$user) {
            return [];
        }

        $courses = $this->courseRepository->findUserFavoriteCourses($user->id);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getPopularCourses($content, $utoken)
    {
        if ($content->popular_all == 1) {
            $manual = $this->courseRepository->findPopularCoursesManual($content->id, $content->type);
            
            $limit_pop = 10 - count($manual);
            $sub_day = $content->sub_day ?? 0;
            $start_day = $sub_day == 0 ? Carbon::now() : Carbon::now()->subDays($sub_day);
            $order_arr = $manual->pluck('id')->toArray();

            $course_1 = $this->courseRepository->findTopCoursesByViews($order_arr, $start_day, $limit_pop);

            $course_merge = [];
            foreach ($manual as $val) {
                $course_merge[] = $val;
            }
            foreach ($course_1 as $val) {
                $course_merge[] = $val;
            }

            $course_merge = $this->addSubscriptionInfo(collect($course_merge));
            return AppHelper::instance()->convertCourse($course_merge, $utoken);
        } else {
            return $this->getTopCourses($utoken);
        }
    }

    public function getTopCourses($utoken)
    {
        $courses = $this->courseRepository->findTopCourses();
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getCourseGroup($group_id, $utoken)
    {
        $group_free = $this->courseGroupRepository->findActiveGroup($group_id);

        if ($group_free) {
            $free_log = $this->courseGroupRepository->findGroupCourses($group_id, 8);
            $free_log = $this->addSubscriptionInfo($free_log);
            $group_free['data'] = AppHelper::instance()->convertCourse($free_log, $utoken);
            return $group_free;
        }

        return [];
    }

    public function getSubscriptionCourses($value, $utoken)
    {
        if (!isset($value->subscription_main_id) || empty($value->subscription_main_id)) {
            return [];
        }

        $subscription_main = $this->subscriptionRepository->findActiveSubscriptionMain($value->subscription_main_id);
        if (!$subscription_main) {
            return [];
        }

        $allowed_categories = $this->subscriptionRepository->getAllowedCategories($value->subscription_main_id);
        if (empty($allowed_categories)) {
            return [];
        }

        $filters = $this->prepareFilters($value);
        $courses = $this->courseRepository->findSubscriptionCourses($allowed_categories, $filters, 8);
        $courses = $this->addSubscriptionInfo($courses);

        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getCurriculumCourses($value, $utoken)
    {
        $filters = $this->prepareFilters($value);
        $courses = $this->courseRepository->findCurriculumCourses($filters, 8);
        $courses = $this->addSubscriptionInfo($courses);
        
        return $courses;
    }

    public function getPromotionCourses($value, $utoken)
    {
        $filters = [
            'department' => $value->department ?? null,
            'learner' => $value->learner ?? null,
            'speaker' => $value->speaker ?? null,
            'category' => $value->category ?? null,
            'level' => $value->level ?? null,
        ];

        $courses = $this->courseRepository->findPromotionCourses($filters, 8);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getUserLevelBasedCourses($utoken)
    {
        if (!$utoken) {
            return [];
        }

        $user = $this->userRepository->findByToken($utoken);
        if (!$user) {
            return [];
        }

        $courses = $this->courseRepository->findUserLevelBasedCourses($user->id);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getUserInterestCourses($utoken)
    {
        if (!$utoken) {
            return [];
        }

        $user = $this->userRepository->findByToken($utoken);
        if (!$user) {
            return [];
        }

        $courses = $this->courseRepository->findUserInterestCourses($user->id);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getLiveCourses($value, $utoken)
    {
        $filters = $this->prepareFilters($value);
        $courses = $this->courseRepository->findLiveCourses($filters, 8);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getEventCourses($value, $utoken)
    {
        $filters = $this->prepareFilters($value);
        $courses = $this->courseRepository->findEventCourses($filters, 8);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }

    public function getCompanyVisitCourses($value, $utoken)
    {
        $filters = $this->prepareFilters($value);
        $courses = $this->courseRepository->findCompanyVisitCourses($filters, 8);
        $courses = $this->addSubscriptionInfo($courses);
        return AppHelper::instance()->convertCourse($courses, $utoken);
    }
}
