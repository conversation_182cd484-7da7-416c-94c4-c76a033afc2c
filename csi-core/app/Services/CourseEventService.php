<?php

namespace App\Services;

use App\Helpers\AppHelper;
use App\Models\Core\Course;
use App\Repositories\Interfaces\CourseEventRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class CourseEventService
{
    protected $courseEventRepository;

    public function __construct(CourseEventRepositoryInterface $courseEventRepository)
    {
        $this->courseEventRepository = $courseEventRepository;
    }

    public function getAll(): Collection
    {
        return $this->courseEventRepository->all();
    }

    public function getCount(): int
    {
        return $this->courseEventRepository->count();
    }

    public function getCourseEventWithRelations(): Collection
    {
        return $this->courseEventRepository->getCourseEventWithRelations();
    }

    public function findById(int $id): ?Model
    {
        return $this->courseEventRepository->find($id);
    }

    public function createCourseEvent(Request $request): Model
    {
        $data = $this->prepareCourseEventData($request);
        $data['position'] = $this->courseEventRepository->count() + 1;

        return $this->courseEventRepository->create($data);
    }

    public function updateCourseEvent(Request $request, int $id): bool
    {
        $data = $this->prepareCourseEventData($request);
        return $this->courseEventRepository->update($id, $data);
    }

    public function deleteCourseEvent(int $id): bool
    {
        return $this->courseEventRepository->delete($id);
    }

    public function toggleStatus(int $id): bool
    {
        return $this->courseEventRepository->updateStatus($id);
    }

    public function updatePosition(int $id, int $position): bool
    {
        return $this->courseEventRepository->updatePosition($id, $position);
    }

    public function prepareDataForCreate(): array
    {
        $model = new Course();
        $model->trailer_media = 5; // Course Event
        
        $old = session()->getOldInput();
        if ($old) {
            foreach ($old as $key => $value) {
                if (property_exists($model, $key)) {
                    $model->$key = $value;
                }
            }
        }

        return [
            'model' => $model
        ];
    }

    public function prepareDataForEdit(int $id): ?array
    {
        $model = $this->findById($id);
        
        if (!$model) {
            return null;
        }

        return [
            'model' => $model
        ];
    }

    protected function prepareCourseEventData(Request $request): array
    {
        $data = [
            'sku_type' => $request->sku_type ?? null,
            'slug' => $request->slug ?? null,
            'course_key' => $request->course_key ?? null,
            'course_lang' => $request->course_lang ?? 1,
            'course_type' => $request->course_type ?? 1,
            'trailer_media' => 5,
            'topic_type' => $request->topic_type ?? null,
            'year' => $request->year ?? null,
            'title_th' => isset($request->title_th) ? AppHelper::instance()->cleanInput($request->title_th) : null,
            'title_en' => isset($request->title_en) ? AppHelper::instance()->cleanInput($request->title_en) : null,
            'subtitle_th' => isset($request->subtitle_th) ? AppHelper::instance()->cleanInput($request->subtitle_th) : null,
            'subtitle_en' => isset($request->subtitle_en) ? AppHelper::instance()->cleanInput($request->subtitle_en) : null,
            'details_th' => isset($request->details_th) ? $request->details_th : null,
            'details_en' => isset($request->details_en) ? $request->details_en : null,
            'host' => $request->host ?? null,
            'gender' => $request->gender ?? null,
            'age' => $request->age ?? null,
            'level' => $request->level ?? null,
            'department' => $request->department ?? null,
            'speaker' => $request->speaker ?? null,
            'learner' => $request->learner ?? null,
            'categories' => $request->categories ?? null,
            'tag' => $request->tag ?? null,
            'key_search' => $request->key_search ?? null,
            'checker' => $request->checker ?? null,
            'is_due' => $request->is_due ?? null,
            'duration_time' => $request->duration_time ?? null,
            'course_duration' => $request->course_duration ?? null,
            'started_date' => $request->started_date ?? null,
            'end_date' => $request->end_date ?? null,
            'started_time' => $request->started_time ?? null,
            'end_time' => $request->end_time ?? null,
            'conference_date' => $request->conference_date ?? null,
            'started_learning' => $request->started_learning ?? null,
            'internal' => $request->internal ?? 1,
            'is_free' => $request->is_free ?? 1,
            'is_suggess' => $request->is_suggess ?? 1,
            'is_promotion' => $request->is_promotion ?? 2,
            'pro_price' => $request->pro_price ?? 0,
            'price' => $request->price ?? 0,
            'is_cme' => $request->is_cme ?? 1,
            'receive_point' => $request->receive_point ?? 0,
            'point_to_pass' => $request->point_to_pass ?? 0,
            'is_cert' => $request->is_cert ?? 1,
            'rate' => $request->rate ?? 0,
            'rating' => $request->rating ?? 0,
            'is_sub' => $request->is_sub ?? 2,
            'is_hot' => $request->is_hot ?? 2,
            'is_oculus' => $request->is_oculus ?? 2,
            'is_new' => $request->is_new ?? 2,
            'position' => $request->position ?? null,
            'status' => $request->status ?? 1,
            'meta_title' => $request->meta_title ?? null,
            'meta_description' => $request->meta_description ?? null,
            'meta_keyword' => $request->meta_keyword ?? null,
        ];

        // Handle file uploads
        if ($request->hasFile('image_th')) {
            $data['image_th'] = $this->handleFileUpload($request->file('image_th'), 'course_event');
        }
        
        if ($request->hasFile('image_en')) {
            $data['image_en'] = $this->handleFileUpload($request->file('image_en'), 'course_event');
        }
        
        if ($request->hasFile('banner')) {
            $data['banner'] = $this->handleFileUpload($request->file('banner'), 'course_event');
        }
        
        if ($request->hasFile('banner_m')) {
            $data['banner_m'] = $this->handleFileUpload($request->file('banner_m'), 'course_event');
        }
        
        if ($request->hasFile('og_image')) {
            $data['og_image'] = $this->handleFileUpload($request->file('og_image'), 'course_event');
        }

        return $data;
    }

    protected function handleFileUpload($file, $folder): string
    {
        $fileName = time() . '_' . $file->getClientOriginalName();
        $file->move(public_path("upload/{$folder}"), $fileName);
        return "upload/{$folder}/" . $fileName;
    }
}
