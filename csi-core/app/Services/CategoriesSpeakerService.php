<?php

namespace App\Services;

use App\Repositories\Interfaces\CategoriesSpeakerRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection as SupportCollection;

class CategoriesSpeakerService
{
    protected $repository;

    public function __construct(CategoriesSpeakerRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function getAllSpeakers(): Collection
    {
        return $this->repository->getActive();
    }

    public function getSpeakerById(int $id): ?Model
    {
        return $this->repository->find($id);
    }

    public function searchSpeakers(string $search): Collection
    {
        return $this->repository->searchByTitle($search);
    }

    public function getSpeakersWithPagination(int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->getWithPagination($perPage);
    }

    public function getSpeakersByPosition(): Collection
    {
        return $this->repository->getByPosition();
    }

    public function getCoursesBySpeakerId(int $speakerId): SupportCollection
    {
        return $this->repository->getCoursesBySpeakerId($speakerId);
    }
}
