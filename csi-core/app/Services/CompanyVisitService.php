<?php

namespace App\Services;

use App\Models\Core\Course;
use App\Repositories\Interfaces\CompanyVisitRepositoryInterface;
use App\Helpers\AppHelper;
use Illuminate\Http\Request;

class CompanyVisitService
{
    protected $companyVisitRepository;

    public function __construct(CompanyVisitRepositoryInterface $companyVisitRepository)
    {
        $this->companyVisitRepository = $companyVisitRepository;
    }

    public function getAll()
    {
        return $this->companyVisitRepository->all();
    }

    public function getCount(): int
    {
        return $this->companyVisitRepository->count();
    }

    public function getCompanyVisitWithRelations()
    {
        return $this->companyVisitRepository->getCompanyVisitWithRelations();
    }

    public function findById(int $id): ?Course
    {
        return $this->companyVisitRepository->find($id);
    }

    public function createCompanyVisit(array $requestData): ?Course
    {
        $data = $this->prepareCompanyVisitData($requestData);
        $data['position'] = $this->companyVisitRepository->count() + 1;
        
        return $this->companyVisitRepository->create($data);
    }

    public function updateCompanyVisit(int $id, array $requestData): ?Course
    {
        $data = $this->prepareCompanyVisitData($requestData);
        
        return $this->companyVisitRepository->update($id, $data);
    }

    public function deleteCompanyVisit(int $id): bool
    {
        return $this->companyVisitRepository->delete($id);
    }

    public function toggleStatus(int $id): bool
    {
        return $this->companyVisitRepository->updateStatus($id);
    }

    public function updatePosition(int $id, int $position): bool
    {
        return $this->companyVisitRepository->updatePosition($id, $position);
    }

    public function prepareDataForCreate(): array
    {
        $model = new Course();
        $model->trailer_media = 4;
        $model->international = 1;
        
        $old = session()->getOldInput();
        if ($old) {
            foreach ($old as $key => $value) {
                if (property_exists($model, $key)) {
                    $model->$key = $value;
                }
            }
        }

        return [
            'model' => $model
        ];
    }

    public function prepareDataForEdit(int $id): ?array
    {
        $model = $this->findById($id);
        
        if (!$model) {
            return null;
        }

        return [
            'model' => $model
        ];
    }

    protected function prepareCompanyVisitData(array $requestData): array
    {
        $data = [
            'sku_type' => $requestData['sku_type'] ?? null,
            'slug' => $requestData['slug'] ?? null,
            'course_key' => $requestData['course_key'] ?? null,
            'title_th' => AppHelper::cleanInput($requestData['title_th'] ?? ''),
            'title_en' => AppHelper::cleanInput($requestData['title_en'] ?? ''),
            'subtitle_th' => AppHelper::cleanInput($requestData['subtitle_th'] ?? ''),
            'subtitle_en' => AppHelper::cleanInput($requestData['subtitle_en'] ?? ''),
            'details_th' => $requestData['details_th'] ?? '',
            'details_en' => $requestData['details_en'] ?? '',
            'price' => $requestData['price'] ?? 0,
            'pro_price' => $requestData['pro_price'] ?? 0,
            'is_free' => $requestData['is_free'] ?? 1,
            'status' => $requestData['status'] ?? 1,
            'course_duration' => $requestData['course_duration'] ?? 0,
            'started_date' => $requestData['started_date'] ?? null,
            'end_date' => $requestData['end_date'] ?? null,
            'meta_title' => AppHelper::cleanInput($requestData['meta_title'] ?? ''),
            'meta_description' => AppHelper::cleanInput($requestData['meta_description'] ?? ''),
            'meta_keyword' => AppHelper::cleanInput($requestData['meta_keyword'] ?? ''),
            'trailer_media' => 4, 
            'international' => 1,
            'updated_at' => now()
        ];

        if (isset($requestData['image_th'])) {
            $data['image_th'] = $requestData['image_th'];
        }
        if (isset($requestData['image_en'])) {
            $data['image_en'] = $requestData['image_en'];
        }
        if (isset($requestData['banner'])) {
            $data['banner'] = $requestData['banner'];
        }
        if (isset($requestData['banner_m'])) {
            $data['banner_m'] = $requestData['banner_m'];
        }

        return $data;
    }
}
