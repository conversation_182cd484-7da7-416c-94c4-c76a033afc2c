<?php

namespace App\Services;

use App\Helpers\AppHelper;
use App\Models\Core\SubscriptionMain;
use App\Repositories\Interfaces\SubscriptionMainRepositoryInterface;
use App\Repositories\Interfaces\SubscriptionRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class SubscriptionMainService
{
    protected $subscriptionMainRepository;
    protected $subscriptionRepository;

    public function __construct(
        SubscriptionMainRepositoryInterface $subscriptionMainRepository,
        SubscriptionRepositoryInterface $subscriptionRepository
    ) {
        $this->subscriptionMainRepository = $subscriptionMainRepository;
        $this->subscriptionRepository = $subscriptionRepository;
    }

    public function getAll(): Collection
    {
        return $this->subscriptionMainRepository->all();
    }

    public function getCount(): int
    {
        return $this->subscriptionMainRepository->count();
    }

    public function getWithSubscriptions(): Collection
    {
        return $this->subscriptionMainRepository->getWithSubscriptions();
    }

    public function getAvailableSubscriptions(?int $mainId = null): Collection
    {
        if ($mainId) {
            return $this->subscriptionRepository->getAvailableSubscriptions($mainId);
        }
        return $this->subscriptionRepository->getAvailableWithoutMain();
    }

    public function findById(int $id): ?Model
    {
        return $this->subscriptionMainRepository->find($id);
    }

    public function createSubscriptionMain(Request $request): Model
    {
        $data = $this->prepareSubscriptionData($request);
        $data['position'] = $this->subscriptionMainRepository->count() + 1;

        $model = $this->subscriptionMainRepository->create($data);

        if (is_array($request->subscription_ids)) {
            $this->subscriptionMainRepository->updateSubscriptionLinks($model->id, $request->subscription_ids);
        }

        return $model;
    }

    public function updateSubscriptionMain(Request $request, int $id): bool
    {
        $model = $this->subscriptionMainRepository->find($id);
        if (!$model) {
            return false;
        }

        $data = $this->prepareSubscriptionData($request);

        // Handle image upload and old image deletion
        if ($request->hasFile('cover_image')) {
            if ($model->cover_image && file_exists(public_path($model->cover_image))) {
                unlink(public_path($model->cover_image));
            }
        }

        $result = $this->subscriptionMainRepository->update($id, $data);

        if ($result) {
            $this->subscriptionMainRepository->updateSubscriptionLinks($id, $request->subscription_ids);
        }

        return $result;
    }

    public function deleteSubscriptionMain(int $id): bool
    {
        $result = $this->subscriptionMainRepository->delete($id);

        if ($result) {
            $this->subscriptionMainRepository->removeSubscriptionLinks($id);
        }

        return $result;
    }

    public function toggleStatus(int $id): bool
    {
        return $this->subscriptionMainRepository->updateStatus($id);
    }

    public function updatePosition(int $id, int $position): bool
    {
        return $this->subscriptionMainRepository->updatePosition($id, $position);
    }

    public function prepareDataForCreate(): array
    {
        $model = new SubscriptionMain();
        $old = session()->getOldInput();
        
        if ($old) {
            foreach ($old as $key => $value) {
                $model[$key] = $value;
            }
        }

        return [
            'model' => $model,
            'available_subscriptions' => $this->getAvailableSubscriptions()
        ];
    }

    public function prepareDataForEdit(int $id): ?array
    {
        $model = $this->findById($id);
        
        if (!$model) {
            return null;
        }

        return [
            'model' => $model,
            'available_subscriptions' => $this->getAvailableSubscriptions($id)
        ];
    }

    protected function prepareSubscriptionData(Request $request): array
    {
        $data = [
            'title' => isset($request->title) ? AppHelper::instance()->cleanInput($request->title) : null,
            'short_description' => isset($request->short_description) ? AppHelper::instance()->cleanInput($request->short_description) : null,
            'description' => isset($request->description) ? AppHelper::instance()->convertDomHTML($request->description) : null,
            'slug' => isset($request->slug) ? AppHelper::instance()->cleanInput($request->slug) : null,
            'period' => isset($request->period) ? AppHelper::instance()->cleanInput($request->period) : null,
            'is_popular' => isset($request->is_popular) ? $request->is_popular : 0,
            'status' => isset($request->status) ? $request->status : 1
        ];

        if ($request->hasFile('cover_image')) {
            $data['cover_image'] = AppHelper::instance()->saveImage($request->file('cover_image'), '/upload/subscription_main');
        }

        return $data;
    }
}