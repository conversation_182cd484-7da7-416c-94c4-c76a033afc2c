<?php
  namespace App\Helpers;

  use Illuminate\Http\Request;

  class SimpleEnDeHelper
  {
    public static function instance()
    {
       return new SimpleEnDeHelper();
    }

    private $en_key = 'Kidkarnmai';
    // private $en_key = 'RBH';

    public function encryptString($string,$en_key='') {
      $en_key = $en_key!='' ? $en_key : $this->en_key;
      $secret_key = $en_key;
      $secret_iv = $en_key;

      $string .= ''.$this->generateRandomString(10);

      $output = false;
      $encrypt_method = "AES-256-CBC";
      $key = hash( 'sha256', $secret_key );
      $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );
      $output = base64_encode( openssl_encrypt( $string, $encrypt_method, $key, 0, $iv ) );

      return $output.$this->generateRandomString(10);

    }

    public function encryptFixString($string,$en_key='') {
      $en_key = $en_key!='' ? $en_key : $this->en_key;
      $secret_key = $en_key;
      $secret_iv = $en_key;

      $output = false;
      $encrypt_method = "AES-256-CBC";
      $key = hash( 'sha256', $secret_key );
      $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );
      $output = base64_encode( openssl_encrypt( $string, $encrypt_method, $key, 0, $iv ) );

      return $output;

    }

    public function decryptString($string,$en_key='') {
      $en_key = $en_key!='' ? $en_key : $this->en_key;
      $secret_key = $en_key;
      $secret_iv = $en_key;

      $rand_string = substr($string, -10);
      $string = substr($string, 0 , -10);

      $output = false;
      $encrypt_method = "AES-256-CBC";
      $key = hash( 'sha256', $secret_key );
      $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );
      $output = openssl_decrypt( base64_decode( $string ), $encrypt_method, $key, 0, $iv );

      $output = substr($output, 0 , -10);

      return $output;
    }

    public function decryptFixString($string,$en_key='') {
      $en_key = $en_key!='' ? $en_key : $this->en_key;
      $secret_key = $en_key;
      $secret_iv = $en_key;

      $output = false;
      $encrypt_method = "AES-256-CBC";
      $key = hash( 'sha256', $secret_key );
      $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );
      $output = openssl_decrypt( base64_decode( $string ), $encrypt_method, $key, 0, $iv );

      $output = substr($output, 0 );

      return $output;
    }

    public function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    public function split_on($string, $num) {
        $length = strlen($string);
        $output[0] = substr($string, 0, $num);
        $output[1] = substr($string, $num, $length );
        return $output;
    }

  }

 ?>
