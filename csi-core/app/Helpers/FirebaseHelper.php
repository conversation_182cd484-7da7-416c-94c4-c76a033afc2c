<?php
  namespace App\Helpers;

  use Illuminate\Http\Request;
  use firebase;

  class FirebaseHelper
  {
    public static function instance()
    {
       return new FirebaseHelper();
    }

    public function testPost(){
      $firebase = app('firebase');
      $database = $firebase->getDatabase();

      $newPost = $database
        ->getReference('blog/posts')
        ->push([
            'title' => 'Post by helper',
            'body' => 'This should probably be longer.'
          ]);

      //$newPost->getKey(); // => -KVr5eu8gcTv7_AHb-3-

      //$newPost->getUri(); // => https://my-project.firebaseio.com/blog/posts/-KVr5eu8gcTv7_AHb-3-

      //$newPost->getChild('title')->set('Changed post title');

      //$newPost->getValue(); // Fetches the data from the realtime database

      //$newPost->remove();

      echo"<pre>";
      print_r($newPost->getvalue());
    }

    public function getData($path){
      $firebase = app('firebase');
      $database = $firebase->getDatabase();

      $data = $database
        ->getReference($path)
        ->getValue();

        return $data;
    }

    public function numchildData($path){
      $firebase = app('firebase');
      $database = $firebase->getDatabase();

      $data = $database
        ->getReference($path)
        ->getSnapshot()
        ->numChildren();

        return $data;
    }

    public function pushData($send,$path){
      $firebase = app('firebase');
      $database = $firebase->getDatabase();

      $newPost = $database
        ->getReference($path)
        ->push($send);

        return $newPost->getKey();
    }

    public function readData($path){
      $firebase = app('firebase');
      $database = $firebase->getDatabase();


    }

    public function removeData($key,$path){
      $firebase = app('firebase');
      $database = $firebase->getDatabase();


      $connect = $database
        ->getReference($path.'/'.$key)
        ->remove();
    }

    public function updateData($send,$key,$path){
      $firebase = app('firebase');
      $database = $firebase->getDatabase();
      $connect = $database
        ->getReference($path.'/'.$key)
        ->set($send);
    }

  }

 ?>
