<?php


namespace App\Helpers;

use Excel;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Intervention\Image\ImageManagerStatic as Image;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\Mail;
use Swift_Mailer;
use Swift_SmtpTransport;

use App\Models\Dummy;
use  App\Mail\InfoTemplate;
use  App\Mail\adminMailTemp;
use Illuminate\Support\Facades\File;
use Mews\Purifier\Facades\Purifier;
use HTMLPurifier;
use HTMLPurifier_Config;
use  App\Mail\MailTemp4;
use  App\Mail\MailTemp3;
use  App\Mail\MailTemp5;
use  App\Mail\MailTemp6;
use  App\Mail\MailTemplate;
use App\Models\Core\BulkCouponCode;
use App\Models\Core\BulkCouponExCate;
use App\Models\Core\BulkCouponExCourse;
use App\Models\Core\BulkCouponGroup;
use App\Models\Core\BulkCouponInCate;
use App\Models\Core\BulkCouponSub;
use App\Models\Core\CateSponsor;
use App\Models\Core\CouponWeb;
use App\Models\Core\CouponWebCode;
use App\Models\Core\CouponWebExclude;
use App\Models\Core\CouponWebInclude;
use App\Models\Core\Course;
use App\Models\Core\CourseCateLog;
use App\Models\Core\CourseExamLog;
use App\Models\Core\CourseGroup;
use App\Models\Core\CourseLessonLog;
use App\Models\Core\CourseLogoLog;
use App\Models\Core\CourseRate;
use App\Models\Core\CourseSpeakerLog;
use App\Models\Core\DynamicCert;
use App\Models\Core\DynamicCertGroup;
use App\Models\Core\GlobalCert;
use App\Models\Core\GroupCode;
use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionExCate;
use App\Models\Core\SubscriptionExCourse;
use App\Models\Core\SubscriptionInCate;
use App\Models\Core\SubscriptionLog;
use App\Models\Core\UserGraduateLog;
use App\Models\Core\UserHistory;
use App\Models\Core\UserPlaylist;
use App\Models\Core\UsersCartLog;
use App\Models\Core\UsersCertLog;
use App\Models\Core\UsersExamPoint;
use App\Models\Core\UsersFavoriteLog;
use App\Models\Core\UsersLearningLog;
use App\Models\Core\UsersOrderList;
use App\Models\Core\UsersOrderLog;
use App\Models\Core\VolumnBy;
use App\Models\Core\VolumnByCourse;
use App\Models\Core\VolumnByExCate;
use App\Models\Core\VolumnByInCate;
use App\Models\Core\VolumnByUser;
use App\Models\User;
use Pelago\Emogrifier\CssInliner;

class AppHelper
{

  public static function instance()
  {
    return new AppHelper();
  }

  public function consoleLog($obj)
  {
    if (config('app.debug')) {
      echo
      "<script>
        var json = " . json_encode($obj) . ";
        console.log(json);
        </script>";
    }
  }

  public function echoData($var, $obj)
  {
    if (isset($obj)) {
      echo
      "<script>
        var " . $var . " = " . json_encode($obj) . ";
        </script>";
    } else {
      echo
      "<script>
        var " . $var . " = '' ;
        </script>";
    }
  }

  public function purifier($req)
  {
    $config = HTMLPurifier_Config::createDefault();
    $config->set('Attr.EnableID', true);
    $config->set('HTML.SafeIframe', true);
    $config->set('HTML.Trusted', true);
    $config->set('URI.SafeIframeRegexp', '%^https://(www.youtube.com/embed/|player.vimeo.com/video/|maps.google.com|www.google.com)%');
    $purifier = new HTMLPurifier($config);
    foreach ($req as $key => $value) {
      if (is_numeric($value) || is_string($value) || is_array($value)) {
        $value = $purifier->purify($value);
      }
    }
    return $req;
  }

  public function cleanHtmlEntities($raw_data)
  {
    $config = HTMLPurifier_Config::createDefault();
    $config->set('Attr.EnableID', true);
    $config->set('HTML.SafeIframe', true);
    $config->set('HTML.Trusted', true);
    $config->set('URI.SafeIframeRegexp', '%^https://(www.youtube.com/embed/|player.vimeo.com/video/|maps.google.com|www.google.com)%');
    $purifier = new HTMLPurifier($config);
    $raw_data = $purifier->purify($raw_data);

    $raw_data = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $raw_data);
    $raw_data = html_entity_decode($raw_data);
    return $raw_data;
  }

  public function convertDomHTML($raw_data)
  {
    if (!empty($raw_data)) {
      $raw_data = $this->cleanHtmlEntities($raw_data);
      $dom = new \domdocument();
      libxml_use_internal_errors(true);
      $dom->loadHtml($raw_data);
      // $dom->loadHtml($raw_data, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

      $images = $dom->getelementsbytagname('img');

      foreach ($images as $k => $img) {
        $data = $img->getattribute('src');
        if (preg_match('/^data:image\/(\w+);base64,/', $data, $type)) {

          $data = substr($data, strpos($data, ',') + 1);
          $type = strtolower($type[1]); // jpg, png, gif

          if (in_array($type, ['jpg', 'jpeg', 'gif', 'png'])) {

            $data = str_replace(' ', '+', $data);
            $data = base64_decode($data);

            $image_name = Carbon::now('Asia/Bangkok')->format('Ymd') . '_s_' . $this->generateRandomString(10) . '.' . $type;
            $dir = public_path('upload/page_builder_image');
            if (!File::isDirectory($dir)) {
              File::makeDirectory($dir, 493, true);
            }

            if (env('FILESYSTEM_DEFAULT') == 's3') {
              $path = env('AWS_PATH') . 'upload/page_builder_image/' . $image_name;
              $images3 = Image::make($data)->encode($type, 90)->stream();
              Storage::disk('s3')->put('upload/page_builder_image/' . $image_name, $images3->__toString());

              $img->removeattribute('src');
              $img->setattribute('src', $path);
            } else {
              $path = public_path('upload/page_builder_image/' . $image_name);
              Image::make($data)->save($path, 90);
              $img->removeattribute('src');
              $img->setattribute('src', asset('upload/page_builder_image/' . $image_name));
            }
          }
        }
      }

      $raw_data = utf8_decode($dom->saveHTML($dom));
      $raw_data = $this->cleanHtmlEntities($raw_data);
      return $raw_data;
    } else {
      return '';
    }
  }

  public function convertPage($raw_data)
  {
    if (!empty($raw_data)) {
      $raw_data = CssInliner::fromHtml($raw_data)->inlineCss()->render();
      $raw_data = $this->cleanHtmlEntities($raw_data);
      $dom = new \domdocument();
      libxml_use_internal_errors(true);
      $dom->loadHtml($raw_data);
      // $dom->loadHtml($raw_data, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

      $images = $dom->getelementsbytagname('img');

      foreach ($images as $k => $img) {
        $data = $img->getattribute('src');
        if (preg_match('/^data:image\/(\w+);base64,/', $data, $type)) {

          $data = substr($data, strpos($data, ',') + 1);
          $type = strtolower($type[1]); // jpg, png, gif

          if (in_array($type, ['jpg', 'jpeg', 'gif', 'png'])) {

            $data = str_replace(' ', '+', $data);
            $data = base64_decode($data);

            $image_name = Carbon::now('Asia/Bangkok')->format('Ymd') . '_s_' . $this->generateRandomString(10) . '.' . $type;
            $dir = public_path('upload/page_builder_image');
            if (!File::isDirectory($dir)) {
              File::makeDirectory($dir, 493, true);
            }

            if (env('FILESYSTEM_DEFAULT') == 's3') {
              $path = env('AWS_PATH') . 'upload/page_builder_image/' . $image_name;
              $images3 = Image::make($data)->encode($type, 90)->stream();
              Storage::disk('s3')->put('upload/page_builder_image/' . $image_name, $images3->__toString());

              $img->removeattribute('src');
              $img->setattribute('src', $path);
            } else {
              $path = public_path('upload/page_builder_image/' . $image_name);
              Image::make($data)->save($path, 90);
              $img->removeattribute('src');
              $img->setattribute('src', asset('upload/page_builder_image/' . $image_name));
            }
          }
        }
      }

      $raw_data = utf8_decode($dom->saveHTML($dom));
      $raw_data = $this->cleanHtmlEntities($raw_data);
      return $raw_data;
    } else {
      return '';
    }
  }

  public function removePageImg($raw_data)
  {
    if (env('FILESYSTEM_DEFAULT') != 's3') {
      if (!empty($raw_data)) {
        $raw_data = $this->cleanHtmlEntities($raw_data);
        $dom = new \domdocument();
        libxml_use_internal_errors(true);
        $dom->loadHtml($raw_data, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $images = $dom->getelementsbytagname('img');
        foreach ($images as $k => $img) {
          $data = $img->getattribute('src');
          if (strpos($data, 'upload/page_builder_image') > -1) {
            $file_ex = explode("/upload", $data);
            if (count($file_ex) > 1) {
              $image_path = 'upload' . $file_ex[1];
              $image_path = public_path() . '/' . $image_path;
              try {
                unlink($image_path);
              } catch (\Exception $e) {
              }
            }
          }
        }
      }
    }
  }

  public function cleanInput($raw_data)
  {
    $config = HTMLPurifier_Config::createDefault();
    $config->set('Attr.EnableID', true);
    $config->set('HTML.SafeIframe', true);
    $config->set('HTML.Trusted', true);
    $config->set('URI.SafeIframeRegexp', '%^https://(www.youtube.com/embed/|player.vimeo.com/video/|maps.google.com|www.google.com)%');
    $purifier = new HTMLPurifier($config);
    $raw_data = $purifier->purify($raw_data);

    $raw_data = $this->cleanHtmlEntities($raw_data);
    $raw_data = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $raw_data);
    $raw_data = preg_replace('#<iframe[^>]+>.*?</iframe>#is', '', $raw_data);
    $raw_data = preg_replace('#<style[^>]+>.*?</style>#is', '', $raw_data);
    $raw_data = str_replace(";", "", $raw_data);
    $raw_data = str_replace("{", "", $raw_data);
    $raw_data = str_replace("}", "", $raw_data);
    $raw_data = preg_replace("/\r|\n/", "", $raw_data);
    $raw_data = strip_tags($raw_data, "<b><ol><ul><li><p><br>");
    $raw_data = str_replace("<p>", "", $raw_data);
    $raw_data = str_replace("</p>", "", $raw_data);
    $raw_data = mb_convert_encoding($raw_data, 'UTF-8', 'UTF-8');
    return $raw_data;
  }

  public function cleanInputSlug($raw_data)
  {
    $config = HTMLPurifier_Config::createDefault();
    $config->set('Attr.EnableID', true);
    $config->set('HTML.SafeIframe', true);
    $config->set('HTML.Trusted', true);
    $config->set('URI.SafeIframeRegexp', '%^https://(www.youtube.com/embed/|player.vimeo.com/video/|maps.google.com|www.google.com)%');
    $purifier = new HTMLPurifier($config);
    $raw_data = $purifier->purify($raw_data);

    $raw_data = $this->cleanHtmlEntities($raw_data);
    $raw_data = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $raw_data);
    $raw_data = preg_replace('#<iframe[^>]+>.*?</iframe>#is', '', $raw_data);
    $raw_data = preg_replace('#<style[^>]+>.*?</style>#is', '', $raw_data);
    $raw_data = str_replace(";", "", $raw_data);
    $raw_data = str_replace("{", "", $raw_data);
    $raw_data = str_replace("}", "", $raw_data);
    $raw_data = str_replace("<p>", "", $raw_data);
    $raw_data = str_replace("</p>", "", $raw_data);
    $raw_data = preg_replace("/\r|\n/", "", $raw_data);
    $raw_data = str_replace(" ", "", $raw_data);
    $raw_data = strip_tags($raw_data, "<b><ol><ul><li><p><br>");
    $raw_data = preg_replace('/[^A-Za-z0-9\-ภถุึคตจขชๆไำพะัีรนยบลฟหกดเ้่าสวงผปแอิืทมใฝฃูฎฑธํ๊ณฯญฐฅฤฆฏโฌ็๋ษศซฉฮฺ์ฒฬฦ๐๑๒๓๔๕๖๗๘๙]/', '', $raw_data);

    $raw_data = mb_convert_encoding($raw_data, 'UTF-8', 'UTF-8');

    return $raw_data;
  }

  public function complexPassword($raw_data)
  {
    // Given password
    $raw_data = $this->cleanInput($raw_data);
    $password = $raw_data;

    // Validate password strength
    $uppercase = preg_match('@[A-Z]@', $password);
    $lowercase = preg_match('@[a-z]@', $password);
    $number    = preg_match('@[0-9]@', $password);
    $specialChars = preg_match('@[^\w]@', $password);

    if (!$uppercase || !$lowercase || !$number || !$specialChars || strlen($password) < 8) {
      return false;
    } else {
      return true;
    }
  }

  public function saveFile($file)
  {
    $filename  = Carbon::now('Asia/Bangkok')->format('Ymd') . '_s_' . AppHelper::instance()->generateRandomString(10) . '.mp4';
    $path = Storage::putFileAs('file', $file, $filename);
    // $dir = public_path('file');
    // if(!File::isDirectory($dir)){
    //   File::makeDirectory($dir,493,true);
    // }
    return asset('upload/' . $path);
  }

  public function saveVideoBase64($file){
    $file_arr = explode(",",$file);
    if(count($file_arr)>1){
      $file_b64 = '';
      foreach($file_arr as $key=>$val){
        if($key!=0){
          $file_b64 .= $val;
        }
      }
      $filename  = Carbon::now('Asia/Bangkok')->format('Ymd') . '_s_' . AppHelper::instance()->generateRandomString(10) . '.mp4';
      Storage::disk('public')->put('file/' . $filename, base64_decode($file_b64));
      return asset('upload/file/' . $filename);
    }else{
      return '';
    }
  }

  public function saveImage($image, $prefix_path, $size = 0)
  {
    $filename  = Carbon::now('Asia/Bangkok')->format('Ymd') . '_s_' . $this->generateRandomString(10) . '.' . $image->getClientOriginalExtension();
    if (env('FILESYSTEM_DEFAULT') == 's3') {
      if ($size != 0) {
        $images3 = Image::make($image->getRealPath())->resize($size, null, function ($constraint) {
          $constraint->aspectRatio();
        })->encode($image->getClientOriginalExtension(), 90)->stream();
      } else {
        $images3 = Image::make($image->getRealPath())->encode($image->getClientOriginalExtension(), 90)->stream();
      }
      Storage::disk('s3')->put('upload/' . $prefix_path . '/' . $filename, $images3->__toString());
      return env('AWS_PATH') . 'upload/' . $prefix_path . '/' . $filename;
    } else {
      $path = public_path($prefix_path . '/' . $filename);
      $dir = public_path($prefix_path);
      if (!File::isDirectory($dir)) {
        File::makeDirectory($dir, 493, true);
      }
      if ($size != 0) {
        Image::make($image->getRealPath())->resize($size, null, function ($constraint) {
          $constraint->aspectRatio();
        })->save($path);
        return asset($prefix_path . '/' . $filename);
      } else {
        // Image::make($image->getRealPath())->save($path);
        Storage::putFileAs(str_replace('\/upload/', "", $prefix_path), $image, $filename);
        return asset('upload/' . $prefix_path . '/' . $filename);
      }
    }
  }


  public function saveImageBase64($src, $prefix_path, $size = 0)
  {
    $src  = substr($src, strpos($src, ",") + 1);
    if (base64_encode(base64_decode($src, true)) === $src) {
      $data = base64_decode($src);
      $filename = Carbon::now('Asia/Bangkok')->format('Ymd') . '_s_' . $this->generateRandomString(10) . '.png';
      $path = public_path($prefix_path . '/' . $filename);
      $dir = public_path($prefix_path);
      if (!File::isDirectory($dir)) {
        File::makeDirectory($dir, 493, true);
      }
      if ($size != 0) {
        Image::make($data)->resize(500, null, function ($constraint) {
          $constraint->aspectRatio();
        })->save($path);
      } else {
        Image::make($data)->save($path);
      }

      return asset($prefix_path . '/' . $filename);
    } else {
      return '';
    }
  }

  public function generateRandomString($length = 10)
  {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
      $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
  }

  public function generateRandomNumber($length = 10)
  {
    $characters = '0123456789';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
      $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
  }

  public function removeImage($file)
  {
    if (env('FILESYSTEM_DEFAULT') != 's3') {
      $file_ex = explode("/upload", $file);
      if (count($file_ex) > 1) {
        $image_path = 'upload' . $file_ex[1];
        $image_path = public_path() . '/' . $image_path;
        try {
          unlink($image_path);
        } catch (\Exception $e) {
        }
      }
    }
  }
  public function removeFile($file)
  {
    $bol_status = false;
    if (env('FILESYSTEM_DEFAULT') != 's3') {
      $file_ex = explode("/upload", $file);
      if (count($file_ex) > 1) {
        $image_path = 'upload' . $file_ex[1];
        $image_path = public_path() . '/' . $image_path;
        try {
          unlink($image_path);
          $bol_status = true;
        } catch (\Exception $e) {
        }
      }
    }
    return $bol_status;
  }
  public function utf8_strlen($s)
  {
    $c = strlen($s);
    $l = 0;
    for ($i = 0; $i < $c; ++$i) if ((ord($s[$i]) & 0xC0) != 0x80) ++$l;
    return $l;
  }
  public function limitChar($string, $limit)
  {
    $string = strip_tags($string);
    if (strlen($string) > $limit) {
      $stringCut = substr($string, 0, $limit);
      $endPoint = strrpos($stringCut, ' ');
      // $string = $endPoint? substr($stringCut, 0, $endPoint):substr($stringCut, 0);
      $string = substr($stringCut, 0);
      $string .= '...';
    }
    return mb_convert_encoding($string, 'UTF-8', 'UTF-8');
  }
  public function limitCharUTF8($string, $limit)
  {
    $str_len = AppHelper::instance()->utf8_strlen($string);
    if ($str_len > $limit) {
      $limit_str = mb_substr($string, 0, $limit, "utf-8") . "...";
    } else {
      $limit_str = $string;
    }
    return $limit_str;
  }

  public function smsAIS($tel, $sms)
  {
    header('Content-Type: text/html; charset=utf-8');
    $charge = ''; //หมายเลขสำหรับเสียเงิน
    $tel = $this->str_replace_first('0', '66', $tel);

    $status = '';
    $sms = html_entity_decode($sms, ENT_QUOTES | ENT_HTML401, 'UTF-8');
    $sms = $this->convertCharsn($sms);

    $curl = \curl_init();
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt_array($curl, array(
      CURLOPT_URL => "https://smsgw-onlinestg.ais.co.th/smsgw/OTP_ShellBDG/OTP_ShellBDG_sms.php",
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => "",
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => "POST",
      CURLOPT_POSTFIELDS => "------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"CMD\"\r\n\r\nSENDMSG\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"FROM\"\r\n\r\nShell\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"TO\"\r\n\r\n" . $tel . "\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"REPORT\"\r\n\r\nY\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"CHARGE\"\r\n\r\n" . $charge . "\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"CODE\"\r\n\r\n45140422001\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"CTYPE\"\r\n\r\nUNICODE\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"CONTENT\"\r\n\r\n" . $sms . "\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--",
      CURLOPT_HTTPHEADER => array(
        "cache-control: no-cache",
        "content-type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW",
        "postman-token: 78179612-3a8b-035f-db4c-9842e6ba3d65"
      ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);

    curl_close($curl);

    if ($err) {
      $status = "cURL Error #:" . $err;
    } else {
      // echo $response;
      // $xml = simplexml_load_string($response);
      // $json = json_encode($xml);
      // $array = json_decode($json,TRUE);

      // echo $array['STATUS'];
      // if($array['STATUS']=='OK'){
      $status = $response . "<br />" . $sms;
      // }else{
      //     $obj->status = 'fail';
      // }
    }

    return $status;
  }

  public function smsInfoBip($tel, $sms)
  {
    $curl = \curl_init();
    $tel = $this->str_replace_first('0', '66', $tel);

    $status = '';

    curl_setopt_array($curl, array(
      CURLOPT_URL => "http://ywyn1.api.infobip.com/sms/2/text/single",
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => "",
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => "POST",
      CURLOPT_POSTFIELDS => "{ \"from\":\"Shell\", \"to\":\"$tel\", \"text\":\"$sms\" }",
      CURLOPT_HTTPHEADER => array(
        "accept: application/json",
        "authorization: Basic U2hlbGx3dW5kZXJtYW46U2hlbGwyMDE5",
        "content-type: application/json"
      ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);

    curl_close($curl);

    if ($err) {
      return "cURL Error #:" . $err;
    } else {
      return $response;
    }
  }

  public function convertCharsn($string)
  {
    $in = '';
    $out = iconv('UTF-8', 'UTF-16BE', $string);
    for ($i = 0; $i < strlen($out); $i++) {
      $in .= '%' . sprintf("%02X", ord($out[$i]));
    }
    return $in;
  }

  public function str_replace_first($from, $to, $content)
  {
    $from = '/' . preg_quote($from, '/') . '/';
    return preg_replace($from, $to, $content, 1);
  }

  public function covertFormatPad($digit)
  {
    return Carbon::now('Asia/Bangkok')->format('ymd') . sprintf('%03d', $digit);
  }

  public function countDupArray($arr)
  {
    return $vals = array_count_values($arr);
  }

  public function countDupArrayWithKey($obj)
  {
    $arrs = array();
    // echo $obj;
    $obj = json_decode($obj);
    foreach ($obj  as $key => $value) {
      array_push($arrs, $value->id . '@' . $value->name);
    }
    return $vals = array_count_values($arrs);
  }

  public function convertMonth($m)
  {
    $m = (int)$m;

    if ($m == 1) {
      $_m = 'มกราคม';
    } else if ($m == 2) {
      $_m = 'กุมภาพันธ์';
    } else if ($m == 3) {
      $_m = 'มีนาคม';
    } else if ($m == 4) {
      $_m = 'เมษายน';
    } else if ($m == 5) {
      $_m = 'พฤษภาคม';
    } else if ($m == 6) {
      $_m = 'มิถุนายน';
    } else if ($m == 7) {
      $_m = 'กรกฎาคม';
    } else if ($m == 8) {
      $_m = 'สิงหาคม';
    } else if ($m == 9) {
      $_m = 'กันยายน';
    } else if ($m == 10) {
      $_m = 'ตุลาคม';
    } else if ($m == 11) {
      $_m = 'พฤศจิกายน';
    } else if ($m == 12) {
      $_m = 'ธันวาคม';
    }
    return $_m;
  }

  public function convertDate($date, $lang)
  {
    if (isset($date)) {
      $date_arr = explode("-", $date);
      if (count($date_arr) == 3) {
        $date_arr[1] = (int)$date_arr[1];
        $date_arr[0] = (int)$date_arr[0];
        if ($lang == 'en') {
          if ($date_arr[1] == 1) {
            $_m = 'Jan';
          } else if ($date_arr[1] == 2) {
            $_m = 'Feb';
          } else if ($date_arr[1] == 3) {
            $_m = 'Mar';
          } else if ($date_arr[1] == 4) {
            $_m = 'Apr';
          } else if ($date_arr[1] == 5) {
            $_m = 'May';
          } else if ($date_arr[1] == 6) {
            $_m = 'Jun';
          } else if ($date_arr[1] == 7) {
            $_m = 'Jul';
          } else if ($date_arr[1] == 8) {
            $_m = 'Aug';
          } else if ($date_arr[1] == 9) {
            $_m = 'Sep';
          } else if ($date_arr[1] == 10) {
            $_m = 'Oct';
          } else if ($date_arr[1] == 11) {
            $_m = 'Nov';
          } else if ($date_arr[1] == 12) {
            $_m = 'Dec';
          }
          $_y = $date_arr[0] - 2000;
        } else {
          if ($date_arr[1] == 1) {
            $_m = 'ม.ค.';
          } else if ($date_arr[1] == 2) {
            $_m = 'ก.พ.';
          } else if ($date_arr[1] == 3) {
            $_m = 'มี.ค.';
          } else if ($date_arr[1] == 4) {
            $_m = 'เม.ย.';
          } else if ($date_arr[1] == 5) {
            $_m = 'พ.ค.';
          } else if ($date_arr[1] == 6) {
            $_m = 'มิ.ย.';
          } else if ($date_arr[1] == 7) {
            $_m = 'ก.ค.';
          } else if ($date_arr[1] == 8) {
            $_m = 'ส.ค.';
          } else if ($date_arr[1] == 9) {
            $_m = 'ก.ย.';
          } else if ($date_arr[1] == 10) {
            $_m = 'ต.ค.';
          } else if ($date_arr[1] == 11) {
            $_m = 'พ.ย.';
          } else if ($date_arr[1] == 12) {
            $_m = 'ธ.ค.';
          }
          $_y = $date_arr[0] + 543 - 2500;
        }
        $result = $date_arr[2] . ' ' . $_m . ' ' . $_y;
      } else {
        $result = '';
      }
    } else {
      $result = '';
    }
    return $result;
  }

  public function convertFullDate($date, $lang)
  {
    if (isset($date)) {
      $date_arr = explode("-", $date);
      if (count($date_arr) == 3) {
        $date_arr[1] = (int)$date_arr[1];
        $date_arr[0] = (int)$date_arr[0];
        if ($lang == 'en') {
          if ($date_arr[1] == 1) {
            $_m = 'January';
          } else if ($date_arr[1] == 2) {
            $_m = 'February';
          } else if ($date_arr[1] == 3) {
            $_m = 'March';
          } else if ($date_arr[1] == 4) {
            $_m = 'April';
          } else if ($date_arr[1] == 5) {
            $_m = 'May';
          } else if ($date_arr[1] == 6) {
            $_m = 'June';
          } else if ($date_arr[1] == 7) {
            $_m = 'July';
          } else if ($date_arr[1] == 8) {
            $_m = 'August';
          } else if ($date_arr[1] == 9) {
            $_m = 'September';
          } else if ($date_arr[1] == 10) {
            $_m = 'October';
          } else if ($date_arr[1] == 11) {
            $_m = 'November';
          } else if ($date_arr[1] == 12) {
            $_m = 'December';
          }
          $_y = $date_arr[0];
        } else {
          if ($date_arr[1] == 1) {
            $_m = 'มกราคม';
          } else if ($date_arr[1] == 2) {
            $_m = 'กุมภาพันธ์';
          } else if ($date_arr[1] == 3) {
            $_m = 'มีนาคม';
          } else if ($date_arr[1] == 4) {
            $_m = 'เมษายน';
          } else if ($date_arr[1] == 5) {
            $_m = 'พฤษภาคม';
          } else if ($date_arr[1] == 6) {
            $_m = 'มิถุนายน';
          } else if ($date_arr[1] == 7) {
            $_m = 'กรกฎาคม';
          } else if ($date_arr[1] == 8) {
            $_m = 'สิงหาคม';
          } else if ($date_arr[1] == 9) {
            $_m = 'กันยายน';
          } else if ($date_arr[1] == 10) {
            $_m = 'ตุลาคม';
          } else if ($date_arr[1] == 11) {
            $_m = 'พฤศจิกายน';
          } else if ($date_arr[1] == 12) {
            $_m = 'ธันวาคม';
          }
          $_y = $date_arr[0] + 543;
        }
        $result = $date_arr[2] . ' ' . $_m . ' ' . $_y;
      } else {
        $result = '';
      }
    } else {
      $result = '';
    }
    return $result;
  }

  public function thDateToDb($date)
  {
    $_m = 99;
    if (isset($date)) {
      $date_arr = explode("-", $date);
      $date_arr_space = explode(" ", $date);
      if ($date_arr && count($date_arr) == 3) {
        $date_arr[2] = (int)$date_arr[2];
        $date_arr[0] = (int)$date_arr[0];
        if ($date_arr[1] == 'ม.ค.' || $date_arr[1] == 'มกราคม' || $date_arr[1] == '01' || $date_arr[1] == '1' || $date_arr[1] == 'Jan' || $date_arr[1] == 'January') {
          $_m = '01';
        } else if ($date_arr[1] == 'ก.พ.' || $date_arr[1] == 'กุมภาพันธ์' || $date_arr[1] == '02' || $date_arr[1] == '2' || $date_arr[1] == 'Feb' || $date_arr[1] == 'Febuary') {
          $_m = '02';
        } else if ($date_arr[1] == 'มี.ค.' || $date_arr[1] == 'มีนาคม' || $date_arr[1] == '03' || $date_arr[1] == '3' || $date_arr[1] == 'Mar' || $date_arr[1] == 'March') {
          $_m = '03';
        } else if ($date_arr[1] == 'เม.ย.' || $date_arr[1] == 'เมษายน' || $date_arr[1] == '04' || $date_arr[1] == '4' || $date_arr[1] == 'Apr' || $date_arr[1] == 'April') {
          $_m = '04';
        } else if ($date_arr[1] == 'พ.ค.' || $date_arr[1] == 'พฤษภาคม' || $date_arr[1] == '05' || $date_arr[1] == '5' || $date_arr[1] == 'May') {
          $_m = '05';
        } else if ($date_arr[1] == 'มิ.ย.' || $date_arr[1] == 'มิถุนายน' || $date_arr[1] == '06' || $date_arr[1] == '6' || $date_arr[1] == 'Jun' || $date_arr[1] == 'June') {
          $_m = '06';
        } else if ($date_arr[1] == 'ก.ค.' || $date_arr[1] == 'กรกฎาคม' || $date_arr[1] == '07' || $date_arr[1] == '7' || $date_arr[1] == 'Jul' || $date_arr[1] == 'July') {
          $_m = '07';
        } else if ($date_arr[1] == 'ส.ค.' || $date_arr[1] == 'สิงหาคม' || $date_arr[1] == '08' || $date_arr[1] == '8' || $date_arr[1] == 'Aug' || $date_arr[1] == 'August') {
          $_m = '08';
        } else if ($date_arr[1] == 'ก.ย.' || $date_arr[1] == 'กันยายน' || $date_arr[1] == '09' || $date_arr[1] == '9' || $date_arr[1] == 'Sep' || $date_arr[1] == 'September') {
          $_m = '09';
        } else if ($date_arr[1] == 'ต.ค.' || $date_arr[1] == 'ตุลาคม' || $date_arr[1] == '10' || $date_arr[1] == 'Oct' || $date_arr[1] == 'October') {
          $_m = '10';
        } else if ($date_arr[1] == 'พ.ย.' || $date_arr[1] == 'พฤศจิกายน' || $date_arr[1] == '11' || $date_arr[1] == 'Nov' || $date_arr[1] == 'November') {
          $_m = '11';
        } else if ($date_arr[1] == 'ธ.ค.' || $date_arr[1] == 'ธันวาคม' || $date_arr[1] == '12' || $date_arr[1] == 'Dec' || $date_arr[1] == 'December') {
          $_m = '12';
        }
        if ($date_arr[2] >= 2500) {
          $_y = $date_arr[2] - 543;
        } else if ($date_arr[2] < 2500 && $date_arr[2] >= 2000) {
          $_y = $date_arr[2];
        } else if ($date_arr[2] < 2000 && $date_arr[2] >= 40) {
          $_y = $date_arr[2] + 2500 - 543;
        } else {
          $_y = $date_arr[2] + 2000;
        }
        $result = $_y . '-' . $_m . '-' . $date_arr[0];
      } else if ($date_arr_space && count($date_arr_space) == 3) {
        $date_arr_space[2] = (int)$date_arr_space[2];
        $date_arr_space[0] = (int)$date_arr_space[0];
        if ($date_arr_space[1] == 'ม.ค.' || $date_arr_space[1] == 'มกราคม' || $date_arr_space[1] == '01' || $date_arr_space[1] == '1' || $date_arr_space[1] == 'Jan' || $date_arr_space[1] == 'January') {
          $_m = '01';
        } else if ($date_arr_space[1] == 'ก.พ.' || $date_arr_space[1] == 'กุมภาพันธ์' || $date_arr_space[1] == '02' || $date_arr_space[1] == '2' || $date_arr_space[1] == 'Feb' || $date_arr_space[1] == 'Febuary') {
          $_m = '02';
        } else if ($date_arr_space[1] == 'มี.ค.' || $date_arr_space[1] == 'มีนาคม' || $date_arr_space[1] == '03' || $date_arr_space[1] == '3' || $date_arr_space[1] == 'Mar' || $date_arr_space[1] == 'March') {
          $_m = '03';
        } else if ($date_arr_space[1] == 'เม.ย.' || $date_arr_space[1] == 'เมษายน' || $date_arr_space[1] == '04' || $date_arr_space[1] == '4' || $date_arr_space[1] == 'Apr' || $date_arr_space[1] == 'April') {
          $_m = '04';
        } else if ($date_arr_space[1] == 'พ.ค.' || $date_arr_space[1] == 'พฤษภาคม' || $date_arr_space[1] == '05' || $date_arr_space[1] == '5' || $date_arr_space[1] == 'May') {
          $_m = '05';
        } else if ($date_arr_space[1] == 'มิ.ย.' || $date_arr_space[1] == 'มิถุนายน' || $date_arr_space[1] == '06' || $date_arr_space[1] == '6' || $date_arr_space[1] == 'Jun' || $date_arr_space[1] == 'June') {
          $_m = '06';
        } else if ($date_arr_space[1] == 'ก.ค.' || $date_arr_space[1] == 'กรกฎาคม' || $date_arr_space[1] == '07' || $date_arr_space[1] == '7' || $date_arr_space[1] == 'Jul' || $date_arr_space[1] == 'July') {
          $_m = '07';
        } else if ($date_arr_space[1] == 'ส.ค.' || $date_arr_space[1] == 'สิงหาคม' || $date_arr_space[1] == '08' || $date_arr_space[1] == '8' || $date_arr_space[1] == 'Aug' || $date_arr_space[1] == 'August') {
          $_m = '08';
        } else if ($date_arr_space[1] == 'ก.ย.' || $date_arr_space[1] == 'กันยายน' || $date_arr_space[1] == '09' || $date_arr_space[1] == '9' || $date_arr_space[1] == 'Sep' || $date_arr_space[1] == 'September') {
          $_m = '09';
        } else if ($date_arr_space[1] == 'ต.ค.' || $date_arr_space[1] == 'ตุลาคม' || $date_arr_space[1] == '10' || $date_arr_space[1] == 'Oct' || $date_arr_space[1] == 'October') {
          $_m = '10';
        } else if ($date_arr_space[1] == 'พ.ย.' || $date_arr_space[1] == 'พฤศจิกายน' || $date_arr_space[1] == '11' || $date_arr_space[1] == 'Nov' || $date_arr_space[1] == 'November') {
          $_m = '11';
        } else if ($date_arr_space[1] == 'ธ.ค.' || $date_arr_space[1] == 'ธันวาคม' || $date_arr_space[1] == '12' || $date_arr_space[1] == 'Dec' || $date_arr_space[1] == 'December') {
          $_m = '12';
        }
        if ($date_arr_space[2] >= 2500) {
          $_y = $date_arr_space[2] - 543;
        } else if ($date_arr_space[2] < 2500 && $date_arr_space[2] >= 2000) {
          $_y = $date_arr_space[2];
        } else if ($date_arr_space[2] < 2000 && $date_arr_space[2] >= 40) {
          $_y = $date_arr_space[2] + 2500 - 543;
        } else {
          $_y = $date_arr_space[2] + 2000;
        }
        $result = $_y . '-' . $_m . '-' . $date_arr_space[0];
      } else {
        $result = null;
      }
    } else {
      $result = null;
    }
    return $result;
  }

  public function convertDateSlash($datetime, $lang)
  {
    $date = Carbon::parse($datetime)->format('Y-m-d');
    if (isset($date)) {
      $date_arr = explode("-", $date);
      if (count($date_arr) == 3) {
        $date_arr[1] = (int)$date_arr[1];
        $date_arr[0] = (int)$date_arr[0];
        if ($date_arr[1] == 1) {
          $_m = '01';
        } else if ($date_arr[1] == 2) {
          $_m = '02';
        } else if ($date_arr[1] == 3) {
          $_m = '03';
        } else if ($date_arr[1] == 4) {
          $_m = '04';
        } else if ($date_arr[1] == 5) {
          $_m = '05';
        } else if ($date_arr[1] == 6) {
          $_m = '06';
        } else if ($date_arr[1] == 7) {
          $_m = '07';
        } else if ($date_arr[1] == 8) {
          $_m = '08';
        } else if ($date_arr[1] == 9) {
          $_m = '09';
        } else if ($date_arr[1] == 10) {
          $_m = '10';
        } else if ($date_arr[1] == 11) {
          $_m = '11';
        } else if ($date_arr[1] == 12) {
          $_m = '12';
        }
        if ($lang == 'en') {
          $_y = $date_arr[0];
        } else {
          $_y = $date_arr[0] + 543;
        }
        $result = $date_arr[2] . '/' . $_m . '/' . $_y;
      } else {
        $result = '';
      }
    } else {
      $result = '';
    }
    return $result;
  }

  public function searchForId($id, $array)
  {
    foreach ($array as $key => $val) {
      if ($val->t === $id) {
        return $key;
      }
    }
    return null;
  }

  public function getTranslations($jsonLangs, $key)
  {
    $keys = $this->searchForId($key, $jsonLangs->data);
    if (empty($keys)) {
      return '';
    } else {
      return $jsonLangs->data[$keys]->v;
    }
  }

  public function mailTemplate($obj)
  {
    $mail = new Dummy();
    $mail['subject'] = $obj['subject'];
    $mail['code'] = $obj['code'];
    try {
      Mail::to($obj['email'])->cc($obj['cc'])->bcc($obj['bcc'])->send(new MailTemplate($mail));
    } catch (\Exception $e) {
    }
  }
  public function adminMailTemplate($obj)
  {
    $mail = new Dummy();
    $mail['subject'] = $obj['subject'];
    $mail['title'] = $obj['title'];
    $mail['message'] = $obj['message'];
    try {
      Mail::to($obj['email'])->cc($obj['cc'])->bcc($obj['bcc'])->send(new adminMailTemp($mail));
    } catch (\Exception $e) {
    }
  }

  public function cleanInputBr($raw_data)
  {
    $config = HTMLPurifier_Config::createDefault();
    $config->set('Attr.EnableID', true);
    $config->set('HTML.SafeIframe', true);
    $config->set('HTML.Trusted', true);
    $config->set('URI.SafeIframeRegexp', '%^https://(www.youtube.com/embed/|player.vimeo.com/video/|maps.google.com|www.google.com)%');
    $purifier = new HTMLPurifier($config);
    $raw_data = $purifier->purify($raw_data);

    $raw_data = $this->cleanHtmlEntities($raw_data);
    $raw_data = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $raw_data);
    $raw_data = preg_replace('#<iframe[^>]+>.*?</iframe>#is', '', $raw_data);
    $raw_data = preg_replace('#<style[^>]+>.*?</style>#is', '', $raw_data);
    $raw_data = str_replace(";", "", $raw_data);
    $raw_data = str_replace("{", "", $raw_data);
    $raw_data = str_replace("}", "", $raw_data);
    $raw_data = strip_tags($raw_data, "<b><ol><ul><li><>");
    $raw_data = str_replace("<p>", "", $raw_data);
    $raw_data = str_replace("</p>", "", $raw_data);
    $raw_data = preg_replace("/\r|\n/", "<br>", $raw_data);
    $raw_data = str_replace("<br><br>", "<br>", $raw_data);
    $raw_data = mb_convert_encoding($raw_data, 'UTF-8', 'UTF-8');
    return $raw_data;
  }

  public function hoursToSecods($hour)
  {
    if($hour!=null && $hour!=''){
      $parse = array();
      if (!preg_match('#^(?<hours>[\d]{2}):(?<mins>[\d]{2}):(?<secs>[\d]{2})$#', $hour, $parse)) {
        return 0;
      }
      return (int) $parse['hours'] * 3600 + (int) $parse['mins'] * 60 + (int) $parse['secs'];
    }else{
      return 0;
    }
  }

  public function DateThaiOnly($strDate)
  {
    $strDate = Carbon::parse($strDate)->format('Y-m-d');
    $strYear = date("Y", strtotime($strDate)) + 543 - 2500;
    $strMonth = date("n", strtotime($strDate));
    $strDay = date("j", strtotime($strDate));
    $strMonthCut = array("", "ม.ค.", "ก.พ.", "มี.ค.", "เม.ย.", "พ.ค.", "มิ.ย.", "ก.ค.", "ส.ค.", "ก.ย.", "ต.ค.", "พ.ย.", "ธ.ค.");
    $strMonthThai = $strMonthCut[$strMonth];
    return $strDay . ' ' . $strMonthThai . ' ' . $strYear;
  }

  public function DateThaiFull($strDate)
  {
    $strDate = Carbon::parse($strDate)->format('Y-m-d');
    $strYear = date("Y", strtotime($strDate)) + 543;
    $strMonth = date("n", strtotime($strDate));
    $strDay = date("j", strtotime($strDate));
    $strMonthCut = array("", "มกราคม", "กุมภาพันธ์", "มีนาคม", "เมษายน", "พฤษภาคม", "มิถุนายน", "กรกฎาคม", "สิงหาคม", "กันยายน", "ตุลาคม", "พฤศจิกายน", "ธันวาคม");
    $strMonthThai = $strMonthCut[$strMonth];
    return $strDay . ' ' . $strMonthThai . ' ' . $strYear;
  }

  public function DateEngFull($strDate)
  {
    $strDate = Carbon::parse($strDate)->format('Y-m-d');
    $strYear = date("Y", strtotime($strDate));
    $strMonth = date("n", strtotime($strDate));
    $strDay = date("j", strtotime($strDate));
    $strMonthCut = array("", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
    $strMonthThai = $strMonthCut[$strMonth];
    return $strMonthThai.' '.$strDay.', '.$strYear;
  }

  public function DateThaiSlash($strDate)
  {
    $strDate = Carbon::parse($strDate)->format('Y-m-d');
    $strYear = date("Y", strtotime($strDate)) + 543 - 2500;
    $strMonth = date("n", strtotime($strDate));
    $strDay = date("j", strtotime($strDate));
    $strMonthCut = array("", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12");
    $strMonthThai = $strMonthCut[$strMonth];
    return $strDay . '/' . $strMonthThai . '/' . $strYear;
  }

  public function DateEngSlash($strDate)
  {
    $strDate = Carbon::parse($strDate)->format('Y-m-d');
    $strYear = date("Y", strtotime($strDate));
    $strMonth = date("n", strtotime($strDate));
    $strDay = date("j", strtotime($strDate));
    $strMonthCut = array("", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12");
    $strMonthThai = $strMonthCut[$strMonth];
    return $strDay . '/' . $strMonthThai . '/' . $strYear;
  }

  public function bahtText(float $amount): string
  {
    [$integer, $fraction] = explode('.', number_format(abs($amount), 2, '.', ''));

    $baht = $this->convert($integer);
    $satang = $this->convert($fraction);

    $output = $amount < 0 ? 'ลบ' : '';
    $output .= $baht ? $baht . 'บาท' : '';
    $output .= $satang ? $satang . 'สตางค์' : 'ถ้วน';

    return $baht . $satang === '' ? 'ศูนย์บาทถ้วน' : $output;
  }

  public function convert(string $number): string
  {
    $values = ['', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า'];
    $places = ['', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน'];
    $exceptions = ['หนึ่งสิบ' => 'สิบ', 'สองสิบ' => 'ยี่สิบ', 'สิบหนึ่ง' => 'สิบเอ็ด'];

    $output = '';

    foreach (str_split(strrev($number)) as $place => $value) {
      if ($place % 6 === 0 && $place > 0) {
        $output = $places[6] . $output;
      }

      if ($value !== '0') {
        $output = $values[$value] . $places[$place % 6] . $output;
      }
    }

    foreach ($exceptions as $search => $replace) {
      $output = str_replace($search, $replace, $output);
    }

    return $output;
  }

  public function ConvertSecTotime($sec)
  {
    $init = $sec;
    $hours = floor($init / 3600);
    $minutes = floor(($init / 60) % 60);
    $seconds = $init % 60;
    return $hours . ':' . $minutes . ':' . $seconds;

    // echo "$seconds";
  }

  public function mailTemplate3($obj)
  {
    $mail = new Dummy();
    $mail['subject'] = $obj['subject'];
    $mail['message'] = $obj['message'];
    $mail['link'] = $obj['link'];
    $mail['article'] = $obj['article'];
    $mail['order'] = $obj['order'];
    $mail['order_total'] = $obj['order_total'];
    $mail['channel'] = $obj['channel'];
    try {
      Mail::to($obj['email'])->cc($obj['cc'])->bcc($obj['bcc'])->send(new MailTemp3($mail));
    } catch (\Exception $e) {
    }
  }


  public function mailTemplate4($obj)
  {
    $mail = new Dummy();
    $mail['subject'] = $obj['subject'];
    $mail['message'] = $obj['message'];
    $mail['link'] = $obj['link'];
    $mail['article'] = $obj['article'];
    $mail['channel'] = $obj['channel'];
    try {
      Mail::to($obj['email'])->cc($obj['cc'])->bcc($obj['bcc'])->send(new MailTemp4($mail));
    } catch (\Exception $e) {
    }
  }

  public function mailTemplate5($obj)
  {
    $mail = new Dummy();
    $mail['subject'] = $obj['subject'];
    $mail['message'] = $obj['message'];
    $mail['link'] = $obj['link'];
    $mail['article'] = $obj['article'];
    try {
      Mail::to($obj['email'])->cc($obj['cc'])->bcc($obj['bcc'])->send(new MailTemp5($mail));
    } catch (\Exception $e) {
    }
  }
  public function mailTemplate6($obj)
  {
    $mail = new Dummy();
    $mail['subject'] = $obj['subject'];
    $mail['title'] = $obj['title'];
    $mail['message'] = $obj['message'];
    try {
      Mail::to($obj['email'])->cc($obj['cc'])->bcc($obj['bcc'])->send(new MailTemp6($mail));
    } catch (\Exception $e) {
    }
  }

  public function createZoom($title, $start, $end)
  {
    $obj_res = new Dummy();
    $obj_res['zoom_id'] = '95204914252';
    $obj_res['zoom_url'] = 'https://example.com/j/11111';
    $obj_res['duration'] = '00:30:00';
    return $obj_res;
  }

  public function assignZoom($zoom_id, $email, $firstname, $lastname)
  {
    $obj_res = new Dummy();
    $obj_res['status'] = 'approve';
    $obj_res['registrant_id'] = '9tboDiHUQAeOnbmudzWa5g';
    return $obj_res;
  }

  public function secToTime($second)
  {
    $hours = floor($second / 3600);
    $mins = floor($second / 60 % 60);
    $secs = floor($second % 60);
    $timeFormat = sprintf('%02d:%02d:%02d', $hours, $mins, $secs);
    return $timeFormat;
  }

  public function convertCourse($raw_data, $token)
  {
    foreach ($raw_data as $key_course => $value_course) {
      $raw_data[$key_course]['is_certificate'] = false;
      $check_cert = DynamicCert::where('status',1)->where('course_id',$value_course->id)->first();
      if($check_cert){
        $raw_data[$key_course]['is_certificate'] = true;
      }
      $raw_data[$key_course]['rating'] = CourseRate::where('course_id',$value_course->id)->count();
      
      $raw_data[$key_course]['is_fav'] = false;
      $raw_data[$key_course]['learning'] = false;
      $raw_data[$key_course]['learning_cover'] = '';
      $raw_data[$key_course]['learning_percent'] = 0;
      $raw_data[$key_course]['is_subscription'] = false;
      $raw_data[$key_course]['course_percent'] = 0;
      $raw_data[$key_course]['learning_ep'] = 1;
      if($value_course->started_learning <= Carbon::now()){
        $raw_data[$key_course]['is_soon'] = false;
      }else{
        $raw_data[$key_course]['is_soon'] = true;
      }

      $count_learner = UserHistory::where('course_id',$value_course->id)->groupby('user_id')->pluck('user_id')->toArray();
      $raw_data[$key_course]['learners'] = count($count_learner);

      $raw_data[$key_course]['is_volume'] = false;
      $raw_data[$key_course]['date'] = AppHelper::instance()->DateThaiOnly($value_course->created_at);

      $raw_data[$key_course]['days_left'] = '';

      $lesson_count = CourseLessonLog::where('status',1)->where('course_id', $value_course->id)->count();
      $raw_data[$key_course]['lesson'] = $lesson_count;

      $raw_data[$key_course]['speaker'] = CourseSpeakerLog::join('categories_speaker', 'categories_speaker.id', 'course_speaker_log.speaker')->select('categories_speaker.*')->where('course_speaker_log.course_id', $value_course->id)->groupby('categories_speaker.id')->get();
      $raw_data[$key_course]['category'] = CourseCateLog::join('categories', 'categories.id', 'course_categories_log.cate_id')->select('categories.*')->where('course_categories_log.course_id', $value_course->id)->groupby('categories.id')->get();

      if($raw_data[$key_course]['course_duration']!=null&&$raw_data[$key_course]['course_duration']!=''&&$raw_data[$key_course]['course_duration']!='null'){
        $raw_data[$key_course]['duration'] = $raw_data[$key_course]['course_duration'];
      }else{
        $raw_data[$key_course]['duration'] = 0;
      }
      $raw_data[$key_course]['allowed'] = false;
      if (isset($token)) {
        $user = User::where('id', SimpleEnDeHelper::instance()->decryptString($token))->first();
        if ($user) {
          $is_fav = UsersFavoriteLog::where('course_id', $value_course->id)->where('user_id', $user->id)->first();
          if ($is_fav) {
            $raw_data[$key_course]['is_fav'] = true;
          }

          $history_log = UserHistory::where('user_id',$user->id)->where('course_id',$value_course->id)->where('expired','>=',Carbon::now())->orderby('expired','desc')->first();
          if($history_log){
            $raw_data[$key_course]['days_left'] = Carbon::now('Asia/Bangkok')->diffInDays(Carbon::parse($history_log->expired));
          }
          $learning = UsersLearningLog::where('course_id',$value_course->id)->where('user_id',$user->id)->orderby('lesson_id','desc')->first();
          if($learning){
            $ep = CourseLessonLog::where('course_id',$value_course->id)->where('status', 1)->where('id', $learning->lesson_id)->first();
            if($ep){
              $duration_all = AppHelper::instance()->hoursToSecods($ep->duration);
              $duration_learn = AppHelper::instance()->hoursToSecods($learning->watching_time);
              if($duration_all==0){
                $raw_data[$key_course]['learning_percent'] = 0;
              }else{
                $raw_data[$key_course]['learning_percent'] = $duration_learn / $duration_all * 100;
              }
              $raw_data[$key_course]['learning'] = true;
              $raw_data[$key_course]['learning_cover'] = $ep->image_th;
              $raw_data[$key_course]['learning_ep'] = $ep->position;
            }
          }

          $check_sub = AppHelper::instance()->courseSubscription($value_course,$user);
        $raw_data[$key_course]['is_subscription'] = $check_sub[0];

          $check_volume = AppHelper::instance()->checkVolume($value_course,$user);
          $raw_data[$key_course]['is_volume'] = $check_volume[0];

          $array_check = AppHelper::instance()->checkCourseAllow($value_course,$user);
          $raw_data[$key_course]['allowed'] = $array_check[0];

          $count_lesson = CourseLessonLog::where('status',1)->where('course_id',$value_course->id)->count();
          $count_learn_log = UsersLearningLog::join('course_lesson','course_lesson.id','user_learning_log.lesson_id')
          ->where('course_lesson.status',1)
          ->where('course_lesson.course_id',$value_course->id)
          ->where('user_learning_log.user_id',$user->id)
          ->where('user_learning_log.course_id', $value_course->id)->get();
          $count_learn = 0;
          foreach($count_learn_log as $val_l){
            $count_learn+=$val_l->watch_percent;
          }
          if($count_learn!=0&&$count_lesson!=0){
            $raw_data[$key_course]['course_percent'] = number_format($count_learn/$count_lesson);
          }
        }
      }
      
      if($value_course->is_promotion==1 && $value_course->pro_started <= Carbon::now() && ($value_course->pro_end >= Carbon::now()||$value_course->pro_period==2)){
        $raw_data[$key_course]['is_promotion'] = true;
        if($value_course->pro_end!=null && $value_course->pro_end!=''){
          $raw_data[$key_course]['pro_countdown'] = Carbon::parse($value_course->pro_end)->format('M d, Y H:i:s');
        }else{
          $raw_data[$key_course]['pro_countdown'] = '';
        }
      }else{
        $raw_data[$key_course]['is_promotion'] = false;
        $raw_data[$key_course]['pro_countdown'] = '';
      }
      $raw_data[$key_course]['pro_period'] = $value_course->pro_period;
    }
    return $raw_data;
  }

  public function getMyCourse($user_id)
  {
    $mycourse = UserHistory::join('course','course.id','user_history.course_id')->select('course.*')->where('user_history.user_id',$user_id)
    ->where('user_history.expired','>=',Carbon::now())->groupby('course.id')->get();
    foreach($mycourse as $key=>$value){
      $count_lesson = CourseLessonLog::where('is_teaser','!=',0)->where('status',1)->where('course_id',$value->id)->count();
      $count_learn_log = UsersLearningLog::join('course_lesson','course_lesson.id','user_learning_log.lesson_id')->where('course_lesson.is_teaser','!=',0)
      ->where('course_lesson.status',1)
      ->where('course_lesson.course_id',$value->id)
      ->where('user_learning_log.user_id',$user_id)
      ->where('user_learning_log.course_id', $value->id)->get();
      $count_learn = 0;
      foreach($count_learn_log as $val_l){
        $count_learn+=$val_l->watch_percent;
      }
      if($count_learn!=0&&$count_lesson!=0){
        $mycourse[$key]['learned_percent'] = number_format($count_learn/$count_lesson);
      }else{
        $mycourse[$key]['learned_percent'] = 0;
      }


      // if($value->course_duration!=null && $value->course_duration !=''){
      //   $course_time = $value->course_duration;
      // }else{
      //   $course_time = 0;
      // }
      // $learn_time = 0;
      // $lesson = UsersLearningLog::select('watching_time')->where('user_learning_log.user_id',$user_id)->where('user_learning_log.course_id',$value->id)->get();
      // foreach($lesson as $log){
      //   $learn_time += AppHelper::instance()->hoursToSecods($log->watching_time);
      // }
      // if($learn_time!=0&&$course_time!=0){
      //   $mycourse[$key]['learned_percent'] = number_format(($learn_time*100)/$course_time);
      // }else{
      //   $mycourse[$key]['learned_percent'] = 0;
      // }
    }
    return $mycourse;
  }

  public function getMySubscription($user_id)
  {
    $mycourse = UserHistory::join('course','course.id','user_history.course_id')->select('course.*')->where('user_history.user_id',$user_id)
    ->where('user_history.type',2)->where('user_history.expired','>=',Carbon::now())->groupby('course.id')->get();
    foreach($mycourse as $key=>$value){
      if($value->course_duration!=null && $value->course_duration !=''){
        $course_time = $value->course_duration;
      }else{
        $course_time = 0;
      }
      $learn_time = 0;
      $lesson = UsersLearningLog::select('watching_time')->where('user_learning_log.user_id',$user_id)->where('user_learning_log.course_id',$value->id)->get();
      foreach($lesson as $log){
        $learn_time += AppHelper::instance()->hoursToSecods($log->watching_time);
      }
      if($learn_time!=0&&$course_time!=0){
        $mycourse[$key]['learned_percent'] = number_format(($learn_time*100)/$course_time);
      }else{
        $mycourse[$key]['learned_percent'] = 0;
      }
    }
    return $mycourse;
  }

  public function checkCourseAllow($course,$user)
  {
    $allowed = false;
    $order_status = 0;
    // $to_date = Carbon::now();
    if($course&&$user){
      $history = UserHistory::where('user_id',$user->id)->where('course_id',$course->id)->
      // where('expired','>=',Carbon::now())->
      first();
      if($history){
        $allowed = true;
      }
      $allow = UsersOrderLog::join('user_order_list', 'user_order_list.order_id', 'user_order_log.id')
      ->where('user_order_log.buy_type', 1)->where('user_order_list.course_id', $course->id)->where('user_order_log.user_id', $user->id)->orderby('user_order_list.id','desc')->first();
      if($allow){
        $order_status=$allow->status;
      }else{
        $allow_group = UsersOrderLog::join('user_order_list','user_order_list.order_id','user_order_log.id')
        ->join('course_group', 'course_group.id', 'user_order_list.group_id')
        ->join('group_log', 'group_log.group_id', 'course_group.id')
        ->join('course', 'course.id', 'group_log.course_id')
        ->select('user_order_log.*','user_order_list.*')
        ->where('user_order_log.buy_type', 1)->where('course.id', $course->id)->where('user_order_log.user_id', $user->id)->orderby('user_order_list.id','desc')->first();
        if($allow_group){
          $order_status=$allow_group->status;
        }
      }
    }
    $data = array();
    array_push($data,$allowed);
    array_push($data,$order_status);
    return $data;
  }

  public function checkVideoAllow($video,$value,$key,$user,$course)
  {
    
    $lock = true;
    if($value->price == 0){
      $lock = false;
    }else{
      if($course&&$user){
        $history = UserHistory::where('user_id',$user->id)->where('course_id',$course->id)->where('expired','>=',Carbon::now())->first();
        if($history){
          if($key != 0){
            $pass = 0;
            for ($x = 0; $x < $key; $x++) {
              $pre_lesson = $video[$x];
              $question = CourseExamLog::where('status',1)->where('lesson_id', $pre_lesson->id)->count();
              if($question!=0&&$pre_lesson->lesson_point!=0){
                $exam_point = UsersExamPoint::join('course_lesson', 'course_lesson.id', 'user_exam_point.lesson_id')
                ->where('user_exam_point.user_id', $user->id)
                ->where('user_exam_point.course_id', $course->id)
                ->where('user_exam_point.lesson_id', $pre_lesson->id)
                ->where('user_exam_point.point', '>=', $pre_lesson->lesson_point)
                ->where('user_exam_point.status', 1)
                ->first();
                if($exam_point){
                  $pass++;
                }else{
                  if($pre_lesson->price==0){
                    $pass++;
                  }
                }
              }else{
                $pass++;
              }
            }
            if($pass == $key){
              $lock = false;
            }
          }else{
            $lock = false;
          }
        }
      }
    }
    return $lock;
  }

  public function checkVolume($course,$user)
  {
    $allow = false;
    $expired = '';
    $company_id = '';
    $company_lot_id = '';
    if($course&&$user){
      $volume = VolumnByUser::join('volumn_lot_log','volumn_lot_log.id','volumn_user_log.lot_id')
      ->join('volumn_by','volumn_by.id','volumn_user_log.company_id')
      ->select('volumn_lot_log.*')
      ->where('volumn_lot_log.status',1)
      ->where('volumn_by.status',1);
      $volume->where(function ($volume) use($user) {
        $volume->where('volumn_user_log.email',$user->email);
      });
      $volume->where(function ($volume) {
        $volume->where('volumn_lot_log.started_date','<=',Carbon::now());
        // $volume->orWhere('volumn_lot_log.started_date',null);
      });
      $volume->where(function ($volume) {
        $volume->where('volumn_lot_log.end_date','>=',Carbon::now());
        // $volume->orWhere('volumn_lot_log.end_date',null);
      });
      $volume = $volume->get();

      $count_check = 0;
      foreach($volume as $key=>$value){
        $check = false;

        $in_course = VolumnByCourse::where('course_id',$course->id)->where('volumn_id',$value->id)->first();
        if($in_course){
          // $check = false;
          $check = true;
          $expired = $value->end_date;
          $company_id = $value->company_id;
          $company_lot_id = $value->id;
        }
        if($check==true){
          $count_check++;
        }
      }
      if($count_check>0){
        $allow = true;
      }else{
        $vol_domain = VolumnBy::join('volumn_lot_log','volumn_lot_log.company_id','volumn_by.id')
        ->join('volumn_domain_log','volumn_domain_log.lot_id','volumn_lot_log.id')
        ->join('categories_domain','categories_domain.id','volumn_domain_log.domain_id')
        ->select('volumn_lot_log.*')
        ->where('volumn_lot_log.status',1)
        ->where('categories_domain.status',1)
        ->where('volumn_by.status',1);
        $vol_domain->where(function ($vol_domain) use($user) {
          if(count(explode("@",$user->email))==2){
            $vol_domain->where('categories_domain.title','@'.explode("@",$user->email)[1]);
          }
        });
        $vol_domain->where(function ($vol_domain) {
          $vol_domain->where('volumn_lot_log.started_date','<=',Carbon::now());
          // $vol_domain->orWhere('volumn_lot_log.started_date',null);
        });
        $vol_domain->where(function ($vol_domain) {
          $vol_domain->where('volumn_lot_log.end_date','>=',Carbon::now());
          // $vol_domain->orWhere('volumn_lot_log.end_date',null);
        });
        $vol_domain->groupby('volumn_lot_log.id');
        $vol_domain = $vol_domain->get();

        $count_check_domain = 0;
        foreach($vol_domain as $key_domain=>$value_domain){
          $check_domain = false;

          $count_user_in = VolumnByUser::where('lot_id',$value_domain->id)->count();

          if($count_user_in<$value_domain->limit){
            $in_course_domain = VolumnByCourse::where('course_id',$course->id)->where('volumn_id',$value_domain->id)->first();
            if($in_course_domain){
              $check_domain = true;
              $expired = $value_domain->end_date;
              $company_id = $value_domain->company_id;
              $company_lot_id = $value_domain->id;
            }
          }

          if($check_domain==true){
            $count_check_domain++;
          }
        }
        if($count_check_domain>0){
          $allow = true;
        }
      }
    }
    $data = array();
    array_push($data,$allow);
    array_push($data,$expired);
    array_push($data,$company_id);
    array_push($data,$company_lot_id);
    return $data;
    // return $allow;
  }

  public function courseSubscription($course,$user)
  {
    $allowed = false;
    $expired = '';
    if($course&&$user){
      $check_course_sub = Course::where('id',$course->id)->where('is_sub',1)->first();
      if($check_course_sub){
        $subscription = SubscriptionLog::join('subscription','subscription.id','subscription_log.subscription_id')
        ->select('subscription.*','subscription_log.expired')
        ->where('subscription_log.user_id',$user->id)->where('subscription_log.expired','>=',Carbon::now())
        ->where('subscription.status',1)->get();
        $count_check = 0;
        foreach($subscription as $key=>$value){
          $check = false;

          if($value->select_type==1){
            $in_cate_count = SubscriptionInCate::where('subscription_in_cate.subscription_id',$value->id)->count();
            if($in_cate_count == 0){
              $check = true;
              $expired = $value->expired;
            }else{
              $in_cate = SubscriptionInCate::join('course_categories_log','course_categories_log.cate_id','subscription_in_cate.cate_id')
              ->where('subscription_in_cate.subscription_id',$value->id)->where('course_categories_log.course_id',$course->id)->first();
              if($in_cate){
                $check = true;
                $expired = $value->expired;
              }else{
                $check = false;
              }
            }
          }else{
            $ex_cate_count = SubscriptionExCate::where('subscription_ex_cate.subscription_id',$value->id)->count();
            if($ex_cate_count == 0){
              $check = true;
              $expired = $value->expired;
            }else{
              $ex_cate = SubscriptionExCate::join('course_categories_log','course_categories_log.cate_id','subscription_ex_cate.cate_id')
              ->where('subscription_ex_cate.subscription_id',$value->id)->where('course_categories_log.course_id',$course->id)->first();
              if($ex_cate){
                $check = false;
              }else{
                $check = true;
                $expired = $value->expired;
              }
            }
          }

          $ex_course = SubscriptionExCourse::where('course_id',$course->id)->where('subscription_id',$value->id)->first();
          if($ex_course){
            $check = false;
          }
          if($value->type==1){
            $check_course_sub = Course::where('id',$course->id)->where('is_sub',1)->first();
            if($check_course_sub){
              $check = true;
              $expired = $value->expired;
            }
          }
          if($check==true){
            $count_check++;
          }
        }
        if($count_check>0){
          $allowed = true;
        }
      }
    }
    $data = array();
    array_push($data,$allowed);
    array_push($data,$expired);
    return $data;
  }

  public function courseSubscriptionCourse($course)
  {
    $allowed = 'No';
    if($course){
      $check_course_sub = Course::where('id',$course->id)->where('is_sub',1)->first();
      if($check_course_sub){
        $subscription = Subscription::where('subscription.status',1)->get();
        $count_check = 0;
        foreach($subscription as $key=>$value){
          $check = false;

          if($value->select_type==1){
            $in_cate_count = SubscriptionInCate::where('subscription_in_cate.subscription_id',$value->id)->count();
            if($in_cate_count == 0){
              $check = true;
            }else{
              $in_cate = SubscriptionInCate::join('course_categories_log','course_categories_log.cate_id','subscription_in_cate.cate_id')
              ->where('subscription_in_cate.subscription_id',$value->id)->where('course_categories_log.course_id',$course)->first();
              if($in_cate){
                $check = true;
              }else{
                $check = false;
              }
            }
          }else{
            $ex_cate_count = SubscriptionExCate::where('subscription_ex_cate.subscription_id',$value->id)->count();
            if($ex_cate_count == 0){
              $check = true;
            }else{
              $ex_cate = SubscriptionExCate::join('course_categories_log','course_categories_log.cate_id','subscription_ex_cate.cate_id')
              ->where('subscription_ex_cate.subscription_id',$value->id)->where('course_categories_log.course_id',$course)->first();
              if($ex_cate){
                $check = false;
              }else{
                $check = true;
              }
            }
          }

          $ex_course = SubscriptionExCourse::where('course_id',$course)->where('subscription_id',$value->id)->first();
          if($ex_course){
            $check = false;
          }
          if($value->type==1){
            $check_course_sub = Course::where('id',$course)->where('is_sub',1)->first();
            if($check_course_sub){
              $check = true;
            }
          }
          if($check==true){
            $count_check++;
          }
        }
        if($count_check>0){
          $allowed = 'Yes';
        }
      }
    }
    return $allowed;
  }

  public function checkSubscription($course_id)
  {
    $allowed = false;
    $check_course_sub = Course::where('id',$course_id)->where('is_sub',1)->first();
    if($check_course_sub){
      $subscription = Subscription::where('subscription.status',1)->get();
      $count_check = 0;
      foreach($subscription as $key=>$value){
        $check = false;

        if($value->select_type==1){
          $in_cate_count = SubscriptionInCate::where('subscription_in_cate.subscription_id',$value->id)->count();
          if($in_cate_count == 0){
            $check = true;
          }else{
            $in_cate = SubscriptionInCate::join('course_categories_log','course_categories_log.cate_id','subscription_in_cate.cate_id')
            ->where('subscription_in_cate.subscription_id',$value->id)->where('course_categories_log.course_id',$course_id)->first();
            if($in_cate){
              $check = true;
            }else{
              $check = false;
            }
          }
        }else{
          $ex_cate_count = SubscriptionExCate::where('subscription_ex_cate.subscription_id',$value->id)->count();
          if($ex_cate_count == 0){
            $check = true;
          }else{
            $ex_cate = SubscriptionExCate::join('course_categories_log','course_categories_log.cate_id','subscription_ex_cate.cate_id')
            ->where('subscription_ex_cate.subscription_id',$value->id)->where('course_categories_log.course_id',$course_id)->first();
            if($ex_cate){
              $check = false;
            }else{
              $check = true;
            }
          }
        }

        $ex_course = SubscriptionExCourse::where('course_id',$course_id)->where('subscription_id',$value->id)->first();
        if($ex_course){
          $check = false;
        }
        if($value->type==1){
          $check_course_sub = Course::where('id',$course_id)->where('is_sub',1)->first();
          if($check_course_sub){
            $check = true;
          }
        }
        if($check==true){
          $count_check++;
        }
      }
      if($count_check>0){
        $allowed = true;
      }
    }
    return $allowed;
  }

  public function getSponsor($course_id,$user)
  {
    $data = array();
    $data_dup = array();
    $course = Course::where('id',$course_id)->first();
    if($course){
      $sponsor = CateSponsor::join('coupon_bulk_sponsor_log','coupon_bulk_sponsor_log.sponsor_id','sponsor.id')
        ->join('coupons_bulk_gen','coupons_bulk_gen.id','coupon_bulk_sponsor_log.coupons_bulk_id')
        ->select('coupons_bulk_gen.*','sponsor.id as sponsor_id','sponsor.image as sponsor_image')
        ->where('sponsor.status',1)
        ->where('coupons_bulk_gen.status',1)
        ->where('coupons_bulk_gen.coupon_type',1)
        ->where('coupons_bulk_gen.sponsor_type',1)
        ->groupby('sponsor.id');
      $sponsor->where(function ($sponsor) {
        $sponsor->where('coupons_bulk_gen.started_date','<=',Carbon::now());
        $sponsor->orWhere('coupons_bulk_gen.started_date',null);
      });
      $sponsor->where(function ($sponsor) {
        $sponsor->where('coupons_bulk_gen.end_date','>=',Carbon::now());
        $sponsor->orWhere('coupons_bulk_gen.end_date',null);
      });
      $sponsor = $sponsor->get();
      foreach($sponsor as $key=>$value){
        if($value->exclude_sale_item==1||($value->exclude_sale_item==2&&$course->is_promotion==2)){
          if($course->is_promotion==1 && $course->pro_started <= Carbon::now() && ($course->pro_end >= Carbon::now()||$course->pro_period==2)){
            $course->price = $course->pro_price;
          }
          if($value->min_spend==null||$value->min_spend==''){
            $value->min_spend=0;
          }
          if($value->max_spend==null||$value->max_spend==''){
            $value->max_spend=999999999;
          }
          if($course->price==null||$course->price==''){
            $course->price = 0;
          }
          if($course->price>=$value->min_spend&&$course->price<=$value->max_spend){
            $allowed = false;

            if($value->select_cate_option==1){
              $in_cate_count = BulkCouponInCate::where('coupons_bulk_in_cate.coupons_bulk_id',$value->id)->count();
              if($in_cate_count == 0){
                $allowed = true;
              }else{
                $in_cate = BulkCouponInCate::join('course_categories_log','course_categories_log.cate_id','coupons_bulk_in_cate.cate_id')
                ->where('coupons_bulk_in_cate.coupons_bulk_id',$value->id)->where('course_categories_log.course_id',$course->id)->first();
                if($in_cate){
                  $allowed = true;
                }else{
                  $allowed = false;
                }
              }
            }else{
              $ex_cate_count = BulkCouponExCate::where('coupons_bulk_ex_cate.coupons_bulk_id',$value->id)->count();
              if($ex_cate_count == 0){
                $allowed = true;
              }else{
                $ex_cate = BulkCouponExCate::join('course_categories_log','course_categories_log.cate_id','coupons_bulk_ex_cate.cate_id')
                ->where('coupons_bulk_ex_cate.coupons_bulk_id',$value->id)->where('course_categories_log.course_id',$course->id)->first();
                if($ex_cate){
                  $allowed = false;
                }else{
                  $allowed = true;
                }
              }
            }

            $ex_course = BulkCouponExCourse::where('course_id',$course->id)->where('coupons_bulk_id',$value->id)->first();
            if($ex_course){
              $allowed = false;
            }

            if($allowed){
              if($value->limitation_coupon==null||$value->limitation_coupon==''){
                $value->limitation_coupon=999999999;
              }
              if($value->limitation_user==null||$value->limitation_user==''){
                $value->limitation_user=999999999;
              }
              $arr = new Dummy();
              $arr['id'] = $value->sponsor_id;
              $arr['image'] = $value->sponsor_image;
              $arr['code'] = '';
              $all_code = BulkCouponCode::where('coupons_bulk_id',$value->id)->where('status',1)->orderby('id','desc')->get();
              foreach($all_code as $key_code=>$val_code){
                $count_use = UsersOrderList::join('user_order_log','user_order_log.id','user_order_list.order_id')->
                where('user_order_log.status','!=',3)->where('user_order_list.code_id',$val_code->id)->count();
                if($count_use < $value->limitation_coupon){
                  $count_user_use = UsersOrderList::join('user_order_log','user_order_log.id','user_order_list.order_id')
                  ->where('user_order_log.status','!=',3)->where('user_order_log.user_id',$user->id)->where('user_order_list.code_id',$val_code->id)->count();
                  if($count_user_use < $value->limitation_user){
                    $is_allow = false;
                    if($value->allow_email==null||$value->allow_email==''){
                      $is_allow = true;
                    }else{
                      $replace_email = str_replace(" ","",$value->allow_email);
                      $arr_email = explode(",",$replace_email);
                      foreach($arr_email as $val_e){
                        if(strpos($user->email, $val_e) !== FALSE){
                          $is_allow = true;
                        }
                      }
                    }
                    if($is_allow){
                      $arr['code'] = $val_code->code;
                    }
                  }
                }
              }
              if($arr['code']!=''){
                if(!in_array($arr['id'], $data_dup)){
                  $data_dup[] = $arr['id'];
                  array_push($data,$arr);
                }
              }
            }
          }
        }
      }
    }
    return $data;
  }

  public function couponAllow($user,$id,$type,$code)
  {
    $data = new Dummy();
    $data['allow'] = false;
    $data['coupon'] = null;
    $data['course'] = null;
    if(isset($user)&&isset($id)&&isset($type)&&isset($code)){
      if($type=='course'){
        $course = Course::where('id',$id)->first();
      }else if($type=='group'){
        $course = CourseGroup::where('id',$id)->first();
      }else if($type=='subscription'){
        $course = Subscription::where('id',$id)->first();
      }
      $coupon = BulkCouponCode::join('coupons_bulk_gen','coupons_bulk_gen.id','coupons_bulk_code_log.coupons_bulk_id')
      ->select('coupons_bulk_gen.*','coupons_bulk_code_log.id as code_id')
      ->where('coupons_bulk_code_log.status',1)
      ->where('coupons_bulk_code_log.code',$code)
      ->where('coupons_bulk_gen.status',1);
      if($type=='course'){
        $coupon->where('coupons_bulk_gen.coupon_type',1);
      }else if($type=='group'){
        $coupon->where('coupons_bulk_gen.coupon_type',3);
      }else if($type=='subscription'){
        $coupon->where('coupons_bulk_gen.coupon_type',2);
      }
      $coupon->where(function ($coupon) {
        $coupon->where('coupons_bulk_gen.started_date','<=',Carbon::now());
        $coupon->orWhere('coupons_bulk_gen.started_date',null);
      });
      $coupon->where(function ($coupon) {
        $coupon->where('coupons_bulk_gen.end_date','>=',Carbon::now());
        $coupon->orWhere('coupons_bulk_gen.end_date',null);
      });
      $coupon = $coupon->first();
      if($coupon&&$course){
        $data['coupon'] = $coupon;
        $data['course'] = $course;
        if($coupon->coupon_type!=1){
          $course->is_promotion=2;
          $course->pro_price=0;
        }
        if($coupon->exclude_sale_item==1||$coupon->coupon_type==2||$coupon->coupon_type==3||($coupon->coupon_type==1&&$coupon->exclude_sale_item==2&&$course->is_promotion==2)){
          if($course->is_promotion==1 && $course->pro_started <= Carbon::now() && ($course->pro_end >= Carbon::now()||$course->pro_period==2)){
            $course->price = $course->pro_price;
          }
          if($coupon->min_spend==null||$coupon->min_spend==''){
            $coupon->min_spend=0;
          }
          if($coupon->max_spend==null||$coupon->max_spend==''){
            $coupon->max_spend=999999999;
          }
          if($course->price==null||$course->price==''){
            $course->price = 0;
          }
          if($course->price>=$coupon->min_spend&&$course->price<=$coupon->max_spend){
            $allowed = false;

            if($coupon->coupon_type==1){
              if($coupon->select_cate_option==1){
                $in_cate_count = BulkCouponInCate::where('coupons_bulk_in_cate.coupons_bulk_id',$coupon->id)->count();
                if($in_cate_count == 0){
                  $allowed = true;
                }else{
                  $in_cate = BulkCouponInCate::join('course_categories_log','course_categories_log.cate_id','coupons_bulk_in_cate.cate_id')
                  ->where('coupons_bulk_in_cate.coupons_bulk_id',$coupon->id)->where('course_categories_log.course_id',$course->id)->first();
                  if($in_cate){
                    $allowed = true;
                  }else{
                    $allowed = false;
                  }
                }
              }else{
                $ex_cate_count = BulkCouponExCate::where('coupons_bulk_ex_cate.coupons_bulk_id',$coupon->id)->count();
                if($ex_cate_count == 0){
                  $allowed = true;
                }else{
                  $ex_cate = BulkCouponExCate::join('course_categories_log','course_categories_log.cate_id','coupons_bulk_ex_cate.cate_id')
                  ->where('coupons_bulk_ex_cate.coupons_bulk_id',$coupon->id)->where('course_categories_log.course_id',$course->id)->first();
                  if($ex_cate){
                    $allowed = false;
                  }else{
                    $allowed = true;
                  }
                }
              }
              $ex_course = BulkCouponExCourse::where('course_id',$course->id)->where('coupons_bulk_id',$coupon->id)->first();
              if($ex_course){
                $allowed = false;
              }
            }else if($coupon->coupon_type==2){
              $in_cate_count = BulkCouponSub::where('coupon_bulk_sub.bulk_id',$coupon->id)->count();
              if($in_cate_count == 0){
                $allowed = true;
              }else{
                $in_cate = BulkCouponSub::join('subscription','subscription.id','coupon_bulk_sub.sub_id')
                ->where('coupon_bulk_sub.bulk_id',$coupon->id)->where('subscription.id',$course->id)->first();
                if($in_cate){
                  $allowed = true;
                }else{
                  $allowed = false;
                }
              }
            }else if($coupon->coupon_type==3){
              $in_cate_count = BulkCouponGroup::where('coupon_bulk_group.bulk_id',$coupon->id)->count();
              if($in_cate_count == 0){
                $allowed = true;
              }else{
                $in_cate = BulkCouponGroup::join('course_group','course_group.id','coupon_bulk_group.group_id')
                ->where('coupon_bulk_group.bulk_id',$coupon->id)->where('course_group.id',$course->id)->first();
                if($in_cate){
                  $allowed = true;
                }else{
                  $allowed = false;
                }
              }
            }

            if($allowed){
              if($coupon->limitation_coupon==null||$coupon->limitation_coupon==''){
                $coupon->limitation_coupon=999999999;
              }
              if($coupon->limitation_user==null||$coupon->limitation_user==''){
                $coupon->limitation_user=999999999;
              }
              $count_use = UsersOrderList::join('user_order_log','user_order_log.id','user_order_list.order_id')->
              where('user_order_log.status','!=',3)->where('user_order_list.code_id',$coupon->code_id)->count();
              if($count_use < $coupon->limitation_coupon){
                $count_user_use = UsersOrderList::join('user_order_log','user_order_log.id','user_order_list.order_id')
                ->where('user_order_log.status','!=',3)->where('user_order_log.user_id',$user->id)->where('user_order_list.code_id',$coupon->code_id)->count();
                if($count_user_use < $coupon->limitation_user){
                  $is_allow = false;
                  if($coupon->allow_email==null||$coupon->allow_email==''){
                    $is_allow = true;
                  }else{
                    $replace_email = str_replace(" ","",$coupon->allow_email);
                    $arr_email = explode(",",$replace_email);
                    foreach($arr_email as $val_e){
                      if(strpos($user->email, $val_e) !== FALSE){
                        $is_allow = true;
                      }
                    }
                  }
                  if($is_allow){
                    $data['allow'] = true;
                  }
                }
              }
            }
          }
        }
      }
    }
    return $data;
  }

  public function couponAllowWeb($user,$code)
  {
    $data = new Dummy();
    $data['allow'] = false;
    $data['coupon'] = null;
    $data['amount'] = 0;
    if(isset($user)&&isset($code)){
      $coupon = CouponWebCode::join('coupon_web','coupon_web.id','coupon_web_code.coupons_bulk_id')
      ->select('coupon_web.*','coupon_web_code.id as code_id')
      ->where('coupon_web_code.status',1)
      ->where('coupon_web_code.code',$code)
      ->where('coupon_web.status',1);
      $coupon->where(function ($coupon) {
        $coupon->where('coupon_web.started_date','<=',Carbon::now());
        $coupon->orWhere('coupon_web.started_date',null);
      });
      $coupon->where(function ($coupon) {
        $coupon->where('coupon_web.end_date','>=',Carbon::now());
        $coupon->orWhere('coupon_web.end_date',null);
      });
      $coupon = $coupon->first();
      if($coupon){
        $data['coupon'] = $coupon;
        if($coupon->min_spend==null||$coupon->min_spend==''){
          $coupon->min_spend=0;
        }
        if($coupon->max_spend==null||$coupon->max_spend==''){
          $coupon->max_spend=999999999;
        }

        $cart_course = UsersCartLog::join('course', 'course.id', 'user_cart_log.course_id')
        ->join('user', 'user.id', 'user_cart_log.user_id')
        ->where('user_cart_log.course_id','!=',null)->where('user_cart_log.user_id', $user->id)
        ->select('user_cart_log.code_id', 'user_cart_log.discount_price', 'user_cart_log.id'
        , 'user_cart_log.created_at', 'user_cart_log.course_id','course.is_promotion','course.pro_started','course.pro_end','course.pro_period'
        , 'course.image_th as image', 'course.title_th as title', 'course.details_th as description', 'course.price', 'course.pro_price')
        ->get();
  
        $cart_group = UsersCartLog::join('course_group', 'course_group.id', 'user_cart_log.group_id')
        ->join('user', 'user.id', 'user_cart_log.user_id')
        ->where('user_cart_log.group_id','!=',null)->where('user_cart_log.user_id', $user->id)
        ->select('user_cart_log.code_id', 'user_cart_log.discount_price', 'user_cart_log.id'
        , 'user_cart_log.created_at', 'user_cart_log.group_id','course_group.thumb as image'
        , 'course_group.title', 'course_group.details as description','course_group.price')
        ->get();

        $cart_subscription = UsersCartLog::join('subscription', 'subscription.id', 'user_cart_log.subscription_id')
        ->join('user', 'user.id', 'user_cart_log.user_id')
        ->where('user_cart_log.subscription_id','!=',null)->where('user_cart_log.user_id', $user->id)
        ->select('user_cart_log.code_id', 'user_cart_log.discount_price', 'user_cart_log.id'
        , 'user_cart_log.created_at', 'user_cart_log.subscription_id','subscription.thumb as image'
        , 'subscription.title', 'subscription.remark as description','subscription.price')
        ->get();
  
        $cart_price = 0;
        $data_course = array();
        $allow_course_id = array();
        foreach($cart_course as $key_course=>$val_course){
          array_push($allow_course_id,$val_course->course_id);
          $cart_course[$key_course]['type'] = 'course';
          if($val_course->is_promotion == 1 && $val_course->pro_started <= Carbon::now() && ($val_course->pro_end >= Carbon::now()||$val_course->pro_period==2)){
            $val_course->price = $val_course->pro_price;
          }
          if($val_course->discount_price!=null && $val_course->discount_price!=''){
            $val_course->price = $val_course->price - $val_course->discount_price;
          }
          $cart_price += $val_course->price;
          array_push($data_course,$val_course);
        }
        foreach($cart_group as $key_group=>$val_group){
          $cart_group[$key_group]['type'] = 'group';
          if($val_group->discount_price!=null && $val_group->discount_price!=''){
            $val_group->price = $val_group->price - $val_group->discount_price;
          }
          $cart_price += $val_group->price;
          array_push($data_course,$val_group);
        }
        foreach($cart_subscription as $key_subscription=>$val_subscription){
          $cart_subscription[$key_subscription]['type'] = 'subscription';
          if($val_subscription->discount_price!=null && $val_subscription->discount_price!=''){
            $val_subscription->price = $val_subscription->price - $val_subscription->discount_price;
          }
          $cart_price += $val_subscription->price;
          array_push($data_course,$val_subscription);
        }
        $data['amount'] = $cart_price;
        if($cart_price>=$coupon->min_spend&&$cart_price<=$coupon->max_spend){
          $allow_condition = false;
          if($coupon->condition_type==1){
            $allow_condition = true;
          }else if($coupon->condition_type==2){
            $count_allow = CouponWebInclude::where('coupon_web_id',$coupon->id)->whereIn('course_id',$allow_course_id)->count();
            if($count_allow>0 && $count_allow==count($allow_course_id)){
              $allow_condition = true;
            }
          }else if($coupon->condition_type==3){
            $count_allow = CouponWebExclude::where('coupon_web_id',$coupon->id)->whereIn('course_id',$allow_course_id)->count();
            if($count_allow==0){
              $allow_condition = true;
            }
          }
          if($allow_condition){
            if($coupon->limitation_coupon==null||$coupon->limitation_coupon==''){
              $coupon->limitation_coupon=999999999;
            }
            if($coupon->limitation_user==null||$coupon->limitation_user==''){
              $coupon->limitation_user=999999999;
            }
            $count_use = UsersOrderLog::where('user_order_log.status','!=',3)->where('user_order_log.web_code_id',$coupon->code_id)->count();
            if($count_use < $coupon->limitation_coupon){
              $count_user_use = UsersOrderLog::where('user_order_log.status','!=',3)->where('user_order_log.user_id',$user->id)->where('user_order_log.web_code_id',$coupon->code_id)->count();
              if($count_user_use < $coupon->limitation_user){
                $is_allow = false;
                if($coupon->allow_email==null||$coupon->allow_email==''){
                  $is_allow = true;
                }else{
                  $replace_email = str_replace(" ","",$coupon->allow_email);
                  $arr_email = explode(",",$replace_email);
                  foreach($arr_email as $val_e){
                    if(strpos($user->email, $val_e) !== FALSE){
                      $is_allow = true;
                    }
                  }
                }
                if($is_allow){
                  $data['allow'] = true;
                }
              }
            }
          }
        }
      }
    }
    return $data;
  }

  public function addCertificateLog($graduate)
  {
    $user = User::where('id',$graduate->user_id)->where('user.status',1)->first();
    if($user){
      $cert_course = DynamicCert::join('course','course.id','cert_dynamic.course_id')
      ->join('user_graduate_log','user_graduate_log.course_id','course.id')
      ->select('cert_dynamic.id')
      ->where('cert_dynamic.status',1)->where('course.status', 1)->where('course.is_cert', 1)
      ->where('user_graduate_log.user_id',$user->id)
      ->where('course.started_date','<=',Carbon::now())
      ->where('user_graduate_log.id',$graduate->id)
      ->where('user_graduate_log.course_id',$graduate->course_id)
      ->orderby('user_graduate_log.created_at','desc');
      $cert_course->where(function ($cert_course) {
        $cert_course->where('course.end_date','>=',Carbon::now());
        $cert_course->orWhere('course.end_date',null);
      });

      //กั้นห้อง
      // $cert_course->leftjoin('course_department_log','course_department_log.course_id','course.id')
      // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
      // ->where(function ($cert_course) use($user) {
      //   $cert_course->where('categories_department.id',$user->dept_id);
      //   $cert_course->orWhere('course.is_option',1);
      // })
      // ->where(function ($cert_course){
      //   $cert_course->where('categories_department.status',1);
      //   $cert_course->orWhere('course.is_option',1);
      // });

      $cert_course = $cert_course->first();

      if($cert_course){
        //add cert
        $add = new UsersCertLog();
        $add->user_id = $user->id;
        $add->certificate_type = 1;
        $add->graduate_id = $graduate->id;
        $add->cert_id = $cert_course->id;
        $add->created_at = $graduate->created_at;
        $add->updated_at = $graduate->created_at;
        $add->save();
      }else{
        $cert_global = GlobalCert::join('cert_global_log','cert_global_log.cert_global_id','cert_global.id')
        ->join('categories_department','categories_department.id','cert_global_log.category_id')
        ->join('course_department_log','course_department_log.department','categories_department.id')
        ->join('course','course.id','course_department_log.course_id')
        ->join('user_graduate_log','user_graduate_log.course_id','course.id')
        ->select('cert_global.id')
        ->where('cert_global.status',1)->where('course.status', 1)->where('course.is_cert', 1)
        // ->where('categories_department.status',1)
        ->where('user_graduate_log.user_id',$user->id)
        ->where('user_graduate_log.id',$graduate->id)
        ->where('user_graduate_log.course_id',$graduate->course_id)
        ->where('course.started_date','<=',Carbon::now())
        ->orderby('user_graduate_log.created_at','desc');
        $cert_global->where(function ($cert_global) {
          $cert_global->where('course.end_date','>=',Carbon::now());
          $cert_global->orWhere('course.end_date',null);
        });

        //กั้นห้อง
        // $cert_global->where(function ($cert_global) use($user) {
        //   $cert_global->where('categories_department.id',$user->dept_id);
        //   $cert_global->orWhere('course.is_option',1);
        // })
        // ->where(function ($cert_global){
        //   $cert_global->where('categories_department.status',1);
        //   $cert_global->orWhere('course.is_option',1);
        // });

        $cert_global = $cert_global->first();
        if($cert_global){
          //add global
          $add = new UsersCertLog();
          $add->user_id = $user->id;
          $add->certificate_type = 2;
          $add->graduate_id = $graduate->id;
          $add->global_cert_id = $cert_global->id;
          $add->created_at = $graduate->created_at;
          $add->updated_at = $graduate->created_at;
          $add->save();
        }else{
          $cert_group = DynamicCertGroup::join('course_group','course_group.id','cert_group_dynamic.series_id')
          ->join('group_log','group_log.group_id','course_group.id')
          ->join('course','course.id','group_log.course_id')
          ->join('user_graduate_log','user_graduate_log.course_id','course.id')
          ->select('cert_group_dynamic.id','group_log.group_id')
          ->where('cert_group_dynamic.status',1)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
          ->where('course.started_date','<=',Carbon::now())
          ->where('user_graduate_log.user_id',$user->id)
          ->where('user_graduate_log.id',$graduate->id)
          ->where('user_graduate_log.course_id',$graduate->course_id)
          ->orderby('user_graduate_log.created_at','desc')->groupby('course_group.id');
          $cert_group->where(function ($cert_group) {
            $cert_group->where('course.end_date','>=',Carbon::now());
            $cert_group->orWhere('course.end_date',null);
          });

          //กั้นห้อง
          // $cert_group->leftjoin('course_department_log','course_department_log.course_id','course.id')
          // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
          // ->where(function ($cert_group) use($user) {
          //   $cert_group->where('categories_department.id',$user->dept_id);
          //   $cert_group->orWhere('course.is_option',1);
          // })
          // ->where(function ($cert_group){
          //   $cert_group->where('categories_department.status',1);
          //   $cert_group->orWhere('course.is_option',1);
          // });

          $cert_group = $cert_group->first();

          if($cert_group){
            $check_fail = 0;
            $group_data = CourseGroup::join('group_log','group_log.group_id','course_group.id')->join('course','course.id','group_log.course_id')
            ->select('course.id')->where('course_group.id',$cert_group->group_id)->where('course.status', 1)->where('course.is_cert', 1)->where('course_group.status', 1)
            ->where('course.started_date','<=',Carbon::now());
            $group_data->where(function ($group_data) {
              $group_data->where('course.end_date','>=',Carbon::now());
              $group_data->orWhere('course.end_date',null);
            });

            //กั้นห้อง
            // $group_data->leftjoin('course_department_log','course_department_log.course_id','course.id')
            // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
            // ->where(function ($group_data) use($user) {
            //   $group_data->where('categories_department.id',$user->dept_id);
            //   $group_data->orWhere('course.is_option',1);
            // })
            // ->where(function ($group_data){
            //   $group_data->where('categories_department.status',1);
            //   $group_data->orWhere('course.is_option',1);
            // });
            $group_data = $group_data->get();
            foreach($group_data as $sub){
              $check_pass = UserGraduateLog::where('user_id',$user->id)->where('course_id',$sub->id)->first();
              if(!$check_pass){
                $check_fail++;
                break;
              }
            }
            if($check_fail==0 && count($group_data)>0){
              //add group
              $add = new UsersCertLog();
              $add->user_id = $user->id;
              $add->certificate_type = 3;
              $add->graduate_id = $graduate->id;
              $add->group_cert_id = $cert_group->id;
              $add->created_at = $graduate->created_at;
              $add->updated_at = $graduate->created_at;
              $add->save();
            }
          }
        }
      }
  
    }
  }

  public function userExportCourse($user,$id)
  {
    $data = null;
    if(isset($user)&&isset($id)){
      $course = Course::select('id','title_th','receive_point','is_cme','is_cert')->where('id',$id)->first();
      if($course){
        $dummy = new Dummy();
        $dummy['course_name'] = $course->title_th;
        $dummy['point'] = '0';
        $dummy['graduated'] = 'No';
        $dummy['certificate'] = 'No';
        $dummy['percent'] = '0%';
        if($course->is_cme==1&&$course->receive_point!=0){
          $dummy['point'] = $course->receive_point;
        }
        $graduate = UserGraduateLog::where('user_id',$user->id)->where('course_id',$course->id)->first();
        if($graduate){
          $dummy['graduated'] = 'Yes';
        }
        $cert_course = DynamicCert::join('course','course.id','cert_dynamic.course_id')
        ->select('cert_dynamic.id')
        ->where('cert_dynamic.status',1)->where('course.status', 1)->where('course.is_cert', 1)
        ->where('course.started_date','<=',Carbon::now())
        ->where('course.id',$course->id);
        $cert_course->where(function ($cert_course) {
          $cert_course->where('course.end_date','>=',Carbon::now());
          $cert_course->orWhere('course.end_date',null);
        });
        //กั้นห้อง
        // $cert_course->leftjoin('course_department_log','course_department_log.course_id','course.id')
        // ->leftjoin('categories_department','categories_department.id','course_department_log.department')
        // ->where(function ($cert_course) use($user) {
        //   $cert_course->where('categories_department.id',$user->dept_id);
        //   $cert_course->orWhere('course.is_option',1);
        // })
        // ->where(function ($cert_course){
        //   $cert_course->where('categories_department.status',1);
        //   $cert_course->orWhere('course.is_option',1);
        // });
        $cert_course = $cert_course->first();
        if($cert_course){
          $dummy['certificate'] = 'Yes';
        }else{
          $cert_global = GlobalCert::join('cert_global_log','cert_global_log.cert_global_id','cert_global.id')
          ->join('categories_department','categories_department.id','cert_global_log.category_id')
          ->join('course_department_log','course_department_log.department','categories_department.id')
          ->join('course','course.id','course_department_log.course_id')
          ->select('cert_global.id')
          ->where('cert_global.status',1)->where('course.status', 1)->where('course.is_cert', 1)
          ->where('course.id',$course->id)
          ->where('course.started_date','<=',Carbon::now());
          $cert_global->where(function ($cert_global) {
            $cert_global->where('course.end_date','>=',Carbon::now());
            $cert_global->orWhere('course.end_date',null);
          });
          //กั้นห้อง
          // $cert_global->where(function ($cert_global) use($user) {
          //   $cert_global->where('categories_department.id',$user->dept_id);
          //   $cert_global->orWhere('course.is_option',1);
          // })
          // ->where(function ($cert_global){
          //   $cert_global->where('categories_department.status',1);
          //   $cert_global->orWhere('course.is_option',1);
          // });
          $cert_global = $cert_global->first();
          if($cert_global){
            $dummy['certificate'] = 'Yes';
          }
        }
        $count_lesson = CourseLessonLog::where('status',1)->where('course_id',$course->id)->count();
        $count_learn_log = UsersLearningLog::join('course_lesson','course_lesson.id','user_learning_log.lesson_id')
        ->where('course_lesson.status',1)
        ->where('course_lesson.course_id',$course->id)
        ->where('user_learning_log.user_id',$user->id)
        ->where('user_learning_log.course_id', $course->id)->get();
        $count_learn = 0;
        foreach($count_learn_log as $val_l){
          $count_learn+=$val_l->watch_percent;
        }
        if($count_learn!=0&&$count_lesson!=0){
          $dummy['percent'] = number_format($count_learn/$count_lesson).'%';
        }

        $data = $dummy;
      }
    }
    return $data;
  }
}
