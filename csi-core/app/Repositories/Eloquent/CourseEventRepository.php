<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\Course;
use App\Repositories\Interfaces\CourseEventRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class CourseEventRepository implements CourseEventRepositoryInterface
{
    protected $model;

    public function __construct(Course $model)
    {
        $this->model = $model;
    }

    public function all(int $limit = 8): Collection
    {
        return $this->model->where('course.trailer_media', 5)
            ->select('course.*')
            ->orderBy('course.position', 'asc')
            ->limit($limit)
            ->get();
    }

    public function count(): int
    {
        return $this->model->where('course.trailer_media', 5)->count();
    }

    public function find(int $id): ?Model
    {
        return $this->model->where('course.trailer_media', 5)
            ->where('course.id', $id)
            ->select('course.*')
            ->first();
    }

    public function create(array $data): Model
    {
        $data['trailer_media'] = 5;
        
        return $this->model->create($data);
    }

    public function update(int $id, array $data): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        $data['trailer_media'] = 5;
        
        return $model->update($data);
    }

    public function delete(int $id): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        return $model->delete();
    }

    public function updateStatus(int $id): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        $newStatus = $model->status == 1 ? 2 : 1;
        return $model->update(['status' => $newStatus]);
    }

    public function updatePosition(int $id, int $position): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        return $model->update(['position' => $position]);
    }

    public function getCourseEventWithRelations(int $limit = 8): Collection
    {
        return $this->all($limit);
    }
}
