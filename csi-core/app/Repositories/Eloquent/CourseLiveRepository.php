<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\Course;
use App\Repositories\Interfaces\CourseLiveRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class CourseLiveRepository implements CourseLiveRepositoryInterface
{
    protected $model;

    public function __construct(Course $model)
    {
        $this->model = $model;
    }

    public function all(int $limit = 8): Collection
    {
        return $this->model
            ->where('course.trailer_media', 2)
            ->orderBy('course.position', 'asc')
            ->limit($limit)
            ->get();
    }

    public function count(): int
    {
        return $this->model
            ->where('course.trailer_media', 2)
            ->count();
    }

    public function find(int $id): ?Model
    {
        return $this->model
            ->where('course.trailer_media', 2)
            ->where('course.id', $id)
            ->first();
    }

    public function create(array $data): Model
    {
        $data['trailer_media'] = 2;
        $data['international'] = 1;
        
        return $this->model->create($data);
    }

    public function update(int $id, array $data): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        $data['trailer_media'] = 2;
        $data['international'] = 1;
        
        return $model->update($data);
    }

    public function delete(int $id): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        return $model->delete();
    }

    public function updateStatus(int $id): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        $model->status = $model->status == '1' ? '2' : '1';
        return $model->save();
    }

    public function updatePosition(int $id, int $position): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        $model->position = $position;
        return $model->save();
    }

    public function getCourseLiveWithRelations(int $limit = 8): Collection
    {
        return $this->model->where('trailer_media', 2)
            ->where('international', 1)
            ->with(['categories', 'speakers', 'tags'])
            ->orderBy('position', 'asc')
            ->limit($limit)
            ->get();
    }
}
