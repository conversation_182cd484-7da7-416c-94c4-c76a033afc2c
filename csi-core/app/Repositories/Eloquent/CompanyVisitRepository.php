<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\Course;
use App\Repositories\Interfaces\CompanyVisitRepositoryInterface;

class CompanyVisitRepository implements CompanyVisitRepositoryInterface
{
    public function all(int $limit = 8)
    {
        return Course::where('course.trailer_media', 4)
            ->select('course.*')
            ->orderBy('course.position', 'ASC')
            ->limit($limit)
            ->get();
    }

    public function count(): int
    {
        return Course::where('course.trailer_media', 4)
            ->count();
    }

    public function find($id)
    {
        return Course::where('course.trailer_media', 4)
            ->where('course.id', $id)
            ->select('course.*')
            ->first();
    }

    public function create(array $data): Course
    {
        $data['trailer_media'] = 4;
        
        return Course::create($data);
    }

    public function update($id, array $data): ?Course
    {
        $course = $this->find($id);
        if ($course) {
            $course->update($data);
            return $course;
        }
        return null;
    }

    public function delete($id): bool
    {
        $course = $this->find($id);
        if ($course) {
            return $course->delete();
        }
        return false;
    }

    public function updateStatus($id): bool
    {
        $course = $this->find($id);
        if ($course) {
            $newStatus = $course->status == 1 ? 2 : 1;
            return $course->update(['status' => $newStatus]);
        }
        return false;
    }

    public function updatePosition($id, $position): bool
    {
        $course = $this->find($id);
        if ($course) {
            return $course->update(['position' => $position]);
        }
        return false;
    }

    public function getCompanyVisitWithRelations(int $limit = 8)
    {
        return Course::where('course.trailer_media', 4)
            ->select('course.*')
            ->with(['categories', 'speakers', 'levels'])
            ->orderBy('course.position', 'ASC')
            ->limit($limit)
            ->get();
    }
}
