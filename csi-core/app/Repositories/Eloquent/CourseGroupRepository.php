<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\CourseGroup;
use App\Repositories\Interfaces\CourseGroupRepositoryInterface;

class CourseGroupRepository implements CourseGroupRepositoryInterface
{
    protected $model;

    public function __construct(CourseGroup $model)
    {
        $this->model = $model;
    }

    public function findActiveGroup(int $groupId)
    {
        return $this->model->where('id', $groupId)
            ->where('status', 1)
            ->first();
    }

    public function findGroupCourses(int $groupId, int $limit = 8)
    {
        return \App\Models\Course::select([
            'course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set',
            'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end',
            'course.pro_period', 'course.price', 'course.started_date',
            'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th',
            'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th',
            'course.duration_time', 'course.created_at', 'course.course_duration',
            'course.trailer_media',
        ])
        ->join('course_group_log', 'course_group_log.course_id', 'course.id')
        ->where('course_group_log.group_id', $groupId)
        ->where('course.status', 1)
        ->orderBy('course_group_log.position', 'asc')
        ->limit($limit)
        ->get();
    }
}
