<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\Subscription;
use App\Models\Core\SubscriptionMain;
use App\Repositories\Interfaces\SubscriptionMainRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class SubscriptionMainRepository implements SubscriptionMainRepositoryInterface
{
    protected $model;

    public function __construct(SubscriptionMain $model)
    {
        $this->model = $model;
    }

    public function all(): Collection
    {
        return $this->model->orderBy('position', 'asc')->get();
    }

    public function count(): int
    {
        return $this->model->count();
    }

    public function find(int $id): ?Model
    {
        return $this->model->find($id);
    }

    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    public function update(int $id, array $data): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }
        return $model->update($data);
    }

    public function delete(int $id): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        if ($model->cover_image && file_exists(public_path($model->cover_image))) {
            unlink(public_path($model->cover_image));
        }

        return $model->delete();
    }

    public function updateStatus(int $id): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        $model->status = $model->status == '1' ? '0' : '1';
        return $model->save();
    }

    public function updatePosition(int $id, int $position): bool
    {
        $model = $this->find($id);
        if (!$model) {
            return false;
        }

        $model->position = $position;
        return $model->save();
    }

    public function getWithSubscriptions(): Collection
    {
        return $this->model->with(['subscriptions'])
            ->orderBy('position', 'asc')
            ->get();
    }

    public function updateSubscriptionLinks(int $mainId, ?array $subscriptionIds): void
    {
        $this->removeSubscriptionLinks($mainId);

        if (is_array($subscriptionIds) && !empty($subscriptionIds)) {
            Subscription::whereIn('id', $subscriptionIds)
                ->update(['subscription_main_id' => $mainId]);
        }
    }

    public function removeSubscriptionLinks(int $mainId): void
    {
        Subscription::where('subscription_main_id', $mainId)
            ->update(['subscription_main_id' => null]);
    }
}