<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\Course;
use App\Repositories\Interfaces\CourseRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;

class CourseRepository implements CourseRepositoryInterface
{
    protected $model;

    public function __construct(Course $model)
    {
        $this->model = $model;
    }

    protected function getBaseSelect(): array
    {
        return [
            'course.is_doc', 'course.doc_text', 'course.is_time', 'course.time_set',
            'course.is_promotion', 'course.pro_price', 'course.pro_started', 'course.pro_end',
            'course.pro_period', 'course.price', 'course.started_date',
            'course.rate', 'course.started_learning', 'course.receive_point', 'course.title_th',
            'course.id', 'course.slug', 'course.image_th', 'course.subtitle_th',
            'course.duration_time', 'course.created_at', 'course.course_duration',
            'course.trailer_media',
        ];
    }

    protected function buildBaseQuery()
    {
        return $this->model->select($this->getBaseSelect());
    }

    protected function applyCommonFilters($query, $filters = [])
    {
        if (!empty($filters['department'])) {
            $query->join('course_department_log', 'course_department_log.course_id', 'course.id')
                ->whereIn('course_department_log.department', explode(',', $filters['department']));
        }

        if (!empty($filters['learner'])) {
            $query->join('course_learner_log', 'course_learner_log.course_id', 'course.id')
                ->whereIn('course_learner_log.learner', explode(',', $filters['learner']));
        }

        if (!empty($filters['speaker'])) {
            $query->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id')
                ->whereIn('course_speaker_log.speaker', explode(',', $filters['speaker']));
        }

        if (!empty($filters['category'])) {
            $query->join('course_categories_log', 'course_categories_log.course_id', 'course.id')
                ->whereIn('course_categories_log.cate_id', explode(',', $filters['category']));
        }

        if (!empty($filters['level'])) {
            $query->whereIn('course.level', explode(',', $filters['level']));
        }
    }

    protected function applyCustomFilters($query, $filters)
    {
        if (!empty($filters['department'])) {
            $query->join('course_department_log', 'course_department_log.course_id', 'course.id')
                ->whereIn('course_department_log.department', explode(',', $filters['department']));
        }

        if (!empty($filters['learner'])) {
            $query->join('course_learner_log', 'course_learner_log.course_id', 'course.id')
                ->whereIn('course_learner_log.learner', explode(',', $filters['learner']));
        }

        if (!empty($filters['speaker'])) {
            $query->join('course_speaker_log', 'course_speaker_log.course_id', 'course.id')
                ->whereIn('course_speaker_log.speaker', explode(',', $filters['speaker']));
        }

        if (!empty($filters['category'])) {
            $query->join('course_categories_log', 'course_categories_log.course_id', 'course.id')
                ->whereIn('course_categories_log.cate_id', explode(',', $filters['category']));
        }

        if (!empty($filters['level'])) {
            $query->whereIn('course.level', explode(',', $filters['level']));
        }
    }

    public function findCoursesWithFilters(array $filters, int $limit = 8): Collection
    {
        $query = $this->buildBaseQuery()
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->where(function ($q) {
                $q->where('course.started_learning', '<=', Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                    ->orWhereNull('course.end_date');
            });

        $this->applyCommonFilters($query, $filters);

        return $query->groupBy('course.id')
            ->orderBy('course.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findFreeCourses(int $limit = 8): Collection
    {
        return $this->model->select($this->getBaseSelect())
            ->where('course.status', 1)
            ->where('course.is_free', 1)
            ->orderBy('course.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findSuggestedCourses(int $limit = 8): Collection
    {
        return $this->model->select($this->getBaseSelect())
            ->where('course.status', 1)
            ->where('course.is_suggestion', 1)
            ->orderBy('course.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findLatestCourses(int $limit = 8): Collection
    {
        return $this->model->select($this->getBaseSelect())
            ->where('course.status', 1)
            ->orderBy('course.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findNewCourses(int $limit = 8): Collection
    {
        return $this->model->select($this->getBaseSelect())
            ->where('course.status', 1)
            ->orderBy('course.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findUpcomingCourses(int $limit = 8): Collection
    {
        $query = $this->model->select($this->getBaseSelect())
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.started_learning', '>=', Carbon::now())
            ->where('course.status', 1)
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                    ->orWhereNull('course.end_date');
            });

        return $query->orderBy('course.started_learning', 'asc')
            ->limit($limit)
            ->get();
    }

    public function findUserRecommendedCourses(int $userId, int $limit = 8): Collection
    {
        // Get user type first
        $user = \App\Models\User::find($userId);
        if (!$user) {
            return collect();
        }

        return $this->model->select($this->getBaseSelect())
            ->join('course_learner_log', 'course_learner_log.course_id', 'course.id')
            ->where('course_learner_log.learner', $user->user_type)
            ->where('course.started_date', '<=', \Carbon\Carbon::now())
            ->where('course.status', 1)
            ->where(function ($q) {
                $q->where('course.started_learning', '<=', \Carbon\Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where(function ($q) {
                $q->where('course.end_date', '>=', \Carbon\Carbon::now())
                    ->orWhereNull('course.end_date');
            })
            ->groupBy('course.id')
            ->limit($limit)
            ->get();
    }

    public function findUserFavoriteCourses(int $userId, int $limit = 8): Collection
    {
        return $this->model->select($this->getBaseSelect())
            ->join('user_favorite_log', 'user_favorite_log.course_id', 'course.id')
            ->where('user_favorite_log.user_id', $userId)
            ->where('course.started_date', '<=', \Carbon\Carbon::now())
            ->where('course.status', 1)
            ->where(function ($q) {
                $q->where('course.started_learning', '<=', \Carbon\Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where(function ($q) {
                $q->where('course.end_date', '>=', \Carbon\Carbon::now())
                    ->orWhereNull('course.end_date');
            })
            ->groupBy('course.id')
            ->limit($limit)
            ->get();
    }

    public function findPopularCoursesManual(int $contentId, int $contentType, int $limit = 8): Collection
    {
        return $this->model->select($this->getBaseSelect())
            ->join('popular_courses_manual', 'popular_courses_manual.course_id', 'course.id')
            ->where('popular_courses_manual.content_id', $contentId)
            ->where('popular_courses_manual.content_type', $contentType)
            ->where('course.status', 1)
            ->orderBy('popular_courses_manual.position', 'asc')
            ->limit($limit)
            ->get();
    }

    public function findTopCoursesByViews(array $excludeIds, Carbon $startDay, int $limit): Collection
    {
        $query = $this->model->select($this->getBaseSelect())
            ->join('course_analytics', 'course_analytics.course_id', 'course.id')
            ->where('course.status', 1)
            ->where('course_analytics.created_at', '>=', $startDay);

        if (!empty($excludeIds)) {
            $query->whereNotIn('course.id', $excludeIds);
        }

        return $query->groupBy('course.id')
            ->orderByRaw('SUM(course_analytics.views) DESC')
            ->limit($limit)
            ->get();
    }

    public function findTopCourses(int $limit = 8): Collection
    {
        return $this->model->select($this->getBaseSelect())
            ->where('course.status', 1)
            ->orderBy('course.rate', 'desc')
            ->orderBy('course.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findSubscriptionCourses(array $categoryIds, array $filters = [], int $limit = 8): Collection
    {
        $query = $this->buildBaseQuery();
        
        $query->join('course_categories_log', 'course_categories_log.course_id', 'course.id')
            ->whereIn('course_categories_log.cate_id', $categoryIds)
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                  ->orWhere('course.end_date', null);
            });

        $this->applyCommonFilters($query, $filters);

        return $query->groupBy('course.id')
            ->orderBy('course.started_date', 'desc')
            ->orderBy('course.id', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findCurriculumCourses(array $filters = [], int $limit = 8): Collection
    {
        $query = $this->buildBaseQuery();
        
        $query->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->where('course.is_curriculum', 1)
            ->where(function ($q) {
                $q->where('course.started_learning', '<=', Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                    ->orWhereNull('course.end_date');
            });

        $this->applyCommonFilters($query, $filters);

        return $query->where('course.trailer_media', 1)
            ->groupBy('course.id')
            ->orderBy('course.position', 'asc')
            ->limit($limit)
            ->get();
    }

    public function findPromotionCourses(array $filters = [], int $limit = 8): Collection
    {
        $query = $this->buildBaseQuery();
        
        $query->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->where('course.is_promotion', 1)
            ->where('course.pro_started', '<=', Carbon::now())
            ->where('course.pro_end', '>=', Carbon::now())
            ->where(function ($q) {
                $q->where('course.started_learning', '<=', Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                    ->orWhereNull('course.end_date');
            });

        $this->applyCustomFilters($query, $filters);

        return $query->groupBy('course.id')
            ->orderBy('course.started_date', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findUserLevelBasedCourses(int $userId, int $limit = 8): Collection
    {
        // Get user's purchased course levels
        $levelIds = $this->model->join('user_order_list', 'user_order_list.course_id', 'course.id')
            ->join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
            ->join('categories_level', 'categories_level.id', 'course.level')
            ->where('user_order_log.user_id', $userId)
            ->pluck('categories_level.id')
            ->unique()
            ->toArray();

        if (empty($levelIds)) {
            return collect();
        }

        // Get user's already purchased courses
        $excludeIds = $this->model->join('user_order_list', 'user_order_list.course_id', 'course.id')
            ->join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
            ->where('user_order_log.user_id', $userId)
            ->pluck('course.id')
            ->toArray();

        return $this->model->select($this->getBaseSelect())
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->whereIn('course.level', $levelIds)
            ->whereNotIn('course.id', $excludeIds)
            ->where(function ($q) {
                $q->where('course.started_learning', '<=', Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                    ->orWhereNull('course.end_date');
            })
            ->groupBy('course.id')
            ->limit($limit)
            ->get();
    }

    public function findUserInterestCourses(int $userId, int $limit = 8): Collection
    {
        // Get user's purchased course categories
        $categoryIds = $this->model->join('user_order_list', 'user_order_list.course_id', 'course.id')
            ->join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
            ->join('course_categories_log', 'course_categories_log.course_id', 'course.id')
            ->where('user_order_log.user_id', $userId)
            ->pluck('course_categories_log.cate_id')
            ->unique()
            ->toArray();

        if (empty($categoryIds)) {
            return collect();
        }

        // Get user's already purchased courses
        $excludeIds = $this->model->join('user_order_list', 'user_order_list.course_id', 'course.id')
            ->join('user_order_log', 'user_order_log.id', 'user_order_list.order_id')
            ->where('user_order_log.user_id', $userId)
            ->pluck('course.id')
            ->toArray();

        return $this->model->join('course_categories_log', 'course_categories_log.course_id', 'course.id')
            ->select($this->getBaseSelect())
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->whereIn('course_categories_log.cate_id', $categoryIds)
            ->whereNotIn('course.id', $excludeIds)
            ->where(function ($q) {
                $q->where('course.started_learning', '<=', Carbon::now())
                    ->orWhereNull('course.started_learning');
            })
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                    ->orWhereNull('course.end_date');
            })
            ->groupBy('course.id')
            ->limit($limit)
            ->get();
    }

    public function findLiveCourses(array $filters = [], int $limit = 8): Collection
    {
        $query = $this->model->select($this->getBaseSelect())
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->where('course.trailer_media', 2)
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                  ->orWhere('course.end_date', null);
            })
            ->orderBy('course.started_date', 'desc')
            ->limit($limit);

        $this->applyCustomFilters($query, $filters);

        return $query->get();
    }

    public function findEventCourses(array $filters = [], int $limit = 8): Collection
    {
        $query = $this->model->select($this->getBaseSelect())
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->where('course.trailer_media', 5)
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                  ->orWhere('course.end_date', null);
            })
            ->orderBy('course.started_date', 'desc')
            ->limit($limit);

        $this->applyCustomFilters($query, $filters);

        return $query->get();
    }

    public function findCompanyVisitCourses(array $filters = [], int $limit = 8): Collection
    {
        $query = $this->model->select($this->getBaseSelect())
            ->where('course.started_date', '<=', Carbon::now())
            ->where('course.status', 1)
            ->where('course.trailer_media', 4)
            ->where(function ($q) {
                $q->where('course.end_date', '>=', Carbon::now())
                  ->orWhere('course.end_date', null);
            })
            ->orderBy('course.started_date', 'desc')
            ->limit($limit);

        $this->applyCustomFilters($query, $filters);

        return $query->get();
    }
}
