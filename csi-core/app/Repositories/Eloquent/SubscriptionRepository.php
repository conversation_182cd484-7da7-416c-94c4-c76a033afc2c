<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\Subscription;
use App\Repositories\Interfaces\SubscriptionRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class SubscriptionRepository implements SubscriptionRepositoryInterface
{
    protected $model;

    public function __construct(Subscription $model)
    {
        $this->model = $model;
    }

    public function getAvailableSubscriptions(?int $mainId = null): Collection
    {
        $query = $this->model->where('status', 1);

        if ($mainId) {
            $query->where(function ($q) use ($mainId) {
                $q->whereNull('subscription_main_id')
                    ->orWhere('subscription_main_id', $mainId);
            });
        } else {
            $query->whereNull('subscription_main_id');
        }

        return $query->orderBy('position', 'asc')->get();
    }

    public function getAvailableWithoutMain(): Collection
    {
        return $this->model->where('status', 1)
            ->whereNull('subscription_main_id')
            ->orderBy('position', 'asc')
            ->get();
    }

    public function findActiveSubscriptionMain(int $subscriptionMainId)
    {
        return \App\Models\Core\SubscriptionMain::where('id', $subscriptionMainId)
            ->where('status', 1)
            ->first();
    }

    public function getAllowedCategories(int $subscriptionMainId): array
    {
        $subscriptions = $this->model->where('subscription_main_id', $subscriptionMainId)
            ->where('type', 1)
            ->where('status', 1)
            ->get();

        if ($subscriptions->isEmpty()) {
            return [];
        }

        $allowed_categories = [];
        foreach ($subscriptions as $subscription) {
            if (!empty($subscription->only_cate)) {
                $categories = explode(',', $subscription->only_cate);
                foreach ($categories as $cat_id) {
                    $cat_id = trim($cat_id);
                    if (!empty($cat_id) && is_numeric($cat_id)) {
                        $allowed_categories[] = (int)$cat_id;
                    }
                }
            }
        }

        return array_unique($allowed_categories);
    }
}