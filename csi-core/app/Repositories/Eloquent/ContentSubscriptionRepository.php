<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\Subscription;
use App\Repositories\Interfaces\ContentSubscriptionRepositoryInterface;

class ContentSubscriptionRepository implements ContentSubscriptionRepositoryInterface
{
    protected $subscriptionModel;

    public function __construct(Subscription $subscriptionModel)
    {
        $this->subscriptionModel = $subscriptionModel;
    }

    public function findActiveSubscriptionMain(int $subscriptionMainId)
    {
        return \App\Models\Core\SubscriptionMain::where('id', $subscriptionMainId)
            ->where('status', 1)
            ->first();
    }

    public function getAllowedCategories(int $subscriptionMainId): array
    {
        $subscriptions = $this->subscriptionModel->where('subscription_main_id', $subscriptionMainId)
            ->where('type', 1)
            ->where('status', 1)
            ->get();

        if ($subscriptions->isEmpty()) {
            return [];
        }

        $allowed_categories = [];
        foreach ($subscriptions as $subscription) {
            if (!empty($subscription->only_cate)) {
                $categories = explode(',', $subscription->only_cate);
                foreach ($categories as $cat_id) {
                    $cat_id = trim($cat_id);
                    if (!empty($cat_id) && is_numeric($cat_id)) {
                        $allowed_categories[] = (int)$cat_id;
                    }
                }
            }
        }

        return array_unique($allowed_categories);
    }
}
