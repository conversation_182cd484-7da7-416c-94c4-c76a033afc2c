<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\CateSpeaker;
use App\Repositories\Interfaces\CategoriesSpeakerRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection as SupportCollection;

class CategoriesSpeakerRepository implements CategoriesSpeakerRepositoryInterface
{
    protected $model;

    public function __construct(CateSpeaker $model)
    {
        $this->model = $model;
    }

    public function all(): Collection
    {
        return $this->model->orderBy('position', 'asc')->get();
    }

    public function find(int $id): ?Model
    {
        return $this->model->find($id);
    }

    public function getActive(): Collection
    {
        return $this->model->where('status', 1)
            ->orderBy('position', 'asc')
            ->get();
    }

    public function getWithPagination(int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where('status', 1)
            ->orderBy('position', 'asc')
            ->paginate($perPage);
    }

    public function searchByTitle(string $search): Collection
    {
        return $this->model->where('status', 1)
            ->where(function ($query) use ($search) {
                $query->where('title_th', 'LIKE', '%' . $search . '%')
                      ->orWhere('title_en', 'LIKE', '%' . $search . '%');
            })
            ->orderBy('position', 'asc')
            ->get();
    }

    public function getByPosition(): Collection
    {
        return $this->model->where('status', 1)
            ->orderBy('position', 'asc')
            ->orderBy('id', 'desc')
            ->get();
    }

    public function getCoursesBySpeakerId(int $speakerId): SupportCollection
    {
        return DB::table('course_speaker_log')
            ->join('course', 'course_speaker_log.course_id', '=', 'course.id')
            ->where('course_speaker_log.speaker', $speakerId)
            ->where('course.status', 1)
            ->select(
                'course.id',
                'course.title_th',
                'course.title_en',
                'course.slug',
                'course.subtitle_th',
                'course.subtitle_en',
                'course.details_th',
                'course.details_en',
                'course.image_th',
                'course.image_en',
                'course.course_duration',
                'course.price',
                'course.is_free',
                'course.created_at',
                'course.updated_at'
            )
            ->orderBy('course.created_at', 'desc')
            ->get();
    }
}
