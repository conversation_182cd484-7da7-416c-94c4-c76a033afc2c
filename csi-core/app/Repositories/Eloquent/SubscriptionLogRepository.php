<?php

namespace App\Repositories\Eloquent;

use App\Models\Core\SubscriptionLog;
use App\Repositories\Interfaces\SubscriptionLogRepositoryInterface;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubscriptionLogRepository implements SubscriptionLogRepositoryInterface
{
    protected $model;

    public function __construct(SubscriptionLog $model)
    {
        $this->model = $model;
    }

    /**
     * Get subscription logs for datatable
     * 
     * @param array $filters
     * @param string $orderBy
     * @param string $orderDirection
     * @param int $offset
     * @param int $limit
     * @return array
     */
    public function getSubscriptionLogsForDatatable(
        array $filters,
        string $orderBy,
        string $orderDirection,
        int $offset,
        int $limit
    ): array {
        $query = $this->buildQuery($filters);

        $orderByColumn = $this->mapOrderByColumn($orderBy);
        
        $results = $query
            ->orderBy($orderByColumn, $orderDirection)
            ->offset($offset)
            ->limit($limit)
            ->get();

        return $this->formatResults($results);
    }

    /**
     * Count filtered subscription logs (grouped)
     * 
     * @param array $filters
     * @return int
     */
    public function countFilteredSubscriptionLogs(array $filters): int
    {
        $subquery = $this->buildQuery($filters);
        return DB::table(DB::raw("({$subquery->toSql()}) as grouped_results"))
            ->mergeBindings($subquery)
            ->count();
    }

    /**
     * Build base query with filters and grouping
     * 
     * @param array $filters
     * @return \Illuminate\Database\Query\Builder
     */
    private function buildQuery(array $filters)
    {
        $query = DB::table('subscription_log')
            ->join('user', 'user.id', '=', 'subscription_log.user_id')
            ->join('subscription', 'subscription.id', '=', 'subscription_log.subscription_id')
            ->leftJoin('subscription_main', 'subscription_main.id', '=', 'subscription.subscription_main_id')
            ->select(
                DB::raw('GROUP_CONCAT(subscription_log.id ORDER BY subscription_log.created_at DESC) as grouped_ids'),
                'user.email',
                'user.name as u_name',
                'user.lastname as u_lastname',
                'user.mobile',
                DB::raw('COALESCE(subscription_main.title, subscription.title) as package'),
                DB::raw('SUM(subscription.period) as period_days'),
                DB::raw('MAX(subscription_log.expired) as end_date'),
                DB::raw('MIN(subscription_log.created_at) as first_created_at'),
                DB::raw('MAX(subscription_log.created_at) as latest_created_at'),
                DB::raw('COUNT(*) as subscription_count')
            )
            ->groupBy([
                'user.email',
                DB::raw('COALESCE(subscription_main.title, subscription.title)'),
                'user.name',
                'user.lastname', 
                'user.mobile'
            ]);

        // Apply filters
        foreach ($filters as $filter) {
            $query->where($filter[0], $filter[1], $filter[2]);
        }

        return $query;
    }

    /**
     * Map client column names to database columns (for grouped queries)
     * 
     * @param string $column
     * @return mixed
     */
    private function mapOrderByColumn(string $column)
    {
        $orderByMap = [
            'email' => 'user.email',
            'u_name' => 'user.name',
            'u_lastname' => 'user.lastname',
            'mobile' => 'user.mobile',
            'package' => DB::raw('COALESCE(subscription_main.title, subscription.title)'),
            'start_date' => DB::raw('MAX(subscription_log.expired) - INTERVAL SUM(subscription.period) DAY'),
            'end_date' => DB::raw('MAX(subscription_log.expired)'),
            'period_days' => DB::raw('SUM(subscription.period)'),
            'left_days' => DB::raw('DATEDIFF(MAX(subscription_log.expired), NOW())'),
            'subscription_count' => DB::raw('COUNT(*)'),
        ];

        return $orderByMap[$column] ?? DB::raw('MAX(subscription_log.created_at)');
    }

    /**
     * Format query results for datatable with grouping
     * 
     * @param \Illuminate\Support\Collection $results
     * @return array
     */
    private function formatResults($results): array
    {
        $formatted = [];

        foreach ($results as $row) {
            $startDate = null;
            if (!empty($row->end_date) && !empty($row->period_days)) {
                $startDate = Carbon::parse($row->end_date)->copy()->subDays((int) $row->period_days);
            }

            $leftDays = 0;
            if (!empty($row->end_date)) {
                $leftDays = max(0, Carbon::now()->diffInDays(Carbon::parse($row->end_date), false));
            }

            // Show package name only
            $packageDisplay = $row->package;

            $formatted[] = [
                'email' => $row->email,
                'u_name' => $row->u_name,
                'u_lastname' => $row->u_lastname,
                'mobile' => $row->mobile,
                'package' => $packageDisplay,
                'start_date' => $startDate ? $startDate->toDateTimeString() : '-',
                'end_date' => $row->end_date,
                'period_days' => $row->period_days,
                'left_days' => $leftDays,
                'subscription_count' => $row->subscription_count,
                'grouped_ids' => $row->grouped_ids,
            ];
        }

        return $formatted;
    }
}