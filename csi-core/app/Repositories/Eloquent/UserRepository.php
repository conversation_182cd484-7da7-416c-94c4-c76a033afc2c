<?php

namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Helpers\SimpleEnDeHelper;

class UserRepository implements UserRepositoryInterface
{
    protected $model;

    public function __construct(User $model)
    {
        $this->model = $model;
    }

    public function findByToken(string $token)
    {
        $userId = SimpleEnDeHelper::instance()->decryptString($token);
        return $this->model->where('id', $userId)->first();
    }
}
