<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection as SupportCollection;

interface CategoriesSpeakerRepositoryInterface
{
    public function all(): Collection;
    public function find(int $id): ?Model;
    public function getActive(): Collection;
    public function getWithPagination(int $perPage = 15): LengthAwarePaginator;
    public function searchByTitle(string $search): Collection;
    public function getByPosition(): Collection;
    public function getCoursesBySpeakerId(int $speakerId): SupportCollection;
}
