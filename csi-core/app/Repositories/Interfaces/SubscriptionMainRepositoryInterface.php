<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

interface SubscriptionMainRepositoryInterface
{
    public function all(): Collection;
    
    public function count(): int;
    
    public function find(int $id): ?Model;
    
    public function create(array $data): Model;
    
    public function update(int $id, array $data): bool;
    
    public function delete(int $id): bool;
    
    public function updateStatus(int $id): bool;
    
    public function updatePosition(int $id, int $position): bool;
    
    public function getWithSubscriptions(): Collection;
    
    public function updateSubscriptionLinks(int $mainId, ?array $subscriptionIds): void;
    
    public function removeSubscriptionLinks(int $mainId): void;
}