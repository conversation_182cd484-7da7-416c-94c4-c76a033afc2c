<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

interface CourseLiveRepositoryInterface
{
    public function all(int $limit = 8): Collection;
    
    public function count(): int;
    
    public function find(int $id): ?Model;
    
    public function create(array $data): Model;
    
    public function update(int $id, array $data): bool;
    
    public function delete(int $id): bool;
    
    public function updateStatus(int $id): bool;
    
    public function updatePosition(int $id, int $position): bool;
    
    public function getCourseLiveWithRelations(int $limit = 8): Collection;
}
