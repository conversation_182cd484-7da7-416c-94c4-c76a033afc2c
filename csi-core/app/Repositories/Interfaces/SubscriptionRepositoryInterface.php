<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;

interface SubscriptionRepositoryInterface
{
    public function getAvailableSubscriptions(?int $mainId = null): Collection;
    
    public function getAvailableWithoutMain(): Collection;
    
    public function findActiveSubscriptionMain(int $subscriptionMainId);
    
    public function getAllowedCategories(int $subscriptionMainId): array;
}