<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;

interface CourseRepositoryInterface
{
    public function findCoursesWithFilters(array $filters, int $limit = 8): Collection;
    public function findFreeCourses(int $limit = 8): Collection;
    public function findSuggestedCourses(int $limit = 8): Collection;
    public function findLatestCourses(int $limit = 8): Collection;
    public function findNewCourses(int $limit = 8): Collection;
    public function findUpcomingCourses(int $limit = 8): Collection;
    public function findUserRecommendedCourses(int $userId, int $limit = 8): Collection;
    public function findUserFavoriteCourses(int $userId, int $limit = 8): Collection;
    public function findPopularCoursesManual(int $contentId, int $contentType, int $limit = 8): Collection;
    public function findTopCoursesByViews(array $excludeIds, \Carbon\Carbon $startDay, int $limit): Collection;
    public function findTopCourses(int $limit = 8): Collection;
    public function findSubscriptionCourses(array $categoryIds, array $filters = [], int $limit = 8): Collection;
    public function findCurriculumCourses(array $filters = [], int $limit = 8): Collection;
    public function findPromotionCourses(array $filters = [], int $limit = 8): Collection;
    public function findUserLevelBasedCourses(int $userId, int $limit = 8): Collection;
    public function findUserInterestCourses(int $userId, int $limit = 8): Collection;
    public function findLiveCourses(array $filters = [], int $limit = 8): Collection;
    public function findEventCourses(array $filters = [], int $limit = 8): Collection;
    public function findCompanyVisitCourses(array $filters = [], int $limit = 8): Collection;
}
