<?php

namespace App\Repositories\Interfaces;

interface CompanyVisitRepositoryInterface
{
    public function all(int $limit = 8);
    public function count();
    public function find($id);
    public function create(array $data);
    public function update($id, array $data);
    public function delete($id);
    public function updateStatus($id);
    public function updatePosition($id, $position);
    public function getCompanyVisitWithRelations(int $limit = 8);
}
