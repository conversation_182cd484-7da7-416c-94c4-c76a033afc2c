<?php

namespace App\Repositories\Interfaces;

use Illuminate\Support\Collection;

interface SubscriptionLogRepositoryInterface
{
    /**
     * Get subscription logs for datatable
     * 
     * @param array $filters
     * @param string $orderBy
     * @param string $orderDirection
     * @param int $offset
     * @param int $limit
     * @return array
     */
    public function getSubscriptionLogsForDatatable(
        array $filters,
        string $orderBy,
        string $orderDirection,
        int $offset,
        int $limit
    ): array;

    /**
     * Count filtered subscription logs
     * 
     * @param array $filters
     * @return int
     */
    public function countFilteredSubscriptionLogs(array $filters): int;
}