<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class Bank extends Model
{
    protected $table = 'banks';
    
    protected $fillable = [
        'bank_name',
        'account_name', 
        'account_number',
        'phone_number',
        'type',
        'qr_code',
        'description',
        'status',
        'is_active',
        'position'
    ];

    protected $casts = [
        'status' => 'integer',
        'is_active' => 'integer',
        'position' => 'integer'
    ];

    // รายชื่อธนาคารในไทย
    public static function getBankOptions()
    {
        return [
            'กสิกรไทย' => 'ธนาคารกสิกรไทย',
            'กรุงเทพ' => 'ธนาคารกรุงเทพ',
            'กรุงไทย' => 'ธนาคารกรุงไทย',
            'ทีเอ็มบีธนชาต' => 'ธนาคารทีเอ็มบีธนชาต',
            'ไทยพาณิชย์' => 'ธนาคารไทยพาณิชย์',
            'กรุงศรีอยุธยา' => 'ธนาคารกรุงศรีอยุธยา',
            'ออมสิน' => 'ธนาคารออมสิน',
            'อาคารสงเคราะห์' => 'ธนาคารอาคารสงเคราะห์',
            'เกียรตินาคิน' => 'ธนาคารเกียรตินาคิน',
            'ซีไอเอ็มบี' => 'ธนาคารซีไอเอ็มบี',
            'ทิสโก้' => 'ธนาคารทิสโก้',
            'ยูโอบี' => 'ธนาคารยูโอบี',
            'แลนด์ แอนด์ เฮาส์' => 'ธนาคารแลนด์ แอนด์ เฮาส์',
            'ไอซีบีซี' => 'ธนาคารไอซีบีซี',
            'อิสลามแห่งประเทศไทย' => 'ธนาคารอิสลามแห่งประเทศไทย',
            'พร้อมเพย์' => 'พร้อมเพย์'
        ];
    }

    // รายการ Bank Code สำหรับไอคอน
    public static function getBankCodes()
    {
        return [
            'กสิกรไทย' => 'kbank',
            'กรุงเทพ' => 'bbl',
            'กรุงไทย' => 'ktb',
            'ทีเอ็มบีธนชาต' => 'ttb',
            'ไทยพาณิชย์' => 'scb',
            'กรุงศรีอยุธยา' => 'bay',
            'ออมสิน' => 'gsb',
            'อาคารสงเคราะห์' => 'ghb',
            'เกียรตินาคิน' => 'kk',
            'ซีไอเอ็มบี' => 'cimb',
            'ทิสโก้' => 'tisco',
            'ยูโอบี' => 'uob',
            'แลนด์ แอนด์ เฮาส์' => 'lhb',
            'ไอซีบีซี' => 'icbc',
            'อิสลามแห่งประเทศไทย' => 'ibank',
            'พร้อมเพย์' => 'promptpay'
        ];
    }

    // ฟังก์ชันดึง Bank Code
    public static function getBankCode($bankName)
    {
        $bankCodes = self::getBankCodes();
        return $bankCodes[$bankName] ?? 'sample';
    }

    // Scope สำหรับดึงข้อมูลที่เปิดใช้งาน
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    // Scope สำหรับดึงข้อมูลที่ใช้งานหลัก
    public function scopeMainActive($query)
    {
        return $query->where('is_active', 1)->where('status', 1);
    }

    // Accessor สำหรับ QR Code URL
    public function getQrCodeUrlAttribute()
    {
        return $this->qr_code ? url('upload/banks/' . $this->qr_code) : null;
    }
}
