<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
  protected $table = 'course';

  protected $casts = [
    'requires_vat' => 'boolean'
  ];

  public function checkoutFields()
  {
      return $this->belongsToMany(CheckoutField::class, 'course_checkout_fields', 'course_id', 'checkout_field_id')
          ->withPivot('position', 'is_required_override')
          ->withTimestamps()
          ->orderBy('position');
  }

  public function activeCheckoutFields()
  {
      return $this->checkoutFields()->where('checkout_fields.status', true);
  }
}
