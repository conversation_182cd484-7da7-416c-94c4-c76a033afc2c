<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class UsersOrderLog extends Model
{
  protected $table = 'user_order_log';

  protected $fillable = [
    'checkout_fields_price',
    'tax_type',
    'vat_amount', 
    'tax_amount',
    'price_before_tax'
  ];

  // Relationships
  public function checkoutFieldSubmissions()
  {
    return $this->hasMany(CheckoutFieldSubmission::class, 'order_id');
  }

  public function user()
  {
    return $this->belongsTo(Users::class, 'user_id');
  }

  public function orderLists()
  {
    return $this->hasMany(UsersOrderList::class, 'order_id');
  }
}
