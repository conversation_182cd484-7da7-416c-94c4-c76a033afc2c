<?php
namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    protected $table = 'subscription';

    protected $casts = [
        'requires_vat' => 'boolean'
    ];

    public function subscriptionMain()
    {
        return $this->belongsTo(SubscriptionMain::class, 'subscription_main_id');
    }

    public function checkoutFields()
    {
        return $this->belongsToMany(CheckoutField::class, 'subscription_checkout_fields', 'subscription_id', 'checkout_field_id')
            ->withPivot('position', 'is_required_override')
            ->withTimestamps()
            ->orderBy('position');
    }

    public function activeCheckoutFields()
    {
        return $this->checkoutFields()->where('checkout_fields.status', true);
    }
}
