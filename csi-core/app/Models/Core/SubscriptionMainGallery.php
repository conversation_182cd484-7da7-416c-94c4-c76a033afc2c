<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionMainGallery extends Model
{
    use HasFactory;

    protected $table = 'subscription_main_gallery';

    protected $fillable = [
        'subscription_main_id',
        'image_path',
        'image_alt',
        'position',
        'status'
    ];

    // Relationships
    public function subscriptionMain()
    {
        return $this->belongsTo(SubscriptionMain::class, 'subscription_main_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        return asset('storage/' . $this->image_path);
    }
}