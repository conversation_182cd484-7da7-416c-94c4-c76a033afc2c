<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class CheckoutFieldSubmission extends Model
{
    protected $table = 'checkout_field_submissions';

    protected $fillable = [
        'checkout_field_id',
        'user_id',
        'order_id',
        'course_id',
        'subscription_id',
        'field_value',
        'submitted_at'
    ];

    protected $dates = [
        'submitted_at'
    ];

    // Relationships
    public function checkoutField()
    {
        return $this->belongsTo(CheckoutField::class, 'checkout_field_id');
    }

    public function user()
    {
        return $this->belongsTo(Users::class, 'user_id');
    }

    public function order()
    {
        return $this->belongsTo(UsersOrderLog::class, 'order_id');
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id');
    }

    public function subscription()
    {
        return $this->belongsTo(SubscriptionMain::class, 'subscription_id');
    }
}