<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class UserBillingHistory extends Model
{
    protected $table = 'user_billing_history';
    
    protected $fillable = [
        'user_id',
        'name',
        'lastname',
        'email',
        'tel',
        'address',
        'subdistrict',
        'district',
        'province',
        'postcode',
        'address_type',
        'iden_no',
        'company_type',
        'company_branch'
    ];

    protected $casts = [
        'address_type' => 'integer',
    ];

    /**
     * Relationship with Users
     */
    public function user()
    {
        return $this->belongsTo(Users::class, 'user_id');
    }

    /**
     * Get the latest billing history for a user
     */
    public static function getLatestByUserId($userId)
    {
        return self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Create or update billing history for a user
     */
    public static function createFromRequest($userId, $request)
    {
        return self::create([
            'user_id' => $userId,
            'name' => $request->name,
            'lastname' => $request->lastname,
            'email' => $request->email,
            'tel' => $request->tel,
            'address' => $request->address,
            'address_type' => $request->address_type,
            'iden_no' => $request->iden_no,
            'company_type' => $request->company_type ?? null,
            'company_branch' => $request->company_branch ?? null,
        ]);
    }
}
