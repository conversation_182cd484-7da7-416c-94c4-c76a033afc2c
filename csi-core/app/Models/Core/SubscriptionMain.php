<?php
namespace App\Models\Core;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionMain extends Model
{
    use HasFactory;

    protected $table = 'subscription_main';

    protected $fillable = [
        'title',
        'short_description', 
        'description',
        'cover_image',
        'period',
        'slug',
        'position',
        'is_featured',
        'is_popular',
        'status',
    ];

    protected $casts = [
        'price'          => 'decimal:2',
        'original_price' => 'decimal:2',
        'is_featured'    => 'boolean',
        'is_popular'     => 'boolean',
    ];

    // Relationships
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'subscription_main_id');
    }

    public function activeSubscriptions()
    {
        return $this->hasMany(Subscription::class, 'subscription_main_id')
            ->where('status', 1)
            ->orderBy('position', 'asc');
    }

    public function gallery()
    {
        return $this->hasMany(SubscriptionMainGallery::class, 'subscription_main_id')
            ->where('status', 1)
            ->orderBy('position', 'asc');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2);
    }

    public function getFormattedOriginalPriceAttribute()
    {
        return $this->original_price ? number_format($this->original_price, 2) : null;
    }

    public function getDiscountPercentAttribute()
    {
        if ($this->original_price && $this->price) {
            return round((($this->original_price - $this->price) / $this->original_price) * 100);
        }
        return 0;
    }

    public function checkoutFields()
    {
        return $this->belongsToMany(CheckoutField::class, 'subscription_checkout_fields', 'subscription_main_id', 'checkout_field_id')
            ->withPivot('position', 'is_required_override')
            ->withTimestamps()
            ->orderBy('position');
    }

    public function activeCheckoutFields()
    {
        return $this->checkoutFields()->where('checkout_fields.status', true);
    }
}
