<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class CheckoutField extends Model
{
    protected $table = 'checkout_fields';

    protected $fillable = [
        'field_name',
        'field_label',
        'field_type',
        'field_options',
        'validation_rules',
        'placeholder',
        'help_text',
        'is_required',
        'has_pricing',
        'status'
    ];

    protected $casts = [
        'field_options' => 'array',
        'validation_rules' => 'array',
        'is_required' => 'boolean',
        'has_pricing' => 'boolean',
        'status' => 'boolean'
    ];

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'course_checkout_fields', 'checkout_field_id', 'course_id')
            ->withPivot('position', 'is_required_override')
            ->withTimestamps()
            ->orderBy('position');
    }

    public function subscriptions()
    {
        return $this->belongsToMany(SubscriptionMain::class, 'subscription_checkout_fields', 'checkout_field_id', 'subscription_main_id')
            ->withPivot('position', 'is_required_override')
            ->withTimestamps()
            ->orderBy('position');
    }

    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function getValidationRulesStringAttribute()
    {
        if (!$this->validation_rules) {
            return '';
        }

        $rules = [];
        foreach ($this->validation_rules as $rule => $value) {
            if ($value === true) {
                $rules[] = $rule;
            } elseif ($value !== false) {
                $rules[] = $rule . ':' . $value;
            }
        }

        return implode('|', $rules);
    }

    /**
     * Get the price for a specific option value
     */
    public function getOptionPrice($optionValue)
    {
        if (!$this->has_pricing || !$this->field_options || !$optionValue) {
            return 0;
        }

        if (isset($this->field_options[$optionValue])) {
            $option = $this->field_options[$optionValue];
            
            // If option is array with price
            if (is_array($option) && isset($option['price'])) {
                return (float) $option['price'];
            }
        }

        return 0;
    }

    /**
     * Calculate total price for multiple option values (for checkbox)
     */
    public function calculateTotalPrice($optionValues)
    {
        if (!$this->has_pricing || !$this->field_options || !$optionValues) {
            return 0;
        }

        $totalPrice = 0;
        $values = is_array($optionValues) ? $optionValues : explode(',', $optionValues);

        foreach ($values as $value) {
            $totalPrice += $this->getOptionPrice(trim($value));
        }

        return $totalPrice;
    }
}