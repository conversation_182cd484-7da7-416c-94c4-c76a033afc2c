<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;

class HomeContent extends Model
{
  protected $table = 'home_content';
  
  protected $fillable = [
    'type', 'name', 'color', 'size', 'playlist', 'popular_all', 'learner', 'level', 
    'category', 'gender', 'department', 'speaker', 'tag', 'group_id', 'start_date', 
    'end_date', 'sub_day', 'position', 'status', 'title', 'subtitle', 'link', 'cta',
    'show_video_details', 'subscription_main_id'
  ];
  
  public function subscriptionMain()
  {
    return $this->belongsTo('App\Models\Core\SubscriptionMain', 'subscription_main_id');
  }
}
