<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Peak API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration settings for the Peak API integration.
    | These values are used by the PeakService to connect and authenticate
    | with the Peak API endpoints.
    |
    */

    'connect_id' => env('PEAK_CONNECT_ID', ''),
    
    'password' => env('PEAK_PASSWORD', ''),
    
    'user_token' => env('PEAK_USER_TOKEN', ''),
    
    'client_token' => env('PEAK_CLIENT_TOKEN', ''),
    
    'base_url' => env('PEAK_BASE_URL', 'https://api.peak.co.th/'),
    
    'payment_method_id' => env('PEAK_PAYMENT_METHOD_ID', '1'),
    
    /*
    |--------------------------------------------------------------------------
    | API Timeout Configuration
    |--------------------------------------------------------------------------
    |
    | The timeout value in seconds for API requests to Peak service.
    |
    */
    
    'timeout' => env('PEAK_API_TIMEOUT', 400),
    
    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for caching Peak API tokens.
    |
    */
    
    'cache' => [
        'client_token_ttl' => env('PEAK_CLIENT_TOKEN_TTL', 120), // minutes
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Default Values
    |--------------------------------------------------------------------------
    |
    | Default values used throughout the Peak integration.
    |
    */
    
    'defaults' => [
        'contact_type' => '5',
        'country' => 'Thailand',
        'branch_code' => '00000',
        'vat_type' => 3,
        'tax_status' => '1',
        'withholding_tax_rate' => 0.03,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Supported Payment Methods
    |--------------------------------------------------------------------------
    |
    | List of payment methods that support receipt creation.
    |
    */
    
    'supported_payment_methods' => [
        'bacs',
        'gbprimepay',
        'kasikorn_kpgw',
        'kasikorn_kpgw_qr',
        'kasikorn_kpgw_installment',
        'kasikorn_kpgw_installment_1',
        'kasikorn_kpgw_installment_2',
        'kasikorn_kpgw_installment_3',
        'gbprimepay_installment'
    ],
];