<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = [
            [
                'title_th' => 'New Course',
                'title_en' => 'new-course',
                'position' => 1,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title_th' => 'Recommended Course',
                'title_en' => 'recommended-course',
                'position' => 2,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title_th' => 'Seminar',
                'title_en' => 'seminar',
                'position' => 3,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title_th' => 'Company Visit',
                'title_en' => 'company-visit',
                'position' => 4,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title_th' => 'CSI Pack',
                'title_en' => 'csi-pack',
                'position' => 5,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title_th' => 'Business Pack',
                'title_en' => 'business-pack',
                'position' => 6,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title_th' => 'CRYPTO Pack',
                'title_en' => 'crypto-pack',
                'position' => 7,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title_th' => 'Trending this month',
                'title_en' => 'trending-this-month',
                'position' => 8,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
           
        ];

        foreach ($categories as $category) {
            DB::table('categories')->updateOrInsert(
                ['title_en' => $category['title_en']],
                $category
            );
        }
    }
}