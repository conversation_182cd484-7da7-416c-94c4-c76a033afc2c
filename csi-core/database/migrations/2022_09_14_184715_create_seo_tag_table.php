<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSeoTagTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seo_tag', function (Blueprint $table) {
            $table->id();
            $table->text('page')->nullable();
            $table->text('key')->nullable();
            $table->text('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keyword')->nullable();
            $table->text('meta_image')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('sessions', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->nullable();
            $table->text('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->text('payload')->nullable();
            $table->integer('last_activity')->nullable();
        });
        Schema::create('stat_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->text('ip')->nullable();
            $table->text('browser')->nullable();
            $table->integer('type')->nullable();
            $table->text('page')->nullable();
            $table->text('keyword')->nullable();
            $table->timestamps();
        });
        Schema::create('subscription', function (Blueprint $table) {
            $table->id();
            $table->text('banner')->nullable();
            $table->text('banner_m')->nullable();
            $table->text('thumb')->nullable();
            $table->text('cta')->nullable();
            $table->text('link')->nullable();
            $table->integer('type')->nullable();
            $table->text('title')->nullable();
            $table->text('details')->nullable();
            $table->text('left_details')->nullable();
            $table->text('right_details')->nullable();
            $table->text('bottom_details')->nullable();
            $table->text('remark')->nullable();
            $table->integer('period')->nullable();
            $table->integer('price')->nullable();
            $table->text('code')->nullable();
            $table->integer('limit')->nullable();
            $table->text('excluding_course')->nullable();
            $table->integer('select_type')->default(1)->comment('1=select/2=exclude');
            $table->text('only_cate')->nullable();
            $table->text('excluding_cate')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('subscription_ex_cate', function (Blueprint $table) {
            $table->id();
            $table->integer('subscription_id')->nullable();
            $table->integer('cate_id')->nullable();
            $table->timestamps();
        });
        Schema::create('subscription_ex_course', function (Blueprint $table) {
            $table->id();
            $table->integer('subscription_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
        Schema::create('subscription_in_cate', function (Blueprint $table) {
            $table->id();
            $table->integer('subscription_id')->nullable();
            $table->integer('cate_id')->nullable();
            $table->timestamps();
        });
        Schema::create('subscription_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('subscription_id')->nullable();
            $table->integer('order_list_id')->nullable();
            $table->datetime('expired')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('seo_tag');
    }
}
