<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseSectionFileLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_section_file_log', function (Blueprint $table) {
            $table->id();
            $table->integer('section_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('file_id')->nullable();
            $table->timestamps();
        });
        Schema::table('course_section', function (Blueprint $table) {
            $table->text('file')->nullable()->after('lesson');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_section_file_log');
    }
}
