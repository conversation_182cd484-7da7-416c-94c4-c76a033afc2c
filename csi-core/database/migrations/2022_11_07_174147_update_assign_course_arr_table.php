<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateAssignCourseArrTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assign_course', function (Blueprint $table) {
            $table->text('course_arr')->nullable()->after('course_id');
            $table->integer('course_type')->default(1)->after('course_arr');
            $table->text('remark')->nullable()->after('group');
        });
        Schema::table('user_history', function (Blueprint $table) {
            $table->integer('assign_id')->nullable()->after('get_type');
            $table->integer('assign_status')->default(1)->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
