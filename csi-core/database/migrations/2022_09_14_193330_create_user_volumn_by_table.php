<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserVolumnByTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('volumn_course_log', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->nullable();
            $table->integer('volumn_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('volumn_domain_log', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->nullable();
            $table->integer('lot_id')->nullable();
            $table->integer('domain_id')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('volumn_lot_log', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->nullable();
            $table->text('lot')->nullable();
            $table->text('lot_name')->nullable();
            $table->text('limit')->nullable();
            $table->datetime('started_date')->nullable();
            $table->datetime('end_date')->nullable();
            $table->text('domain')->nullable();
            $table->text('ex_course')->nullable();
            $table->integer('type')->default(1)->comment('1:include/2:exclude');
            $table->text('in_cate')->nullable();
            $table->text('ex_cate')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('volumn_user_log', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->nullable();
            $table->integer('lot_id')->nullable();
            $table->text('email')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_volumn_by');
    }
}
