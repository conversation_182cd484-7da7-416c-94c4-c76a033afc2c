<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateLrkUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->integer('g_id')->nullable()->after('identity_no')->comment('GID');
            $table->integer('e_id')->nullable()->after('g_id')->comment('รหัสพนักงาน');
            $table->integer('prefix')->nullable()->after('e_id')->comment('1:Mr/2:Miss/3:Mrs');
            $table->text('position')->nullable()->after('lastname')->comment('ชื่อตำแหน่ง');
            $table->text('job_grade')->nullable()->after('position')->comment('Job grade');
            $table->text('location_code')->nullable()->after('job_grade')->comment('รหัสสถานที่ทำงาน');
            $table->text('institution_code')->nullable()->after('location_code')->comment('รหัสหน่วยงาน');
            $table->integer('dept_id')->nullable()->after('institution_code')->comment('Dept ID');
            $table->text('department')->nullable()->after('dept_id')->comment('ชื่อหน่วยงาน');
            $table->date('started_date')->nullable()->after('department')->comment('วันที่เข้างาน');
            $table->date('termination_date')->nullable()->after('started_date')->comment('วันที่พ้นสภาพงาน');
            $table->integer('manager')->nullable()->after('email')->comment('Manager');
            $table->integer('short_name')->nullable()->after('manager')->comment('Short Name');
            $table->text('nationality')->nullable()->after('short_name')->comment('ชื่อสัญชาติ');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
