<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course', function (Blueprint $table) {
            $table->id();
            $table->text('sku_type')->nullable();
            $table->text('slug')->nullable();
            $table->text('course_key')->nullable();
            $table->integer('course_lang')->default(1)->comment('1:TH/2:TH+EN');
            $table->integer('course_type')->default(1);
            $table->integer('trailer_media')->default(1)->comment('1:course/2:zoom/3:podcast/4:seminar/5:info');
            $table->integer('topic_type')->nullable();
            $table->integer('year')->nullable();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('subtitle_th')->nullable();
            $table->text('subtitle_en')->nullable();
            $table->text('details_th')->nullable();
            $table->text('details_en')->nullable();
            $table->text('image_th')->nullable();
            $table->text('image_en')->nullable();
            $table->text('banner')->nullable();
            $table->text('banner_m')->nullable();
            $table->text('og_image')->nullable();
            $table->integer('host')->nullable();
            $table->text('gender')->nullable();
            $table->text('age')->nullable();
            $table->integer('level')->nullable();
            $table->text('department')->nullable();
            $table->text('speaker')->nullable();
            $table->text('learner')->nullable();
            $table->text('categories')->nullable();
            $table->text('tag')->nullable();
            $table->text('key_search')->nullable();
            $table->text('checker')->nullable();
            $table->integer('is_due')->nullable()->comment('1:Y/2:N');
            $table->integer('duration_time')->nullable()->comment('กำหนดระยะเวลา');
            $table->integer('course_duration')->nullable()->comment('เวลารวมทั้งคอร์ส หน่วยวินาที');
            $table->date('started_date')->nullable();
            $table->date('end_date')->nullable();
            $table->datetime('last_sync')->nullable();
            $table->datetime('started_time')->nullable();
            $table->text('zoom_id')->nullable();
            $table->text('zoom_start_url')->nullable();
            $table->text('zoom_join_url')->nullable();
            $table->text('zoom_password')->nullable();
            $table->integer('zoom_duration')->nullable();
            $table->datetime('end_time')->nullable();
            $table->datetime('conference_date')->nullable();
            $table->datetime('started_learning')->nullable();
            $table->integer('internal')->default(1);
            $table->integer('is_free')->default(1);
            $table->integer('is_suggess')->default(1);
            $table->integer('is_promotion')->default(2);
            $table->float('pro_price')->default(0);
            $table->float('price')->default(0);
            $table->integer('is_cme')->default(1);
            $table->integer('receive_point')->default(0);
            $table->integer('point_to_pass')->default(0);
            $table->integer('is_cert')->default(1);
            $table->float('rate')->default(0);
            $table->integer('rating')->default(0);
            $table->integer('is_sub')->default(2);
            $table->integer('is_hot')->default(2);
            $table->integer('is_oculus')->default(2);
            $table->integer('is_new')->default(2);
            $table->text('logo')->nullable();
            $table->text('media_type')->nullable();
            $table->text('content_group')->nullable();
            $table->text('category_air')->nullable();
            $table->text('sub_category_air')->nullable();
            $table->text('no_sku_in_group')->nullable();
            $table->text('sku_in_group')->nullable();
            $table->text('production_source')->nullable();
            $table->text('remark')->nullable();
            $table->text('content_format')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->text('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keyword')->nullable();
            $table->text('meta_image')->nullable();
            $table->timestamps();
        });
        Schema::create('course_age_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('age_id')->nullable();
            $table->timestamps();
        });
        Schema::create('course_categories_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('cate_id')->nullable();
            $table->timestamps();
        });
        Schema::create('course_checker_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('admin_id')->nullable();
            $table->timestamps();
        });
        Schema::create('course_department_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('department')->nullable();
            $table->timestamps();
        });
        Schema::create('course_exam', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->integer('exam_type')->nullable()->comment('1:ปรนัย/2:อัตนัย/3:อัพโหลด');
            $table->integer('answer_type')->nullable()->comment('1:text/2:image');
            $table->text('question_th')->nullable();
            $table->text('question_en')->nullable();
            $table->integer('point')->default(0);
            $table->integer('position')->nullable();
            $table->text('file_pptx')->nullable();
            $table->text('file_excel')->nullable();
            $table->text('file_word')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('course_exam_answer', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->integer('exam_id')->nullable();
            $table->text('answer_th')->nullable();
            $table->text('answer_en')->nullable();
            $table->text('image_th')->nullable();
            $table->text('image_en')->nullable();
            $table->integer('is_right')->default(1);
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('course_file', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('type')->nullable();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('file_th')->nullable();
            $table->text('file_en')->nullable();
            $table->text('file_pptx_th')->nullable();
            $table->text('file_pptx_en')->nullable();
            $table->text('file_xlsx_th')->nullable();
            $table->text('file_xlsx_en')->nullable();
            $table->text('file_pdf_th')->nullable();
            $table->text('file_pdf_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('course_gender_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('gender_id')->nullable();
            $table->timestamps();
        });
        Schema::create('course_group', function (Blueprint $table) {
            $table->id();
            $table->integer('group_cert')->default(1);
            $table->text('title')->nullable();
            $table->text('details')->nullable();
            $table->text('banner')->nullable();
            $table->text('thumb')->nullable();
            $table->integer('is_free')->default(1);
            $table->integer('price')->default(0);
            $table->text('course')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('course_key_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('key_id')->nullable();
            $table->text('key_search')->nullable();
            $table->timestamps();
        });
        Schema::create('course_learner_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('learner')->nullable();
            $table->timestamps();
        });
        Schema::create('course_lesson', function (Blueprint $table) {
            $table->id();
            $table->text('email')->nullable();
            $table->integer('price')->default(0);
            $table->integer('is_teaser')->default(1);
            $table->integer('course_id')->nullable();
            $table->integer('type')->default(1);
            $table->integer('lesson_type')->default(1);
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('image_th')->nullable();
            $table->text('image_en')->nullable();
            $table->text('lesson_key')->nullable();
            $table->text('zoom_id')->nullable();
            $table->text('link')->nullable();
            $table->text('link_2')->nullable();
            $table->text('link_3')->nullable();
            $table->time('duration')->nullable();
            $table->integer('point')->nullable();
            $table->integer('is_skip')->default(1);
            $table->integer('skip_sec')->nullable();
            $table->integer('skip_intro')->default(2);
            $table->integer('exam_time')->default(0);
            $table->integer('exam_type')->default(1);
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('course_logo_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('logo_id')->nullable();
            $table->timestamps();
        });
        Schema::create('course_rate_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->float('rate')->nullable();
            $table->timestamps();
        });
        Schema::create('course_speaker_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('speaker')->nullable();
            $table->timestamps();
        });
        Schema::create('course_tag_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('tag_id')->nullable();
            $table->timestamps();
        });
        Schema::create('course_view', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course');
    }
}
