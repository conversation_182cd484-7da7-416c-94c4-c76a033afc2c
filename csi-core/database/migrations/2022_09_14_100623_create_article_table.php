<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateArticleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('article', function (Blueprint $table) {
            $table->id();
            $table->text('slug')->nullable();
            $table->integer('is_article')->default(1)->comment('1:article/2:news');
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('subtitle_th')->nullable();
            $table->text('subtitle_en')->nullable();
            $table->text('details_th')->nullable();
            $table->text('details_en')->nullable();
            $table->text('banner')->nullable();
            $table->text('banner_en')->nullable();
            $table->text('image')->nullable();
            $table->text('image_en')->nullable();
            $table->text('tag')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->text('meta_title')->nullable();
            $table->text('meta_title_en')->nullable();
            $table->text('meta_keyword')->nullable();
            $table->text('meta_keyword_en')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_description_en')->nullable();
            $table->text('meta_image')->nullable();
            $table->text('meta_image_en')->nullable();
            $table->timestamps();
        });
        Schema::create('article_pin', function (Blueprint $table) {
            $table->id();
            $table->integer('article_id')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('article_log', function (Blueprint $table) {
            $table->id();
            $table->integer('article_id')->nullable();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('image')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('article_tag_log', function (Blueprint $table) {
            $table->id();
            $table->integer('article_id')->nullable();
            $table->integer('tag_id')->nullable();
            $table->timestamps();
        });
        Schema::create('news_pin', function (Blueprint $table) {
            $table->id();
            $table->integer('news_id')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('article');
    }
}
