<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCertDynamicTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cert_dynamic', function (Blueprint $table) {
            $table->id();
            $table->integer('lang_type')->default(1)->comment('1:TH/2:EN/3:TH+EN');
            $table->integer('course_id')->nullable();
            $table->text('couse_name')->nullable();
            $table->text('course_name_en')->nullable();
            $table->integer('type')->default(1);
            $table->text('bg_color')->nullable();
            $table->text('title_en')->nullable();
            $table->text('bg_color_en')->nullable();
            $table->text('course_director')->nullable();
            $table->text('course_director_en')->nullable();
            $table->text('signature')->nullable();
            $table->text('signature_en')->nullable();
            $table->text('director_position')->nullable();
            $table->text('director_position_en')->nullable();
            $table->text('course_director_2')->nullable();
            $table->text('course_director_2_en')->nullable();
            $table->text('signature_2')->nullable();
            $table->text('signature_2_en')->nullable();
            $table->text('director_position_2')->nullable();
            $table->text('director_position_2_en')->nullable();
            $table->text('course_director_3')->nullable();
            $table->text('course_director_3_en')->nullable();
            $table->text('signature_3')->nullable();
            $table->text('signature_3_en')->nullable();
            $table->text('director_position_3')->nullable();
            $table->text('director_position_3_en')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('cert_group_dynamic', function (Blueprint $table) {
            $table->id();
            $table->integer('lang_type')->default(1)->comment('1:TH/2:EN/3:TH+EN');
            $table->integer('series_id')->nullable();
            $table->text('series_name')->nullable();
            $table->text('series_name_en')->nullable();
            $table->integer('type')->default(1);
            $table->text('bg_color')->nullable();
            $table->text('title_en')->nullable();
            $table->text('bg_color_en')->nullable();
            $table->text('course_director')->nullable();
            $table->text('course_director_en')->nullable();
            $table->text('signature')->nullable();
            $table->text('signature_en')->nullable();
            $table->text('director_position')->nullable();
            $table->text('director_position_en')->nullable();
            $table->text('course_director_2')->nullable();
            $table->text('course_director_2_en')->nullable();
            $table->text('signature_2')->nullable();
            $table->text('signature_2_en')->nullable();
            $table->text('director_position_2')->nullable();
            $table->text('director_position_2_en')->nullable();
            $table->text('course_director_3')->nullable();
            $table->text('course_director_3_en')->nullable();
            $table->text('signature_3')->nullable();
            $table->text('signature_3_en')->nullable();
            $table->text('director_position_3')->nullable();
            $table->text('director_position_3_en')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('cert_template', function (Blueprint $table) {
            $table->id();
            $table->text('logo')->nullable();
            $table->text('name')->nullable();
            $table->text('address')->nullable();
            $table->text('tax_no')->nullable();
            $table->text('approve_signature')->nullable();
            $table->text('receiver_signature')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cert_dynamic');
    }
}
