<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use Illuminate\Support\Facades\DB;
class CreateIntroWordTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('intro_word', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->default(1)->comment('1:home/2:course/3:course inter');
            $table->text('title_1')->nullable();
            $table->text('description_1')->nullable();
            $table->text('title_2')->nullable();
            $table->text('description_2')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });

        DB::table('intro_word')->insert(
            array(
                'type' => 1,
                'title_1' => 'DEK’67 เรียนอะไรดี ?? กับครูพี่โอม',
                'description_1' => 'เปิดศักราชเข้าสู่สนาม TCAS’67 เตรียมตัวก่อนได้เปรียบ พร้อมรับมือข้อสอบแนวใหม่<br>ที่การันตีแล้วว่าตรงที่สุดเหมือนครูพี่โอมเป็นคนออกข้อสอบด้วยตนเอง
                <br>จากประสบการณ์สอนและสอบเองกว่า 15 ปี!!<br>เก่งอังกฤษแบบก้าวกระโดดกับครูพี่โอม สู่เส้นชัยมหาวิทยาลัยในฝัน<br>พร้อมฟันคะแนน ENG ได้ทั้งสองสนาม',
                'title_2' => 'DEK’67 & DEK’68 ลงคอร์สไหนกับครูพี่โอม<br>เพื่อประสิทธิภาพสูงสุด ??',
                'description_2' => 'เตรียมความพร้อมล่วงหน้า ปรับพื้นฐานแกร่ง เสริมเทคนิคเด็ดเฉพาะตัวของครูพี่โอม<br>ให้น้องๆ DEK’67 และ DEK’68 พร้อมรับมือข้อสอบอังกฤษทุกรูปแบบในสนาม
                <br>TCAS’67 และ TCAS’68 มาฟังครูพี่โอมแนะนำคอร์สภาษาอังกฤษ<br>ที่จะช่วยให้น้องๆเก่งอังกฤษได้ครบทุกมิติ ทั้ง "Grammar-Vocab-Reading"
                <br>แม้พื้นฐานไม่แน่น เรียนจบคอร์สนี้ แน่นเหมือนเรียนอังกฤษจบ ม.6 เลยนะ',
                'status' => 1,
            )
        );
        DB::table('intro_word')->insert(
            array(
                'type' => 2,
                'title_1' => 'DEK’67 เรียนอะไรดี ?? กับครูพี่โอม',
                'description_1' => 'เปิดศักราชเข้าสู่สนาม TCAS’67 เตรียมตัวก่อนได้เปรียบ พร้อมรับมือข้อสอบแนวใหม่<br>ที่การันตีแล้วว่าตรงที่สุดเหมือนครูพี่โอมเป็นคนออกข้อสอบด้วยตนเอง
                <br>จากประสบการณ์สอนและสอบเองกว่า 15 ปี!!<br>เก่งอังกฤษแบบก้าวกระโดดกับครูพี่โอม สู่เส้นชัยมหาวิทยาลัยในฝัน<br>พร้อมฟันคะแนน ENG ได้ทั้งสองสนาม',
                'title_2' => 'DEK’67 & DEK’68 ลงคอร์สไหนกับครูพี่โอม<br>เพื่อประสิทธิภาพสูงสุด ??',
                'description_2' => 'เตรียมความพร้อมล่วงหน้า ปรับพื้นฐานแกร่ง เสริมเทคนิคเด็ดเฉพาะตัวของครูพี่โอม<br>ให้น้องๆ DEK’67 และ DEK’68 พร้อมรับมือข้อสอบอังกฤษทุกรูปแบบในสนาม
                <br>TCAS’67 และ TCAS’68 มาฟังครูพี่โอมแนะนำคอร์สภาษาอังกฤษ<br>ที่จะช่วยให้น้องๆเก่งอังกฤษได้ครบทุกมิติ ทั้ง "Grammar-Vocab-Reading"
                <br>แม้พื้นฐานไม่แน่น เรียนจบคอร์สนี้ แน่นเหมือนเรียนอังกฤษจบ ม.6 เลยนะ',
                'status' => 1,
            )
        );
        DB::table('intro_word')->insert(
            array(
                'type' => 3,
                'title_1' => 'DEK’67 เรียนอะไรดี ?? กับครูพี่โอม',
                'description_1' => 'เปิดศักราชเข้าสู่สนาม TCAS’67 เตรียมตัวก่อนได้เปรียบ พร้อมรับมือข้อสอบแนวใหม่<br>ที่การันตีแล้วว่าตรงที่สุดเหมือนครูพี่โอมเป็นคนออกข้อสอบด้วยตนเอง
                <br>จากประสบการณ์สอนและสอบเองกว่า 15 ปี!!<br>เก่งอังกฤษแบบก้าวกระโดดกับครูพี่โอม สู่เส้นชัยมหาวิทยาลัยในฝัน<br>พร้อมฟันคะแนน ENG ได้ทั้งสองสนาม',
                'title_2' => 'DEK’67 & DEK’68 ลงคอร์สไหนกับครูพี่โอม<br>เพื่อประสิทธิภาพสูงสุด ??',
                'description_2' => 'เตรียมความพร้อมล่วงหน้า ปรับพื้นฐานแกร่ง เสริมเทคนิคเด็ดเฉพาะตัวของครูพี่โอม<br>ให้น้องๆ DEK’67 และ DEK’68 พร้อมรับมือข้อสอบอังกฤษทุกรูปแบบในสนาม
                <br>TCAS’67 และ TCAS’68 มาฟังครูพี่โอมแนะนำคอร์สภาษาอังกฤษ<br>ที่จะช่วยให้น้องๆเก่งอังกฤษได้ครบทุกมิติ ทั้ง "Grammar-Vocab-Reading"
                <br>แม้พื้นฐานไม่แน่น เรียนจบคอร์สนี้ แน่นเหมือนเรียนอังกฤษจบ ม.6 เลยนะ',
                'status' => 1,
            )
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('intro_word');
    }
}
