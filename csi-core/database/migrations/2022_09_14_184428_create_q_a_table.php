<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateQATable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('q_a', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('banner_d')->nullable();
            $table->text('banner_m')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('q_a_log', function (Blueprint $table) {
            $table->id();
            $table->integer('q_a_id')->nullable();
            $table->text('question_th')->nullable();
            $table->text('question_en')->nullable();
            $table->text('answer_th')->nullable();
            $table->text('answer_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('q_a');
    }
}
