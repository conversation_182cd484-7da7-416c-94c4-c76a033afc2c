<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserSectionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->text('user_section')->nullable()->after('department');
        });
        Schema::table('categories_department', function (Blueprint $table) {
            $table->text('plant_dev')->nullable()->after('id');
            $table->text('div_title')->nullable()->after('title_th');
            $table->text('div_center')->nullable()->after('div_title');
            $table->text('hrms_full_id')->nullable()->after('div_center');
            $table->text('plant')->nullable()->after('hrms_dept_id');
            $table->text('group')->nullable()->after('plant');
            $table->text('type')->nullable()->after('group');
            $table->text('cent')->nullable()->after('type');
            $table->text('div')->nullable()->after('cent');
            $table->text('dept')->nullable()->after('div');
            $table->text('sec')->nullable()->after('dept');
            $table->text('upload_hrms')->nullable()->after('sec');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
