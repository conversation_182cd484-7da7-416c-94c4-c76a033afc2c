<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateHomeContentSubtitleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('home_content', function (Blueprint $table) {
            $table->text('subtitle')->nullable()->after('title');
        });
        Schema::create('home_content_km_log', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->text('image')->nullable();
            $table->text('title')->nullable();
            $table->text('subtitle')->nullable();
            $table->text('link')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
