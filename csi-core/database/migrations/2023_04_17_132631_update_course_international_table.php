<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCourseInternationalTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('course', function (Blueprint $table) {
            $table->integer('international')->default(1)->after('id')->comment('1:No/2:Yes');
        });

        Schema::table('home_banner', function (Blueprint $table) {
            $table->integer('page')->default(1)->after('id')->comment('1:Home/2:Course/3:International Course');
        });

        Schema::create('setting_center_review', function (Blueprint $table) {
            $table->id();
            $table->text('title')->nullable();
            $table->text('value')->nullable();
            $table->text('thumb')->nullable();
            $table->datetime('start')->nullable();
            $table->datetime('end')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
