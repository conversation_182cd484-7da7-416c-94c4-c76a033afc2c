<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCheckoutFieldsPriceToUserOrderLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->decimal('checkout_fields_price', 10, 2)->default(0)->after('total_price')->comment('Additional price from checkout fields');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->dropColumn('checkout_fields_price');
        });
    }
}
