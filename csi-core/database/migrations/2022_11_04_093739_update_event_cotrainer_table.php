<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateEventCotrainerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('event', function (Blueprint $table) {
            $table->text('co_trainer')->nullable()->after('trainer_name');
            $table->text('is_limit')->nullable()->after('co_trainer');
        });
        Schema::create('event_training_log', function (Blueprint $table) {
            $table->id();
            $table->foreignId('speaker_id')->constrained('categories_speaker');
            $table->foreignId('event_id')->constrained('event');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
