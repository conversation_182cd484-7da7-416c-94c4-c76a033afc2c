<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHomeBannerWidgetTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('home_banner_widget', function (Blueprint $table) {
            $table->id();
            $table->text('icon')->nullable();
            $table->text('title')->nullable();
            $table->text('description')->nullable();
            $table->text('link')->nullable();
            $table->timestamps();
        });
        Schema::create('home_content', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->nullable();
            $table->text('name')->nullable();
            $table->text('color')->nullable();
            $table->float('size')->nullable();
            $table->integer('playlist')->nullable();
            $table->integer('popular_all')->nullable();
            $table->text('learner')->nullable();
            $table->text('level')->nullable();
            $table->text('category')->nullable();
            $table->text('gender')->nullable();
            $table->text('department')->nullable();
            $table->text('speaker')->nullable();
            $table->text('tag')->nullable();
            $table->integer('group_id')->nullable();
            $table->datetime('start_date')->nullable();
            $table->datetime('end_date')->nullable();
            $table->integer('sub_day')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('home_content_category', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('category')->nullable();
            $table->timestamps();
        });
        Schema::create('home_content_department', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('department')->nullable();
            $table->timestamps();
        });
        Schema::create('home_content_gender', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('gender')->nullable();
            $table->timestamps();
        });
        Schema::create('home_content_learner', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('learner')->nullable();
            $table->timestamps();
        });
        Schema::create('home_content_level', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('level')->nullable();
            $table->timestamps();
        });
        Schema::create('home_content_popular_manual', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('home_content_speaker', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('speaker')->nullable();
            $table->timestamps();
        });
        Schema::create('home_content_speaker_manual', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('speaker_id')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('home_tag_log', function (Blueprint $table) {
            $table->id();
            $table->integer('home_content_id')->nullable();
            $table->integer('tag')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('home_banner_widget');
    }
}
