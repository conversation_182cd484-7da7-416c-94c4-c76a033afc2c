<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePeakIntegrationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // เพิ่มฟิลด์ Peak ใน table user (สำหรับ contact)
        Schema::table('user', function (Blueprint $table) {
            $table->string('peak_contact_id')->nullable()->after('birthday')->comment('Peak Contact ID');
            $table->text('tax_number')->nullable()->after('peak_contact_id')->comment('เลขประจำตัวผู้เสียภาษี');
            $table->integer('peak_sync_status')->default(1)->after('tax_number')->comment('1:Not Synced/2:Synced/3:Error');
            $table->datetime('peak_last_sync')->nullable()->after('peak_sync_status')->comment('วันที่ sync ล่าสุด');
        });

        // เพิ่มฟิลด์ Peak ใน table course (สำหรับ product)
        Schema::table('course', function (Blueprint $table) {
            $table->string('peak_product_id')->nullable()->after('international')->comment('Peak Product ID');
            $table->string('peak_product_code')->nullable()->after('peak_product_id')->comment('Peak Product Code');
            $table->integer('peak_sync_status')->default(1)->after('peak_product_code')->comment('1:Not Synced/2:Synced/3:Error');
            $table->datetime('peak_last_sync')->nullable()->after('peak_sync_status')->comment('วันที่ sync ล่าสุด');
        });

        // เพิ่มฟิลด์ Peak ใน table user_order_log (สำหรับ invoice/receipt)
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->string('peak_contact_id')->nullable()->after('progress_report')->comment('Peak Contact ID');
            $table->string('peak_quotation_id')->nullable()->after('peak_contact_id')->comment('Peak Quotation ID');
            $table->string('peak_invoice_id')->nullable()->after('peak_quotation_id')->comment('Peak Invoice ID');
            $table->string('peak_receipt_id')->nullable()->after('peak_invoice_id')->comment('Peak Receipt ID');
            $table->text('peak_invoice_link')->nullable()->after('peak_receipt_id')->comment('Peak Invoice Document Link');
            $table->text('peak_receipt_link')->nullable()->after('peak_invoice_link')->comment('Peak Receipt Document Link');
            $table->integer('peak_sync_status')->default(1)->after('peak_receipt_link')->comment('1:Not Synced/2:Synced/3:Error');
            $table->datetime('peak_last_sync')->nullable()->after('peak_sync_status')->comment('วันที่ sync ล่าสุด');
            $table->text('peak_error_message')->nullable()->after('peak_last_sync')->comment('ข้อความ Error จาก Peak');
        });

        Schema::table('subscription', function (Blueprint $table) {
            $table->string('peak_product_id')->nullable()->after('status')->comment('Peak Product ID');
            $table->string('peak_product_code')->nullable()->after('peak_product_id')->comment('Peak Product Code');
            $table->integer('peak_sync_status')->default(1)->after('peak_product_code')->comment('1:Not Synced/2:Synced/3:Error');
            $table->datetime('peak_last_sync')->nullable()->after('peak_sync_status')->comment('วันที่ sync ล่าสุด');
            $table->text('peak_error_message')->nullable()->after('peak_last_sync')->comment('ข้อความ Error จาก Peak');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscription', function (Blueprint $table) {
            $table->dropColumn([
                'peak_product_id',
                'peak_product_code',
                'peak_sync_status',
                'peak_last_sync',
                'peak_error_message'
            ]);
        });

        Schema::table('user_order_log', function (Blueprint $table) {
            $table->dropColumn([
                'peak_contact_id',
                'peak_quotation_id',
                'peak_invoice_id',
                'peak_receipt_id',
                'peak_invoice_link',
                'peak_receipt_link',
                'peak_sync_status',
                'peak_last_sync',
                'peak_error_message'
            ]);
        });

        Schema::table('course', function (Blueprint $table) {
            $table->dropColumn([
                'peak_product_id',
                'peak_product_code',
                'peak_sync_status',
                'peak_last_sync'
            ]);
        });

        Schema::table('user', function (Blueprint $table) {
            $table->dropColumn([
                'peak_contact_id',
                'tax_number',
                'peak_sync_status',
                'peak_last_sync'
            ]);
        });
    }
}
