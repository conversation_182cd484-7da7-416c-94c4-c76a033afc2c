<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateAdminTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admin', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->char('username',50)->unique();
            $table->string('password');
            $table->string('avatar')->nullable();
            $table->integer('status')->default('1');
            $table->tinyInteger('level');
            $table->integer('company_id')->nullable();
            $table->integer('company_level')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        DB::table('admin')->insert(
            array(
                'name' => 'Lrk SuperAdmin',
                'username' => '<EMAIL>',
                'password' => '$2y$10$Xqdwo/UJMLJ8NuJ1J.2X6.rbw.f60udXjhr0k7wZLE7gJlFOKN2Hm',
                'avatar' => '',
                'level' => '99'
            )
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin');
    }
}
