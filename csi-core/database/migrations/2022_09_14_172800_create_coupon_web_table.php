<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCouponWebTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupon_web', function (Blueprint $table) {
            $table->id();
            $table->integer('sponsor_type')->default(2)->comment('1:Y/2:N');
            $table->text('sponsor')->nullable();
            $table->integer('no')->nullable();
            $table->integer('discount_type')->default(1)->comment('2:เปอร์เซนท์/1:จำนวนเต็ม');
            $table->integer('amount')->nullable();
            $table->text('prefix')->nullable();
            $table->integer('min_spend')->nullable();
            $table->integer('max_spend')->nullable();
            $table->text('allow_email')->nullable()->comment('ระบุsubscription');
            $table->integer('limitation_coupon')->nullable()->comment('ระบุกลุ่ม');
            $table->integer('limitation_user')->nullable()->comment('ระบุsubscription');
            $table->datetime('started_date')->nullable()->comment('ระบุกลุ่ม');
            $table->datetime('end_date')->nullable()->comment('ระบุกลุ่ม');
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('coupon_web_code', function (Blueprint $table) {
            $table->id();
            $table->integer('coupons_bulk_id')->nullable();
            $table->text('code')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('coupon_web_sponsor', function (Blueprint $table) {
            $table->id();
            $table->integer('coupons_bulk_id')->nullable();
            $table->integer('sponsor_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupon_web');
    }
}
