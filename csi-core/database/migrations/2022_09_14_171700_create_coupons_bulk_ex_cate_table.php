<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCouponsBulkExCateTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupons_bulk_ex_cate', function (Blueprint $table) {
            $table->id();
            $table->integer('coupons_bulk_id')->nullable();
            $table->integer('cate_id')->nullable();
            $table->timestamps();
        });
        Schema::create('coupons_bulk_ex_course', function (Blueprint $table) {
            $table->id();
            $table->integer('coupons_bulk_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
        Schema::create('coupons_bulk_gen', function (Blueprint $table) {
            $table->id();
            $table->integer('coupon_type')->default(1);
            $table->integer('no')->nullable();
            $table->integer('discount_type')->default(1)->comment('2:เปอร์เซนท์/1:จำนวนเต็ม');
            $table->integer('amount')->nullable();
            $table->integer('valid')->default(1);
            $table->integer('valid_type')->default(1)->comment('1:days/2:month');

            $table->text('prefix')->nullable();
            $table->text('suffix')->nullable();

            $table->integer('length')->nullable();
            $table->integer('min_spend')->nullable();
            $table->integer('max_spend')->nullable();
            $table->integer('individual_use_only')->default(1)->comment('1:Yes/2:No');
            $table->integer('exclude_sale_item')->default(1)->comment('1:Yes/2:No');
            $table->integer('select_type')->nullable();
            $table->text('sub_tag')->nullable()->comment('ระบุsubscription');
            $table->text('group_tag')->nullable()->comment('ระบุกลุ่ม');

            $table->integer('select_cate_option')->nullable();
            $table->text('including_course')->nullable()->comment('ระบุsubscription');
            $table->text('excluding_course')->nullable()->comment('ระบุกลุ่ม');
            $table->text('only_cate')->nullable()->comment('ระบุsubscription');
            $table->text('excluding_cate')->nullable()->comment('ระบุกลุ่ม');
            $table->text('allow_email')->nullable()->comment('ระบุsubscription');
            $table->integer('limitation_coupon')->nullable()->comment('ระบุกลุ่ม');
            $table->integer('limitation_user')->nullable()->comment('ระบุsubscription');
            $table->datetime('expiry')->nullable()->comment('ระบุกลุ่ม');

            $table->datetime('started_date')->nullable()->comment('ระบุกลุ่ม');
            $table->datetime('end_date')->nullable()->comment('ระบุกลุ่ม');
            
            $table->integer('sponsor_type')->default(2)->comment('1:Y/2:N');
            $table->text('sponsor')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('coupons_bulk_in_cate', function (Blueprint $table) {
            $table->id();
            $table->integer('coupons_bulk_id')->nullable();
            $table->integer('cate_id')->nullable();
            $table->timestamps();
        });
        Schema::create('coupon_bulk_sponsor_log', function (Blueprint $table) {
            $table->id();
            $table->integer('coupons_bulk_id')->nullable();
            $table->integer('code_id')->nullable();
            $table->integer('sponsor_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupons_bulk_ex_cate');
    }
}
