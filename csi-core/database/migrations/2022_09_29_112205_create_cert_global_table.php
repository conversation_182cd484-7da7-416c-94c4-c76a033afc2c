<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCertGlobalTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cert_global', function (Blueprint $table) {
            $table->id();
            $table->integer('lang_type')->default(1);
            $table->integer('type')->default(1);
            $table->text('category')->nullable();
            $table->text('bg_color')->nullable();
            $table->text('bg_color_en')->nullable();
            $table->text('course_director')->nullable();
            $table->text('course_director_en')->nullable();
            $table->text('director_position')->nullable();
            $table->text('director_position_en')->nullable();
            $table->text('signature')->nullable();
            $table->text('signature_en')->nullable();
            $table->text('course_director_2')->nullable();
            $table->text('course_director_2_en')->nullable();
            $table->text('director_position_2')->nullable();
            $table->text('director_position_2_en')->nullable();
            $table->text('signature_2')->nullable();
            $table->text('signature_2_en')->nullable();
            $table->text('course_director_3')->nullable();
            $table->text('course_director_3_en')->nullable();
            $table->text('director_position_3')->nullable();
            $table->text('director_position_3_en')->nullable();
            $table->text('signature_3')->nullable();
            $table->text('signature_3_en')->nullable();
            $table->integer('status')->default(1)->comment('1:active/2:inactive');
            $table->timestamps();
        });
        Schema::create('cert_global_log', function (Blueprint $table) {
            $table->id();
            $table->integer('cert_global_id')->nullable();
            $table->integer('category_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cert_global');
    }
}
