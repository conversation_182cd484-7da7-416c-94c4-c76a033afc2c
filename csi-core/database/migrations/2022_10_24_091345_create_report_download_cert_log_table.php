<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReportDownloadCertLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('report_download_cert_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('cert_id')->nullable()->comment('relate : user_certificate_log');
            $table->timestamps();
        });
        Schema::table('user_certificate_log', function (Blueprint $table) {
            $table->integer('graduate_id')->nullable()->after('certificate_type');
            $table->integer('cert_id')->nullable()->after('graduate_id');
            $table->integer('global_cert_id')->nullable()->after('cert_id');
            $table->integer('group_cert_id')->nullable()->after('global_cert_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('report_download_cert_log');
    }
}
