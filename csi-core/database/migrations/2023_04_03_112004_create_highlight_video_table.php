<?php

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use Illuminate\Support\Facades\DB;

class CreateHighlightVideoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('setting_center', function (Blueprint $table) {
            $table->id();
            $table->text('title')->nullable();
            $table->text('value')->nullable();
            $table->text('key')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });

        

        DB::table('setting_center')->insert(
            array(
                'title' => 'Home Video',
                'value' => 'https://vimeo.com/813414308',
                'key' => 'home_vdo',
                'status' => '1',
            )
        );

        DB::table('setting_center')->insert(
            array(
                'title' => 'Review Video',
                'value' => null,
                'key' => 'review_vdo',
                'status' => '1',
            )
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('highlight_video');
    }
}
