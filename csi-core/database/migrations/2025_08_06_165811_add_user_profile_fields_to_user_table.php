<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUserProfileFieldsToUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->text('occupation')->nullable()->after('position')->comment('อาชีพ');
            $table->text('company_name')->nullable()->after('occupation')->comment('ชื่อบริษัท');
            $table->text('business_type')->nullable()->after('company_name')->comment('ประเภทธุรกิจ');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->dropColumn(['occupation', 'company_name', 'business_type']);
        });
    }
}
