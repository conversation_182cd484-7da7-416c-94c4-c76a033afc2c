<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_age', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_department', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_domain', function (Blueprint $table) {
            $table->id();
            $table->text('title')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_gender', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_host', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_key', function (Blueprint $table) {
            $table->id();
            $table->text('title')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_learner', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('tag_learner')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_level', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_logo', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('logo')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_playlist', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_speaker', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('name_en')->nullable();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->text('avatar')->nullable();
            $table->text('image')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_topic', function (Blueprint $table) {
            $table->id();
            $table->text('title_th')->nullable();
            $table->text('title_en')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('categories');
    }
}
