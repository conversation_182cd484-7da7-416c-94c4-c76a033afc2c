<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSettingCenterReviewLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('review_video_log', function (Blueprint $table) {
            $table->id();
            $table->foreignId('video_id')->constrained('setting_center_review');
            $table->text('title')->nullable();
            $table->text('description')->nullable();
            $table->text('value')->nullable();
            $table->text('thumb')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('setting_center_review_log');
    }
}
