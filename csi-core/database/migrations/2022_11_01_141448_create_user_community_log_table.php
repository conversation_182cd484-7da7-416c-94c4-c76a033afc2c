<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserCommunityLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_community_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->text('feed')->nullable();
            $table->integer('pin')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('user_community_replied_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('feed_id')->nullable();
            $table->text('comment')->nullable();
            $table->text('image')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_community_log');
    }
}
