<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateHomeContentCtaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('home_banner', function (Blueprint $table) {
            $table->text('title')->nullable()->after('name');
            $table->text('subtitle')->nullable()->after('title');
            $table->integer('object_position')->nullable()->after('position');
        });
        Schema::table('home_content', function (Blueprint $table) {
            $table->text('background')->nullable()->after('size');
            $table->text('title')->nullable()->after('background');
            $table->text('cta')->nullable()->after('title');
            $table->text('link')->nullable()->after('cta');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
