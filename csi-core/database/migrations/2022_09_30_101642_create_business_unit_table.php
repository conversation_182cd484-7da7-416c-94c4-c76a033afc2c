<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBusinessUnitTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('business_unit', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('name_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('sub_unit', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('name_en')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('course_business_unit_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('business_id')->nullable();
            $table->timestamps();
        });
        Schema::create('course_sub_unit_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('sub_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('business_unit');
    }
}
