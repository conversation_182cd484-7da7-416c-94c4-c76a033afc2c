<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateEmailTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email', function (Blueprint $table) {
            $table->id();
            $table->text('role')->nullable();
            $table->text('email')->nullable();
            $table->text('cc')->nullable();
            $table->text('bcc')->nullable();
            $table->timestamps();
        });
        Schema::create('email_dynamic', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('subject')->nullable();
            $table->longtext('details')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'แจ้งหลักฐานการชำระเงิน',
                'subject' => 'แจ้งหลักฐานการชำระเงิน',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                                style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ตรวจสอบการชำระเงิน
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;">
                            คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;">
                            ได้แนบหลักฐานการชำระเงิน 
                            <br>หมายเลขคำสั่งซื้อ {{order_no}}
                            <br>ณ วันที่ {{order_date}}
                            <br>ตรวจสอบข้อมูลได้ที่นี่
                            <br>{{link}}
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 10px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table
                    style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'อนุมัติรายการสั่งซื้อ',
                'subject' => 'รายการสั่งซื้อของคุณ',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                                style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ขอบคุณสำหรับการสั่งซื้อของคุณ
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;">
                            สวัสดีค่ะ คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;">
                            ระบบได้อนุมัติคำสั่งซื้อของท่านแล้ว 
                            <br>หมายเลขคำสั่งซื้อ {{order_no}}
                            <br>ณ วันที่ {{order_date}}
                            <br>ผ่านช่องทาง {{payment_channel}} แล้ว!
                            <br>ตรวจสอบรายการสั่งซื้อได้ที่นี่
                            <br>{{link}}
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 10px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:left;padding: 0 0 30px 0;">
                    {{order_list}}
<tr>
                        <td style="width:30px;"></td>
                        <td colspan="3" style="width: 100px;text-align: right;">{{discount_web}}</td>
                        <td style="width:30px;"></td>
                    </tr>
                    <tr>
                        <td style="width:30px;"></td>
                        <td colspan="3" style="width: 100px;text-align: right;"><b>รวมทั้งสิ้น : {{order_total}} THB</b></td>
                        <td style="width:30px;"></td>
                    </tr>
                </table>
                <table
                    style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                            CSI Society <br>
                            Address <br>
                            <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                            <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'ได้รับคำสั่งซื้อ',
                'subject' => 'รายการสั่งซื้อของคุณ',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                                style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ขอบคุณสำหรับการสั่งซื้อของคุณ
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;">
                            สวัสดีค่ะ คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;">
                            ระบบได้รับคำสั่งซื้อของท่านแล้ว 
                            <br>หมายเลขคำสั่งซื้อ {{order_no}}
                            <br>ณ วันที่ {{order_date}}
                            <br>ตรวจสอบรายการสั่งซื้อได้ที่นี่
                            <br>{{link}}
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 10px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:left;padding: 0 0 30px 0;">
                    {{order_list}}
<tr>
                        <td style="width:30px;"></td>
                        <td colspan="3" style="width: 100px;text-align: right;">{{discount_web}}</td>
                        <td style="width:30px;"></td>
                    </tr>
                    <tr>
                        <td style="width:30px;"></td>
                        <td colspan="3" style="width: 100px;text-align: right;"><b>รวมทั้งสิ้น : {{order_total}} THB</b></td>
                        <td style="width:30px;"></td>
                    </tr>
                </table>
                <table
                    style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'ตรวจสอบแบบทดสอบ',
                'subject' => 'ตรวจสอบแบบทดสอบ',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                                style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ตรวจสอบแบบทดสอบ
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;">
                            คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;">
                            ได้ส่งคำตอบแบบทดสอบ 
                            <br>หัวข้อ : {{title}}
                            <br>ณ วันที่ {{date}}
                            <br>ตรวจสอบแบบทดสอบได้ที่นี่
                            <br>{{link}}
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 10px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table
                    style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'ผลตรวจแบบทดสอบ',
                'subject' => 'ผลตรวจแบบทดสอบ',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                                style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ตรวจสอบแบบทดสอบ
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;">
                            คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;">
                            ระบบได้ตรวจสอบผลสอบของคุณ 
                            <br>หัวข้อ : {{title}}
                            <br>ณ วันที่ {{date}}
                            <br>ตรวจสอบผลสอบได้ที่นี่
                            <br>{{link}}
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 10px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table
                    style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'ได้รับคะแนน',
                'subject' => 'ยินดีด้วย! คุณได้รับคะแนน',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png" style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            สวัสดี คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;line-height: 1.6;">
                            ยินดีด้วย! คุณได้รับคะแนน : {{point}} คะแนน
                            <br>จากการเรียน : {{title}}<br>คะแนน CME ของท่านจะถูกอัพเดทกับแพทยสภาภายหลังจาก 45 วัน<br>สามารถดูคะแนนทั้งหมดได้ที่โปรไฟล์ของท่าน
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 30px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'คอร์สกำลังจะหมดอายุ',
                'subject' => 'คอร์สของคุณกำลังจะหมดอายุ',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px;"></td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: left;padding: 0 15px;">
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                            style="max-width: 100%; width: 60px;padding: 5px 0 0 0;">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                                style="max-width: 100%; width: 60%;padding: 0 0;">
            
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ระบบแจ้งเตือนอัตโนมัติ
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;">
                            สวัสดี คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                </table>
            
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 18px;text-decoration: underline;">
                            คอร์ส {{course}}<br>ของคุณกำลังจะหมดอายุภายใน
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 82px; color: #0271cc;">
                            {{date}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 18px;">
                            วัน
                        </td>
                    </tr>
                </table>
            
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-size: 16px; padding: 0 15px;">
                            <p style="margin: 0 0 0 0;"><b>เข้าเรียนได้ที่ : </b> {{link}}</p></td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 10px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table
                    style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );
        
        DB::table('email_dynamic')->insert(
            array(
                'name' => 'อนุมัติการลงทะเบียนเรียน',
                'subject' => 'การลงทะเบียนเรียน',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png"
                                style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ขอบคุณสำหรับการลงทะเบียนเรียน
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;">
                            สวัสดีค่ะ คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;">
                            ระบบได้อนุมัติลงทะเบียนเรียนของท่านแล้ว 
                            <br>ณ วันที่ {{order_date}}
                            <br>สามารถคลิ๊กเข้าเรียนได้ที่
                            <br>{{link}}
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 10px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:left;padding: 0 0 30px 0;">
                    {{order_list}}
                    <tr>
                        <td style="width:30px;"></td>
                        <td colspan="3" style="width: 100px;text-align: right;"><b>รวมทั้งสิ้น : {{order_total}} THB</b></td>
                        <td style="width:30px;"></td>
                    </tr>
                </table>
                <table
                    style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'ติดต่อสอบถาม',
                'subject' => 'ข้อมูลการติดต่อสอบถาม',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png" style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            คุณ {{name}} {{lastname}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;line-height: 1.6;">
                            ได้สอบถามข้อมูลเพิ่มเติม
                            <br>คอร์ส : {{title}}
                            <br>ตรวจสอบได้ที่นี่ {{link}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 30px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );

        DB::table('email_dynamic')->insert(
            array(
                'name' => 'ได้รับของขวัญ',
                'subject' => 'ยินดีด้วย! คุณได้รับของขวัญ',
                'details' => '<body style="background-color: #F3F3F3;">
                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png" style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            คุณ {{sender}} ส่งของขวัญให้คุณ<br>{{message}}
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fdfdfd;max-width: 600px;margin: 0 auto;width: 100%;text-align:left;padding: 0 0 30px 0;">
                    {{order_list}}
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td style="text-align: center;padding: 15px 30%;">
                            <a href="{{link}}" style="width: 120px;" >
                                <button style="width: 100%;background-color: #0271cc;color: #fff;border: 0;padding: 10px 10px;font-size: 16px;cursor: pointer;">คลิกรับของขวัญ</button>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 5px;">
                            หรือ ในกรณีที่คุณคลิกที่ปุ่มไม่ได้ กรุณาหรือคลิกที่ URL นี้
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 5px;">
                            <a href="{{link}}" style="color:#0271cc;">{{link}}</a>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 30px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );
        
        DB::table('email_dynamic')->insert(
            array(
                'name' => 'การส่งอีเมล์แจ้งเตือน',
                'subject' => 'การส่งอีเมล์แจ้งเตือน',
                'details' => '
                <table id="ib4x" style="background-color:#0271cc;max-width:600px;margin:0 auto;width:100%;"><tbody><tr><td id="ijfi" style="height:5px;">
                      </td></tr></tbody></table>
                <table id="itpd" style="background-color:#fff;max-width:600px;margin:0 auto;width:100%;text-align:center;"><tbody>
                <tr><td><img src="https://core.csisociety.com/assets/images/logo/logo.png" id="idsj" style="padding:15px 0;" alt="header-logo-dark.png" /></td></tr>
                <tr><td id="iri6t" style="text-align:center;font-weight:bold;font-size:18px;padding-bottom:10px;">
                        ตรวจสอบแบบทดสอบ
                      </td></tr>
                <tr><td id="intm8" style="text-align:center;font-weight:300;font-size:16px;padding-bottom:15px;">
                        คุณ {{name}} {{lastname}}
                      </td></tr>
                <tr><td id="i71v1" style="text-align:center;font-weight:300;font-size:16px;">
                        ระบบได้ตรวจสอบผลสอบของคุณ 
                        <br />หัวข้อ : {{title}}
                        <br />ณ วันที่ {{date}}
                        <br />ตรวจสอบผลสอบได้ที่นี่
                        <br />{{link}}
                      </td></tr>
                </tbody></table>
                <table id="iy9de" style="background-color:#fdfdfd;max-width:600px;margin:0 auto;width:100%;text-align:center;"><tbody><tr><td id="ig7cy" style="text-align:center;font-size:12px;padding:10px 10px 10px 10px;"><u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล
                        </u></td></tr></tbody></table>
                <table id="i51il" style="background-color:#0271cc;max-width:600px;margin:0 auto;width:100%;text-align:center;border-top:2px solid #0271cc;"><tbody><tr><td id="iuoog" style="text-align:center;font-size:14px;padding:10px;line-height:1.6;color:#fff;">
                CSI Society <br>
                Address <br>
                <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                </td></tr></tbody></table>
                ',
            )
        );
        DB::table('email_dynamic')->insert(
            array(
                'name' => 'แจ้งปลดล็อกใบประกาศ',
                'subject' => 'ยินดีด้วย! คุณปลดล็อกใบประกาศ',
                'details' => '<body style="background-color: #F3F3F3;">

                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;border-radius: 18px 18px 0 0;">
                    <tr>
                        <td style="height: 5px">
                        </td>
                    </tr>
                </table>
                <table style="background-color:#fff;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;">
                    <tr>
                        <td>
                            <img src="https://core.csisociety.com/assets/images/logo/logo.png" style="max-width: 100%; width: 30%;padding: 15px 0;">
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: bold; font-size: 18px;padding-bottom: 10px;">
                            ยินดีด้วย! คุณปลดล็อกใบประกาศ<br>{{certificate}}
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-weight: 300; font-size: 16px;padding-bottom: 15px;line-height: 1.6;">
                            สามารถดาวน์โหลดใบประกาศได้ที่โปรไฟล์ของท่าน
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align:center;padding:15px 30%;">
                            <a href="{{link}}" style="width:120px;"><button style="width:100%;
                            background-color:#0271cc;
                            color:#fff;
                            border:0;
                            padding:10px 10px;
                            font-size:16px;
                            cursor:pointer;">หรือคลิกที่นี่</button></a>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; font-size: 12px;padding: 10px 10px 30px 10px;">
                            <u>นี่เป็นเมลอัตโนมัติ โปรดอย่าตอบกลับอีเมล</u>
                        </td>
                    </tr>
                </table>
                <table style="background-color:#0271cc;max-width: 600px;margin: 0 auto;width: 100%;text-align:center;border-top: 2px solid #0271cc;border-radius: 0 0 18px 18px;">
                    <tr>
                        <td style="text-align: center; font-size: 14px;padding: 10px;line-height: 1.6;color: #fff;">
                        CSI Society <br>
                        Address <br>
                        <a href="tel:0 0000 0000" style="color: #fff;">0 0000 0000</a><br>
                        <a href="mailto:<EMAIL>" style="color: #fff;"><EMAIL></a>
                        </td>
                    </tr>
                </table>
            
            </body>',
            )
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email');
        Schema::dropIfExists('email_dynamic');
    }
}
