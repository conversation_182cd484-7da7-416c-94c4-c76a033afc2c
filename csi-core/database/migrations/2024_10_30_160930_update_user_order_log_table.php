<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserOrderLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->string('credit_payment_type', 20)->nullable()->after('payment_type')
                  ->comment("'full' for full payment, 'installment' for 0% installment");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->dropColumn('credit_payment_type');
        });
    }
}
