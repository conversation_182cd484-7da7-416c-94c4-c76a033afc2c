<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVolumnByTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('volumn_by', function (Blueprint $table) {
            $table->id();
            $table->text('slug')->nullable();
            $table->text('company_image')->nullable();
            $table->text('company_name')->nullable();
            $table->text('company_title')->nullable();
            $table->text('domain')->nullable();
            $table->text('company_subtitle')->nullable();
            $table->text('company_details')->nullable();
            $table->text('admin_name')->nullable();
            $table->char('username',50)->unique()->nullable();
            $table->string('password')->nullable();
            $table->integer('status')->nullable();
            $table->text('meta_title')->nullable();
            $table->text('meta_keyword')->nullable();
            $table->text('meta_image')->nullable();
            $table->text('meta_description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('volumn_by');
    }
}
