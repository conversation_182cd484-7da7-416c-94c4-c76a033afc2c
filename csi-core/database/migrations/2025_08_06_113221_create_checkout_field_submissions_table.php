<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCheckoutFieldSubmissionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('checkout_field_submissions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('checkout_field_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('course_id')->nullable()->comment('If submitted for a course');
            $table->unsignedBigInteger('subscription_id')->nullable()->comment('If submitted for a subscription');
            $table->text('field_value')->nullable()->comment('The submitted value for this field');
            $table->timestamp('submitted_at');
            $table->timestamps();

            $table->foreign('checkout_field_id')->references('id')->on('checkout_fields')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('user')->onDelete('cascade');
            $table->foreign('order_id')->references('id')->on('user_order_log')->onDelete('cascade');
            $table->foreign('course_id')->references('id')->on('course')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscription_main')->onDelete('cascade');
            
            $table->index(['order_id', 'checkout_field_id']);
            $table->index(['user_id', 'order_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('checkout_field_submissions');
    }
}
