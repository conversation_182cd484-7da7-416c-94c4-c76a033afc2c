<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubscriptionCheckoutFieldsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscription_checkout_fields', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('subscription_main_id')->nullable();
            $table->unsignedBigInteger('subscription_id')->nullable();
            $table->unsignedBigInteger('checkout_field_id');
            $table->integer('position')->default(0);
            $table->boolean('is_required_override')->nullable()->comment('override field default required setting');
            $table->timestamps();

            $table->foreign('subscription_main_id')->references('id')->on('subscription_main')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscription')->onDelete('cascade');
            $table->foreign('checkout_field_id')->references('id')->on('checkout_fields')->onDelete('cascade');
            $table->index(['subscription_main_id', 'position']);
            $table->index(['subscription_id', 'position']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscription_checkout_fields');
    }
}
