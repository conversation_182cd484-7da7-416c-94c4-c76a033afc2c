<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateJobLevelLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_job_level_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('job_level_id')->nullable();
            $table->timestamps();
        });
        Schema::table('user', function (Blueprint $table) {
            $table->integer('job_level')->nullable()->after('e_id');
        });
        Schema::table('course', function (Blueprint $table) {
            $table->text('job_level')->nullable()->after('level');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('job_level_log');
    }
}
