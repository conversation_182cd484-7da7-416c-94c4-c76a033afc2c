<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCouponWebLog extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coupon_web', function (Blueprint $table) {
            $table->integer('condition_type')->default(1)->after('limitation_user')->comment('1:ไม่ระบุ/2:include course/3:exclude course');
            $table->text('include_course')->nullable()->after('condition_type');
            $table->text('exclude_course')->nullable()->after('include_course');
        });
        Schema::create('coupon_web_include', function (Blueprint $table) {
            $table->id();
            $table->integer('coupon_web_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
        Schema::create('coupon_web_exclude', function (Blueprint $table) {
            $table->id();
            $table->integer('coupon_web_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
