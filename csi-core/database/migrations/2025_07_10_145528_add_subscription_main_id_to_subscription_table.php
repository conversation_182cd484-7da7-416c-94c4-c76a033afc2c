<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSubscriptionMainIdToSubscriptionTable extends Migration
{
    public function up()
    {
        Schema::table('subscription', function (Blueprint $table) {
            $table->unsignedBigInteger('subscription_main_id')->nullable()->after('id');
            $table->foreign('subscription_main_id')->references('id')->on('subscription_main')->onDelete('set null');
            $table->index('subscription_main_id');
        });
    }

    public function down()
    {
        Schema::table('subscription', function (Blueprint $table) {
            $table->dropForeign(['subscription_main_id']);
            $table->dropColumn('subscription_main_id');
        });
    }
}