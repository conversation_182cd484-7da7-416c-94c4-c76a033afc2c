<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseCheckoutFieldsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_checkout_fields', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('course_id');
            $table->unsignedBigInteger('checkout_field_id');
            $table->integer('position')->default(0);
            $table->boolean('is_required_override')->nullable()->comment('override field default required setting');
            $table->timestamps();

            $table->foreign('course_id')->references('id')->on('course')->onDelete('cascade');
            $table->foreign('checkout_field_id')->references('id')->on('checkout_fields')->onDelete('cascade');
            $table->unique(['course_id', 'checkout_field_id']);
            $table->index(['course_id', 'position']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_checkout_fields');
    }
}
