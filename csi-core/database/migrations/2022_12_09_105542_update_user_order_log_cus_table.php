<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserOrderLogCusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->text('cus_name')->nullable()->after('postcode');
            $table->text('cus_lastname')->nullable()->after('cus_name');
            $table->text('cus_email')->nullable()->after('cus_lastname');
            $table->text('cus_mobile')->nullable()->after('cus_email');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
