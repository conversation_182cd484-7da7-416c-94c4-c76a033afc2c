<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserLevelTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->integer('level')->default(1)->after('job_level')->comment('1=user/2=head');
        });
        Schema::table('volumn_course_log', function (Blueprint $table) {
            $table->integer('type')->default(1)->after('course_id')->comment('1=optional/2=compulsory');
        });
        Schema::table('volumn_user_log', function (Blueprint $table) {
            $table->integer('user_id')->nullable()->after('email');
        });
        Schema::table('volumn_by', function (Blueprint $table) {
            $table->integer('user_id')->nullable()->after('company_name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
