<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSubscriptionMainIdToHomeContentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('home_content', function (Blueprint $table) {
            $table->unsignedBigInteger('subscription_main_id')->nullable()->after('category');
            $table->index('subscription_main_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('home_content', function (Blueprint $table) {
            $table->dropIndex(['subscription_main_id']);
            $table->dropColumn('subscription_main_id');
        });
    }
}
