<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEventTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('event', function (Blueprint $table) {
            $table->id();
            $table->text('title')->nullable();
            $table->text('subtitle')->nullable();
            $table->text('details')->nullable();
            $table->datetime('public_date')->nullable();
            $table->datetime('started_enroll')->nullable();
            $table->datetime('end_enroll')->nullable();
            $table->datetime('started_event')->nullable();
            $table->datetime('end_event')->nullable();
            $table->text('image')->nullable();
            $table->text('thumb')->nullable();
            $table->text('trainer_name')->nullable();
            $table->integer('amount')->default(0);
            $table->text('slug')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('event_enrolment_log', function (Blueprint $table) {
            $table->id();
            $table->integer('event_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('event');
    }
}
