<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTaxFieldsToUserOrderLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->tinyInteger('tax_type')->default(2)->after('checkout_fields_price')->comment('1=Corporate (VAT+Tax), 2=Individual (VAT only)');
            $table->decimal('vat_amount', 10, 2)->default(0)->after('tax_type')->comment('VAT 7% amount');
            $table->decimal('tax_amount', 10, 2)->default(0)->after('vat_amount')->comment('Additional tax 3% for corporate');
            $table->decimal('price_before_tax', 10, 2)->default(0)->after('tax_amount')->comment('Price before VAT calculation');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->dropColumn(['tax_type', 'vat_amount', 'tax_amount', 'price_before_tax']);
        });
    }
}
