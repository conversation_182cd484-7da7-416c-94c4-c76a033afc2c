<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAssessmentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assessment', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->default(1);
            $table->text('question_th')->nullable();
            $table->text('question_en')->nullable();
            $table->integer('point_type')->default(1);
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });

        Schema::create('assessment_choice', function (Blueprint $table) {
            $table->id();
            $table->integer('assessment_id')->nullable();
            $table->text('choice_th')->nullable();
            $table->text('choice_en')->nullable();
            $table->text('icon')->nullable();
            $table->integer('point')->default(0);
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });

        Schema::create('assessment_log', function (Blueprint $table) {
            $table->id();
            $table->integer('assessment_id')->nullable();
            $table->integer('etc_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('choice')->nullable();
            $table->text('input')->nullable();
            $table->integer('point')->default(0);
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assessment');
    }
}
