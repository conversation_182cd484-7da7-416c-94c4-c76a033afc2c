<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseFaqTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_faq', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->text('question')->nullable();
            $table->text('answer')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });

        Schema::create('course_section', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('type')->default(1)->comment('1:text/2:ep/3:exam');
            $table->text('title')->nullable();
            $table->text('details')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_faq');
    }
}
