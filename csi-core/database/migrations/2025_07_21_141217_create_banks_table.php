<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBanksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('banks', function (Blueprint $table) {
            $table->id();
            $table->enum('bank_name', [
                'กสิกรไทย',
                'กรุงเทพ',
                'กรุงไทย',
                'ทีเอ็มบีธนชาต',
                'ไทยพาณิชย์',
                'กรุงศรีอยุธยา',
                'ออมสิน',
                'อาคารสงเคราะห์',
                'เกียรตินาคิน',
                'ซีไอเอ็มบี',
                'ทิสโก้',
                'ยูโอบี',
                'แลนด์ แอนด์ เฮาส์',
                'ไอซีบีซี',
                'อิสลามแห่งประเทศไทย',
                'พร้อมเพย์'
            ]); // ชื่อธนาคาร/พร้อมเพย์
            $table->string('account_name', 255); // ชื่อบัญชี
            $table->string('account_number', 100)->nullable(); // เลขบัญชี (ถ้าเป็นธนาคาร)
            $table->string('phone_number', 20)->nullable(); // เบอร์โทรศัพท์ (ถ้าเป็นพร้อมเพย์)
            $table->enum('type', ['bank', 'promptpay'])->default('bank'); // ประเภท
            $table->string('qr_code', 500)->nullable(); // QR Code path
            $table->text('description')->nullable(); // คำอธิบาย
            $table->tinyInteger('status')->default(1); // สถานะ 1=เปิดใช้งาน, 0=ปิดใช้งาน
            $table->tinyInteger('is_active')->default(0); // 1=ใช้งานหลัก, 0=ไม่ได้ใช้งานหลัก
            $table->integer('position')->default(0); // ลำดับการแสดง
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banks');
    }
}
