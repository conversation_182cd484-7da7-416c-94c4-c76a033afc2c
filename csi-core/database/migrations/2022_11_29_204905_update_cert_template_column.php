<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateCertTemplateColumn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cert_template', function (Blueprint $table) {
            $table->text('email')->nullable()->after('name');
            $table->text('tel')->nullable()->after('email');
            $table->text('website')->nullable()->after('tel');
            $table->text('remark')->nullable()->after('website');
            $table->text('approve_name')->nullable()->after('approve_signature');
            $table->text('approve_position')->nullable()->after('approve_name');
            $table->text('receiver_name')->nullable()->after('receiver_signature');
            $table->text('receiver_position')->nullable()->after('receiver_name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
