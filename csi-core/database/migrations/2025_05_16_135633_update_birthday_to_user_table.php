<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateBirthdayToUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->dropColumn('user_id_bundit');
            $table->dropColumn('user_id_online_bundit');

            $table->string('tel')->nullable();
            $table->string('id_card')->nullable();
            $table->text('id_card_file')->nullable();
            $table->date('birthday')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->string('user_id_bundit')->nullable();
            $table->string('user_id_online_bundit')->nullable();

            $table->dropColumn('tel');
            $table->dropColumn('id_card');
            $table->dropColumn('id_card_file');
            $table->dropColumn('birthday');
        });
    }
}
