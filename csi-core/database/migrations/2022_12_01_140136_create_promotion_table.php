<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePromotionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_promotion', function (Blueprint $table) {
            $table->id();
            $table->text('title')->nullable();
            $table->text('course')->nullable();
            $table->integer('promotion_type')->default(2)->comment('1:Baht/2:Percent');
            $table->integer('value')->default(0);
            $table->datetime('pro_started')->nullable();
            $table->datetime('pro_end')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('course_promotion_log', function (Blueprint $table) {
            $table->id();
            $table->integer('promotion_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
        Schema::table('course', function (Blueprint $table) {
            $table->integer('promotion_type')->default(2)->comment('1:Baht/2:Percent');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('promotion');
    }
}
