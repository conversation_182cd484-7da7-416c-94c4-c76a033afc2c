<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSpeakerCertifyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('categories_speaker', function (Blueprint $table) {
            $table->text('certified')->nullable()->after('title_th');
            $table->text('biography')->nullable()->after('certified');
        });
        Schema::table('user_comment_log', function (Blueprint $table) {
            $table->float('rate')->nullable()->after('replied_comment');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
