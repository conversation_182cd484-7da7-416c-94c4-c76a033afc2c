<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubscriptionMainGalleryTable extends Migration
{
    public function up()
    {
        Schema::create('subscription_main_gallery', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('subscription_main_id');
            $table->string('image_path')->comment('path ของรูปภาพ');
            $table->string('image_alt')->nullable()->comment('alt text');
            $table->integer('position')->default(0)->comment('ลำดับการแสดง');
            $table->integer('status')->default(1)->comment('สถานะ');
            $table->timestamps();
            
            $table->foreign('subscription_main_id')->references('id')->on('subscription_main')->onDelete('cascade');
            $table->index(['subscription_main_id', 'status']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('subscription_main_gallery');
    }
}