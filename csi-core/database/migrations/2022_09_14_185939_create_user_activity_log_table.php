<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserActivityLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_activity_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('activity_type')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->integer('article_id')->nullable();
            $table->integer('info_id')->nullable();
            $table->timestamps();
        });
        Schema::create('user_cart_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('action')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->integer('group_id')->nullable();
            $table->integer('subscription_id')->nullable();
            $table->integer('web_code_id')->nullable();
            $table->text('web_code')->nullable();
            $table->integer('web_price')->nullable();
            $table->integer('code_id')->nullable();
            $table->text('discount_channel')->nullable();
            $table->text('discount_code')->nullable();
            $table->text('discount_price')->nullable();
            $table->timestamps();
        });
        Schema::create('user_certificate_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('certificate_type')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->text('cert_path')->nullable();
            $table->timestamps();
        });
        Schema::create('user_check_exam', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('user_comment_like', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('comment_id')->nullable();
            $table->timestamps();
        });
        Schema::create('user_comment_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('admin_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->integer('article_id')->nullable();
            $table->integer('info_id')->nullable();
            $table->text('comment')->nullable();
            $table->text('replied_comment')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('user_comment_reply', function (Blueprint $table) {
            $table->id();
            $table->integer('comment_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('admin_id')->nullable();
            $table->text('comment')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('user_exam_log', function (Blueprint $table) {
            $table->id();
            $table->integer('round')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->integer('exam_id')->nullable();
            $table->text('answer_text')->nullable();
            $table->text('question_text')->nullable();
            $table->integer('exam')->nullable();
            $table->integer('answer')->nullable();
            $table->integer('is_right')->default(1);
            $table->integer('point')->nullable();
            $table->text('file')->nullable();
            $table->text('remark')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('user_exam_point', function (Blueprint $table) {
            $table->id();
            $table->integer('round')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->integer('exam_id')->nullable();
            $table->integer('point')->nullable();
            $table->integer('max_point')->nullable();
            $table->integer('status')->default(2);
            $table->integer('check_status')->default(2);
            $table->text('remark')->nullable();
            $table->timestamps();
        });
        Schema::create('user_favorite_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('favorite_type')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->integer('article_id')->nullable();
            $table->integer('info_id')->nullable();
            $table->timestamps();
        });
        Schema::create('user_gift_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('order_id')->nullable();
            $table->text('receive_email')->nullable();
            $table->integer('receive_user_id')->nullable();
            $table->datetime('receive_date')->nullable();
            $table->integer('status')->default(2);
            $table->timestamps();
        });
        Schema::create('user_graduate_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
        Schema::create('user_history', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('type')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('order_list_id')->nullable();
            $table->integer('get_type')->default(1)->comment('1:free/2:buy/3:gift/4:internal/5:member/6:volume/7:assign');
            $table->text('zoom_join_url')->nullable();
            $table->integer('company_id')->nullable();
            $table->integer('company_lot_id')->nullable();
            $table->datetime('expired')->nullable();
            $table->integer('status')->default(2);
            $table->timestamps();
        });
        Schema::create('user_learning_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('action')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->time('watching_time')->nullable();
            $table->text('log_file')->nullable();
            $table->float('watch_percent')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('user_noti_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->text('image')->nullable();
            $table->text('title')->nullable();
            $table->text('description')->nullable();
            $table->text('cta')->nullable();
            $table->text('link')->nullable();
            $table->timestamps();
        });
        Schema::create('user_order_list', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id')->nullable();
            $table->integer('type')->default(1)->comment('1:course/2:group');
            $table->integer('course_id')->nullable();
            $table->integer('group_id')->nullable();
            $table->integer('subscription_id')->nullable();
            $table->text('registrant_id')->nullable();
            $table->float('price')->default(0);
            $table->integer('code_id')->nullable();
            $table->text('discount_code')->nullable();
            $table->float('discount_value')->default(0);
            $table->float('total_price')->default(0);
            $table->timestamps();
        });
        Schema::create('user_order_log', function (Blueprint $table) {
            $table->id();
            $table->integer('assign_id')->nullable();
            $table->bigInteger('order_no')->nullable()->comment('หมายเลขออเดอร์');
            $table->text('utm_source')->nullable();
            $table->bigInteger('user_id')->nullable();
            $table->integer('order_type')->nullable();
            $table->integer('buy_type')->default(1)->comment('1:normal/2:gift');
            $table->text('receive_email')->nullable();
            $table->text('receive_message')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('media_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->integer('payment_type')->nullable();
            $table->text('payment_remark')->nullable();
            $table->text('by_bank')->nullable();
            $table->text('to_bank')->nullable();
            $table->float('amount')->default(0);
            $table->datetime('t_date')->nullable();
            $table->text('remark')->nullable();
            $table->text('address_type')->nullable();
            $table->text('iden_no')->nullable();
            $table->text('name')->nullable();
            $table->text('lastname')->nullable();
            $table->text('address')->nullable();
            $table->text('subdistrict')->nullable();
            $table->text('district')->nullable();
            $table->text('province')->nullable();
            $table->text('postcode')->nullable();
            $table->text('receipt_type')->nullable();
            $table->text('receipt')->nullable();
            $table->text('receipt_path')->nullable();
            $table->text('image_slip')->nullable();
            $table->float('tax')->default(0);
            $table->text('tax_remark')->nullable();
            $table->float('price')->default(0);
            $table->text('discount_code')->nullable();
            $table->float('discount_value')->default(0);
            $table->float('total_price')->default(0);
            $table->text('reference')->nullable();
            $table->text('qr_id')->nullable();
            $table->integer('status')->default(1);
            $table->integer('is_free')->default(1);
            $table->integer('web_code_id')->nullable();
            $table->text('web_code')->nullable();
            $table->float('web_price')->default(0);
            $table->integer('company_type')->nullable();
            $table->text('company_branch')->nullable();
            $table->datetime('created_receipt_date')->nullable();
            $table->timestamps();
        });
        Schema::create('user_payment_log', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id')->nullable();
            $table->text('reference')->nullable();
            $table->timestamps();
        });
        Schema::create('user_playlist', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->text('title')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('user_playlist_log', function (Blueprint $table) {
            $table->id();
            $table->integer('playlist_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
        Schema::create('user_point_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('point_type')->nullable()->comment('1:receive/2:redeem');
            $table->integer('course_id')->nullable();
            $table->integer('seminar_id')->nullable();
            $table->integer('point')->nullable();
            $table->timestamps();
        });
        Schema::create('user_receipt_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('order_id')->nullable();
            $table->integer('receipt_type')->nullable()->comment('1:บุคคลธรรมดา/2:นิติบุคคล');
            $table->integer('tax_type')->nullable()->comment('1:ต้องการหัก ณ ที่จ่าย/2:ไม่ต้องการหัก ณ ที่จ่าย');
            $table->text('receipt_path')->nullable();
            $table->timestamps();
        });
        Schema::create('user_reply_comment_like', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('comment_id')->nullable();
            $table->integer('reply_id')->nullable();
            $table->timestamps();
        });
        Schema::create('user_verify', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->text('code')->nullable();
            $table->integer('status')->default(1)->comment('1:ยังไม่ได้รับการยืนยัน/2:ยืนยันแล้ว');
            $table->timestamps();
        });
        Schema::create('user_verify_internal', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->text('code')->nullable();
            $table->integer('status')->default(1)->comment('1:ยังไม่ได้รับการยืนยัน/2:ยืนยันแล้ว');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_activity_log');
    }
}
