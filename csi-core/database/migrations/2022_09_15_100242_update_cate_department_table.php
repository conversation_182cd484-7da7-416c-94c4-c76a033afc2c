<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCateDepartmentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('categories_department', function (Blueprint $table) {
            $table->integer('center')->nullable()->after('title_en')->comment('Center');
            $table->integer('division')->nullable()->after('center')->comment('Division');
            $table->text('dept_code')->nullable()->after('division')->comment('Department');
            $table->text('short_name')->nullable()->after('dept_code')->comment('Short Name');
            $table->text('gid')->nullable()->after('short_name')->comment('GID');
            $table->text('hrms_dept_id')->nullable()->after('gid')->comment('HRMS_DEPT_ID');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
