<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->string('user_id_bundit')->nullable()->comment("id นี้มาจาก bundit.org");
            $table->string('user_id_online_bundit')->nullable()->comment("id นี้มาจาก csisociety.com");
        }); //
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user', function (Blueprint $table) {
            $table->dropColumn(['user_id_bundit', 'user_id_online_bundit']);
        });
    }
}
