<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCourseProPeriodTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('course', function (Blueprint $table) {
            $table->integer('pro_period')->default(1)->after('pro_end')->comment('1:Y/2:N');
        });
        Schema::table('course_promotion', function (Blueprint $table) {
            $table->integer('pro_period')->default(1)->after('pro_end')->comment('1:Y/2:N');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
