<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserHrmsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('categories_interesting', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('categories_job_function', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });

        Schema::table('user', function (Blueprint $table) {
            $table->text('hrms')->nullable()->after('department');
            $table->integer('job_function')->nullable()->after('job_level');
            $table->text('interesting')->nullable()->after('job_function');
        });
        
        Schema::create('user_interesting_log', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('user');
            $table->foreignId('interesting_id')->constrained('categories_interesting');
            $table->timestamps();
        });

        Schema::table('course', function (Blueprint $table) {
            $table->text('job_function')->nullable()->after('department');
            $table->text('interesting')->nullable()->after('job_function');
        });

        Schema::create('course_interesting_log', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained('course');
            $table->foreignId('interesting_id')->constrained('categories_interesting');
            $table->timestamps();
        });

        Schema::create('course_job_function_log', function (Blueprint $table) {
            $table->id(); 
            $table->foreignId('course_id')->constrained('course');
            $table->foreignId('job_function_id')->constrained('categories_job_function');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
