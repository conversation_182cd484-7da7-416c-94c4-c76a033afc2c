<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateDynamicKeyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dynamic_key', function (Blueprint $table) {
            $table->id();
            $table->text('key')->nullable();
            $table->text('value')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('dynamic_page', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('key')->nullable();
            $table->longtext('details')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        

        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu1',
                'value' => 'คอร์สออนไลน์',
            )
        );

        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu2',
                'value' => 'คอร์สสอนสด',
            )
        );

        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu3',
                'value' => 'คอร์สสัมนา',
            )
        );

        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu4',
                'value' => 'Knowledge Shortcuts',
            )
        );

        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu5',
                'value' => 'ข่าวสาร',
            )
        );
        
        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu6',
                'value' => 'Infographic',
            )
        );
        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu7',
                'value' => 'About Us',
            )
        );
        DB::table('dynamic_key')->insert(
            array(
                'key' => 'menu8',
                'value' => 'Package',
            )
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dynamic_key');
    }
}
