<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubscriptionMainTable extends Migration
{
    public function up()
    {
        Schema::create('subscription_main', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable()->comment('ชื่อหลัก');
            $table->text('description')->nullable()->comment('รายละเอียดหลัก');
            $table->text('short_description')->nullable()->comment('รายละเอียดสั้น');
            $table->string('cover_image')->nullable()->comment('รูปภาพปก');
            $table->integer('period')->nullable()->comment('ระยะเวลาหลัก (วัน)');
            $table->decimal('price', 10, 2)->nullable()->comment('ราคาหลัก');
            $table->decimal('original_price', 10, 2)->nullable()->comment('ราคาเดิม');
            $table->string('slug')->unique()->nullable()->comment('URL slug');
            $table->text('features')->nullable()->comment('คุณสมบัติหลัก (JSON)');
            $table->string('color_theme')->nullable()->comment('สีธีม');
            $table->integer('position')->default(0)->comment('ลำดับการแสดง');
            $table->boolean('is_featured')->default(false)->comment('แนะนำ');
            $table->boolean('is_popular')->default(false)->comment('ยอดนิยม');
            $table->integer('status')->default(1)->comment('สถานะ 1=เปิด 0=ปิด');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('subscription_main');
    }
}