<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTelToUserOrderLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->string('tel', 20)->nullable(); // เบอร์โทรศัพท์
            $table->unsignedBigInteger('bank_id')->nullable(); // ID ของธนาคารที่ใช้
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->dropColumn(['tel', 'bank_id']);
        });
    }
}
