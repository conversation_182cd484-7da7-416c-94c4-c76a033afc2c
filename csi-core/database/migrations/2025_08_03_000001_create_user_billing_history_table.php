<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserBillingHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_billing_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('name')->nullable();
            $table->string('lastname')->nullable();
            $table->string('email')->nullable();
            $table->string('tel', 20)->nullable();
            $table->text('address')->nullable();
            $table->string('subdistrict', 100)->nullable();
            $table->string('district', 100)->nullable();
            $table->string('province', 100)->nullable();
            $table->string('postcode', 10)->nullable();
            $table->tinyInteger('address_type')->default(1)->comment('1=บุคคลธรรมดา, 2=นิติบุคคล');
            $table->string('iden_no', 20)->nullable()->comment('เลขประจำตัว/เลขผู้เสียภาษี');
            $table->string('company_type', 100)->nullable()->comment('สำหรับนิติบุคคล');
            $table->string('company_branch', 100)->nullable()->comment('สำหรับนิติบุคคล');
            $table->timestamps();
            
            $table->index('user_id');
            $table->index('created_at');
            $table->foreign('user_id')->references('id')->on('user')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_billing_history');
    }
}
