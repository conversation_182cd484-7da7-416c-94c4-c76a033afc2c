<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAssignCourseTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assign_course', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('select_type')->defualt(1);
            $table->text('user')->nullable();
            $table->text('group')->nullable();
            $table->timestamps();
        });

        Schema::create('assign_course_cate', function (Blueprint $table) {
            $table->id();
            $table->integer('assign_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->text('course_name')->nullable();
            $table->integer('cate_id')->nullable();
            $table->timestamps();
        });

        Schema::create('assign_course_log', function (Blueprint $table) {
            $table->id();
            $table->integer('assign_id')->nullable();
            $table->text('course_name')->nullable();
            $table->integer('user')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assign_course');
    }
}
