<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRequiresVatToCourseAndSubscriptionTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('course', function (Blueprint $table) {
            $table->boolean('requires_vat')->default(true)->after('status')->comment('Whether this course requires VAT calculation');
        });

        Schema::table('subscription', function (Blueprint $table) {
            $table->boolean('requires_vat')->default(true)->after('status')->comment('Whether this subscription requires VAT calculation');
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('course', function (Blueprint $table) {
            $table->dropColumn('requires_vat');
        });

        Schema::table('subscription', function (Blueprint $table) {
            $table->dropColumn('requires_vat');
        });

    }
}
