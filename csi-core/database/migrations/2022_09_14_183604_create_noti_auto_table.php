<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateNotiAutoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('noti_auto', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('image')->nullable();
            $table->text('title')->nullable();
            $table->text('description')->nullable();
            $table->text('cta')->nullable();
            $table->text('link')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('noti_auto_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('noti_auto_id')->nullable();
            $table->text('image')->nullable();
            $table->text('title')->nullable();
            $table->text('description')->nullable();
            $table->text('cta')->nullable();
            $table->text('link')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('noti_global', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('image')->nullable();
            $table->text('title')->nullable();
            $table->text('description')->nullable();
            $table->text('cta')->nullable();
            $table->text('link')->nullable();
            $table->datetime('started_date')->nullable();
            $table->datetime('end_date')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('noti_global_log', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('noti_global_id')->nullable();
            $table->timestamps();
        });
        Schema::create('oculus_pin', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('pin')->nullable();
            $table->timestamps();
        });
        Schema::create('point_setting', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->default(1);
            $table->integer('point')->nullable();
            $table->integer('value')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        

        DB::table('noti_auto')->insert(
            array(
                'name' => 'approve_payment',
                'title' => 'การสั่งซื้อของท่านได้รับการยืนยันแล้ว',
                'description' => 'ท่านสามารถทำการตรวสอบโดย',
                'cta' => 'click!',
                'link' => 'https://csisociety.com/dashboard/history',
            )
        );

        DB::table('noti_auto')->insert(
            array(
                'name' => 'reject_payment',
                'title' => 'การสั่งซื้อของท่านได้รับการปกิเสธแล้ว',
                'description' => 'ท่านสามารถทำการตรวสอบโดย',
                'cta' => 'click!',
                'link' => 'https://csisociety.com/dashboard/history',
            )
        );
        
        DB::table('noti_auto')->insert(
            array(
                'name' => 'assign_course',
                'title' => 'ท่านได้รับการ Assign Course',
                'description' => 'ท่านสามารถทำการตรวสอบโดย',
                'cta' => 'click!',
                'link' => 'https://csisociety.com/dashboard',
            )
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('noti_auto');
    }
}
