<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBannerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('home_banner', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->default(1)->comment('1:link/2:non link');
            $table->text('name')->nullable();
            $table->text('image_th_desktop')->nullable();
            $table->text('image_th_mobile')->nullable();
            $table->text('image_en_desktop')->nullable();
            $table->text('image_en_mobile')->nullable();
            $table->text('cta')->nullable();
            $table->text('link')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('login_background', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('image_desktop')->nullable();
            $table->text('image_mobile')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('web_background', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('image_desktop')->nullable();
            $table->text('image_mobile')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('single_sponsor_banner', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('image_th_desktop')->nullable();
            $table->text('image_th_mobile')->nullable();
            $table->text('image_en_desktop')->nullable();
            $table->text('image_en_mobile')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('multi_sponsor_banner', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->text('image_th_desktop')->nullable();
            $table->text('image_th_mobile')->nullable();
            $table->text('image_en_desktop')->nullable();
            $table->text('image_en_mobile')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('sponsor', function (Blueprint $table) {
            $table->id();
            $table->text('name_th')->nullable();
            $table->text('name_en')->nullable();
            $table->text('image')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('sponsor_pin', function (Blueprint $table) {
            $table->id();
            $table->integer('sponsor')->nullable();
            $table->integer('position')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banner');
    }
}
