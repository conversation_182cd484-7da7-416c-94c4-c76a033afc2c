<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTagTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tag', function (Blueprint $table) {
            $table->id();
            $table->text('title')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
        Schema::create('tag_learner', function (Blueprint $table) {
            $table->id();
            $table->integer('learner_id')->nullable();
            $table->integer('user_type')->nullable();
            $table->timestamps();
        });
        Schema::create('top_course_log', function (Blueprint $table) {
            $table->id();
            $table->integer('course_id')->nullable();
            $table->integer('count')->nullable();
            $table->date('date')->nullable();
            $table->timestamps();
        });
        Schema::create('top_speaker_log', function (Blueprint $table) {
            $table->id();
            $table->integer('speaker_id')->nullable();
            $table->integer('count')->nullable();
            $table->date('date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tag');
    }
}
