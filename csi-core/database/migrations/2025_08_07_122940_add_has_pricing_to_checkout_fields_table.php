<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddHasPricingToCheckoutFieldsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('checkout_fields', function (Blueprint $table) {
            $table->boolean('has_pricing')->default(false)->after('field_options')
                  ->comment('Whether this field options affect pricing calculation');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('checkout_fields', function (Blueprint $table) {
            $table->dropColumn('has_pricing');
        });
    }
}
