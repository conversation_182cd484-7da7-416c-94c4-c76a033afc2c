<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGroupLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('group_log', function (Blueprint $table) {
            $table->id();
            $table->integer('group_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
        Schema::create('group_code', function (Blueprint $table) {
            $table->id();
            $table->text('group_code')->nullable();
            $table->integer('in_a')->default(1);
            $table->integer('in_b')->default(1);
            $table->integer('in_c')->default(1);
            $table->integer('in_d')->default(1);
            $table->integer('ex_a')->default(1);
            $table->integer('ex_b')->default(1);
            $table->integer('ex_c')->default(1);
            $table->integer('ex_d')->default(1);
            $table->integer('ex_e')->default(1);
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('group_log');
    }
}
