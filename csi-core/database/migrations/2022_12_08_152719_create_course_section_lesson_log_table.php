<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseSectionLessonLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_section_lesson_log', function (Blueprint $table) {
            $table->id();
            $table->integer('section_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->integer('lesson_id')->nullable();
            $table->timestamps();
        });
        Schema::table('course_section', function (Blueprint $table) {
            $table->text('lesson')->nullable()->after('lesson_id');
        });
        Schema::table('course', function (Blueprint $table) {
            $table->integer('is_time')->default(2)->after('duration_time')->comment('1:Y/2:N');
            $table->integer('time_set')->default(0)->after('is_time');
            $table->integer('is_doc')->default(2)->after('time_set')->comment('1:Y/2:N');
            $table->text('doc_text')->nullable()->after('is_doc');
        });
        Schema::table('user_order_log', function (Blueprint $table) {
            $table->text('doc_address')->nullable()->after('postcode');
            $table->text('doc_subdistrict')->nullable()->after('doc_address');
            $table->text('doc_district')->nullable()->after('doc_subdistrict');
            $table->text('doc_province')->nullable()->after('doc_district');
            $table->text('doc_postcode')->nullable()->after('doc_province');
        });
        Schema::table('course_promotion', function (Blueprint $table) {
            $table->integer('is_all')->default(2)->comment('1:Y/2:N')->after('title');
        });
        Schema::create('course_promotion_excluding', function (Blueprint $table) {
            $table->id();
            $table->integer('promotion_id')->nullable();
            $table->integer('course_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_section_lesson_log');
    }
}
